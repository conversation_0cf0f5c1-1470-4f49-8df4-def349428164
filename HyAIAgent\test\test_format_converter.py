"""
格式转换工具测试模块

测试FormatConverter类的所有功能，包括文件格式转换、编码转换和批量转换。
"""

import pytest
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import MagicMock
import asyncio

# 导入被测试的类
from utils.format_converter import FormatConverter


class TestFormatConverter:
    """FormatConverter测试类"""
    
    @pytest.fixture
    def temp_workspace(self):
        """创建临时工作空间"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def format_converter(self, temp_workspace):
        """创建FormatConverter实例"""
        converter = FormatConverter(temp_workspace)
        # Mock安全管理器以允许测试文件访问
        converter.security_manager.validate_operation = MagicMock(return_value={'allowed': True, 'reason': 'test'})
        return converter
    
    @pytest.fixture
    def sample_data_files(self, temp_workspace):
        """创建示例数据文件"""
        files = {}
        
        # JSON文件
        json_data = {
            "name": "TestApp",
            "version": "1.0.0",
            "config": {
                "debug": True,
                "port": 8080
            },
            "features": ["auth", "logging"]
        }
        
        json_path = Path(temp_workspace) / "test.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2)
        files['json'] = str(json_path)
        
        # CSV文件
        csv_content = """name,age,city
Alice,25,New York
Bob,30,London
Charlie,35,Tokyo"""
        
        csv_path = Path(temp_workspace) / "test.csv"
        with open(csv_path, 'w', encoding='utf-8') as f:
            f.write(csv_content)
        files['csv'] = str(csv_path)
        
        # XML文件
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<root>
    <person id="1">
        <name>Alice</name>
        <age>25</age>
    </person>
    <person id="2">
        <name>Bob</name>
        <age>30</age>
    </person>
</root>"""
        
        xml_path = Path(temp_workspace) / "test.xml"
        with open(xml_path, 'w', encoding='utf-8') as f:
            f.write(xml_content)
        files['xml'] = str(xml_path)
        
        # 文本文件
        txt_content = """This is a test file.
It contains multiple lines.
Used for testing format conversion."""
        
        txt_path = Path(temp_workspace) / "test.txt"
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(txt_content)
        files['txt'] = str(txt_path)
        
        return files
    
    @pytest.mark.asyncio
    async def test_json_to_yaml_conversion(self, format_converter, sample_data_files, temp_workspace):
        """测试JSON到YAML的转换"""
        source_path = sample_data_files['json']
        target_path = Path(temp_workspace) / "converted.yaml"
        
        result = await format_converter.convert_file(
            source_path, str(target_path), 'yaml'
        )
        
        if result['status'] == 'error' and 'PyYAML' in result.get('error', ''):
            pytest.skip("PyYAML not available")
        
        assert result['status'] == 'success'
        assert result['source_format'] == 'json'
        assert result['target_format'] == 'yaml'
        assert target_path.exists()
        assert result['target_size'] > 0
    
    @pytest.mark.asyncio
    async def test_csv_to_json_conversion(self, format_converter, sample_data_files, temp_workspace):
        """测试CSV到JSON的转换"""
        source_path = sample_data_files['csv']
        target_path = Path(temp_workspace) / "converted.json"
        
        result = await format_converter.convert_file(
            source_path, str(target_path), 'json'
        )
        
        assert result['status'] == 'success'
        assert result['source_format'] == 'csv'
        assert result['target_format'] == 'json'
        assert target_path.exists()
        
        # 验证转换后的JSON内容
        with open(target_path, 'r', encoding='utf-8') as f:
            converted_data = json.load(f)
        
        assert isinstance(converted_data, list)
        assert len(converted_data) == 3
        assert converted_data[0]['name'] == 'Alice'
    
    @pytest.mark.asyncio
    async def test_xml_to_json_conversion(self, format_converter, sample_data_files, temp_workspace):
        """测试XML到JSON的转换"""
        source_path = sample_data_files['xml']
        target_path = Path(temp_workspace) / "converted.json"
        
        result = await format_converter.convert_file(
            source_path, str(target_path), 'json'
        )
        
        assert result['status'] == 'success'
        assert result['source_format'] == 'xml'
        assert result['target_format'] == 'json'
        assert target_path.exists()
        
        # 验证转换后的JSON内容
        with open(target_path, 'r', encoding='utf-8') as f:
            converted_data = json.load(f)
        
        assert isinstance(converted_data, dict)
        assert 'person' in converted_data
    
    @pytest.mark.asyncio
    async def test_text_file_conversion(self, format_converter, sample_data_files, temp_workspace):
        """测试文本文件转换"""
        source_path = sample_data_files['txt']
        target_path = Path(temp_workspace) / "converted.md"
        
        result = await format_converter.convert_file(
            source_path, str(target_path), 'md'
        )
        
        assert result['status'] == 'success'
        assert result['source_format'] == 'txt'
        assert result['target_format'] == 'md'
        assert target_path.exists()
        
        # 验证内容保持一致
        with open(source_path, 'r', encoding='utf-8') as f:
            source_content = f.read()
        with open(target_path, 'r', encoding='utf-8') as f:
            target_content = f.read()
        
        assert source_content == target_content
    
    @pytest.mark.asyncio
    async def test_batch_conversion(self, format_converter, sample_data_files, temp_workspace):
        """测试批量转换"""
        file_mappings = [
            {
                'source': sample_data_files['json'],
                'target': str(Path(temp_workspace) / "batch1.txt"),
                'format': 'txt'
            },
            {
                'source': sample_data_files['csv'],
                'target': str(Path(temp_workspace) / "batch2.txt"),
                'format': 'txt'
            }
        ]
        
        result = await format_converter.batch_convert_files(file_mappings)
        
        assert result['status'] == 'success'
        assert result['total_files'] == 2
        assert result['successful_conversions'] >= 1  # 至少一个成功
        
        # 检查文件是否创建
        for mapping in file_mappings:
            assert Path(mapping['target']).exists()
    
    @pytest.mark.asyncio
    async def test_encoding_conversion(self, format_converter, temp_workspace):
        """测试编码转换"""
        # 创建GBK编码的文件
        test_content = "这是一个测试文件，包含中文字符。"
        test_path = Path(temp_workspace) / "test_gbk.txt"
        
        with open(test_path, 'w', encoding='gbk') as f:
            f.write(test_content)
        
        # 转换为UTF-8
        result = await format_converter.convert_encoding(str(test_path), 'utf-8')
        
        assert result['status'] == 'success'
        assert result['target_encoding'] == 'utf-8'
        
        # 验证转换后的内容
        with open(test_path, 'r', encoding='utf-8') as f:
            converted_content = f.read()
        
        assert converted_content == test_content
    
    @pytest.mark.asyncio
    async def test_format_detection(self, format_converter):
        """测试格式检测"""
        test_cases = [
            ('test.json', 'json'),
            ('test.yaml', 'yaml'),
            ('test.yml', 'yaml'),
            ('test.ini', 'ini'),
            ('test.cfg', 'ini'),
            ('test.xml', 'xml'),
            ('test.csv', 'csv'),
            ('test.txt', 'txt')
        ]
        
        for filename, expected_format in test_cases:
            detected_format = format_converter._detect_file_format(filename)
            assert detected_format == expected_format
    
    @pytest.mark.asyncio
    async def test_error_handling(self, format_converter, temp_workspace):
        """测试错误处理"""
        # 测试不存在的源文件
        result = await format_converter.convert_file(
            "nonexistent.json",
            str(Path(temp_workspace) / "output.yaml"),
            'yaml'
        )
        assert result['status'] == 'error'
        
        # 测试无效的JSON文件
        invalid_json_path = Path(temp_workspace) / "invalid.json"
        with open(invalid_json_path, 'w') as f:
            f.write('{"invalid": json content}')
        
        result = await format_converter.convert_file(
            str(invalid_json_path),
            str(Path(temp_workspace) / "output.yaml"),
            'yaml'
        )
        assert result['status'] == 'error'
    
    def test_statistics_tracking(self, format_converter):
        """测试统计信息跟踪"""
        # 重置统计
        format_converter.reset_stats()
        initial_stats = format_converter.get_conversion_stats()
        assert initial_stats['total_conversions'] == 0
        
        # 更新统计
        format_converter._update_stats('successful', 'json', 'yaml')
        format_converter._update_stats('failed', 'xml', 'json')
        
        # 检查统计更新
        stats = format_converter.get_conversion_stats()
        assert stats['total_conversions'] == 2
        assert stats['successful_conversions'] == 1
        assert stats['failed_conversions'] == 1
        assert 'json_to_yaml' in stats['formats_converted']
    
    def test_supported_formats(self, format_converter):
        """测试支持的格式列表"""
        formats = format_converter.get_supported_formats()
        
        assert 'text' in formats
        assert 'config' in formats
        assert 'data' in formats
        assert 'markup' in formats
        
        assert 'json' in formats['config']
        assert 'csv' in formats['data']
        assert 'txt' in formats['text']
    
    @pytest.mark.asyncio
    async def test_xml_dict_conversion(self, format_converter):
        """测试XML和字典之间的转换"""
        # 测试字典到XML
        test_dict = {
            'root': {
                'name': 'test',
                'value': 123,
                'items': ['item1', 'item2']
            }
        }
        
        xml_content = format_converter._dict_to_xml(test_dict)
        assert '<?xml' in xml_content
        assert '<root>' in xml_content
        assert '<name>test</name>' in xml_content
    
    @pytest.mark.asyncio
    async def test_validation_functionality(self, format_converter, sample_data_files, temp_workspace):
        """测试转换验证功能"""
        source_path = sample_data_files['json']
        target_path = Path(temp_workspace) / "validated.json"
        
        # 先进行转换
        result = await format_converter.convert_file(
            source_path, str(target_path), 'json', validation=True
        )
        
        assert result['status'] == 'success'
        assert 'validation' in result
        validation = result['validation']
        assert 'valid' in validation
        assert validation['target_exists'] == True
