"""
报告生成器模块

提供自动报告生成引擎、多种报告格式支持、报告模板系统和数据源集成功能。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import tempfile
import os

# 可选依赖处理
try:
    from jinja2 import Template, Environment, FileSystemLoader
    JINJA2_AVAILABLE = True
except ImportError:
    JINJA2_AVAILABLE = False

try:
    import markdown
    MARKDOWN_AVAILABLE = True
except ImportError:
    MARKDOWN_AVAILABLE = False

try:
    from weasyprint import HTML, CSS
    WEASYPRINT_AVAILABLE = True
except ImportError:
    WEASYPRINT_AVAILABLE = False


class ReportFormat(Enum):
    """报告格式枚举"""
    HTML = "html"
    PDF = "pdf"
    MARKDOWN = "markdown"
    JSON = "json"
    TXT = "txt"
    DOCX = "docx"


class DataSourceType(Enum):
    """数据源类型枚举"""
    JSON = "json"
    CSV = "csv"
    DATABASE = "database"
    API = "api"
    FILE = "file"


@dataclass
class ReportTemplate:
    """报告模板"""
    template_id: str
    name: str
    description: str
    template_content: str
    format: ReportFormat
    variables: List[str] = field(default_factory=list)
    sections: List[str] = field(default_factory=list)
    style_config: Dict[str, Any] = field(default_factory=dict)
    usage_count: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class DataSource:
    """数据源配置"""
    source_id: str
    name: str
    source_type: DataSourceType
    connection_config: Dict[str, Any]
    query_config: Dict[str, Any] = field(default_factory=dict)
    refresh_interval: int = 3600  # 秒
    last_updated: datetime = field(default_factory=datetime.now)
    is_active: bool = True


@dataclass
class ReportConfig:
    """报告配置"""
    report_id: str
    title: str
    template_id: str
    data_sources: List[str]
    format: ReportFormat
    output_path: Optional[str] = None
    variables: Dict[str, Any] = field(default_factory=dict)
    auto_refresh: bool = False
    refresh_interval: int = 3600
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class ReportResult:
    """报告生成结果"""
    report_id: str
    title: str
    format: ReportFormat
    file_path: str
    file_size: int
    generation_time: float
    data_sources_used: List[str]
    variables_used: Dict[str, Any]
    generated_at: datetime = field(default_factory=datetime.now)
    status: str = "success"
    error_message: Optional[str] = None


class ReportBuilder:
    """报告构建器"""
    
    def __init__(self, templates_dir: Optional[str] = None):
        """初始化报告构建器"""
        self.logger = logging.getLogger(__name__)
        self.templates_dir = templates_dir or "templates"
        self.templates: Dict[str, ReportTemplate] = {}
        self.data_sources: Dict[str, DataSource] = {}
        self.reports: Dict[str, ReportConfig] = {}
        
        # 初始化模板引擎
        if JINJA2_AVAILABLE:
            self.jinja_env = Environment(
                loader=FileSystemLoader(self.templates_dir) if os.path.exists(self.templates_dir) else None
            )
        else:
            self.jinja_env = None
        
        self.logger.info(f"报告构建器初始化完成，Jinja2可用: {JINJA2_AVAILABLE}")
    
    async def create_template(self, template: ReportTemplate) -> bool:
        """创建报告模板"""
        try:
            self.templates[template.template_id] = template
            self.logger.info(f"模板创建成功: {template.template_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"模板创建失败: {e}")
            return False
    
    async def get_template(self, template_id: str) -> Optional[ReportTemplate]:
        """获取报告模板"""
        return self.templates.get(template_id)
    
    async def list_templates(self) -> List[ReportTemplate]:
        """列出所有模板"""
        return list(self.templates.values())
    
    async def add_data_source(self, data_source: DataSource) -> bool:
        """添加数据源"""
        try:
            self.data_sources[data_source.source_id] = data_source
            self.logger.info(f"数据源添加成功: {data_source.source_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据源添加失败: {e}")
            return False
    
    async def get_data_source(self, source_id: str) -> Optional[DataSource]:
        """获取数据源"""
        return self.data_sources.get(source_id)
    
    async def fetch_data_from_source(self, source_id: str) -> Dict[str, Any]:
        """从数据源获取数据"""
        try:
            source = self.data_sources.get(source_id)
            if not source:
                raise ValueError(f"数据源不存在: {source_id}")
            
            if not source.is_active:
                raise ValueError(f"数据源未激活: {source_id}")
            
            # 根据数据源类型获取数据
            if source.source_type == DataSourceType.JSON:
                return await self._fetch_json_data(source)
            elif source.source_type == DataSourceType.CSV:
                return await self._fetch_csv_data(source)
            elif source.source_type == DataSourceType.FILE:
                return await self._fetch_file_data(source)
            elif source.source_type == DataSourceType.API:
                return await self._fetch_api_data(source)
            else:
                raise ValueError(f"不支持的数据源类型: {source.source_type}")
                
        except Exception as e:
            self.logger.error(f"数据获取失败: {e}")
            return {}
    
    async def _fetch_json_data(self, source: DataSource) -> Dict[str, Any]:
        """获取JSON数据"""
        file_path = source.connection_config.get("file_path")
        if not file_path or not os.path.exists(file_path):
            return {}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    async def _fetch_csv_data(self, source: DataSource) -> Dict[str, Any]:
        """获取CSV数据"""
        file_path = source.connection_config.get("file_path")
        if not file_path or not os.path.exists(file_path):
            return {}
        
        # 简单的CSV读取实现
        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            if lines:
                headers = lines[0].strip().split(',')
                for line in lines[1:]:
                    values = line.strip().split(',')
                    if len(values) == len(headers):
                        data.append(dict(zip(headers, values)))
        
        return {"data": data, "count": len(data)}
    
    async def _fetch_file_data(self, source: DataSource) -> Dict[str, Any]:
        """获取文件数据"""
        file_path = source.connection_config.get("file_path")
        if not file_path or not os.path.exists(file_path):
            return {}

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # 如果UTF-8解码失败，尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except UnicodeDecodeError:
                # 如果仍然失败，以二进制模式读取
                with open(file_path, 'rb') as f:
                    content = f.read().decode('utf-8', errors='ignore')

        return {
            "content": content,
            "file_path": file_path,
            "file_size": os.path.getsize(file_path),
            "last_modified": datetime.fromtimestamp(os.path.getmtime(file_path))
        }
    
    async def _fetch_api_data(self, source: DataSource) -> Dict[str, Any]:
        """获取API数据"""
        # 这里应该实现HTTP请求，暂时返回模拟数据
        return {
            "api_url": source.connection_config.get("url", ""),
            "status": "success",
            "data": {"message": "API数据获取功能需要实现HTTP客户端"}
        }
    
    async def generate_report(self, config: ReportConfig) -> ReportResult:
        """生成报告"""
        try:
            start_time = datetime.now()
            
            # 获取模板
            template = await self.get_template(config.template_id)
            if not template:
                raise ValueError(f"模板不存在: {config.template_id}")
            
            # 收集数据
            report_data = {}
            for source_id in config.data_sources:
                source_data = await self.fetch_data_from_source(source_id)
                report_data[source_id] = source_data
            
            # 合并变量
            all_variables = {**config.variables, **report_data}
            
            # 渲染报告内容
            content = await self._render_template(template, all_variables)
            
            # 生成输出文件
            output_path = config.output_path or f"report_{config.report_id}.{config.format.value}"
            file_path = await self._save_report(content, output_path, config.format)
            
            # 计算生成时间
            generation_time = (datetime.now() - start_time).total_seconds()
            
            # 创建结果
            result = ReportResult(
                report_id=config.report_id,
                title=config.title,
                format=config.format,
                file_path=file_path,
                file_size=os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                generation_time=generation_time,
                data_sources_used=config.data_sources,
                variables_used=all_variables
            )
            
            # 更新模板使用次数
            template.usage_count += 1
            
            self.logger.info(f"报告生成成功: {config.report_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"报告生成失败: {e}")
            return ReportResult(
                report_id=config.report_id,
                title=config.title,
                format=config.format,
                file_path="",
                file_size=0,
                generation_time=0,
                data_sources_used=config.data_sources,
                variables_used={},
                status="error",
                error_message=str(e)
            )
    
    async def _render_template(self, template: ReportTemplate, variables: Dict[str, Any]) -> str:
        """渲染模板"""
        try:
            if JINJA2_AVAILABLE and self.jinja_env:
                # 使用Jinja2渲染
                jinja_template = Template(template.template_content)
                return jinja_template.render(**variables)
            else:
                # 简单的字符串替换
                content = template.template_content
                for key, value in variables.items():
                    placeholder = f"{{{{{key}}}}}"
                    content = content.replace(placeholder, str(value))
                return content
                
        except Exception as e:
            self.logger.error(f"模板渲染失败: {e}")
            return f"模板渲染错误: {e}"
    
    async def _save_report(self, content: str, output_path: str, format: ReportFormat) -> str:
        """保存报告文件"""
        try:
            if format == ReportFormat.HTML:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
            elif format == ReportFormat.PDF:
                if WEASYPRINT_AVAILABLE:
                    HTML(string=content).write_pdf(output_path)
                else:
                    # 如果WeasyPrint不可用，保存为HTML
                    html_path = output_path.replace('.pdf', '.html')
                    with open(html_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    output_path = html_path
                    
            elif format == ReportFormat.MARKDOWN:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
            elif format == ReportFormat.TXT:
                # 移除HTML标签（简单实现）
                import re
                text_content = re.sub(r'<[^>]+>', '', content)
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                    
            else:
                # 默认保存为文本
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            return output_path
            
        except Exception as e:
            self.logger.error(f"报告保存失败: {e}")
            raise

    async def create_report_config(self, config: ReportConfig) -> bool:
        """创建报告配置"""
        try:
            self.reports[config.report_id] = config
            self.logger.info(f"报告配置创建成功: {config.report_id}")
            return True

        except Exception as e:
            self.logger.error(f"报告配置创建失败: {e}")
            return False

    async def get_report_config(self, report_id: str) -> Optional[ReportConfig]:
        """获取报告配置"""
        return self.reports.get(report_id)

    async def list_reports(self) -> List[ReportConfig]:
        """列出所有报告配置"""
        return list(self.reports.values())

    async def update_report_config(self, report_id: str, updates: Dict[str, Any]) -> bool:
        """更新报告配置"""
        try:
            if report_id not in self.reports:
                return False

            config = self.reports[report_id]
            for key, value in updates.items():
                if hasattr(config, key):
                    setattr(config, key, value)

            config.updated_at = datetime.now()
            self.logger.info(f"报告配置更新成功: {report_id}")
            return True

        except Exception as e:
            self.logger.error(f"报告配置更新失败: {e}")
            return False

    async def delete_report_config(self, report_id: str) -> bool:
        """删除报告配置"""
        try:
            if report_id in self.reports:
                del self.reports[report_id]
                self.logger.info(f"报告配置删除成功: {report_id}")
                return True
            return False

        except Exception as e:
            self.logger.error(f"报告配置删除失败: {e}")
            return False

    async def schedule_report(self, report_id: str, interval: int = 3600) -> bool:
        """调度报告自动生成"""
        try:
            config = self.reports.get(report_id)
            if not config:
                return False

            config.auto_refresh = True
            config.refresh_interval = interval
            config.updated_at = datetime.now()

            self.logger.info(f"报告调度设置成功: {report_id}, 间隔: {interval}秒")
            return True

        except Exception as e:
            self.logger.error(f"报告调度设置失败: {e}")
            return False

    async def get_report_status(self, report_id: str) -> Dict[str, Any]:
        """获取报告状态"""
        config = self.reports.get(report_id)
        if not config:
            return {"status": "not_found"}

        return {
            "report_id": report_id,
            "title": config.title,
            "format": config.format.value,
            "auto_refresh": config.auto_refresh,
            "refresh_interval": config.refresh_interval,
            "data_sources_count": len(config.data_sources),
            "created_at": config.created_at.isoformat(),
            "updated_at": config.updated_at.isoformat(),
            "status": "active" if config.auto_refresh else "inactive"
        }

    async def validate_template(self, template: ReportTemplate) -> Dict[str, Any]:
        """验证模板"""
        try:
            validation_result = {
                "valid": True,
                "errors": [],
                "warnings": []
            }

            # 检查模板内容
            if not template.template_content.strip():
                validation_result["valid"] = False
                validation_result["errors"].append("模板内容不能为空")

            # 检查变量
            if JINJA2_AVAILABLE:
                try:
                    jinja_template = Template(template.template_content)
                    # 尝试渲染空变量
                    jinja_template.render()
                except Exception as e:
                    validation_result["warnings"].append(f"模板语法警告: {e}")

            return validation_result

        except Exception as e:
            return {
                "valid": False,
                "errors": [f"模板验证失败: {e}"],
                "warnings": []
            }

    async def get_available_formats(self) -> List[str]:
        """获取可用的报告格式"""
        formats = [format.value for format in ReportFormat]

        # 根据依赖可用性过滤格式
        if not WEASYPRINT_AVAILABLE:
            # PDF格式需要WeasyPrint
            pass  # 仍然返回PDF，但在生成时会降级为HTML

        if not MARKDOWN_AVAILABLE:
            # Markdown格式需要markdown库
            pass  # 仍然返回，使用简单实现

        return formats

    async def get_template_variables(self, template_id: str) -> List[str]:
        """获取模板中的变量"""
        template = await self.get_template(template_id)
        if not template:
            return []

        # 简单的变量提取（查找{{variable}}模式）
        import re
        variables = re.findall(r'\{\{(\w+)\}\}', template.template_content)
        return list(set(variables))

    async def preview_report(self, config: ReportConfig, sample_data: Optional[Dict[str, Any]] = None) -> str:
        """预览报告（不保存文件）"""
        try:
            template = await self.get_template(config.template_id)
            if not template:
                return "模板不存在"

            # 使用示例数据或空数据
            preview_data = sample_data or {}
            for source_id in config.data_sources:
                if source_id not in preview_data:
                    preview_data[source_id] = {"preview": True, "data": "示例数据"}

            # 合并变量
            all_variables = {**config.variables, **preview_data}

            # 渲染模板
            content = await self._render_template(template, all_variables)
            return content

        except Exception as e:
            self.logger.error(f"报告预览失败: {e}")
            return f"预览失败: {e}"

    async def export_template(self, template_id: str, export_path: str) -> bool:
        """导出模板"""
        try:
            template = await self.get_template(template_id)
            if not template:
                return False

            template_data = {
                "template_id": template.template_id,
                "name": template.name,
                "description": template.description,
                "template_content": template.template_content,
                "format": template.format.value,
                "variables": template.variables,
                "sections": template.sections,
                "style_config": template.style_config,
                "created_at": template.created_at.isoformat(),
                "updated_at": template.updated_at.isoformat()
            }

            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"模板导出成功: {template_id} -> {export_path}")
            return True

        except Exception as e:
            self.logger.error(f"模板导出失败: {e}")
            return False

    async def import_template(self, import_path: str) -> Optional[str]:
        """导入模板"""
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)

            template = ReportTemplate(
                template_id=template_data["template_id"],
                name=template_data["name"],
                description=template_data["description"],
                template_content=template_data["template_content"],
                format=ReportFormat(template_data["format"]),
                variables=template_data.get("variables", []),
                sections=template_data.get("sections", []),
                style_config=template_data.get("style_config", {})
            )

            success = await self.create_template(template)
            if success:
                self.logger.info(f"模板导入成功: {import_path} -> {template.template_id}")
                return template.template_id

            return None

        except Exception as e:
            self.logger.error(f"模板导入失败: {e}")
            return None
