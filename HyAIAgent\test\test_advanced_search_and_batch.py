"""
测试高级文件搜索和批量处理功能
步骤3.11和3.12的集成测试
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta

from operations.file_operations import FileOperations
from operations.batch_processor import BatchProcessor
from operations.security_manager import SecurityManager


class TestAdvancedSearchAndBatch:
    """测试高级搜索和批量处理功能"""
    
    @pytest.fixture
    def setup_test_environment(self):
        """设置测试环境"""
        # 创建临时工作目录
        temp_dir = tempfile.mkdtemp()
        workspace = Path(temp_dir) / "test_workspace"
        workspace.mkdir(parents=True, exist_ok=True)
        
        # 创建测试文件
        test_files = {
            "test1.txt": "Hello world! This is a test file.",
            "test2.py": "print('Hello Python')\n# This is a Python file",
            "config.json": '{"name": "test", "version": "1.0"}',
            "readme.md": "# Test Project\nThis is a markdown file.",
            "data.csv": "name,age,city\nJohn,25,NYC\nJane,30,LA",
            "subdir/nested.txt": "This is a nested file.",
            "subdir/script.py": "import os\nprint('Nested Python file')",
            ".hidden.txt": "This is a hidden file.",
            "large_file.txt": "Large content " * 1000,
            "old_file.txt": "This is an old file."
        }
        
        for file_path, content in test_files.items():
            full_path = workspace / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            full_path.write_text(content, encoding='utf-8')
        
        # 设置不同的修改时间
        old_file = workspace / "old_file.txt"
        old_time = datetime.now() - timedelta(days=30)
        import os
        os.utime(old_file, (old_time.timestamp(), old_time.timestamp()))
        
        # 创建组件实例
        security_manager = SecurityManager(str(workspace))
        file_operations = FileOperations(str(workspace), security_manager)
        batch_processor = BatchProcessor(str(workspace), security_manager, file_operations)
        
        yield {
            "workspace": workspace,
            "security_manager": security_manager,
            "file_operations": file_operations,
            "batch_processor": batch_processor
        }
        
        # 清理测试环境
        shutil.rmtree(temp_dir)
    
    @pytest.mark.asyncio
    async def test_advanced_search_basic(self, setup_test_environment):
        """测试基础高级搜索功能"""
        env = setup_test_environment
        file_ops = env["file_operations"]
        
        # 测试基础搜索
        result = await file_ops.advanced_search_files("test")
        assert result["success"] is True
        assert result["count"] > 0
        
        # 验证搜索结果包含预期文件
        file_names = [r["name"] for r in result["results"]]
        assert "test1.txt" in file_names
        assert "test2.py" in file_names
    
    @pytest.mark.asyncio
    async def test_advanced_search_with_content(self, setup_test_environment):
        """测试内容搜索功能"""
        env = setup_test_environment
        file_ops = env["file_operations"]
        
        # 测试内容搜索
        search_options = {
            "search_content": True,
            "case_sensitive": False
        }
        
        result = await file_ops.advanced_search_files("Python", search_options)
        assert result["success"] is True
        assert result["count"] >= 2  # test2.py 和 script.py
        
        # 验证匹配详情
        for file_result in result["results"]:
            if file_result["content_match"]:
                assert "match_details" in file_result
    
    @pytest.mark.asyncio
    async def test_advanced_search_with_filters(self, setup_test_environment):
        """测试带过滤器的高级搜索"""
        env = setup_test_environment
        file_ops = env["file_operations"]
        
        # 测试文件扩展名过滤
        search_options = {
            "file_extensions": [".py"],
            "max_results": 10
        }
        
        result = await file_ops.advanced_search_files("", search_options)
        assert result["success"] is True
        
        # 验证所有结果都是Python文件
        for file_result in result["results"]:
            assert file_result["name"].endswith(".py")
    
    @pytest.mark.asyncio
    async def test_advanced_search_regex(self, setup_test_environment):
        """测试正则表达式搜索"""
        env = setup_test_environment
        file_ops = env["file_operations"]
        
        # 测试正则表达式搜索
        search_options = {
            "regex_mode": True,
            "search_content": True
        }
        
        result = await file_ops.advanced_search_files(r"\d+", search_options)
        assert result["success"] is True
        
        # 应该找到包含数字的文件
        assert result["count"] > 0
    
    @pytest.mark.asyncio
    async def test_file_filter(self, setup_test_environment):
        """测试文件过滤功能"""
        env = setup_test_environment
        file_ops = env["file_operations"]
        
        # 测试基础过滤
        filter_options = {
            "file_extensions": [".txt"],
            "sort_by": "size",
            "sort_order": "desc"
        }
        
        result = await file_ops.filter_files(filter_options)
        assert result["success"] is True
        assert result["count"] > 0
        
        # 验证排序
        sizes = [r["size"] for r in result["results"]]
        assert sizes == sorted(sizes, reverse=True)
    
    @pytest.mark.asyncio
    async def test_batch_rename_files(self, setup_test_environment):
        """测试批量重命名功能"""
        env = setup_test_environment
        batch_processor = env["batch_processor"]
        
        # 测试批量重命名
        rename_rule = {
            "pattern": "renamed_{index}_{name}",
            "start_index": 1,
            "preserve_extension": True
        }
        
        result = await batch_processor.batch_rename_files(["*.txt"], rename_rule)
        assert result["success"] is True
        assert len(result["renamed_files"]) > 0
        
        # 验证重命名结果
        for renamed in result["renamed_files"]:
            assert "renamed_" in renamed["new_name"]
    
    @pytest.mark.asyncio
    async def test_batch_copy_files(self, setup_test_environment):
        """测试批量复制功能"""
        env = setup_test_environment
        batch_processor = env["batch_processor"]
        
        # 测试批量复制
        copy_options = {
            "preserve_structure": True,
            "overwrite": False
        }
        
        result = await batch_processor.batch_copy_files(
            ["*.py"], "backup", copy_options
        )
        assert result["success"] is True
        assert len(result["copied_files"]) >= 2
        
        # 验证复制结果
        workspace = env["workspace"]
        backup_dir = workspace / "backup"
        assert backup_dir.exists()
    
    @pytest.mark.asyncio
    async def test_batch_move_files(self, setup_test_environment):
        """测试批量移动功能"""
        env = setup_test_environment
        batch_processor = env["batch_processor"]
        
        # 先创建一些测试文件用于移动
        workspace = env["workspace"]
        move_dir = workspace / "to_move"
        move_dir.mkdir(exist_ok=True)
        
        test_file = move_dir / "move_test.txt"
        test_file.write_text("This file will be moved")
        
        # 测试批量移动
        move_options = {
            "preserve_structure": False,
            "overwrite": False
        }
        
        result = await batch_processor.batch_move_files(
            ["to_move/*.txt"], "moved", move_options
        )
        assert result["success"] is True
        assert len(result["moved_files"]) >= 1
        
        # 验证移动结果
        moved_dir = workspace / "moved"
        assert moved_dir.exists()
        assert not test_file.exists()  # 原文件应该不存在
    
    @pytest.mark.asyncio
    async def test_batch_delete_files(self, setup_test_environment):
        """测试批量删除功能"""
        env = setup_test_environment
        batch_processor = env["batch_processor"]
        workspace = env["workspace"]
        
        # 创建要删除的测试文件
        delete_dir = workspace / "to_delete"
        delete_dir.mkdir(exist_ok=True)
        
        test_files = ["delete1.txt", "delete2.txt"]
        for filename in test_files:
            (delete_dir / filename).write_text("To be deleted")
        
        # 测试批量删除
        delete_options = {
            "create_backup": True,
            "backup_dir": "deleted_backup"
        }
        
        result = await batch_processor.batch_delete_files(
            ["to_delete/*.txt"], delete_options
        )
        assert result["success"] is True
        assert len(result["deleted_files"]) == 2
        
        # 验证删除结果
        for filename in test_files:
            assert not (delete_dir / filename).exists()
        
        # 验证备份存在
        backup_dir = workspace / "deleted_backup"
        assert backup_dir.exists()
    
    @pytest.mark.asyncio
    async def test_batch_content_processing(self, setup_test_environment):
        """测试批量内容处理功能"""
        env = setup_test_environment
        batch_processor = env["batch_processor"]
        
        # 定义内容处理函数
        def uppercase_processor(content: str) -> str:
            return content.upper()
        
        # 测试批量内容处理
        process_options = {
            "create_backup": True,
            "dry_run": False
        }
        
        result = await batch_processor.batch_process_content(
            ["*.txt"], uppercase_processor, process_options
        )
        assert result["success"] is True
        assert len(result["processed_files"]) > 0
        
        # 验证处理结果
        modified_files = [f for f in result["processed_files"] if f["status"] == "modified"]
        assert len(modified_files) > 0
    
    @pytest.mark.asyncio
    async def test_batch_checksum_calculation(self, setup_test_environment):
        """测试批量校验和计算功能"""
        env = setup_test_environment
        batch_processor = env["batch_processor"]
        
        # 测试MD5校验和计算
        result = await batch_processor.batch_calculate_checksums(["*.txt"], "md5")
        assert result["success"] is True
        assert len(result["checksums"]) > 0
        
        # 验证校验和结果
        for checksum_info in result["checksums"]:
            assert "checksum" in checksum_info
            assert len(checksum_info["checksum"]) == 32  # MD5长度
            assert checksum_info["algorithm"] == "md5"
    
    @pytest.mark.asyncio
    async def test_processing_stats(self, setup_test_environment):
        """测试处理统计功能"""
        env = setup_test_environment
        batch_processor = env["batch_processor"]
        
        # 执行一个操作
        result = await batch_processor.batch_calculate_checksums(["*.txt"])
        assert result["success"] is True
        
        # 检查统计信息
        stats = result["stats"]
        assert "total_operations" in stats
        assert "successful_operations" in stats
        assert stats["total_operations"] > 0

        # 获取完整统计信息
        full_stats = batch_processor.get_processing_stats()
        assert "duration_seconds" in full_stats


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
