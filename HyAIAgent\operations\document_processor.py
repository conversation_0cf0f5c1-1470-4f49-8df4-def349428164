"""
文档处理器模块

该模块提供了全面的文档处理功能，支持多种文档格式的读取、解析、转换和处理。
主要功能包括：
- 多格式文档读取（TXT、MD、JSON、XML、CSV等）
- 文档内容解析和结构化
- 文档元数据提取
- 文档格式转换
- 文档内容搜索和分析
- 文档模板处理

作者: HyAIAgent开发团队
创建时间: 2025-07-28
版本: 1.0.0
"""

import asyncio
import json
import csv
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import logging
import mimetypes
import chardet
import re
from datetime import datetime

# 导入项目模块
from .security_manager import SecurityManager
from .file_operations import FileOperations
from .file_utils import FileUtils

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """
    文档处理器类
    
    提供全面的文档处理功能，包括多格式文档的读取、解析、转换和分析。
    支持的文档格式：
    - 文本文档：TXT, MD, RTF
    - 数据文档：JSON, XML, CSV, TSV
    - 配置文档：INI, YAML, TOML
    - 标记文档：HTML, XML
    
    特性：
    - 异步文档处理
    - 自动格式检测
    - 编码自动识别
    - 文档结构解析
    - 元数据提取
    - 内容搜索和分析
    - 安全性验证
    """
    
    def __init__(self, workspace_path: str = "./workspace", 
                 security_manager: Optional[SecurityManager] = None,
                 file_operations: Optional[FileOperations] = None):
        """
        初始化文档处理器
        
        Args:
            workspace_path (str): 工作空间路径
            security_manager (SecurityManager, optional): 安全管理器实例
            file_operations (FileOperations, optional): 文件操作实例
        """
        self.workspace_path = Path(workspace_path).resolve()
        self.security_manager = security_manager or SecurityManager(str(self.workspace_path))
        self.file_operations = file_operations or FileOperations(str(self.workspace_path), self.security_manager)
        self.file_utils = FileUtils()
        
        # 支持的文档格式
        self.supported_formats = {
            'text': ['.txt', '.md', '.rst', '.rtf'],
            'data': ['.json', '.xml', '.csv', '.tsv'],
            'config': ['.ini', '.yaml', '.yml', '.toml'],
            'markup': ['.html', '.htm', '.xml']
        }
        
        # 处理统计
        self.processing_stats = {
            "total_processed": 0,
            "successful_processed": 0,
            "failed_processed": 0,
            "formats_processed": {},
            "last_processed": None
        }
        
        logger.info(f"DocumentProcessor initialized with workspace: {self.workspace_path}")
    
    async def process_document(self, file_path: str, 
                             format_hint: Optional[str] = None,
                             encoding: Optional[str] = None) -> Dict[str, Any]:
        """
        处理文档文件
        
        Args:
            file_path (str): 文档文件路径
            format_hint (str, optional): 格式提示
            encoding (str, optional): 编码提示
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            self.processing_stats["total_processed"] += 1
            
            # 安全验证
            validation_result = self.security_manager.validate_operation(file_path, "read")
            if not validation_result.get('allowed', False):
                reason = validation_result.get('reason', 'Unknown error')
                raise ValueError(f"Security validation failed: {reason}")
            
            # 获取文件信息
            full_path = self.workspace_path / file_path
            if not full_path.exists():
                raise FileNotFoundError(f"Document not found: {file_path}")
            
            # 检测文档格式
            doc_format = format_hint or self._detect_document_format(full_path)
            
            # 读取文档内容
            content_result = await self.file_operations.read_file(file_path, encoding)
            if not content_result['success']:
                raise Exception(f"Failed to read document: {content_result['error']}")
            
            # 解析文档
            parsed_content = await self._parse_document(content_result['content'], doc_format, full_path)
            
            # 提取元数据
            metadata = await self._extract_metadata(full_path, doc_format, parsed_content)
            
            # 更新统计
            self.processing_stats["successful_processed"] += 1
            self.processing_stats["formats_processed"][doc_format] = \
                self.processing_stats["formats_processed"].get(doc_format, 0) + 1
            self.processing_stats["last_processed"] = datetime.now().isoformat()
            
            return {
                "success": True,
                "file_path": file_path,
                "format": doc_format,
                "content": parsed_content,
                "metadata": metadata,
                "processing_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.processing_stats["failed_processed"] += 1
            logger.error(f"Document processing failed for {file_path}: {str(e)}")
            return {
                "success": False,
                "file_path": file_path,
                "error": str(e),
                "processing_time": datetime.now().isoformat()
            }
    
    async def batch_process_documents(self, file_patterns: List[str],
                                    max_concurrent: int = 5) -> Dict[str, Any]:
        """
        批量处理文档
        
        Args:
            file_patterns (List[str]): 文件模式列表
            max_concurrent (int): 最大并发数
            
        Returns:
            Dict[str, Any]: 批量处理结果
        """
        try:
            # 收集所有匹配的文件
            all_files = []
            for pattern in file_patterns:
                files_result = await self.file_operations.list_files("", pattern, recursive=True)
                if files_result['success']:
                    all_files.extend([f['path'] for f in files_result['files']])
            
            if not all_files:
                return {
                    "success": True,
                    "processed_count": 0,
                    "results": [],
                    "message": "No files found matching patterns"
                }
            
            # 创建信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_with_semaphore(file_path):
                async with semaphore:
                    return await self.process_document(file_path)
            
            # 并发处理所有文件
            tasks = [process_with_semaphore(file_path) for file_path in all_files]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            successful = sum(1 for r in results if isinstance(r, dict) and r.get('success'))
            failed = len(results) - successful
            
            return {
                "success": True,
                "processed_count": len(results),
                "successful_count": successful,
                "failed_count": failed,
                "results": [r for r in results if isinstance(r, dict)],
                "processing_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Batch processing failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "processing_time": datetime.now().isoformat()
            }
    
    def _detect_document_format(self, file_path: Path) -> str:
        """
        检测文档格式
        
        Args:
            file_path (Path): 文件路径
            
        Returns:
            str: 文档格式
        """
        suffix = file_path.suffix.lower()
        
        for format_type, extensions in self.supported_formats.items():
            if suffix in extensions:
                return format_type
        
        # 使用MIME类型检测
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if mime_type:
            if mime_type.startswith('text/'):
                return 'text'
            elif mime_type in ['application/json', 'application/xml']:
                return 'data'
        
        return 'text'  # 默认为文本格式
    
    async def _parse_document(self, content: str, doc_format: str, file_path: Path) -> Dict[str, Any]:
        """
        解析文档内容
        
        Args:
            content (str): 文档内容
            doc_format (str): 文档格式
            file_path (Path): 文件路径
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            if doc_format == 'data':
                return await self._parse_data_document(content, file_path.suffix.lower())
            elif doc_format == 'config':
                return await self._parse_config_document(content, file_path.suffix.lower())
            elif doc_format == 'markup':
                return await self._parse_markup_document(content, file_path.suffix.lower())
            else:  # text format
                return await self._parse_text_document(content)
                
        except Exception as e:
            logger.warning(f"Failed to parse document as {doc_format}, falling back to text: {str(e)}")
            return await self._parse_text_document(content)
    
    async def _parse_text_document(self, content: str) -> Dict[str, Any]:
        """解析文本文档"""
        lines = content.split('\n')
        
        return {
            "type": "text",
            "content": content,
            "line_count": len(lines),
            "char_count": len(content),
            "word_count": len(content.split()),
            "lines": lines[:10] if len(lines) > 10 else lines,  # 前10行预览
            "structure": {
                "paragraphs": len([line for line in lines if line.strip()]),
                "empty_lines": len([line for line in lines if not line.strip()])
            }
        }
    
    async def _parse_data_document(self, content: str, extension: str) -> Dict[str, Any]:
        """解析数据文档"""
        if extension == '.json':
            try:
                data = json.loads(content)
                return {
                    "type": "json",
                    "content": content,
                    "parsed_data": data,
                    "structure": self._analyze_json_structure(data)
                }
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON format: {str(e)}")
        
        elif extension in ['.csv', '.tsv']:
            delimiter = '\t' if extension == '.tsv' else ','
            try:
                lines = content.strip().split('\n')
                reader = csv.reader(lines, delimiter=delimiter)
                rows = list(reader)
                
                return {
                    "type": "csv",
                    "content": content,
                    "rows": rows,
                    "structure": {
                        "row_count": len(rows),
                        "column_count": len(rows[0]) if rows else 0,
                        "headers": rows[0] if rows else [],
                        "sample_data": rows[1:6] if len(rows) > 1 else []
                    }
                }
            except Exception as e:
                raise ValueError(f"Invalid CSV format: {str(e)}")
        
        elif extension == '.xml':
            try:
                root = ET.fromstring(content)
                return {
                    "type": "xml",
                    "content": content,
                    "root_tag": root.tag,
                    "structure": self._analyze_xml_structure(root)
                }
            except ET.ParseError as e:
                raise ValueError(f"Invalid XML format: {str(e)}")
        
        return await self._parse_text_document(content)

    async def _parse_config_document(self, content: str, extension: str) -> Dict[str, Any]:
        """解析配置文档"""
        if extension == '.ini':
            try:
                import configparser
                config = configparser.ConfigParser()
                config.read_string(content)

                sections = {}
                for section_name in config.sections():
                    sections[section_name] = dict(config[section_name])

                return {
                    "type": "ini",
                    "content": content,
                    "sections": sections,
                    "structure": {
                        "section_count": len(sections),
                        "total_keys": sum(len(section) for section in sections.values())
                    }
                }
            except Exception as e:
                raise ValueError(f"Invalid INI format: {str(e)}")

        elif extension in ['.yaml', '.yml']:
            try:
                import yaml
                data = yaml.safe_load(content)
                return {
                    "type": "yaml",
                    "content": content,
                    "parsed_data": data,
                    "structure": self._analyze_yaml_structure(data)
                }
            except Exception as e:
                raise ValueError(f"Invalid YAML format: {str(e)}")

        return await self._parse_text_document(content)

    async def _parse_markup_document(self, content: str, extension: str) -> Dict[str, Any]:
        """解析标记文档"""
        if extension in ['.html', '.htm']:
            try:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(content, 'html.parser')

                return {
                    "type": "html",
                    "content": content,
                    "title": soup.title.string if soup.title else None,
                    "text_content": soup.get_text(),
                    "structure": {
                        "tag_count": len(soup.find_all()),
                        "link_count": len(soup.find_all('a')),
                        "image_count": len(soup.find_all('img')),
                        "headings": [h.get_text() for h in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])]
                    }
                }
            except Exception as e:
                logger.warning(f"Failed to parse HTML with BeautifulSoup: {str(e)}")
                return await self._parse_text_document(content)

        return await self._parse_text_document(content)

    async def _extract_metadata(self, file_path: Path, doc_format: str, parsed_content: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取文档元数据

        Args:
            file_path (Path): 文件路径
            doc_format (str): 文档格式
            parsed_content (Dict[str, Any]): 解析后的内容

        Returns:
            Dict[str, Any]: 元数据
        """
        try:
            stat = file_path.stat()

            metadata = {
                "file_name": file_path.name,
                "file_size": stat.st_size,
                "file_extension": file_path.suffix.lower(),
                "format": doc_format,
                "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "mime_type": mimetypes.guess_type(str(file_path))[0]
            }

            # 添加内容相关的元数据
            if parsed_content.get('type') == 'text':
                metadata.update({
                    "line_count": parsed_content.get('line_count', 0),
                    "word_count": parsed_content.get('word_count', 0),
                    "char_count": parsed_content.get('char_count', 0)
                })
            elif parsed_content.get('type') == 'json':
                metadata.update({
                    "json_keys": len(parsed_content.get('parsed_data', {})) if isinstance(parsed_content.get('parsed_data'), dict) else 0
                })
            elif parsed_content.get('type') == 'csv':
                structure = parsed_content.get('structure', {})
                metadata.update({
                    "row_count": structure.get('row_count', 0),
                    "column_count": structure.get('column_count', 0)
                })

            return metadata

        except Exception as e:
            logger.error(f"Failed to extract metadata: {str(e)}")
            return {
                "file_name": file_path.name,
                "error": str(e)
            }

    def _analyze_json_structure(self, data: Any) -> Dict[str, Any]:
        """分析JSON结构"""
        if isinstance(data, dict):
            return {
                "type": "object",
                "key_count": len(data),
                "keys": list(data.keys())[:10],  # 前10个键
                "nested_objects": sum(1 for v in data.values() if isinstance(v, dict)),
                "arrays": sum(1 for v in data.values() if isinstance(v, list))
            }
        elif isinstance(data, list):
            return {
                "type": "array",
                "length": len(data),
                "item_types": list(set(type(item).__name__ for item in data[:10]))
            }
        else:
            return {
                "type": type(data).__name__,
                "value": str(data)[:100]
            }

    def _analyze_xml_structure(self, root: ET.Element) -> Dict[str, Any]:
        """分析XML结构"""
        def count_elements(element):
            count = 1
            for child in element:
                count += count_elements(child)
            return count

        return {
            "root_tag": root.tag,
            "total_elements": count_elements(root),
            "direct_children": len(list(root)),
            "attributes": dict(root.attrib),
            "child_tags": list(set(child.tag for child in root))
        }

    def _analyze_yaml_structure(self, data: Any) -> Dict[str, Any]:
        """分析YAML结构"""
        return self._analyze_json_structure(data)  # YAML结构类似JSON

    async def search_in_documents(self, query: str, file_patterns: List[str] = ["*"],
                                case_sensitive: bool = False) -> Dict[str, Any]:
        """
        在文档中搜索内容

        Args:
            query (str): 搜索查询
            file_patterns (List[str]): 文件模式
            case_sensitive (bool): 是否区分大小写

        Returns:
            Dict[str, Any]: 搜索结果
        """
        try:
            search_results = []

            # 获取所有匹配的文件
            all_files = []
            for pattern in file_patterns:
                files_result = await self.file_operations.list_files("", pattern, recursive=True)
                if files_result['success']:
                    all_files.extend([f['path'] for f in files_result['files']])

            # 在每个文件中搜索
            for file_path in all_files:
                try:
                    # 处理文档
                    doc_result = await self.process_document(file_path)
                    if not doc_result['success']:
                        continue

                    content = doc_result['content'].get('content', '')
                    if not case_sensitive:
                        content_lower = content.lower()
                        query_lower = query.lower()
                        matches = content_lower.count(query_lower)
                    else:
                        matches = content.count(query)

                    if matches > 0:
                        # 找到匹配的行
                        lines = content.split('\n')
                        matching_lines = []

                        for i, line in enumerate(lines):
                            search_line = line if case_sensitive else line.lower()
                            search_query = query if case_sensitive else query.lower()

                            if search_query in search_line:
                                matching_lines.append({
                                    "line_number": i + 1,
                                    "content": line.strip(),
                                    "context": {
                                        "before": lines[max(0, i-1)].strip() if i > 0 else "",
                                        "after": lines[min(len(lines)-1, i+1)].strip() if i < len(lines)-1 else ""
                                    }
                                })

                        search_results.append({
                            "file_path": file_path,
                            "matches": matches,
                            "matching_lines": matching_lines[:10],  # 最多10行
                            "format": doc_result['format'],
                            "metadata": doc_result['metadata']
                        })

                except Exception as e:
                    logger.warning(f"Failed to search in {file_path}: {str(e)}")
                    continue

            return {
                "success": True,
                "query": query,
                "total_files_searched": len(all_files),
                "files_with_matches": len(search_results),
                "total_matches": sum(r['matches'] for r in search_results),
                "results": search_results,
                "search_time": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Document search failed: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "search_time": datetime.now().isoformat()
            }

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self.processing_stats.copy()

        if stats["total_processed"] > 0:
            stats["success_rate"] = (stats["successful_processed"] / stats["total_processed"]) * 100
        else:
            stats["success_rate"] = 0.0

        stats["workspace_path"] = str(self.workspace_path)
        return stats

    def reset_stats(self) -> None:
        """重置处理统计"""
        self.processing_stats = {
            "total_processed": 0,
            "successful_processed": 0,
            "failed_processed": 0,
            "formats_processed": {},
            "last_processed": None
        }
