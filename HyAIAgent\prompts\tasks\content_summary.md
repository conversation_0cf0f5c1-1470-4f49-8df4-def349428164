# 内容摘要专用提示词模板

## 基础摘要生成

### 通用内容摘要
```
你是一个专业的内容摘要专家。请为以下内容生成高质量的摘要。

原始内容: {original_content}
摘要长度: {summary_length}
目标受众: {target_audience}
摘要目的: {summary_purpose}

摘要要求：
1. 保留核心信息和关键观点
2. 保持逻辑结构清晰
3. 使用简洁明了的语言
4. 突出重要数据和结论
5. 适合目标受众的理解水平

## 内容摘要

### 核心要点
- {key_point_1}
- {key_point_2}
- {key_point_3}

### 主要内容
{main_content_summary}

### 重要数据
- {important_data_1}
- {important_data_2}
- {important_data_3}

### 结论与建议
{conclusions_and_recommendations}

### 信息来源
- 原始来源: {original_source}
- 发布时间: {publication_date}
- 可信度评级: {credibility_rating}
```

### 分层次摘要
```
你是一个分层次摘要专家。请为以下内容生成不同详细程度的摘要。

原始内容: {original_content}
内容类型: {content_type}
摘要层次: {summary_levels}

## 一句话摘要 (20字以内)
{one_sentence_summary}

## 简要摘要 (100字以内)
{brief_summary}

## 详细摘要 (300字以内)
### 背景信息
{background_info}

### 主要内容
{main_content}

### 关键发现
{key_findings}

### 重要结论
{important_conclusions}

## 完整摘要 (500字以内)
### 内容概述
{content_overview}

### 详细分析
{detailed_analysis}

### 数据支撑
{supporting_data}

### 影响评估
{impact_assessment}

### 未来展望
{future_outlook}
```

### 结构化摘要
```
你是一个结构化摘要专家。请按照标准结构为以下内容生成摘要。

原始内容: {original_content}
内容领域: {content_domain}
结构化要求: {structure_requirements}

## 结构化摘要

### 1. 背景与目的 (Background & Objective)
- 研究背景: {research_background}
- 主要目的: {main_objective}
- 研究意义: {research_significance}

### 2. 方法与数据 (Methods & Data)
- 研究方法: {research_methods}
- 数据来源: {data_sources}
- 分析工具: {analysis_tools}

### 3. 主要发现 (Key Findings)
- 核心发现: {core_findings}
- 重要数据: {important_data}
- 关键指标: {key_indicators}

### 4. 结论与建议 (Conclusions & Recommendations)
- 主要结论: {main_conclusions}
- 实践建议: {practical_recommendations}
- 政策含义: {policy_implications}

### 5. 局限与展望 (Limitations & Future Work)
- 研究局限: {research_limitations}
- 未来方向: {future_directions}
- 改进建议: {improvement_suggestions}
```

## 专业领域摘要

### 技术文档摘要
```
你是一个技术文档摘要专家。请为以下技术内容生成专业摘要。

技术内容: {technical_content}
技术领域: {technical_domain}
技术水平: {technical_level}

## 技术摘要

### 技术概述
- 技术名称: {technology_name}
- 技术类型: {technology_type}
- 应用场景: {application_scenarios}
- 技术成熟度: {technology_maturity}

### 核心特性
- 主要功能: {main_functions}
- 技术优势: {technical_advantages}
- 性能指标: {performance_metrics}
- 兼容性: {compatibility}

### 实现要点
- 核心算法: {core_algorithms}
- 关键技术: {key_technologies}
- 实现难点: {implementation_challenges}
- 最佳实践: {best_practices}

### 应用指南
- 使用场景: {use_cases}
- 部署要求: {deployment_requirements}
- 配置说明: {configuration_guide}
- 注意事项: {precautions}

### 技术评估
- 优势分析: {advantage_analysis}
- 局限性: {limitations}
- 风险评估: {risk_assessment}
- 发展前景: {development_prospects}
```

### 商业报告摘要
```
你是一个商业报告摘要专家。请为以下商业内容生成专业摘要。

商业内容: {business_content}
报告类型: {report_type}
行业领域: {industry_sector}

## 商业摘要

### 执行摘要
- 报告主题: {report_topic}
- 核心发现: {core_findings}
- 关键建议: {key_recommendations}
- 战略意义: {strategic_significance}

### 市场分析
- 市场规模: {market_size}
- 增长趋势: {growth_trends}
- 竞争格局: {competitive_landscape}
- 市场机会: {market_opportunities}

### 财务表现
- 收入情况: {revenue_performance}
- 盈利能力: {profitability}
- 成本结构: {cost_structure}
- 财务健康度: {financial_health}

### 风险与机遇
- 主要风险: {major_risks}
- 发展机遇: {development_opportunities}
- 应对策略: {response_strategies}
- 投资建议: {investment_recommendations}

### 未来展望
- 发展预测: {development_forecast}
- 战略规划: {strategic_planning}
- 关键里程碑: {key_milestones}
- 成功因素: {success_factors}
```

### 学术论文摘要
```
你是一个学术论文摘要专家。请为以下学术内容生成标准摘要。

学术内容: {academic_content}
研究领域: {research_field}
论文类型: {paper_type}

## 学术摘要

### 研究背景 (Background)
{research_background}

### 研究目的 (Objective)
{research_objective}

### 研究方法 (Methods)
- 研究设计: {research_design}
- 数据收集: {data_collection}
- 分析方法: {analysis_methods}
- 样本规模: {sample_size}

### 主要结果 (Results)
- 核心发现: {core_results}
- 统计数据: {statistical_data}
- 显著性检验: {significance_tests}
- 效应大小: {effect_sizes}

### 研究结论 (Conclusions)
- 主要结论: {main_conclusions}
- 理论贡献: {theoretical_contributions}
- 实践意义: {practical_implications}
- 政策建议: {policy_recommendations}

### 研究局限 (Limitations)
{research_limitations}

### 未来研究 (Future Research)
{future_research_directions}

### 关键词 (Keywords)
{keywords}
```

## 特殊类型摘要

### 多媒体内容摘要
```
你是一个多媒体内容摘要专家。请为以下多媒体内容生成摘要。

多媒体内容: {multimedia_content}
内容类型: {content_type} (视频/音频/图文混合)
摘要用途: {summary_purpose}

## 多媒体摘要

### 内容概览
- 标题: {content_title}
- 时长/篇幅: {content_duration}
- 主要形式: {main_format}
- 目标受众: {target_audience}

### 核心内容
- 主要话题: {main_topics}
- 关键信息: {key_information}
- 重要观点: {important_viewpoints}
- 数据亮点: {data_highlights}

### 视觉/听觉要素
- 重要图表: {important_charts}
- 关键画面: {key_scenes}
- 音频亮点: {audio_highlights}
- 演示内容: {demonstration_content}

### 互动元素
- 问答环节: {qa_sessions}
- 讨论要点: {discussion_points}
- 用户反馈: {user_feedback}
- 互动结果: {interaction_results}

### 价值评估
- 信息价值: {information_value}
- 教育意义: {educational_significance}
- 娱乐价值: {entertainment_value}
- 实用性: {practicality}
```

### 对话内容摘要
```
你是一个对话内容摘要专家。请为以下对话内容生成摘要。

对话内容: {conversation_content}
对话类型: {conversation_type}
参与者: {participants}

## 对话摘要

### 对话基本信息
- 对话主题: {conversation_topic}
- 参与人员: {participant_list}
- 对话时长: {conversation_duration}
- 对话场景: {conversation_context}

### 主要议题
1. **议题一**: {topic_1}
   - 讨论要点: {discussion_points_1}
   - 不同观点: {different_views_1}
   - 达成共识: {consensus_1}

2. **议题二**: {topic_2}
   - 讨论要点: {discussion_points_2}
   - 不同观点: {different_views_2}
   - 达成共识: {consensus_2}

3. **议题三**: {topic_3}
   - 讨论要点: {discussion_points_3}
   - 不同观点: {different_views_3}
   - 达成共识: {consensus_3}

### 关键决策
- 决策事项: {decision_items}
- 决策结果: {decision_results}
- 执行计划: {execution_plans}
- 责任分工: {responsibility_assignment}

### 行动项目
- 待办事项: {action_items}
- 负责人员: {responsible_persons}
- 完成时限: {completion_deadlines}
- 跟进机制: {follow_up_mechanisms}

### 未解决问题
- 悬而未决的问题: {unresolved_issues}
- 需要进一步讨论的话题: {topics_for_further_discussion}
- 后续会议安排: {follow_up_meetings}
```

## 摘要质量控制

### 摘要质量检查
```
你是一个摘要质量检查专家。请对以下摘要进行质量评估和改进建议。

原始摘要: {original_summary}
原始内容: {source_content}
质量标准: {quality_standards}

## 质量评估报告

### 1. 准确性检查 (0-10分)
- 事实准确性: {factual_accuracy}/10
- 数据准确性: {data_accuracy}/10
- 观点准确性: {viewpoint_accuracy}/10
- 问题识别: {accuracy_issues}

### 2. 完整性检查 (0-10分)
- 要点覆盖: {key_point_coverage}/10
- 逻辑完整性: {logical_completeness}/10
- 结构完整性: {structural_completeness}/10
- 问题识别: {completeness_issues}

### 3. 简洁性检查 (0-10分)
- 语言简洁度: {language_conciseness}/10
- 信息密度: {information_density}/10
- 冗余程度: {redundancy_level}/10
- 问题识别: {conciseness_issues}

### 4. 可读性检查 (0-10分)
- 语言流畅性: {language_fluency}/10
- 结构清晰度: {structural_clarity}/10
- 理解难度: {comprehension_difficulty}/10
- 问题识别: {readability_issues}

### 5. 改进建议
- 内容改进: {content_improvements}
- 结构优化: {structure_optimization}
- 语言润色: {language_polishing}
- 格式调整: {format_adjustments}

### 6. 修订版摘要
{revised_summary}
```
