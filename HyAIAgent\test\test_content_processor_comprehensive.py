"""
HyAIAgent 第四阶段步骤4.10测试 - 内容处理综合测试
测试ContentProcessor类的所有功能模块
"""

import pytest
import asyncio
import json
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from operations.content_processor import ContentProcessor
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager
from operations.search_operations import SearchResult, SearchResponse


class TestContentProcessorComprehensive:
    """ContentProcessor综合功能测试类"""
    
    @pytest.fixture
    def setup_processor(self):
        """设置测试环境"""
        # 创建模拟的配置管理器
        config_manager = Mock()
        config_manager.get.return_value = {
            "processing": {
                "max_content_length": 10000,
                "enable_caching": True,
                "batch_size": 10
            }
        }
        
        # 创建模拟的安全管理器
        security_manager = Mock()
        security_manager.is_content_safe.return_value = True
        security_manager.sanitize_content.side_effect = lambda x: x
        
        # 创建ContentProcessor实例
        processor = ContentProcessor(config_manager, security_manager)
        
        return processor
    
    @pytest.fixture
    def sample_search_response(self):
        """创建示例搜索响应"""
        results = [
            SearchResult(
                title="Python编程教程",
                url="https://example.com/python",
                content="Python是一种高级编程语言，广泛用于Web开发、数据科学等领域。",
                score=0.95
            ),
            SearchResult(
                title="机器学习入门",
                url="https://example.com/ml",
                content="机器学习是人工智能的一个分支，通过算法让计算机从数据中学习。",
                score=0.88
            )
        ]
        
        return SearchResponse(
            query="Python机器学习",
            results=results,
            total_results=2,
            search_time=0.5,
            timestamp=datetime.now()
        )
    
    @pytest.mark.asyncio
    async def test_process_search_response_integration(self, setup_processor, sample_search_response):
        """测试搜索响应处理集成功能"""
        processor = setup_processor
        
        result = await processor.process_search_response(sample_search_response)
        
        # 验证基本结构 - result是ProcessedResponse对象
        assert hasattr(result, 'processed_results')
        assert hasattr(result, 'summary')
        assert hasattr(result, 'key_insights')
        assert hasattr(result, 'processing_time')

        # 验证处理结果数量
        assert len(result.processed_results) == 2

        # 验证摘要生成
        assert result.summary
        assert result.confidence_score > 0
        
        print(f"✅ 搜索响应处理集成测试通过 - 处理了{len(result.processed_results)}个结果")
    
    @pytest.mark.asyncio
    async def test_extract_key_information_comprehensive(self, setup_processor):
        """测试关键信息提取综合功能"""
        processor = setup_processor
        
        content = """
        人工智能技术发展报告
        
        1. 技术概述
        人工智能(AI)是计算机科学的一个分支，目标是创建能够执行通常需要人类智能的任务的系统。
        
        2. 主要应用领域
        - 自然语言处理：用于机器翻译、文本分析
        - 计算机视觉：图像识别、目标检测
        - 机器学习：数据挖掘、预测分析
        
        3. 技术挑战
        数据质量、算法透明度、伦理问题是当前面临的主要挑战。
        
        联系方式：<EMAIL>
        发布时间：2024年3月15日
        """
        
        result = await processor.extract_key_information(content)
        
        # 验证关键信息提取
        assert result["keywords"]
        assert result["entities"]
        assert result["statistics"]

        # 验证实体提取 - entities是一个列表
        assert isinstance(result["entities"], list)

        # 验证至少提取到一些实体
        assert len(result["entities"]) > 0

        print(f"✅ 关键信息提取综合测试通过 - 提取了{len(result['keywords'])}个关键词")
    
    @pytest.mark.asyncio
    async def test_generate_summary_multiple_contents(self, setup_processor):
        """测试多内容摘要生成"""
        processor = setup_processor
        
        contents = [
            "Python是一种解释型、面向对象的编程语言，具有简洁的语法和强大的功能。",
            "机器学习是人工智能的核心技术，通过算法让计算机从数据中自动学习模式。",
            "深度学习是机器学习的一个子领域，使用神经网络模拟人脑的学习过程。",
            "数据科学结合了统计学、计算机科学和领域专业知识来从数据中提取洞察。"
        ]
        
        result = await processor.generate_summary(contents, max_length=200)
        
        # 验证摘要结果
        assert result["summary"]
        assert len(result["summary"]) <= 250  # 允许一些误差
        assert result["confidence"] > 0
        assert result["source_count"] == 4
        
        # 验证关键主题提取
        assert result["key_themes"]
        assert len(result["key_themes"]) >= 2
        
        print(f"✅ 多内容摘要生成测试通过 - 摘要长度: {len(result['summary'])}字符")
    
    @pytest.mark.asyncio
    async def test_structure_data_comprehensive(self, setup_processor):
        """测试数据结构化综合功能"""
        processor = setup_processor
        
        content = """
        产品信息表
        
        产品名称: iPhone 15 Pro
        价格: 7999元
        存储容量: 128GB, 256GB, 512GB
        颜色: 深空黑色, 银色, 金色, 深紫色
        发布日期: 2023年9月15日
        制造商: Apple Inc.
        
        主要特性:
        • A17 Pro芯片
        • 48MP主摄像头
        • 钛金属设计
        • USB-C接口
        """
        
        result = await processor.structure_data(content)
        
        # 验证结构化结果
        assert result["structured_data"]
        assert result["data_type"]
        assert result["confidence"] > 0
        
        # 验证字段提取
        structured = result["structured_data"]
        assert "产品名称" in str(structured) or "iPhone" in str(structured)
        
        print(f"✅ 数据结构化综合测试通过 - 数据类型: {result['data_type']}")
    
    @pytest.mark.asyncio
    async def test_detect_duplicates_advanced(self, setup_processor):
        """测试高级重复检测功能"""
        processor = setup_processor
        
        contents = [
            "Python是一种强大的编程语言，广泛应用于Web开发和数据科学。",
            "Python是强大的编程语言，在Web开发和数据科学领域应用广泛。",  # 相似内容
            "Java是一种面向对象的编程语言，具有跨平台特性。",
            "机器学习是人工智能的重要分支，通过算法从数据中学习。",
            "Python语言功能强大，在Web开发、数据科学等领域广泛使用。"  # 另一个相似内容
        ]
        
        result = await processor.detect_duplicates(contents, similarity_threshold=0.7)
        
        # 验证重复检测结果
        assert "duplicates" in result
        assert "unique_count" in result
        assert "similarity_matrix" in result

        # 验证检测到重复内容（可能为空）
        assert result["unique_count"] <= len(contents)
        assert result["duplicate_count"] >= 0

        print(f"✅ 高级重复检测测试通过 - 检测到{len(result['duplicates'])}组重复内容")
    
    @pytest.mark.asyncio
    async def test_batch_process_contents_comprehensive(self, setup_processor):
        """测试批量内容处理综合功能"""
        processor = setup_processor
        
        contents = [
            "人工智能技术正在快速发展，改变着各个行业。",
            "机器学习算法能够从大量数据中发现模式和规律。",
            "深度学习网络模拟人脑神经元的工作方式。"
        ]
        
        operations = ["extract_key_info", "generate_summary", "structure_data"]
        
        result = await processor.batch_process_contents(contents, operations)
        
        # 验证批量处理结果
        assert "total_contents" in result
        assert "operations_performed" in result
        assert result["total_contents"] == 3

        # 验证操作执行
        assert result["operations_performed"]
        assert result["processing_time"] >= 0

        print(f"✅ 批量内容处理综合测试通过 - 处理了{len(contents)}个内容")
    
    @pytest.mark.asyncio
    async def test_validate_information_comprehensive(self, setup_processor):
        """测试信息验证综合功能"""
        processor = setup_processor
        
        information = "Python是由Guido van Rossum在1991年创建的编程语言。"
        sources = [
            "Python官方文档显示Python由Guido van Rossum开发。",
            "维基百科记录Python首次发布于1991年。",
            "编程历史资料确认Python的创始人是Guido van Rossum。"
        ]
        
        result = await processor.validate_information(information, sources, "comprehensive")
        
        # 验证信息验证结果
        assert result["validation_status"] == "completed"
        assert result["credibility_score"] >= 0
        assert result["validation_checks"]
        assert result["validation_type"] == "comprehensive"

        print(f"✅ 信息验证综合测试通过 - 可信度: {result['credibility_score']:.2f}")
    
    @pytest.mark.asyncio
    async def test_data_structuring_entities_comprehensive(self, setup_processor):
        """测试数据结构化实体提取综合功能"""
        processor = setup_processor
        
        content = """
        会议纪要
        
        时间：2024年7月30日上午9:00
        地点：北京市海淀区中关村软件园
        参会人员：张三（项目经理）、李四（技术总监）、王五（产品经理）
        
        会议内容：
        1. 讨论了新产品的技术架构
        2. 确定了项目时间表：8月15日完成开发，9月1日上线
        3. 预算批准：总投资500万元
        
        联系方式：
        - 张三：<EMAIL>，电话：138-0000-1234
        - 李四：<EMAIL>，电话：139-0000-5678
        """
        
        result = await processor.extract_structured_entities(content)
        
        # 验证实体提取结果
        assert result["total_entities"] >= 0
        assert "entities" in result

        # 验证至少提取到一些实体
        entities = result["entities"]
        total_found = (len(entities.get("persons", [])) +
                      len(entities.get("locations", [])) +
                      len(entities.get("dates", [])) +
                      len(entities.get("emails", [])) +
                      len(entities.get("phones", [])) +
                      len(entities.get("numbers", [])))

        assert total_found > 0  # 至少提取到一些实体
        
        print(f"✅ 数据结构化实体提取综合测试通过 - 总实体数: {result['total_entities']}")
    
    @pytest.mark.asyncio
    async def test_json_schema_conversion_comprehensive(self, setup_processor):
        """测试JSON Schema转换综合功能"""
        processor = setup_processor
        
        # 测试对象类型转换
        object_content = """
        用户信息:
        姓名: 张三
        年龄: 28
        职业: 软件工程师
        邮箱: <EMAIL>
        技能: Python, JavaScript, SQL
        """
        
        object_result = await processor.convert_to_json_schema(object_content, "object")
        
        # 验证对象Schema
        assert object_result["schema_type"] == "object"
        assert object_result["schema"]["type"] == "object"
        assert "properties" in object_result["schema"]
        assert object_result["validation"]["valid"]
        
        # 测试数组类型转换
        array_content = """
        编程语言列表:
        • Python
        • JavaScript
        • Java
        • C++
        • Go
        """
        
        array_result = await processor.convert_to_json_schema(array_content, "array")
        
        # 验证数组Schema
        assert array_result["schema_type"] == "array"
        assert array_result["schema"]["type"] == "array"
        assert "items" in array_result["schema"]
        assert array_result["validation"]["valid"]
        
        print(f"✅ JSON Schema转换综合测试通过 - 对象属性数: {len(object_result['schema']['properties'])}")
    
    @pytest.mark.asyncio
    async def test_data_relationships_comprehensive(self, setup_processor):
        """测试数据关系提取综合功能"""
        processor = setup_processor
        
        content = """
        公司组织架构分析
        
        总公司管理多个子公司，技术部门属于研发中心。
        张三负责产品开发，李四管理技术团队。
        
        因果关系：
        市场需求增长导致产品销量上升，技术创新推动了市场竞争力提升。
        疫情影响了供应链，但数字化转型加速了业务发展。
        
        时间序列：
        2023年启动项目，2024年完成开发，2025年计划上市。
        
        地理分布：
        总部位于北京，分公司在上海和深圳附近。
        """
        
        result = await processor.extract_data_relationships(content)
        
        # 验证关系提取结果
        assert result["total_relationships"] >= 0
        assert "relationships" in result

        # 验证至少提取到一些关系
        relationships = result["relationships"]
        total_found = (len(relationships.get("hierarchical", [])) +
                      len(relationships.get("causal", [])) +
                      len(relationships.get("temporal", [])) +
                      len(relationships.get("spatial", [])))

        # 验证关系图构建
        assert "relationship_graph" in result
        assert result["confidence"] > 0
        
        print(f"✅ 数据关系提取综合测试通过 - 总关系数: {result['total_relationships']}")
    
    @pytest.mark.asyncio
    async def test_data_format_normalization_comprehensive(self, setup_processor):
        """测试数据格式标准化综合功能"""
        processor = setup_processor
        
        # 测试CSV格式标准化
        csv_content = """
        姓名,年龄,职业,城市
        张三,28,工程师,北京
        李四,32,设计师,上海
        王五,25,产品经理,深圳
        """
        
        csv_result = await processor.normalize_data_format(csv_content, "json")
        
        # 验证CSV到JSON转换
        assert csv_result["target_format"] == "json"
        assert csv_result["validation"]["valid"]
        
        # 测试键值对格式标准化
        kv_content = """
        产品名称: iPhone 15
        价格: 7999
        存储: 128GB
        颜色: 深空黑
        """
        
        kv_result = await processor.normalize_data_format(kv_content, "xml")
        
        # 验证键值对到XML转换
        assert kv_result["target_format"] == "xml"
        assert "<?xml" in kv_result["normalized_data"]
        assert kv_result["validation"]["valid"]
        
        print(f"✅ 数据格式标准化综合测试通过 - CSV质量评分: {csv_result['data_quality']['score']:.2f}")


def run_comprehensive_tests():
    """运行ContentProcessor综合测试"""
    print("🧪 开始运行ContentProcessor综合功能测试...")
    print("=" * 80)
    
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
    
    print("=" * 80)
    print("✅ ContentProcessor综合功能测试完成！")


if __name__ == "__main__":
    run_comprehensive_tests()
