"""
性能监控器

实时监控系统性能指标，提供性能数据收集和分析功能
"""

import asyncio
import time
import threading
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import json
from pathlib import Path

# 尝试导入loguru，如果不可用则使用print
try:
    from loguru import logger
except ImportError:
    class MockLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def debug(self, msg): print(f"DEBUG: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
    logger = MockLogger()

from .performance_optimizer import PerformanceMetrics, PerformanceOptimizer


@dataclass
class MonitoringConfig:
    """监控配置"""
    collection_interval: float = 5.0  # 数据收集间隔（秒）
    alert_thresholds: Dict[str, float] = None
    auto_optimization: bool = True
    max_history_size: int = 1000
    export_enabled: bool = True
    export_interval: int = 300  # 导出间隔（秒）


class PerformanceMonitor:
    """性能监控器
    
    负责实时监控系统性能，收集性能数据，触发告警和自动优化
    """
    
    def __init__(self, workspace_path: str = "./workspace", config: Optional[MonitoringConfig] = None):
        """初始化性能监控器
        
        Args:
            workspace_path (str): 工作空间路径
            config (Optional[MonitoringConfig]): 监控配置
        """
        self.workspace_path = Path(workspace_path)
        self.config = config or MonitoringConfig()
        
        # 初始化性能优化器
        self.optimizer = PerformanceOptimizer(workspace_path)
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.export_task: Optional[asyncio.Task] = None
        
        # 性能数据
        self.current_metrics: Optional[PerformanceMetrics] = None
        self.metrics_history: List[PerformanceMetrics] = []
        self.alerts_history: List[Dict[str, Any]] = []
        
        # 回调函数
        self.alert_callbacks: List[Callable] = []
        self.metrics_callbacks: List[Callable] = []
        
        # 统计信息
        self.monitoring_stats = {
            'start_time': None,
            'total_collections': 0,
            'total_alerts': 0,
            'total_optimizations': 0,
            'uptime_seconds': 0
        }
        
        # 设置默认告警阈值
        if self.config.alert_thresholds is None:
            self.config.alert_thresholds = {
                'cpu_usage': 85.0,
                'memory_usage': 85.0,
                'response_time': 3000.0,  # 毫秒
                'error_rate': 0.05
            }
        
        logger.info(f"性能监控器初始化完成: {workspace_path}")
    
    async def start_monitoring(self) -> bool:
        """开始性能监控
        
        Returns:
            bool: 启动是否成功
        """
        try:
            if self.is_monitoring:
                logger.warning("性能监控已在运行")
                return True
            
            self.is_monitoring = True
            self.monitoring_stats['start_time'] = datetime.now()
            
            # 启动监控任务
            self.monitor_task = asyncio.create_task(self._monitoring_loop())
            
            # 启动导出任务（如果启用）
            if self.config.export_enabled:
                self.export_task = asyncio.create_task(self._export_loop())
            
            logger.info("性能监控已启动")
            return True
            
        except Exception as e:
            logger.error(f"启动性能监控失败: {e}")
            self.is_monitoring = False
            return False
    
    async def stop_monitoring(self) -> bool:
        """停止性能监控
        
        Returns:
            bool: 停止是否成功
        """
        try:
            if not self.is_monitoring:
                logger.warning("性能监控未在运行")
                return True
            
            self.is_monitoring = False
            
            # 取消监控任务
            if self.monitor_task and not self.monitor_task.done():
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass
            
            # 取消导出任务
            if self.export_task and not self.export_task.done():
                self.export_task.cancel()
                try:
                    await self.export_task
                except asyncio.CancelledError:
                    pass
            
            # 更新统计信息
            if self.monitoring_stats['start_time']:
                self.monitoring_stats['uptime_seconds'] = (
                    datetime.now() - self.monitoring_stats['start_time']
                ).total_seconds()
            
            logger.info("性能监控已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止性能监控失败: {e}")
            return False
    
    async def _monitoring_loop(self):
        """监控循环"""
        try:
            while self.is_monitoring:
                # 收集性能指标
                metrics = await self.optimizer.collect_metrics()
                self.current_metrics = metrics
                
                # 添加到历史记录
                self.metrics_history.append(metrics)
                if len(self.metrics_history) > self.config.max_history_size:
                    self.metrics_history = self.metrics_history[-self.config.max_history_size//2:]
                
                # 更新统计信息
                self.monitoring_stats['total_collections'] += 1
                
                # 检查告警条件
                await self._check_alerts(metrics)
                
                # 调用指标回调
                for callback in self.metrics_callbacks:
                    try:
                        await callback(metrics)
                    except Exception as e:
                        logger.error(f"指标回调执行失败: {e}")
                
                # 自动优化（如果启用）
                if self.config.auto_optimization:
                    await self._auto_optimize(metrics)
                
                # 等待下次收集
                await asyncio.sleep(self.config.collection_interval)
                
        except asyncio.CancelledError:
            logger.info("监控循环被取消")
        except Exception as e:
            logger.error(f"监控循环异常: {e}")
            self.is_monitoring = False
    
    async def _check_alerts(self, metrics: PerformanceMetrics):
        """检查告警条件
        
        Args:
            metrics (PerformanceMetrics): 性能指标
        """
        try:
            alerts = []
            
            # 检查各项指标
            if metrics.cpu_usage > self.config.alert_thresholds['cpu_usage']:
                alerts.append({
                    'type': 'cpu_high',
                    'level': 'warning',
                    'message': f'CPU使用率过高: {metrics.cpu_usage:.1f}%',
                    'value': metrics.cpu_usage,
                    'threshold': self.config.alert_thresholds['cpu_usage']
                })
            
            if metrics.memory_usage > self.config.alert_thresholds['memory_usage']:
                alerts.append({
                    'type': 'memory_high',
                    'level': 'warning',
                    'message': f'内存使用率过高: {metrics.memory_usage:.1f}%',
                    'value': metrics.memory_usage,
                    'threshold': self.config.alert_thresholds['memory_usage']
                })
            
            if metrics.response_time > self.config.alert_thresholds['response_time']:
                alerts.append({
                    'type': 'response_slow',
                    'level': 'warning',
                    'message': f'响应时间过长: {metrics.response_time:.2f}ms',
                    'value': metrics.response_time,
                    'threshold': self.config.alert_thresholds['response_time']
                })
            
            if metrics.error_rate > self.config.alert_thresholds['error_rate']:
                alerts.append({
                    'type': 'error_rate_high',
                    'level': 'critical',
                    'message': f'错误率过高: {metrics.error_rate:.2%}',
                    'value': metrics.error_rate,
                    'threshold': self.config.alert_thresholds['error_rate']
                })
            
            # 处理告警
            for alert in alerts:
                alert['timestamp'] = datetime.now()
                alert['metrics'] = asdict(metrics)
                
                self.alerts_history.append(alert)
                self.monitoring_stats['total_alerts'] += 1
                
                logger.warning(f"性能告警: {alert['message']}")
                
                # 调用告警回调
                for callback in self.alert_callbacks:
                    try:
                        await callback(alert)
                    except Exception as e:
                        logger.error(f"告警回调执行失败: {e}")
                        
        except Exception as e:
            logger.error(f"检查告警失败: {e}")
    
    async def _auto_optimize(self, metrics: PerformanceMetrics):
        """自动优化
        
        Args:
            metrics (PerformanceMetrics): 性能指标
        """
        try:
            # 检查是否需要优化
            need_optimization = (
                metrics.cpu_usage > 90 or
                metrics.memory_usage > 90 or
                metrics.response_time > 5000 or
                metrics.error_rate > 0.1
            )
            
            if need_optimization:
                logger.info("触发自动优化")
                result = await self.optimizer.optimize_system()
                
                if result['success']:
                    self.monitoring_stats['total_optimizations'] += 1
                    logger.info(f"自动优化完成，性能改善: {result.get('performance_improvement', 0):.2%}")
                else:
                    logger.error(f"自动优化失败: {result.get('error', '未知错误')}")
                    
        except Exception as e:
            logger.error(f"自动优化失败: {e}")
    
    async def _export_loop(self):
        """导出循环"""
        try:
            while self.is_monitoring:
                await asyncio.sleep(self.config.export_interval)
                
                if self.metrics_history:
                    await self._export_metrics()
                    
        except asyncio.CancelledError:
            logger.info("导出循环被取消")
        except Exception as e:
            logger.error(f"导出循环异常: {e}")
    
    async def _export_metrics(self):
        """导出性能指标"""
        try:
            export_data = {
                'timestamp': datetime.now().isoformat(),
                'config': asdict(self.config),
                'stats': self.monitoring_stats.copy(),
                'recent_metrics': [asdict(m) for m in self.metrics_history[-100:]],
                'recent_alerts': self.alerts_history[-50:] if self.alerts_history else []
            }
            
            export_file = self.workspace_path / "performance_data.json"
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
            
            logger.debug(f"性能数据已导出: {export_file}")
            
        except Exception as e:
            logger.error(f"导出性能数据失败: {e}")
    
    def add_alert_callback(self, callback: Callable):
        """添加告警回调函数
        
        Args:
            callback (Callable): 回调函数
        """
        self.alert_callbacks.append(callback)
    
    def add_metrics_callback(self, callback: Callable):
        """添加指标回调函数
        
        Args:
            callback (Callable): 回调函数
        """
        self.metrics_callbacks.append(callback)
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前监控状态
        
        Returns:
            Dict[str, Any]: 监控状态
        """
        return {
            'is_monitoring': self.is_monitoring,
            'current_metrics': asdict(self.current_metrics) if self.current_metrics else None,
            'stats': self.monitoring_stats.copy(),
            'config': asdict(self.config),
            'recent_alerts_count': len([a for a in self.alerts_history if 
                                      (datetime.now() - a['timestamp']).total_seconds() < 3600]),
            'metrics_history_size': len(self.metrics_history)
        }
    
    async def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要
        
        Returns:
            Dict[str, Any]: 性能摘要
        """
        try:
            if not self.metrics_history:
                return {
                    'success': False,
                    'error': '没有性能数据'
                }
            
            # 获取优化器的性能报告
            optimizer_report = await self.optimizer.get_performance_report()
            
            # 添加监控特有的信息
            summary = {
                'success': True,
                'monitoring_status': self.get_current_status(),
                'performance_report': optimizer_report,
                'alert_summary': {
                    'total_alerts': len(self.alerts_history),
                    'recent_alerts': len([a for a in self.alerts_history if 
                                        (datetime.now() - a['timestamp']).total_seconds() < 3600]),
                    'alert_types': {}
                }
            }
            
            # 统计告警类型
            for alert in self.alerts_history:
                alert_type = alert.get('type', 'unknown')
                summary['alert_summary']['alert_types'][alert_type] = \
                    summary['alert_summary']['alert_types'].get(alert_type, 0) + 1
            
            return summary
            
        except Exception as e:
            logger.error(f"获取性能摘要失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
