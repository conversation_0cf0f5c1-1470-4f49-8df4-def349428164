#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
第五阶段综合功能测试
测试所有高级AI能力、用户界面、系统监控和工具集成功能
"""

import asyncio
import pytest
import sys
import os
import time
import psutil
from pathlib import Path
from typing import Dict, Any, List
from unittest.mock import Mock, patch, AsyncMock

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 导入核心模块
from core.config_manager import ConfigManager
from core.ai_client import SimpleAIClient
from core.kv_store import KVStore
from core.task_manager import TaskManager
from core.execution_engine import ExecutionEngine

# 导入高级AI能力模块
from advanced.reasoning_engine import ReasoningEngine
from advanced.knowledge_graph import KnowledgeGraph
from advanced.learning_system import LearningSystem
from advanced.creativity_engine import CreativityEngine

# 导入UI模块
from ui.advanced_widgets import AdvancedWidgets
from ui.visualization import Visualization
from ui.settings_manager import SettingsManager

# 导入监控模块
from monitoring.performance_monitor import PerformanceMonitor
from monitoring.error_analyzer import ErrorAnalyzer
from monitoring.usage_tracker import UsageTracker
from monitoring.optimization_engine import OptimizationEngine

# 导入工具模块
from tools.code_executor import CodeExecutor
from tools.chart_generator import ChartGenerator
from tools.report_builder import ReportBuilder
from tools.workflow_engine import WorkflowEngine

class TestStage5ComprehensiveFunctionality:
    """第五阶段综合功能测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_method(self):
        """测试前置设置"""
        self.config_manager = ConfigManager()
        self.kv_store = KVStore(db_path="data/test_comprehensive.json")

        # Mock AI客户端
        self.ai_client = Mock(spec=SimpleAIClient)
        self.ai_client.chat = AsyncMock(return_value="测试响应")

        # Mock PromptManager
        self.prompt_manager = Mock()
        self.prompt_manager.get_template_prompt = Mock(return_value="测试提示词")

        # 性能监控数据
        self.performance_data = []
        self.start_time = time.time()

        yield

        # 清理
        try:
            if hasattr(self, 'kv_store'):
                asyncio.run(self.kv_store.close())
            if os.path.exists("data/test_comprehensive.json"):
                os.remove("data/test_comprehensive.json")
        except:
            pass
    
    def record_performance(self, test_name: str, duration: float, memory_usage: float):
        """记录性能数据"""
        self.performance_data.append({
            'test_name': test_name,
            'duration': duration,
            'memory_usage': memory_usage,
            'timestamp': time.time()
        })
    
    @pytest.mark.asyncio
    async def test_advanced_reasoning_functionality(self):
        """测试高级推理功能"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 创建推理引擎
        reasoning_engine = ReasoningEngine(
            ai_client=self.ai_client,
            prompt_manager=self.prompt_manager,
            kv_store=self.kv_store
        )
        
        # 测试多步推理
        problem = "如何提高软件开发效率？"
        reasoning_chain = await reasoning_engine.multi_step_reasoning(problem)
        
        assert reasoning_chain is not None
        assert reasoning_chain.problem == problem
        assert len(reasoning_chain.steps) > 0
        assert reasoning_chain.confidence > 0
        
        # 测试逻辑推理
        premises = ["所有程序员都使用IDE", "小明是程序员"]
        conclusion = await reasoning_engine.logical_deduction(premises)
        
        assert conclusion is not None
        assert conclusion.conclusion is not None
        assert conclusion.confidence > 0
        
        # 记录性能
        duration = time.time() - start_time
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        self.record_performance("advanced_reasoning", duration, end_memory - start_memory)
        
        print(f"✅ 高级推理功能测试通过 - 用时: {duration:.2f}s")
    
    @pytest.mark.asyncio
    async def test_knowledge_graph_functionality(self):
        """测试知识图谱功能"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 创建知识图谱
        knowledge_graph = KnowledgeGraph(
            ai_client=self.ai_client,
            prompt_manager=self.prompt_manager,
            kv_store=self.kv_store
        )
        
        # 测试添加知识
        await knowledge_graph.add_knowledge("Python", "是一种", "编程语言")
        await knowledge_graph.add_knowledge("Python", "适用于", "AI开发")
        await knowledge_graph.add_knowledge("AI开发", "需要", "机器学习")
        
        # 测试查询知识
        results = await knowledge_graph.query_knowledge("Python")
        assert len(results.triples) > 0
        
        # 测试推理关系
        inferred = await knowledge_graph.infer_relations("Python")
        assert len(inferred.relations) >= 0
        
        # 记录性能
        duration = time.time() - start_time
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        self.record_performance("knowledge_graph", duration, end_memory - start_memory)
        
        print(f"✅ 知识图谱功能测试通过 - 用时: {duration:.2f}s")
    
    @pytest.mark.asyncio
    async def test_learning_system_functionality(self):
        """测试学习系统功能"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 创建学习系统
        learning_system = LearningSystem(
            ai_client=self.ai_client,
            prompt_manager=self.prompt_manager,
            kv_store=self.kv_store
        )
        
        # 测试交互学习
        interaction = {
            'user_id': 'test_user',
            'query': '如何学习Python？',
            'response': '建议从基础语法开始学习',
            'feedback': 'helpful',
            'timestamp': time.time()
        }
        
        await learning_system.learn_from_interaction(interaction)
        
        # 测试偏好更新
        preferences = {
            'language': 'python',
            'difficulty': 'beginner',
            'topics': ['syntax', 'data_structures']
        }
        
        await learning_system.update_preferences('test_user', preferences)
        
        # 测试响应改进
        feedback = {
            'response_id': 'test_response',
            'rating': 4,
            'comments': '很有帮助',
            'improvements': ['更多示例']
        }
        
        await learning_system.improve_responses(feedback)
        
        # 记录性能
        duration = time.time() - start_time
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        self.record_performance("learning_system", duration, end_memory - start_memory)
        
        print(f"✅ 学习系统功能测试通过 - 用时: {duration:.2f}s")
    
    @pytest.mark.asyncio
    async def test_creativity_engine_functionality(self):
        """测试创意引擎功能"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 创建创意引擎
        creativity_engine = CreativityEngine(
            ai_client=self.ai_client,
            prompt_manager=self.prompt_manager,
            kv_store=self.kv_store
        )
        
        # 测试创意写作
        writing_request = {
            'type': 'story',
            'theme': '科技与未来',
            'length': 'short',
            'style': 'narrative'
        }
        
        creative_writing = await creativity_engine.generate_creative_writing(writing_request)
        assert creative_writing is not None
        assert creative_writing.content is not None
        assert creative_writing.creativity_score > 0
        
        # 测试方案设计
        design_request = {
            'problem': '如何设计一个智能家居系统',
            'constraints': ['成本控制', '用户友好'],
            'requirements': ['自动化', '安全性']
        }
        
        design_solution = await creativity_engine.generate_solution_design(design_request)
        assert design_solution is not None
        assert len(design_solution.components) > 0
        
        # 记录性能
        duration = time.time() - start_time
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        self.record_performance("creativity_engine", duration, end_memory - start_memory)
        
        print(f"✅ 创意引擎功能测试通过 - 用时: {duration:.2f}s")
    
    @pytest.mark.asyncio
    async def test_ui_components_functionality(self):
        """测试UI组件功能"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 测试高级组件（使用Mock避免GUI依赖）
        with patch('ui.advanced_widgets.QWidget'):
            advanced_widgets = AdvancedWidgets()
            
            # 测试智能输入组件
            input_widget = advanced_widgets.create_smart_input_widget()
            assert input_widget is not None
            
            # 测试动态布局组件
            layout_widget = advanced_widgets.create_dynamic_layout_widget()
            assert layout_widget is not None
        
        # 测试可视化组件
        visualization = Visualization()
        
        # 测试任务流程可视化
        task_data = {
            'tasks': [
                {'id': '1', 'name': '任务1', 'status': 'completed'},
                {'id': '2', 'name': '任务2', 'status': 'running'}
            ]
        }
        
        flow_chart = await visualization.create_task_flow_visualization(task_data)
        assert flow_chart is not None
        
        # 记录性能
        duration = time.time() - start_time
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        self.record_performance("ui_components", duration, end_memory - start_memory)
        
        print(f"✅ UI组件功能测试通过 - 用时: {duration:.2f}s")
    
    @pytest.mark.asyncio
    async def test_monitoring_functionality(self):
        """测试监控功能"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 测试性能监控
        performance_monitor = PerformanceMonitor()
        metrics = await performance_monitor.collect_system_metrics()
        
        assert 'cpu_usage' in metrics
        assert 'memory_usage' in metrics
        assert 'disk_usage' in metrics
        assert metrics['cpu_usage'] >= 0
        
        # 测试错误分析
        error_analyzer = ErrorAnalyzer()
        error_data = {
            'error_type': 'ValueError',
            'message': 'Invalid input',
            'stack_trace': 'test stack trace',
            'timestamp': time.time()
        }
        
        analysis = await error_analyzer.analyze_error(error_data)
        assert analysis is not None
        assert analysis.error_category is not None
        
        # 记录性能
        duration = time.time() - start_time
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        self.record_performance("monitoring", duration, end_memory - start_memory)
        
        print(f"✅ 监控功能测试通过 - 用时: {duration:.2f}s")
    
    @pytest.mark.asyncio
    async def test_tools_functionality(self):
        """测试工具功能"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        # 测试代码执行器
        code_executor = CodeExecutor()
        
        # 测试Python代码执行
        python_code = "print('Hello, World!')\nresult = 2 + 2\nprint(f'Result: {result}')"
        execution_result = await code_executor.execute_python_code(python_code)
        
        assert execution_result.success is True
        assert 'Hello, World!' in execution_result.output
        assert 'Result: 4' in execution_result.output
        
        # 测试图表生成器
        chart_generator = ChartGenerator()
        
        chart_data = {
            'type': 'bar',
            'data': {
                'labels': ['A', 'B', 'C'],
                'values': [10, 20, 15]
            },
            'title': '测试图表'
        }
        
        chart_result = await chart_generator.generate_chart(chart_data)
        assert chart_result.success is True
        assert chart_result.chart_path is not None
        
        # 记录性能
        duration = time.time() - start_time
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024
        self.record_performance("tools", duration, end_memory - start_memory)
        
        print(f"✅ 工具功能测试通过 - 用时: {duration:.2f}s")
    
    def test_performance_summary(self):
        """性能测试总结"""
        if not self.performance_data:
            pytest.skip("没有性能数据")
        
        print("\n" + "="*60)
        print("📊 第五阶段功能性能测试总结")
        print("="*60)
        
        total_duration = sum(data['duration'] for data in self.performance_data)
        total_memory = sum(data['memory_usage'] for data in self.performance_data)
        
        print(f"总测试时间: {total_duration:.2f}秒")
        print(f"总内存使用: {total_memory:.2f}MB")
        print(f"平均响应时间: {total_duration/len(self.performance_data):.2f}秒")
        
        print("\n详细性能数据:")
        for data in self.performance_data:
            print(f"  {data['test_name']}: {data['duration']:.2f}s, {data['memory_usage']:.2f}MB")
        
        # 性能基准检查
        assert total_duration < 30.0, f"总测试时间过长: {total_duration:.2f}s"
        assert total_memory < 100.0, f"内存使用过多: {total_memory:.2f}MB"
        
        print("\n✅ 所有性能指标符合要求")

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
