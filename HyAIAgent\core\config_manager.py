"""
配置管理器模块
负责加载和管理应用配置，包括配置文件和环境变量
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from loguru import logger


class ConfigManager:
    """
    配置管理器
    负责加载配置文件、环境变量，并提供配置值的获取和设置功能
    """
    
    def __init__(self, config_path: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_path (str): 配置文件路径，默认为"config.json"
        """
        self.config_path = Path(config_path)
        self.config = {}
        self.load_config()
        self.load_env()
    
    def load_config(self) -> None:
        """
        加载配置文件
        如果配置文件不存在，则创建默认配置
        """
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                logger.info(f"配置文件加载成功: {self.config_path}")
            else:
                logger.warning(f"配置文件不存在，创建默认配置: {self.config_path}")
                self.config = self.get_default_config()
                self.save_config()
        except json.JSONDecodeError as e:
            logger.error(f"配置文件JSON格式错误: {e}")
            self.config = self.get_default_config()
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            self.config = self.get_default_config()
    
    def load_env(self) -> None:
        """
        加载环境变量
        从.env文件和系统环境变量中获取API密钥等敏感信息
        """
        try:
            # 加载.env文件
            load_dotenv()
            logger.info("环境变量加载完成")
            
            # 替换配置中的环境变量占位符
            self._replace_env_variables()
            
        except Exception as e:
            logger.error(f"环境变量加载失败: {e}")
    
    def _replace_env_variables(self) -> None:
        """
        替换配置中的环境变量占位符
        将${VAR_NAME}格式的占位符替换为实际的环境变量值
        """
        def replace_recursive(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    obj[key] = replace_recursive(value)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    obj[i] = replace_recursive(item)
            elif isinstance(obj, str) and obj.startswith('${') and obj.endswith('}'):
                env_var = obj[2:-1]  # 移除${和}
                env_value = os.getenv(env_var)
                if env_value:
                    return env_value
                else:
                    logger.warning(f"环境变量未找到: {env_var}")
                    return obj
            return obj
        
        replace_recursive(self.config)
    
    def get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置
        
        Returns:
            Dict[str, Any]: 默认配置字典
        """
        return {
            "application": {
                "name": "HyAIAgent",
                "version": "1.0.0-stage1",
                "window_title": "HyAIAgent - AI助手"
            },
            "ai_providers": {
                "default": "openai",
                "openai": {
                    "api_key": "${OPENAI_API_KEY}",
                    "base_url": "https://api.openai.com/v1",
                    "model": "gpt-3.5-turbo",
                    "max_tokens": 1000,
                    "temperature": 0.7,
                    "timeout": 30
                }
            },
            "kv_database": {
                "path": "data/kv_store.json",
                "auto_cleanup": True,
                "cleanup_interval": 3600
            },
            "ui": {
                "window_size": [800, 600],
                "font_family": "Microsoft YaHei",
                "font_size": 10
            },
            "logging": {
                "level": "INFO",
                "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
            }
        }
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保目录存在
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            logger.info(f"配置文件保存成功: {self.config_path}")
            return True
        except Exception as e:
            logger.error(f"配置文件保存失败: {e}")
            return False
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值（支持点号分隔的路径）
        
        Args:
            key_path (str): 配置键路径，如"ai_providers.openai.model"
            default (Any): 默认值
            
        Returns:
            Any: 配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key_path (str): 配置键路径
            value (Any): 配置值
            
        Returns:
            bool: 设置是否成功
        """
        try:
            keys = key_path.split('.')
            config = self.config
            
            # 导航到目标位置
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # 设置值
            config[keys[-1]] = value
            
            # 保存配置
            return self.save_config()
            
        except Exception as e:
            logger.error(f"设置配置值失败: {key_path} = {value}, 错误: {e}")
            return False
    
    def get_ai_config(self, provider: Optional[str] = None) -> Dict[str, Any]:
        """
        获取AI提供商配置
        
        Args:
            provider (str, optional): AI提供商名称，如果为None则使用默认提供商
            
        Returns:
            Dict[str, Any]: AI配置字典
        """
        if provider is None:
            provider = self.get('ai_providers.default', 'openai')
        
        ai_config = self.get(f'ai_providers.{provider}', {})
        if not ai_config:
            logger.warning(f"AI提供商配置未找到: {provider}")
            # 返回默认的openai配置
            ai_config = self.get('ai_providers.openai', {})
        
        return ai_config
    
    def validate_config(self) -> bool:
        """
        验证配置的完整性
        
        Returns:
            bool: 配置是否有效
        """
        required_keys = [
            'application.name',
            'ai_providers.default',
            'ui.window_size',
            'logging.level'
        ]
        
        for key in required_keys:
            if self.get(key) is None:
                logger.error(f"必需的配置项缺失: {key}")
                return False
        
        # 验证AI配置
        default_provider = self.get('ai_providers.default')
        ai_config = self.get_ai_config(default_provider)
        if not ai_config.get('api_key'):
            logger.error(f"AI提供商API密钥未配置: {default_provider}")
            return False
        
        logger.info("配置验证通过")
        return True
