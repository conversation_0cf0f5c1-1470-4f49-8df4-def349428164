"""
任务系统集成测试模块

测试文件操作功能与任务管理系统的集成。
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock

from operations.task_integration import FileOperationTaskExecutor, TaskSystemIntegrator
from core.task_models import Task, TaskType, TaskPriority, TaskStatus
from core.task_manager import TaskManager
from core.execution_engine import ExecutionEngine


class TestFileOperationTaskExecutor:
    """测试文件操作任务执行器"""
    
    @pytest.fixture
    async def setup_test_environment(self):
        """设置测试环境"""
        # 创建临时工作目录
        temp_dir = tempfile.mkdtemp()
        workspace = Path(temp_dir) / "test_workspace"
        workspace.mkdir(exist_ok=True)
        
        # 创建测试文件
        test_files = {
            "test1.txt": "Hello World",
            "test2.txt": "Python Testing",
            "config.json": '{"name": "test", "value": 123}',
            "data.csv": "name,age\nAlice,25\nBob,30"
        }
        
        for filename, content in test_files.items():
            (workspace / filename).write_text(content, encoding='utf-8')
        
        # 创建子目录
        subdir = workspace / "subdir"
        subdir.mkdir(exist_ok=True)
        (subdir / "nested.txt").write_text("Nested file content", encoding='utf-8')
        
        # 初始化任务执行器
        executor = FileOperationTaskExecutor(str(workspace))
        
        yield {
            "executor": executor,
            "workspace": workspace,
            "temp_dir": temp_dir
        }
        
        # 清理
        shutil.rmtree(temp_dir)
    
    @pytest.mark.asyncio
    async def test_file_read_task(self, setup_test_environment):
        """测试文件读取任务"""
        env = await setup_test_environment.__anext__()
        executor = env["executor"]
        
        # 创建文件读取任务
        task = Task(
            description="Read test file",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.MEDIUM,
            parameters={
                "operation_type": "file_read",
                "file_path": "test1.txt"
            }
        )
        
        # 执行任务
        result = await executor.execute_task(task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.result["success"] is True
        assert "Hello World" in result.result["content"]
        assert result.error_message is None
    
    @pytest.mark.asyncio
    async def test_file_write_task(self, setup_test_environment):
        """测试文件写入任务"""
        env = await setup_test_environment.__anext__()
        executor = env["executor"]
        
        # 创建文件写入任务
        task = Task(
            description="Write test file",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.MEDIUM,
            parameters={
                "operation_type": "file_write",
                "file_path": "new_file.txt",
                "content": "New file content"
            }
        )
        
        # 执行任务
        result = await executor.execute_task(task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.result["success"] is True
        
        # 验证文件确实被创建
        new_file = env["workspace"] / "new_file.txt"
        assert new_file.exists()
        assert new_file.read_text(encoding='utf-8') == "New file content"
    
    @pytest.mark.asyncio
    async def test_file_search_task(self, setup_test_environment):
        """测试文件搜索任务"""
        env = setup_test_environment
        executor = env["executor"]
        
        # 创建文件搜索任务
        task = Task(
            description="Search files",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.MEDIUM,
            parameters={
                "operation_type": "file_search",
                "query": "test",
                "search_options": {
                    "search_content": True,
                    "case_sensitive": False
                }
            }
        )
        
        # 执行任务
        result = await executor.execute_task(task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.result["success"] is True
        assert len(result.result["matches"]) > 0
    
    @pytest.mark.asyncio
    async def test_batch_rename_task(self, setup_test_environment):
        """测试批量重命名任务"""
        env = setup_test_environment
        executor = env["executor"]
        
        # 创建批量重命名任务
        task = Task(
            description="Batch rename files",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.MEDIUM,
            parameters={
                "operation_type": "batch_rename",
                "file_patterns": ["test*.txt"],
                "rename_rule": {
                    "pattern": "renamed_{name}",
                    "preserve_extension": True
                }
            }
        )
        
        # 执行任务
        result = await executor.execute_task(task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.result["success"] is True
        assert result.result["stats"]["successful_operations"] > 0
    
    @pytest.mark.asyncio
    async def test_batch_checksum_task(self, setup_test_environment):
        """测试批量校验和计算任务"""
        env = setup_test_environment
        executor = env["executor"]
        
        # 创建批量校验和计算任务
        task = Task(
            description="Calculate checksums",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.MEDIUM,
            parameters={
                "operation_type": "batch_checksum",
                "file_patterns": ["*.txt"],
                "algorithm": "md5"
            }
        )
        
        # 执行任务
        result = await executor.execute_task(task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.result["success"] is True
        assert len(result.result["checksums"]) > 0
        
        # 验证校验和格式
        for file_path, checksum in result.result["checksums"].items():
            assert len(checksum) == 32  # MD5长度
    
    @pytest.mark.asyncio
    async def test_document_extract_task(self, setup_test_environment):
        """测试文档提取任务"""
        env = setup_test_environment
        executor = env["executor"]
        
        # 创建文档提取任务
        task = Task(
            description="Extract document content",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.MEDIUM,
            parameters={
                "operation_type": "document_extract",
                "file_path": "test1.txt",
                "extract_options": {
                    "include_metadata": True
                }
            }
        )
        
        # 执行任务
        result = await executor.execute_task(task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.result["success"] is True
        assert "content" in result.result
    
    @pytest.mark.asyncio
    async def test_config_read_task(self, setup_test_environment):
        """测试配置读取任务"""
        env = setup_test_environment
        executor = env["executor"]
        
        # 创建配置读取任务
        task = Task(
            description="Read config file",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.MEDIUM,
            parameters={
                "operation_type": "config_read",
                "config_path": "config.json",
                "config_format": "json"
            }
        )
        
        # 执行任务
        result = await executor.execute_task(task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.result["success"] is True
        assert result.result["config"]["name"] == "test"
        assert result.result["config"]["value"] == 123
    
    @pytest.mark.asyncio
    async def test_invalid_operation_task(self, setup_test_environment):
        """测试无效操作任务"""
        env = setup_test_environment
        executor = env["executor"]
        
        # 创建无效操作任务
        task = Task(
            description="Invalid operation",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.MEDIUM,
            parameters={
                "operation_type": "invalid_operation"
            }
        )
        
        # 执行任务
        result = await executor.execute_task(task)
        
        # 验证结果
        assert result.status == TaskStatus.FAILED
        assert result.error_message is not None
        assert "Unsupported operation type" in result.error_message
    
    @pytest.mark.asyncio
    async def test_missing_parameters_task(self, setup_test_environment):
        """测试缺少参数的任务"""
        env = setup_test_environment
        executor = env["executor"]
        
        # 创建缺少参数的任务
        task = Task(
            description="Missing parameters",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.MEDIUM,
            parameters={
                "operation_type": "file_read"
                # 缺少file_path参数
            }
        )
        
        # 执行任务
        result = await executor.execute_task(task)
        
        # 验证结果
        assert result.status == TaskStatus.FAILED
        assert result.error_message is not None
        assert "Missing file_path parameter" in result.error_message


class TestTaskSystemIntegrator:
    """测试任务系统集成器"""
    
    @pytest.fixture
    async def setup_integrator(self):
        """设置集成器测试环境"""
        # 创建临时工作目录
        temp_dir = tempfile.mkdtemp()
        workspace = Path(temp_dir) / "test_workspace"
        workspace.mkdir(exist_ok=True)
        
        # 创建模拟的任务管理器和执行引擎
        mock_ai_client = Mock()
        mock_prompt_manager = Mock()
        mock_kv_store = Mock()
        
        task_manager = TaskManager(mock_ai_client, mock_prompt_manager, mock_kv_store)
        execution_engine = ExecutionEngine(mock_ai_client, mock_prompt_manager)
        
        # 创建集成器
        integrator = TaskSystemIntegrator(task_manager, execution_engine, str(workspace))
        
        yield {
            "integrator": integrator,
            "task_manager": task_manager,
            "execution_engine": execution_engine,
            "workspace": workspace,
            "temp_dir": temp_dir
        }
        
        # 清理
        shutil.rmtree(temp_dir)
    
    @pytest.mark.asyncio
    async def test_create_file_operation_task(self, setup_integrator):
        """测试创建文件操作任务"""
        env = setup_integrator
        integrator = env["integrator"]
        
        # 创建文件操作任务
        task = await integrator.create_file_operation_task(
            operation_type="file_read",
            parameters={"file_path": "test.txt"},
            description="Read test file",
            priority="HIGH"
        )
        
        # 验证任务
        assert task.task_type == TaskType.FILE_OPERATION
        assert task.priority == TaskPriority.HIGH
        assert task.parameters["operation_type"] == "file_read"
        assert task.parameters["file_path"] == "test.txt"
        assert "Read test file" in task.description
    
    def test_get_supported_operations(self, setup_integrator):
        """测试获取支持的操作列表"""
        env = setup_integrator
        integrator = env["integrator"]
        
        # 获取支持的操作
        operations = integrator.get_supported_operations()
        
        # 验证操作列表
        assert len(operations) > 0
        
        # 检查特定操作
        operation_types = [op["operation_type"] for op in operations]
        assert "file_read" in operation_types
        assert "file_write" in operation_types
        assert "batch_rename" in operation_types
        assert "document_extract" in operation_types
        
        # 检查操作分类
        categories = set(op["category"] for op in operations)
        assert "基础文件操作" in categories
        assert "批量文件处理" in categories
        assert "文档处理" in categories
    
    @pytest.mark.asyncio
    async def test_get_file_operation_stats(self, setup_integrator):
        """测试获取文件操作统计信息"""
        env = setup_integrator
        integrator = env["integrator"]
        
        # 获取统计信息
        stats = await integrator.get_file_operation_stats()
        
        # 验证统计信息
        assert "file_operations" in stats
        assert "batch_processing" in stats
        assert "workspace_path" in stats
        assert "total_registered_executors" in stats
        assert "available_operations" in stats
        
        # 验证操作数量
        assert stats["total_registered_executors"] > 0
        assert len(stats["available_operations"]) > 0
