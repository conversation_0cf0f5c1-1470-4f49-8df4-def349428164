"""
聊天窗口界面模块
基于PyQt6实现的AI聊天界面
"""

import sys
from pathlib import Path
from typing import Optional
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QLineEdit, QPushButton, QSplitter, QMenuBar, QMenu,
    QStatusBar, QLabel, QScrollArea, QFrame, QMessageBox, QDialog,
    QFormLayout, QSpinBox, QDoubleSpinBox, QComboBox, QCheckBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QTextCursor, QAction, QIcon
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager
from core.ai_client import SimpleAIClient
from core.kv_store import KVStore
from core.prompt_manager import PromptManager


class AIResponseThread(QThread):
    """AI响应线程，避免界面阻塞"""
    
    response_received = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, ai_client: SimpleAIClient, message: str, system_prompt: Optional[str] = None):
        super().__init__()
        self.ai_client = ai_client
        self.message = message
        self.system_prompt = system_prompt
    
    def run(self):
        """执行AI请求"""
        try:
            response = self.ai_client.chat(self.message, self.system_prompt)
            self.response_received.emit(response)
        except Exception as e:
            self.error_occurred.emit(str(e))


class SettingsDialog(QDialog):
    """设置对话框"""
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(400, 300)
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout()
        
        # 创建表单布局
        form_layout = QFormLayout()
        
        # AI配置
        self.model_combo = QComboBox()
        self.model_combo.addItems(["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"])
        form_layout.addRow("模型:", self.model_combo)
        
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(1, 4000)
        self.max_tokens_spin.setValue(1000)
        form_layout.addRow("最大Token数:", self.max_tokens_spin)
        
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(0.0, 2.0)
        self.temperature_spin.setSingleStep(0.1)
        self.temperature_spin.setValue(0.7)
        form_layout.addRow("温度:", self.temperature_spin)
        
        # 界面配置
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 24)
        self.font_size_spin.setValue(12)
        form_layout.addRow("字体大小:", self.font_size_spin)
        
        self.auto_save_check = QCheckBox()
        self.auto_save_check.setChecked(True)
        form_layout.addRow("自动保存对话:", self.auto_save_check)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def load_settings(self):
        """加载设置"""
        try:
            ai_config = self.config_manager.get_ai_config()
            ui_config = self.config_manager.get("ui", {})
            
            # 加载AI配置
            model = ai_config.get("model", "gpt-3.5-turbo")
            if model in [self.model_combo.itemText(i) for i in range(self.model_combo.count())]:
                self.model_combo.setCurrentText(model)
            
            self.max_tokens_spin.setValue(ai_config.get("max_tokens", 1000))
            self.temperature_spin.setValue(ai_config.get("temperature", 0.7))
            
            # 加载界面配置
            self.font_size_spin.setValue(ui_config.get("font_size", 12))
            self.auto_save_check.setChecked(ui_config.get("auto_save", True))
            
        except Exception as e:
            logger.error(f"加载设置失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            # 保存AI配置
            self.config_manager.set("ai.model", self.model_combo.currentText())
            self.config_manager.set("ai.max_tokens", self.max_tokens_spin.value())
            self.config_manager.set("ai.temperature", self.temperature_spin.value())
            
            # 保存界面配置
            self.config_manager.set("ui.font_size", self.font_size_spin.value())
            self.config_manager.set("ui.auto_save", self.auto_save_check.isChecked())
            
            return True
        except Exception as e:
            logger.error(f"保存设置失败: {e}")
            return False


class ChatWindow(QMainWindow):
    """主聊天窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("HyAIAgent - AI聊天助手")
        self.setGeometry(100, 100, 800, 600)
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.kv_store = KVStore()
        self.prompt_manager = PromptManager()
        self.ai_client = None
        self.ai_thread = None
        
        # 界面组件
        self.chat_display = None
        self.input_field = None
        self.send_button = None
        self.status_label = None
        
        # 初始化界面和AI客户端
        self.setup_ui()
        self.setup_ai_client()
        self.setup_connections()
        
        logger.info("聊天窗口初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        main_layout.addWidget(splitter)
        
        # 聊天显示区域
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setFont(QFont("Microsoft YaHei", 12))
        splitter.addWidget(self.chat_display)
        
        # 输入区域
        input_widget = QWidget()
        input_layout = QVBoxLayout()
        input_widget.setLayout(input_layout)
        
        # 输入框和发送按钮
        input_row_layout = QHBoxLayout()
        self.input_field = QLineEdit()
        self.input_field.setPlaceholderText("请输入您的问题...")
        self.input_field.setFont(QFont("Microsoft YaHei", 11))
        
        self.send_button = QPushButton("发送")
        self.send_button.setFixedWidth(80)
        
        input_row_layout.addWidget(self.input_field)
        input_row_layout.addWidget(self.send_button)
        input_layout.addLayout(input_row_layout)
        
        splitter.addWidget(input_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 100])
        
        # 创建菜单栏
        self.setup_menu_bar()
        
        # 创建状态栏
        self.setup_status_bar()
    
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        new_chat_action = QAction("新建对话", self)
        new_chat_action.triggered.connect(self.new_chat)
        file_menu.addAction(new_chat_action)
        
        save_chat_action = QAction("保存对话", self)
        save_chat_action.triggered.connect(self.save_chat)
        file_menu.addAction(save_chat_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 设置菜单
        settings_menu = menubar.addMenu("设置")
        
        preferences_action = QAction("首选项", self)
        preferences_action.triggered.connect(self.show_settings)
        settings_menu.addAction(preferences_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        status_bar = self.statusBar()
        self.status_label = QLabel("就绪")
        status_bar.addWidget(self.status_label)
    
    def setup_ai_client(self):
        """设置AI客户端"""
        try:
            ai_config = self.config_manager.get_ai_config()
            
            self.ai_client = SimpleAIClient(
                api_key=ai_config["api_key"],
                base_url=ai_config.get("base_url", "https://api.openai.com/v1"),
                model=ai_config.get("model", "gpt-3.5-turbo"),
                max_tokens=ai_config.get("max_tokens", 1000),
                temperature=ai_config.get("temperature", 0.7)
            )
            
            self.status_label.setText("AI客户端已连接")
            logger.info("AI客户端设置完成")
            
        except Exception as e:
            error_msg = f"AI客户端设置失败: {e}"
            logger.error(error_msg)
            self.status_label.setText("AI客户端连接失败")
            QMessageBox.critical(self, "错误", error_msg)
    
    def setup_connections(self):
        """设置信号连接"""
        self.send_button.clicked.connect(self.send_message)
        self.input_field.returnPressed.connect(self.send_message)
    
    def send_message(self):
        """发送消息"""
        if not self.ai_client:
            QMessageBox.warning(self, "警告", "AI客户端未连接")
            return
        
        message = self.input_field.text().strip()
        if not message:
            return
        
        # 显示用户消息
        self.append_message("用户", message)
        self.input_field.clear()
        
        # 禁用发送按钮
        self.send_button.setEnabled(False)
        self.status_label.setText("AI正在思考...")
        
        # 创建AI响应线程
        self.ai_thread = AIResponseThread(self.ai_client, message)
        self.ai_thread.response_received.connect(self.on_ai_response)
        self.ai_thread.error_occurred.connect(self.on_ai_error)
        self.ai_thread.finished.connect(self.on_ai_finished)
        self.ai_thread.start()
    
    def append_message(self, sender: str, message: str):
        """添加消息到聊天显示区域"""
        cursor = self.chat_display.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        
        # 添加发送者标识
        cursor.insertText(f"\n{sender}:\n")
        cursor.insertText(f"{message}\n")
        cursor.insertText("-" * 50 + "\n")
        
        # 滚动到底部
        self.chat_display.setTextCursor(cursor)
        self.chat_display.ensureCursorVisible()
    
    def on_ai_response(self, response: str):
        """处理AI响应"""
        self.append_message("AI助手", response)
    
    def on_ai_error(self, error: str):
        """处理AI错误"""
        self.append_message("系统", f"错误: {error}")
    
    def on_ai_finished(self):
        """AI响应完成"""
        self.send_button.setEnabled(True)
        self.status_label.setText("就绪")
        self.ai_thread = None
    
    def new_chat(self):
        """新建对话"""
        if self.ai_client:
            self.ai_client.clear_history()
        self.chat_display.clear()
        self.status_label.setText("新对话已开始")
    
    def save_chat(self):
        """保存对话"""
        try:
            chat_content = self.chat_display.toPlainText()
            if chat_content:
                # 使用时间戳作为键名
                import time
                timestamp = str(int(time.time()))
                self.kv_store.set(f"chat_{timestamp}", chat_content)
                self.status_label.setText("对话已保存")
            else:
                QMessageBox.information(self, "提示", "没有对话内容可保存")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存对话失败: {e}")
    
    def show_settings(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self.config_manager, self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            if dialog.save_settings():
                self.status_label.setText("设置已保存")
                # 重新设置AI客户端
                self.setup_ai_client()
            else:
                QMessageBox.critical(self, "错误", "保存设置失败")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "HyAIAgent v1.0\n\n"
                         "一个基于PyQt6的AI聊天助手\n"
                         "支持OpenAI兼容的API接口")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 停止AI线程
            if self.ai_thread and self.ai_thread.isRunning():
                self.ai_thread.terminate()
                self.ai_thread.wait()
            
            # 关闭数据库连接
            if self.kv_store:
                self.kv_store.close()
            
            logger.info("聊天窗口已关闭")
            event.accept()
            
        except Exception as e:
            logger.error(f"关闭窗口时发生错误: {e}")
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("HyAIAgent")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("HyDevelop")
    
    # 创建并显示主窗口
    window = ChatWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
