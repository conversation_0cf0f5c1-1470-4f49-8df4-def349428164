"""
批量文件处理模块
提供高效的批量文件操作功能，支持并发处理和进度监控
"""

import os
import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime
import shutil
import fnmatch
from concurrent.futures import ThreadPoolExecutor
import hashlib

from .security_manager import SecurityManager
from .file_operations import FileOperations

logger = logging.getLogger(__name__)


class BatchProcessor:
    """
    批量文件处理器
    
    提供以下功能：
    - 批量文件重命名
    - 批量文件复制和移动
    - 批量文件删除
    - 批量格式转换
    - 批量内容处理
    - 并发处理和进度监控
    """
    
    def __init__(self, workspace_path: str = "./workspace", 
                 security_manager: Optional[SecurityManager] = None,
                 file_operations: Optional[FileOperations] = None,
                 max_workers: int = 4):
        """
        初始化批量处理器
        
        Args:
            workspace_path (str): 工作目录路径
            security_manager (Optional[SecurityManager]): 安全管理器实例
            file_operations (Optional[FileOperations]): 文件操作实例
            max_workers (int): 最大并发工作线程数
        """
        self.workspace_path = Path(workspace_path).resolve()
        self.security_manager = security_manager or SecurityManager(workspace_path)
        self.file_operations = file_operations or FileOperations(workspace_path, security_manager)
        self.max_workers = max_workers
        self.processing_stats = {
            "total_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "bytes_processed": 0,
            "start_time": None,
            "end_time": None
        }
    
    async def batch_rename_files(self, file_patterns: List[str], 
                                rename_rule: Dict[str, Any]) -> Dict[str, Any]:
        """
        批量重命名文件
        
        Args:
            file_patterns (List[str]): 文件模式列表
            rename_rule (Dict[str, Any]): 重命名规则
                - pattern (str): 重命名模式，支持占位符 {name}, {ext}, {index}, {timestamp}
                - start_index (int): 起始索引号
                - preserve_extension (bool): 是否保留扩展名
                - case_transform (str): 大小写转换 (upper, lower, title, none)
                - add_prefix (str): 添加前缀
                - add_suffix (str): 添加后缀
                - replace_text (Dict[str, str]): 文本替换规则
                
        Returns:
            Dict[str, Any]: 重命名结果
        """
        try:
            self._reset_stats()
            self.processing_stats["start_time"] = datetime.now()
            
            # 收集要重命名的文件
            files_to_rename = []
            for pattern in file_patterns:
                for file_path in self.workspace_path.rglob(pattern):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(self.workspace_path)
                        if self.security_manager.validate_path(str(relative_path)):
                            files_to_rename.append(file_path)
            
            if not files_to_rename:
                return {
                    "success": True,
                    "message": "No files found matching the patterns",
                    "renamed_files": [],
                    "failed_files": []
                }
            
            # 设置默认重命名规则
            rule = {
                "pattern": "{name}",
                "start_index": 1,
                "preserve_extension": True,
                "case_transform": "none",
                "add_prefix": "",
                "add_suffix": "",
                "replace_text": {}
            }
            rule.update(rename_rule)
            
            renamed_files = []
            failed_files = []
            
            # 批量重命名处理
            for index, file_path in enumerate(files_to_rename):
                try:
                    old_name = file_path.name
                    name_without_ext = file_path.stem
                    extension = file_path.suffix
                    
                    # 应用文本替换
                    if rule["replace_text"]:
                        for old_text, new_text in rule["replace_text"].items():
                            name_without_ext = name_without_ext.replace(old_text, new_text)
                    
                    # 应用大小写转换
                    if rule["case_transform"] == "upper":
                        name_without_ext = name_without_ext.upper()
                    elif rule["case_transform"] == "lower":
                        name_without_ext = name_without_ext.lower()
                    elif rule["case_transform"] == "title":
                        name_without_ext = name_without_ext.title()
                    
                    # 构建新文件名
                    new_name = rule["pattern"].format(
                        name=name_without_ext,
                        ext=extension.lstrip('.'),
                        index=rule["start_index"] + index,
                        timestamp=datetime.now().strftime("%Y%m%d_%H%M%S")
                    )
                    
                    # 添加前缀和后缀
                    if rule["add_prefix"]:
                        new_name = rule["add_prefix"] + new_name
                    if rule["add_suffix"]:
                        new_name = new_name + rule["add_suffix"]
                    
                    # 保留扩展名
                    if rule["preserve_extension"] and extension:
                        if not new_name.endswith(extension):
                            new_name += extension
                    
                    # 执行重命名
                    new_path = file_path.parent / new_name
                    
                    # 检查目标文件是否已存在
                    if new_path.exists():
                        # 添加数字后缀避免冲突
                        counter = 1
                        base_name = new_path.stem
                        extension = new_path.suffix
                        while new_path.exists():
                            new_name = f"{base_name}_{counter}{extension}"
                            new_path = file_path.parent / new_name
                            counter += 1
                    
                    # 执行重命名操作
                    file_path.rename(new_path)
                    
                    renamed_files.append({
                        "old_name": old_name,
                        "new_name": new_path.name,
                        "path": str(new_path.relative_to(self.workspace_path))
                    })
                    
                    self.processing_stats["successful_operations"] += 1
                    
                except Exception as e:
                    logger.error(f"Failed to rename file {file_path}: {e}")
                    failed_files.append({
                        "file": str(file_path.relative_to(self.workspace_path)),
                        "error": str(e)
                    })
                    self.processing_stats["failed_operations"] += 1
                
                self.processing_stats["total_operations"] += 1
            
            self.processing_stats["end_time"] = datetime.now()
            
            return {
                "success": True,
                "renamed_files": renamed_files,
                "failed_files": failed_files,
                "stats": self.processing_stats.copy()
            }
            
        except Exception as e:
            logger.error(f"Batch rename operation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def batch_copy_files(self, source_patterns: List[str], 
                              destination_dir: str,
                              copy_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        批量复制文件
        
        Args:
            source_patterns (List[str]): 源文件模式列表
            destination_dir (str): 目标目录
            copy_options (Dict[str, Any]): 复制选项
                - overwrite (bool): 是否覆盖已存在的文件
                - preserve_structure (bool): 是否保持目录结构
                - create_backup (bool): 是否创建备份
                - max_concurrent (int): 最大并发数
                
        Returns:
            Dict[str, Any]: 复制结果
        """
        try:
            self._reset_stats()
            self.processing_stats["start_time"] = datetime.now()
            
            # 设置默认复制选项
            options = {
                "overwrite": False,
                "preserve_structure": True,
                "create_backup": False,
                "max_concurrent": self.max_workers
            }
            if copy_options:
                options.update(copy_options)
            
            # 验证目标目录
            dest_path = self.workspace_path / destination_dir
            if not self.security_manager.validate_path(str(dest_path.relative_to(self.workspace_path))):
                return {
                    "success": False,
                    "error": "Destination directory is not safe"
                }
            
            # 确保目标目录存在
            dest_path.mkdir(parents=True, exist_ok=True)
            
            # 收集要复制的文件
            files_to_copy = []
            for pattern in source_patterns:
                for file_path in self.workspace_path.rglob(pattern):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(self.workspace_path)
                        if self.security_manager.validate_path(str(relative_path)):
                            files_to_copy.append(file_path)
            
            if not files_to_copy:
                return {
                    "success": True,
                    "message": "No files found matching the patterns",
                    "copied_files": [],
                    "failed_files": []
                }
            
            copied_files = []
            failed_files = []
            
            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(options["max_concurrent"])
            
            async def copy_single_file(source_path: Path) -> None:
                async with semaphore:
                    try:
                        # 计算目标路径
                        if options["preserve_structure"]:
                            relative_path = source_path.relative_to(self.workspace_path)
                            target_path = dest_path / relative_path
                        else:
                            target_path = dest_path / source_path.name
                        
                        # 确保目标目录存在
                        target_path.parent.mkdir(parents=True, exist_ok=True)
                        
                        # 检查是否需要备份
                        if target_path.exists():
                            if not options["overwrite"]:
                                # 生成新的文件名
                                counter = 1
                                base_name = target_path.stem
                                extension = target_path.suffix
                                while target_path.exists():
                                    new_name = f"{base_name}_{counter}{extension}"
                                    target_path = target_path.parent / new_name
                                    counter += 1
                            elif options["create_backup"]:
                                backup_path = target_path.with_suffix(target_path.suffix + ".bak")
                                shutil.copy2(target_path, backup_path)
                        
                        # 执行复制操作
                        await asyncio.get_event_loop().run_in_executor(
                            None, shutil.copy2, source_path, target_path
                        )
                        
                        # 获取文件大小
                        file_size = source_path.stat().st_size
                        self.processing_stats["bytes_processed"] += file_size
                        
                        copied_files.append({
                            "source": str(source_path.relative_to(self.workspace_path)),
                            "destination": str(target_path.relative_to(self.workspace_path)),
                            "size": file_size
                        })
                        
                        self.processing_stats["successful_operations"] += 1
                        
                    except Exception as e:
                        logger.error(f"Failed to copy file {source_path}: {e}")
                        failed_files.append({
                            "file": str(source_path.relative_to(self.workspace_path)),
                            "error": str(e)
                        })
                        self.processing_stats["failed_operations"] += 1
                    
                    self.processing_stats["total_operations"] += 1
            
            # 并发执行复制操作
            tasks = [copy_single_file(file_path) for file_path in files_to_copy]
            await asyncio.gather(*tasks)
            
            self.processing_stats["end_time"] = datetime.now()
            
            return {
                "success": True,
                "copied_files": copied_files,
                "failed_files": failed_files,
                "stats": self.processing_stats.copy()
            }
            
        except Exception as e:
            logger.error(f"Batch copy operation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _reset_stats(self) -> None:
        """重置处理统计"""
        self.processing_stats = {
            "total_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "bytes_processed": 0,
            "start_time": None,
            "end_time": None
        }
    
    async def batch_delete_files(self, file_patterns: List[str],
                                delete_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        批量删除文件

        Args:
            file_patterns (List[str]): 文件模式列表
            delete_options (Dict[str, Any]): 删除选项
                - create_backup (bool): 是否创建备份
                - backup_dir (str): 备份目录
                - confirm_delete (bool): 是否需要确认删除
                - max_concurrent (int): 最大并发数

        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            self._reset_stats()
            self.processing_stats["start_time"] = datetime.now()

            # 设置默认删除选项
            options = {
                "create_backup": True,
                "backup_dir": "backup",
                "confirm_delete": False,
                "max_concurrent": self.max_workers
            }
            if delete_options:
                options.update(delete_options)

            # 收集要删除的文件
            files_to_delete = []
            for pattern in file_patterns:
                for file_path in self.workspace_path.rglob(pattern):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(self.workspace_path)
                        if self.security_manager.validate_path(str(relative_path)):
                            files_to_delete.append(file_path)

            if not files_to_delete:
                return {
                    "success": True,
                    "message": "No files found matching the patterns",
                    "deleted_files": [],
                    "failed_files": []
                }

            # 创建备份目录
            backup_path = None
            if options["create_backup"]:
                backup_path = self.workspace_path / options["backup_dir"]
                backup_path.mkdir(parents=True, exist_ok=True)

            deleted_files = []
            failed_files = []

            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(options["max_concurrent"])

            async def delete_single_file(file_path: Path) -> None:
                async with semaphore:
                    try:
                        file_size = file_path.stat().st_size
                        relative_path = file_path.relative_to(self.workspace_path)

                        # 创建备份
                        if options["create_backup"] and backup_path:
                            backup_file_path = backup_path / relative_path
                            backup_file_path.parent.mkdir(parents=True, exist_ok=True)
                            await asyncio.get_event_loop().run_in_executor(
                                None, shutil.copy2, file_path, backup_file_path
                            )

                        # 删除文件
                        await asyncio.get_event_loop().run_in_executor(
                            None, file_path.unlink
                        )

                        deleted_files.append({
                            "file": str(relative_path),
                            "size": file_size,
                            "backup_created": options["create_backup"]
                        })

                        self.processing_stats["bytes_processed"] += file_size
                        self.processing_stats["successful_operations"] += 1

                    except Exception as e:
                        logger.error(f"Failed to delete file {file_path}: {e}")
                        failed_files.append({
                            "file": str(file_path.relative_to(self.workspace_path)),
                            "error": str(e)
                        })
                        self.processing_stats["failed_operations"] += 1

                    self.processing_stats["total_operations"] += 1

            # 并发执行删除操作
            tasks = [delete_single_file(file_path) for file_path in files_to_delete]
            await asyncio.gather(*tasks)

            self.processing_stats["end_time"] = datetime.now()

            return {
                "success": True,
                "deleted_files": deleted_files,
                "failed_files": failed_files,
                "backup_location": str(backup_path.relative_to(self.workspace_path)) if backup_path else None,
                "stats": self.processing_stats.copy()
            }

        except Exception as e:
            logger.error(f"Batch delete operation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def batch_move_files(self, source_patterns: List[str],
                              destination_dir: str,
                              move_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        批量移动文件

        Args:
            source_patterns (List[str]): 源文件模式列表
            destination_dir (str): 目标目录
            move_options (Dict[str, Any]): 移动选项
                - overwrite (bool): 是否覆盖已存在的文件
                - preserve_structure (bool): 是否保持目录结构
                - create_backup (bool): 是否创建备份
                - max_concurrent (int): 最大并发数

        Returns:
            Dict[str, Any]: 移动结果
        """
        try:
            self._reset_stats()
            self.processing_stats["start_time"] = datetime.now()

            # 设置默认移动选项
            options = {
                "overwrite": False,
                "preserve_structure": True,
                "create_backup": False,
                "max_concurrent": self.max_workers
            }
            if move_options:
                options.update(move_options)

            # 验证目标目录
            dest_path = self.workspace_path / destination_dir
            if not self.security_manager.validate_path(str(dest_path.relative_to(self.workspace_path))):
                return {
                    "success": False,
                    "error": "Destination directory is not safe"
                }

            # 确保目标目录存在
            dest_path.mkdir(parents=True, exist_ok=True)

            # 收集要移动的文件
            files_to_move = []
            for pattern in source_patterns:
                for file_path in self.workspace_path.rglob(pattern):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(self.workspace_path)
                        if self.security_manager.validate_path(str(relative_path)):
                            files_to_move.append(file_path)

            if not files_to_move:
                return {
                    "success": True,
                    "message": "No files found matching the patterns",
                    "moved_files": [],
                    "failed_files": []
                }

            moved_files = []
            failed_files = []

            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(options["max_concurrent"])

            async def move_single_file(source_path: Path) -> None:
                async with semaphore:
                    try:
                        # 计算目标路径
                        if options["preserve_structure"]:
                            relative_path = source_path.relative_to(self.workspace_path)
                            target_path = dest_path / relative_path
                        else:
                            target_path = dest_path / source_path.name

                        # 确保目标目录存在
                        target_path.parent.mkdir(parents=True, exist_ok=True)

                        # 检查是否需要备份
                        if target_path.exists():
                            if not options["overwrite"]:
                                # 生成新的文件名
                                counter = 1
                                base_name = target_path.stem
                                extension = target_path.suffix
                                while target_path.exists():
                                    new_name = f"{base_name}_{counter}{extension}"
                                    target_path = target_path.parent / new_name
                                    counter += 1
                            elif options["create_backup"]:
                                backup_path = target_path.with_suffix(target_path.suffix + ".bak")
                                shutil.copy2(target_path, backup_path)

                        # 获取文件大小
                        file_size = source_path.stat().st_size

                        # 执行移动操作
                        await asyncio.get_event_loop().run_in_executor(
                            None, shutil.move, source_path, target_path
                        )

                        self.processing_stats["bytes_processed"] += file_size

                        moved_files.append({
                            "source": str(source_path.relative_to(self.workspace_path)),
                            "destination": str(target_path.relative_to(self.workspace_path)),
                            "size": file_size
                        })

                        self.processing_stats["successful_operations"] += 1

                    except Exception as e:
                        logger.error(f"Failed to move file {source_path}: {e}")
                        failed_files.append({
                            "file": str(source_path.relative_to(self.workspace_path)),
                            "error": str(e)
                        })
                        self.processing_stats["failed_operations"] += 1

                    self.processing_stats["total_operations"] += 1

            # 并发执行移动操作
            tasks = [move_single_file(file_path) for file_path in files_to_move]
            await asyncio.gather(*tasks)

            self.processing_stats["end_time"] = datetime.now()

            return {
                "success": True,
                "moved_files": moved_files,
                "failed_files": failed_files,
                "stats": self.processing_stats.copy()
            }

        except Exception as e:
            logger.error(f"Batch move operation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def batch_process_content(self, file_patterns: List[str],
                                   content_processor: Callable[[str], str],
                                   process_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        批量处理文件内容

        Args:
            file_patterns (List[str]): 文件模式列表
            content_processor (Callable[[str], str]): 内容处理函数
            process_options (Dict[str, Any]): 处理选项
                - create_backup (bool): 是否创建备份
                - encoding (str): 文件编码
                - max_concurrent (int): 最大并发数
                - dry_run (bool): 是否只是预览，不实际修改

        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            self._reset_stats()
            self.processing_stats["start_time"] = datetime.now()

            # 设置默认处理选项
            options = {
                "create_backup": True,
                "encoding": "utf-8",
                "max_concurrent": self.max_workers,
                "dry_run": False
            }
            if process_options:
                options.update(process_options)

            # 收集要处理的文件
            files_to_process = []
            for pattern in file_patterns:
                for file_path in self.workspace_path.rglob(pattern):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(self.workspace_path)
                        if (self.security_manager.validate_path(str(relative_path)) and
                            self.security_manager.check_file_type(str(relative_path))):
                            files_to_process.append(file_path)

            if not files_to_process:
                return {
                    "success": True,
                    "message": "No files found matching the patterns",
                    "processed_files": [],
                    "failed_files": []
                }

            processed_files = []
            failed_files = []

            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(options["max_concurrent"])

            async def process_single_file(file_path: Path) -> None:
                async with semaphore:
                    try:
                        # 读取文件内容
                        encoding = await self.file_operations._detect_encoding(file_path)
                        if not encoding:
                            encoding = options["encoding"]

                        def read_file():
                            with open(file_path, 'r', encoding=encoding) as f:
                                return f.read()

                        original_content = await asyncio.get_event_loop().run_in_executor(
                            None, read_file
                        )

                        # 处理内容
                        processed_content = await asyncio.get_event_loop().run_in_executor(
                            None, content_processor, original_content
                        )

                        # 检查内容是否有变化
                        if original_content == processed_content:
                            processed_files.append({
                                "file": str(file_path.relative_to(self.workspace_path)),
                                "status": "no_changes",
                                "size": len(original_content)
                            })
                        else:
                            if not options["dry_run"]:
                                # 创建备份
                                if options["create_backup"]:
                                    backup_path = file_path.with_suffix(file_path.suffix + ".bak")
                                    await asyncio.get_event_loop().run_in_executor(
                                        None, shutil.copy2, file_path, backup_path
                                    )

                                # 写入处理后的内容
                                def write_file():
                                    with open(file_path, 'w', encoding=encoding) as f:
                                        f.write(processed_content)

                                await asyncio.get_event_loop().run_in_executor(
                                    None, write_file
                                )

                            processed_files.append({
                                "file": str(file_path.relative_to(self.workspace_path)),
                                "status": "modified" if not options["dry_run"] else "preview",
                                "original_size": len(original_content),
                                "new_size": len(processed_content),
                                "backup_created": options["create_backup"] and not options["dry_run"]
                            })

                        self.processing_stats["bytes_processed"] += len(original_content)
                        self.processing_stats["successful_operations"] += 1

                    except Exception as e:
                        logger.error(f"Failed to process file {file_path}: {e}")
                        failed_files.append({
                            "file": str(file_path.relative_to(self.workspace_path)),
                            "error": str(e)
                        })
                        self.processing_stats["failed_operations"] += 1

                    self.processing_stats["total_operations"] += 1

            # 并发执行处理操作
            tasks = [process_single_file(file_path) for file_path in files_to_process]
            await asyncio.gather(*tasks)

            self.processing_stats["end_time"] = datetime.now()

            return {
                "success": True,
                "processed_files": processed_files,
                "failed_files": failed_files,
                "dry_run": options["dry_run"],
                "stats": self.processing_stats.copy()
            }

        except Exception as e:
            logger.error(f"Batch content processing failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def batch_calculate_checksums(self, file_patterns: List[str],
                                       algorithm: str = "md5") -> Dict[str, Any]:
        """
        批量计算文件校验和

        Args:
            file_patterns (List[str]): 文件模式列表
            algorithm (str): 哈希算法 (md5, sha1, sha256)

        Returns:
            Dict[str, Any]: 校验和结果
        """
        try:
            self._reset_stats()
            self.processing_stats["start_time"] = datetime.now()

            # 验证算法
            if algorithm not in ['md5', 'sha1', 'sha256']:
                return {
                    "success": False,
                    "error": f"Unsupported algorithm: {algorithm}"
                }

            # 收集要处理的文件
            files_to_process = []
            for pattern in file_patterns:
                for file_path in self.workspace_path.rglob(pattern):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(self.workspace_path)
                        if self.security_manager.validate_path(str(relative_path)):
                            files_to_process.append(file_path)

            if not files_to_process:
                return {
                    "success": True,
                    "message": "No files found matching the patterns",
                    "checksums": []
                }

            checksums = []
            failed_files = []

            # 使用信号量控制并发
            semaphore = asyncio.Semaphore(self.max_workers)

            async def calculate_checksum(file_path: Path) -> None:
                async with semaphore:
                    try:
                        # 计算文件哈希
                        hash_obj = getattr(hashlib, algorithm)()

                        def _calculate_hash():
                            with open(file_path, 'rb') as f:
                                for chunk in iter(lambda: f.read(4096), b""):
                                    hash_obj.update(chunk)
                            return hash_obj.hexdigest()

                        checksum = await asyncio.get_event_loop().run_in_executor(
                            None, _calculate_hash
                        )

                        file_size = file_path.stat().st_size

                        checksums.append({
                            "file": str(file_path.relative_to(self.workspace_path)),
                            "checksum": checksum,
                            "algorithm": algorithm,
                            "size": file_size
                        })

                        self.processing_stats["bytes_processed"] += file_size
                        self.processing_stats["successful_operations"] += 1

                    except Exception as e:
                        logger.error(f"Failed to calculate checksum for {file_path}: {e}")
                        failed_files.append({
                            "file": str(file_path.relative_to(self.workspace_path)),
                            "error": str(e)
                        })
                        self.processing_stats["failed_operations"] += 1

                    self.processing_stats["total_operations"] += 1

            # 并发执行校验和计算
            tasks = [calculate_checksum(file_path) for file_path in files_to_process]
            await asyncio.gather(*tasks)

            self.processing_stats["end_time"] = datetime.now()

            return {
                "success": True,
                "checksums": checksums,
                "failed_files": failed_files,
                "algorithm": algorithm,
                "stats": self.processing_stats.copy()
            }

        except Exception as e:
            logger.error(f"Batch checksum calculation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        stats = self.processing_stats.copy()
        if stats["start_time"] and stats["end_time"]:
            duration = (stats["end_time"] - stats["start_time"]).total_seconds()
            stats["duration_seconds"] = duration
            if duration > 0:
                stats["operations_per_second"] = stats["total_operations"] / duration
                stats["bytes_per_second"] = stats["bytes_processed"] / duration
        return stats
