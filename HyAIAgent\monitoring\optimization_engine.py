"""
优化引擎

基于使用数据和性能指标提供自动化优化建议和系统调优功能。
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import json
import logging

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class OptimizationRule:
    """优化规则数据模型"""
    rule_id: str
    rule_name: str
    category: str  # performance, resource, usage, security
    condition: str  # 触发条件描述
    action: str  # 优化动作描述
    priority: int  # 优先级 1-10
    enabled: bool = True
    auto_apply: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


@dataclass
class OptimizationSuggestion:
    """优化建议数据模型"""
    suggestion_id: str
    rule_id: str
    category: str
    title: str
    description: str
    impact_level: str  # low, medium, high, critical
    estimated_improvement: str
    implementation_effort: str  # easy, medium, hard
    timestamp: datetime
    applied: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class OptimizationResult:
    """优化结果数据模型"""
    result_id: str
    suggestion_id: str
    applied_at: datetime
    success: bool
    before_metrics: Dict[str, Any]
    after_metrics: Dict[str, Any]
    improvement_summary: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['applied_at'] = self.applied_at.isoformat()
        return data


class OptimizationEngine:
    """
    优化引擎
    
    基于使用数据和性能指标提供自动化优化建议和系统调优功能。
    支持规则引擎、智能分析、自动优化和效果评估。
    """
    
    def __init__(self, 
                 config_manager=None,
                 kv_store=None,
                 performance_monitor=None,
                 usage_tracker=None,
                 ai_client=None):
        """
        初始化优化引擎
        
        Args:
            config_manager: 配置管理器
            kv_store: 键值存储
            performance_monitor: 性能监控器
            usage_tracker: 使用跟踪器
            ai_client: AI客户端
        """
        self.config_manager = config_manager
        self.kv_store = kv_store
        self.performance_monitor = performance_monitor
        self.usage_tracker = usage_tracker
        self.ai_client = ai_client
        
        # 优化数据存储
        self.optimization_rules: Dict[str, OptimizationRule] = {}
        self.suggestions_history: List[OptimizationSuggestion] = []
        self.optimization_results: List[OptimizationResult] = []
        
        # 优化回调函数
        self.optimization_callbacks: List[Callable] = []
        
        # 统计信息
        self.stats = {
            'total_suggestions': 0,
            'applied_optimizations': 0,
            'successful_optimizations': 0,
            'avg_improvement_rate': 0.0,
            'last_analysis_time': None,
            'optimization_categories': defaultdict(int)
        }
        
        # 初始化内置优化规则
        self._initialize_optimization_rules()
        
        logger.info("优化引擎初始化完成")
    
    def _initialize_optimization_rules(self) -> None:
        """初始化内置优化规则"""
        rules = [
            OptimizationRule(
                rule_id="high_cpu_usage",
                rule_name="高CPU使用率优化",
                category="performance",
                condition="CPU使用率持续超过80%",
                action="建议优化CPU密集型操作或增加资源",
                priority=8,
                auto_apply=False
            ),
            OptimizationRule(
                rule_id="high_memory_usage",
                rule_name="高内存使用率优化",
                category="resource",
                condition="内存使用率超过85%",
                action="建议清理内存或优化内存使用",
                priority=9,
                auto_apply=False
            ),
            OptimizationRule(
                rule_id="slow_response_time",
                rule_name="响应时间优化",
                category="performance",
                condition="平均响应时间超过2秒",
                action="建议优化算法或增加缓存",
                priority=7,
                auto_apply=False
            ),
            OptimizationRule(
                rule_id="unused_features",
                rule_name="未使用功能清理",
                category="usage",
                condition="功能7天内无使用记录",
                action="建议禁用或移除未使用功能",
                priority=3,
                auto_apply=False
            ),
            OptimizationRule(
                rule_id="frequent_errors",
                rule_name="频繁错误优化",
                category="performance",
                condition="错误率超过5%",
                action="建议修复错误或改进错误处理",
                priority=8,
                auto_apply=False
            ),
            OptimizationRule(
                rule_id="resource_leak",
                rule_name="资源泄漏检测",
                category="resource",
                condition="资源使用持续增长",
                action="建议检查资源泄漏并修复",
                priority=9,
                auto_apply=False
            )
        ]
        
        for rule in rules:
            self.optimization_rules[rule.rule_id] = rule
    
    async def analyze_and_suggest(self) -> List[OptimizationSuggestion]:
        """
        分析系统状态并生成优化建议
        
        Returns:
            List[OptimizationSuggestion]: 优化建议列表
        """
        try:
            suggestions = []
            
            # 获取性能数据
            performance_stats = None
            if self.performance_monitor:
                performance_stats = self.performance_monitor.get_performance_stats()
            
            # 获取使用数据
            usage_stats = None
            if self.usage_tracker:
                usage_stats = self.usage_tracker.get_usage_stats()
            
            # 应用优化规则
            for rule in self.optimization_rules.values():
                if not rule.enabled:
                    continue
                
                suggestion = await self._apply_optimization_rule(
                    rule, performance_stats, usage_stats
                )
                if suggestion:
                    suggestions.append(suggestion)
            
            # AI智能分析
            if self.ai_client and (performance_stats or usage_stats):
                ai_suggestions = await self._ai_optimization_analysis(
                    performance_stats, usage_stats
                )
                suggestions.extend(ai_suggestions)
            
            # 保存建议
            self.suggestions_history.extend(suggestions)
            self.stats['total_suggestions'] += len(suggestions)
            self.stats['last_analysis_time'] = datetime.now()
            
            # 更新分类统计
            for suggestion in suggestions:
                self.stats['optimization_categories'][suggestion.category] += 1
            
            logger.info(f"优化分析完成，生成{len(suggestions)}条建议")
            return suggestions
            
        except Exception as e:
            logger.error(f"优化分析失败: {e}")
            return []
    
    async def _apply_optimization_rule(self, rule: OptimizationRule,
                                     performance_stats: Optional[Dict[str, Any]],
                                     usage_stats: Optional[Dict[str, Any]]) -> Optional[OptimizationSuggestion]:
        """应用优化规则"""
        try:
            suggestion = None
            
            if rule.rule_id == "high_cpu_usage" and performance_stats:
                if performance_stats.get('avg_cpu_usage', 0) > 80:
                    suggestion = self._create_suggestion(
                        rule, "CPU使用率过高",
                        f"当前平均CPU使用率为{performance_stats['avg_cpu_usage']:.1f}%，建议优化CPU密集型操作",
                        "high", "显著提升系统响应速度", "medium"
                    )
            
            elif rule.rule_id == "high_memory_usage" and performance_stats:
                if performance_stats.get('avg_memory_usage', 0) > 85:
                    suggestion = self._create_suggestion(
                        rule, "内存使用率过高",
                        f"当前平均内存使用率为{performance_stats['avg_memory_usage']:.1f}%，建议优化内存使用",
                        "critical", "防止系统崩溃", "medium"
                    )
            
            elif rule.rule_id == "slow_response_time" and performance_stats:
                if performance_stats.get('avg_response_time', 0) > 2.0:
                    suggestion = self._create_suggestion(
                        rule, "响应时间过长",
                        f"当前平均响应时间为{performance_stats['avg_response_time']:.2f}秒，建议优化性能",
                        "high", "提升用户体验", "hard"
                    )
            
            elif rule.rule_id == "unused_features" and usage_stats:
                # 检查未使用功能
                if self.usage_tracker:
                    feature_usage = self.usage_tracker.get_feature_usage()
                    cutoff_time = datetime.now() - timedelta(days=7)
                    
                    unused_features = []
                    for feature_name, stats in feature_usage.items():
                        last_used_str = stats.get('last_used')
                        if last_used_str:
                            last_used = datetime.fromisoformat(last_used_str)
                            if last_used < cutoff_time:
                                unused_features.append(feature_name)
                    
                    if unused_features:
                        suggestion = self._create_suggestion(
                            rule, "发现未使用功能",
                            f"以下功能7天内未使用：{', '.join(unused_features[:5])}",
                            "low", "减少系统复杂度", "easy"
                        )
            
            elif rule.rule_id == "frequent_errors" and performance_stats:
                # 检查错误率
                total_events = performance_stats.get('total_samples', 0)
                alert_count = performance_stats.get('alert_count', 0)
                if total_events > 0:
                    error_rate = (alert_count / total_events) * 100
                    if error_rate > 5:
                        suggestion = self._create_suggestion(
                            rule, "错误率过高",
                            f"当前错误率为{error_rate:.1f}%，建议修复错误处理",
                            "high", "提升系统稳定性", "medium"
                        )
            
            return suggestion
            
        except Exception as e:
            logger.error(f"应用优化规则失败 {rule.rule_id}: {e}")
            return None
    
    def _create_suggestion(self, rule: OptimizationRule, title: str, description: str,
                          impact_level: str, estimated_improvement: str,
                          implementation_effort: str) -> OptimizationSuggestion:
        """创建优化建议"""
        suggestion_id = f"suggestion_{int(time.time() * 1000)}"
        
        return OptimizationSuggestion(
            suggestion_id=suggestion_id,
            rule_id=rule.rule_id,
            category=rule.category,
            title=title,
            description=description,
            impact_level=impact_level,
            estimated_improvement=estimated_improvement,
            implementation_effort=implementation_effort,
            timestamp=datetime.now()
        )

    async def _ai_optimization_analysis(self, performance_stats: Optional[Dict[str, Any]],
                                       usage_stats: Optional[Dict[str, Any]]) -> List[OptimizationSuggestion]:
        """AI智能优化分析"""
        try:
            if not self.ai_client:
                return []

            # 构建分析提示
            prompt = f"""
            请基于以下系统数据提供优化建议：

            性能统计：
            {json.dumps(performance_stats, ensure_ascii=False, indent=2) if performance_stats else "无数据"}

            使用统计：
            {json.dumps(usage_stats, ensure_ascii=False, indent=2) if usage_stats else "无数据"}

            请提供3-5条具体的优化建议，每条建议包含：
            1. 标题
            2. 详细描述
            3. 影响级别 (low/medium/high/critical)
            4. 预期改进效果
            5. 实施难度 (easy/medium/hard)

            请以JSON格式返回建议列表。
            """

            response = await self.ai_client.chat(prompt)

            # 解析AI响应
            try:
                ai_suggestions_data = json.loads(response)
                suggestions = []

                for i, data in enumerate(ai_suggestions_data):
                    suggestion_id = f"ai_suggestion_{int(time.time() * 1000)}_{i}"
                    suggestion = OptimizationSuggestion(
                        suggestion_id=suggestion_id,
                        rule_id="ai_analysis",
                        category="ai_generated",
                        title=data.get('title', ''),
                        description=data.get('description', ''),
                        impact_level=data.get('impact_level', 'medium'),
                        estimated_improvement=data.get('estimated_improvement', ''),
                        implementation_effort=data.get('implementation_effort', 'medium'),
                        timestamp=datetime.now()
                    )
                    suggestions.append(suggestion)

                return suggestions

            except json.JSONDecodeError:
                logger.warning("AI响应格式不正确，无法解析优化建议")
                return []

        except Exception as e:
            logger.error(f"AI优化分析失败: {e}")
            return []

    async def apply_optimization(self, suggestion_id: str,
                               custom_implementation: Optional[Callable] = None) -> OptimizationResult:
        """
        应用优化建议

        Args:
            suggestion_id: 建议ID
            custom_implementation: 自定义实现函数

        Returns:
            OptimizationResult: 优化结果
        """
        try:
            # 查找建议
            suggestion = None
            for s in self.suggestions_history:
                if s.suggestion_id == suggestion_id:
                    suggestion = s
                    break

            if not suggestion:
                raise ValueError(f"未找到优化建议: {suggestion_id}")

            # 记录优化前的指标
            before_metrics = await self._collect_current_metrics()

            # 应用优化
            success = False
            if custom_implementation:
                success = await custom_implementation(suggestion)
            else:
                success = await self._apply_default_optimization(suggestion)

            # 等待一段时间让优化生效
            await asyncio.sleep(5)

            # 记录优化后的指标
            after_metrics = await self._collect_current_metrics()

            # 生成改进摘要
            improvement_summary = self._generate_improvement_summary(
                before_metrics, after_metrics, suggestion
            )

            # 创建优化结果
            result_id = f"result_{int(time.time() * 1000)}"
            result = OptimizationResult(
                result_id=result_id,
                suggestion_id=suggestion_id,
                applied_at=datetime.now(),
                success=success,
                before_metrics=before_metrics,
                after_metrics=after_metrics,
                improvement_summary=improvement_summary
            )

            # 保存结果
            self.optimization_results.append(result)
            suggestion.applied = True

            # 更新统计
            self.stats['applied_optimizations'] += 1
            if success:
                self.stats['successful_optimizations'] += 1

            # 调用回调函数
            for callback in self.optimization_callbacks:
                try:
                    await callback(result)
                except Exception as e:
                    logger.error(f"优化回调执行失败: {e}")

            logger.info(f"优化应用完成: {suggestion_id} (成功: {success})")
            return result

        except Exception as e:
            logger.error(f"应用优化失败: {e}")
            # 返回失败结果
            return OptimizationResult(
                result_id=f"failed_{int(time.time() * 1000)}",
                suggestion_id=suggestion_id,
                applied_at=datetime.now(),
                success=False,
                before_metrics={},
                after_metrics={},
                improvement_summary=f"优化应用失败: {str(e)}"
            )

    async def _collect_current_metrics(self) -> Dict[str, Any]:
        """收集当前系统指标"""
        metrics = {}

        try:
            if self.performance_monitor:
                perf_stats = self.performance_monitor.get_performance_stats()
                metrics['performance'] = perf_stats

            if self.usage_tracker:
                usage_stats = self.usage_tracker.get_usage_stats()
                metrics['usage'] = usage_stats

            metrics['timestamp'] = datetime.now().isoformat()

        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")

        return metrics

    async def _apply_default_optimization(self, suggestion: OptimizationSuggestion) -> bool:
        """应用默认优化实现"""
        try:
            # 这里实现一些基础的自动优化
            if suggestion.rule_id == "unused_features":
                # 记录未使用功能（实际实现中可能需要禁用功能）
                logger.info(f"标记未使用功能: {suggestion.description}")
                return True

            elif suggestion.rule_id == "high_memory_usage":
                # 触发垃圾回收（Python示例）
                import gc
                gc.collect()
                logger.info("执行内存清理")
                return True

            else:
                # 其他优化需要自定义实现
                logger.info(f"优化建议需要手动实现: {suggestion.title}")
                return False

        except Exception as e:
            logger.error(f"默认优化实现失败: {e}")
            return False

    def _generate_improvement_summary(self, before_metrics: Dict[str, Any],
                                    after_metrics: Dict[str, Any],
                                    suggestion: OptimizationSuggestion) -> str:
        """生成改进摘要"""
        try:
            summary_parts = []

            # 比较性能指标
            if 'performance' in before_metrics and 'performance' in after_metrics:
                before_perf = before_metrics['performance']
                after_perf = after_metrics['performance']

                # CPU使用率比较
                if 'avg_cpu_usage' in before_perf and 'avg_cpu_usage' in after_perf:
                    cpu_before = before_perf['avg_cpu_usage']
                    cpu_after = after_perf['avg_cpu_usage']
                    cpu_change = cpu_after - cpu_before
                    if abs(cpu_change) > 1:
                        summary_parts.append(f"CPU使用率变化: {cpu_change:+.1f}%")

                # 内存使用率比较
                if 'avg_memory_usage' in before_perf and 'avg_memory_usage' in after_perf:
                    mem_before = before_perf['avg_memory_usage']
                    mem_after = after_perf['avg_memory_usage']
                    mem_change = mem_after - mem_before
                    if abs(mem_change) > 1:
                        summary_parts.append(f"内存使用率变化: {mem_change:+.1f}%")

                # 响应时间比较
                if 'avg_response_time' in before_perf and 'avg_response_time' in after_perf:
                    resp_before = before_perf['avg_response_time']
                    resp_after = after_perf['avg_response_time']
                    resp_change = resp_after - resp_before
                    if abs(resp_change) > 0.1:
                        summary_parts.append(f"响应时间变化: {resp_change:+.2f}秒")

            if summary_parts:
                return f"优化效果: {'; '.join(summary_parts)}"
            else:
                return f"已应用优化建议: {suggestion.title}"

        except Exception as e:
            logger.error(f"生成改进摘要失败: {e}")
            return f"优化已应用，但无法生成详细摘要"

    def get_optimization_suggestions(self, category: Optional[str] = None,
                                   impact_level: Optional[str] = None,
                                   applied: Optional[bool] = None) -> List[Dict[str, Any]]:
        """
        获取优化建议列表

        Args:
            category: 类别过滤
            impact_level: 影响级别过滤
            applied: 是否已应用过滤

        Returns:
            List[Dict[str, Any]]: 优化建议列表
        """
        suggestions = self.suggestions_history

        # 应用过滤条件
        if category:
            suggestions = [s for s in suggestions if s.category == category]
        if impact_level:
            suggestions = [s for s in suggestions if s.impact_level == impact_level]
        if applied is not None:
            suggestions = [s for s in suggestions if s.applied == applied]

        return [suggestion.to_dict() for suggestion in suggestions]

    def get_optimization_results(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取优化结果列表

        Args:
            limit: 限制返回数量

        Returns:
            List[Dict[str, Any]]: 优化结果列表
        """
        results = self.optimization_results
        if limit:
            results = results[-limit:]

        return [result.to_dict() for result in results]

    def get_optimization_stats(self) -> Dict[str, Any]:
        """
        获取优化统计信息

        Returns:
            Dict[str, Any]: 优化统计信息
        """
        # 计算成功率
        success_rate = 0.0
        if self.stats['applied_optimizations'] > 0:
            success_rate = (self.stats['successful_optimizations'] /
                          self.stats['applied_optimizations']) * 100

        # 计算平均改进率
        improvement_rates = []
        for result in self.optimization_results:
            if result.success and 'performance' in result.before_metrics and 'performance' in result.after_metrics:
                # 简单的改进率计算示例
                before_cpu = result.before_metrics['performance'].get('avg_cpu_usage', 0)
                after_cpu = result.after_metrics['performance'].get('avg_cpu_usage', 0)
                if before_cpu > 0:
                    improvement = (before_cpu - after_cpu) / before_cpu * 100
                    improvement_rates.append(improvement)

        avg_improvement = sum(improvement_rates) / len(improvement_rates) if improvement_rates else 0.0

        return {
            **self.stats,
            'success_rate': success_rate,
            'avg_improvement_rate': avg_improvement,
            'pending_suggestions': len([s for s in self.suggestions_history if not s.applied]),
            'rules_count': len(self.optimization_rules),
            'enabled_rules_count': len([r for r in self.optimization_rules.values() if r.enabled])
        }

    def add_optimization_rule(self, rule: OptimizationRule) -> bool:
        """
        添加优化规则

        Args:
            rule: 优化规则

        Returns:
            bool: 添加是否成功
        """
        try:
            self.optimization_rules[rule.rule_id] = rule
            logger.info(f"优化规则已添加: {rule.rule_id}")
            return True
        except Exception as e:
            logger.error(f"添加优化规则失败: {e}")
            return False

    def remove_optimization_rule(self, rule_id: str) -> bool:
        """
        移除优化规则

        Args:
            rule_id: 规则ID

        Returns:
            bool: 移除是否成功
        """
        try:
            if rule_id in self.optimization_rules:
                del self.optimization_rules[rule_id]
                logger.info(f"优化规则已移除: {rule_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"移除优化规则失败: {e}")
            return False

    def enable_rule(self, rule_id: str) -> bool:
        """
        启用优化规则

        Args:
            rule_id: 规则ID

        Returns:
            bool: 启用是否成功
        """
        try:
            if rule_id in self.optimization_rules:
                self.optimization_rules[rule_id].enabled = True
                logger.info(f"优化规则已启用: {rule_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"启用优化规则失败: {e}")
            return False

    def disable_rule(self, rule_id: str) -> bool:
        """
        禁用优化规则

        Args:
            rule_id: 规则ID

        Returns:
            bool: 禁用是否成功
        """
        try:
            if rule_id in self.optimization_rules:
                self.optimization_rules[rule_id].enabled = False
                logger.info(f"优化规则已禁用: {rule_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"禁用优化规则失败: {e}")
            return False

    def get_optimization_rules(self) -> List[Dict[str, Any]]:
        """
        获取所有优化规则

        Returns:
            List[Dict[str, Any]]: 优化规则列表
        """
        return [rule.to_dict() for rule in self.optimization_rules.values()]

    def add_optimization_callback(self, callback: Callable) -> None:
        """
        添加优化回调函数

        Args:
            callback: 回调函数
        """
        self.optimization_callbacks.append(callback)

    def remove_optimization_callback(self, callback: Callable) -> bool:
        """
        移除优化回调函数

        Args:
            callback: 要移除的回调函数

        Returns:
            bool: 是否成功移除
        """
        try:
            self.optimization_callbacks.remove(callback)
            return True
        except ValueError:
            return False

    async def schedule_optimization_analysis(self, interval_hours: int = 24) -> None:
        """
        定期优化分析

        Args:
            interval_hours: 分析间隔（小时）
        """
        try:
            while True:
                await self.analyze_and_suggest()
                await asyncio.sleep(interval_hours * 3600)
        except asyncio.CancelledError:
            logger.info("定期优化分析已停止")
        except Exception as e:
            logger.error(f"定期优化分析失败: {e}")

    def clear_suggestions_history(self, older_than_hours: Optional[int] = None) -> int:
        """
        清理建议历史

        Args:
            older_than_hours: 清理多少小时前的建议，None表示清理全部

        Returns:
            int: 清理的建议数量
        """
        try:
            if older_than_hours is None:
                count = len(self.suggestions_history)
                self.suggestions_history.clear()
                logger.info(f"已清理所有优化建议: {count}条")
                return count

            cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
            original_count = len(self.suggestions_history)

            self.suggestions_history = [
                s for s in self.suggestions_history
                if s.timestamp > cutoff_time
            ]

            cleared_count = original_count - len(self.suggestions_history)
            logger.info(f"已清理{older_than_hours}小时前的优化建议: {cleared_count}条")
            return cleared_count

        except Exception as e:
            logger.error(f"清理建议历史失败: {e}")
            return 0

    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.suggestions_history.clear()
            self.optimization_results.clear()
            self.optimization_callbacks.clear()
            self.stats.clear()
            logger.info("优化引擎资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")
