"""
HyAIAgent 第四阶段 - 基础搜索操作测试
步骤4.4测试代码
"""

import asyncio
import pytest
import json
import os
import sys
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager
from core.kv_store import KVStore
from operations.search_operations import (
    SearchOperations, BasicSearchOperation, SearchToolkit,
    SearchResult, SearchResponse, TavilySearchClient
)


class TestBasicSearchOperation:
    """基础搜索操作测试类"""
    
    @pytest.fixture
    async def setup_test_environment(self):
        """设置测试环境"""
        # 创建模拟配置
        test_config = {
            "search": {
                "tavily": {
                    "api_key": "test_api_key",
                    "base_url": "https://api.tavily.com",
                    "timeout": 30
                },
                "cache": {
                    "enabled": True,
                    "ttl": 3600
                }
            }
        }
        
        # 创建模拟的配置管理器
        config_manager = Mock(spec=ConfigManager)
        config_manager.get.side_effect = lambda key, default=None: test_config.get(key, default)
        
        # 创建模拟的安全管理器
        security_manager = Mock(spec=SecurityManager)
        
        # 创建模拟的KV存储
        kv_store = Mock(spec=KVStore)
        kv_store.get.return_value = None
        kv_store.set.return_value = True
        
        return {
            "config_manager": config_manager,
            "security_manager": security_manager,
            "kv_store": kv_store,
            "test_config": test_config
        }
    
    @pytest.mark.asyncio
    async def test_basic_search_operation_init(self, setup_test_environment):
        """测试基础搜索操作初始化"""
        env = await setup_test_environment
        
        # 创建搜索操作管理器
        with patch('operations.search_operations.TavilySearchClient'):
            search_ops = SearchOperations(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            
            # 创建基础搜索操作
            basic_search = BasicSearchOperation(search_ops)
            
            assert basic_search.operation_type.value == "network"
            assert basic_search.name == "basic_search"
            assert basic_search.description == "执行基础网络搜索操作"
            assert basic_search.search_operations == search_ops
    
    @pytest.mark.asyncio
    async def test_validate_params_success(self, setup_test_environment):
        """测试参数验证成功情况"""
        env = await setup_test_environment
        
        with patch('operations.search_operations.TavilySearchClient'):
            search_ops = SearchOperations(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            basic_search = BasicSearchOperation(search_ops)
            
            # 测试有效参数
            valid_params = {
                "query": "Python编程",
                "search_depth": "basic",
                "max_results": 5,
                "use_cache": True
            }
            
            result = await basic_search.validate_params(valid_params)
            
            assert result["valid"] is True
            assert len(result["errors"]) == 0
    
    @pytest.mark.asyncio
    async def test_validate_params_missing_query(self, setup_test_environment):
        """测试缺少查询参数的验证"""
        env = await setup_test_environment
        
        with patch('operations.search_operations.TavilySearchClient'):
            search_ops = SearchOperations(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            basic_search = BasicSearchOperation(search_ops)
            
            # 测试缺少query参数
            invalid_params = {
                "search_depth": "basic",
                "max_results": 5
            }
            
            result = await basic_search.validate_params(invalid_params)
            
            assert result["valid"] is False
            assert "缺少必需参数: query" in result["errors"]
    
    @pytest.mark.asyncio
    async def test_validate_params_invalid_search_depth(self, setup_test_environment):
        """测试无效搜索深度参数的验证"""
        env = await setup_test_environment
        
        with patch('operations.search_operations.TavilySearchClient'):
            search_ops = SearchOperations(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            basic_search = BasicSearchOperation(search_ops)
            
            # 测试无效的search_depth
            invalid_params = {
                "query": "test query",
                "search_depth": "invalid_depth"
            }
            
            result = await basic_search.validate_params(invalid_params)
            
            assert result["valid"] is False
            assert any("search_depth必须是'basic'或'advanced'" in error for error in result["errors"])
    
    @pytest.mark.asyncio
    async def test_execute_search_success(self, setup_test_environment):
        """测试成功执行搜索操作"""
        env = await setup_test_environment
        
        # 创建模拟的搜索响应
        mock_search_result = SearchResult(
            title="测试标题",
            url="https://example.com",
            content="测试内容",
            score=0.9,
            published_date="2025-07-29"
        )
        
        mock_search_response = SearchResponse(
            query="Python编程",
            results=[mock_search_result],
            total_results=1,
            search_time=0.5,
            timestamp=datetime.now(),
            search_depth="basic"
        )
        
        with patch('operations.search_operations.TavilySearchClient'):
            search_ops = SearchOperations(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            
            # 模拟search_web方法
            search_ops.search_web = AsyncMock(return_value=mock_search_response)
            
            basic_search = BasicSearchOperation(search_ops)
            
            # 执行搜索
            result = await basic_search.execute(
                query="Python编程",
                search_depth="basic",
                max_results=5
            )
            
            assert result.success is True
            assert result.data is not None
            assert result.data["query"] == "Python编程"
            assert result.data["total_results"] == 1
            assert "search_response" in result.data
    
    @pytest.mark.asyncio
    async def test_execute_search_invalid_params(self, setup_test_environment):
        """测试执行搜索时参数无效的情况"""
        env = await setup_test_environment
        
        with patch('operations.search_operations.TavilySearchClient'):
            search_ops = SearchOperations(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            basic_search = BasicSearchOperation(search_ops)
            
            # 测试空查询参数
            result = await basic_search.execute(query="")
            
            assert result.success is False
            assert "搜索查询参数(query)不能为空" in result.error_message
    
    @pytest.mark.asyncio
    async def test_execute_search_exception(self, setup_test_environment):
        """测试搜索执行时发生异常的情况"""
        env = await setup_test_environment
        
        with patch('operations.search_operations.TavilySearchClient'):
            search_ops = SearchOperations(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            
            # 模拟search_web方法抛出异常
            search_ops.search_web = AsyncMock(side_effect=Exception("API连接失败"))
            
            basic_search = BasicSearchOperation(search_ops)

            result = await basic_search.execute(
                query="Python编程",
                search_depth="basic",
                max_results=5
            )
            
            assert result.success is False
            assert "基础搜索操作失败" in result.error_message
            assert "API连接失败" in result.error_message


class TestSearchToolkit:
    """搜索工具包测试类"""
    
    @pytest.fixture
    async def setup_toolkit(self):
        """设置搜索工具包测试环境"""
        test_config = {
            "search": {
                "tavily": {
                    "api_key": "test_api_key",
                    "base_url": "https://api.tavily.com",
                    "timeout": 30
                },
                "cache": {
                    "enabled": True,
                    "ttl": 3600
                }
            }
        }
        
        config_manager = Mock(spec=ConfigManager)
        config_manager.get.side_effect = lambda key, default=None: test_config.get(key, default)
        
        with patch('operations.search_operations.TavilySearchClient'):
            toolkit = SearchToolkit(config_manager)
            return toolkit
    
    @pytest.mark.asyncio
    async def test_quick_search(self, setup_toolkit):
        """测试快速搜索功能"""
        toolkit = await setup_toolkit
        
        # 模拟搜索结果
        mock_result = Mock()
        mock_result.success = True
        mock_result.data = {"query": "test", "total_results": 3}
        mock_result.error_message = None
        mock_result.metadata = {"execution_time": 0.5}
        
        toolkit.basic_search_op.execute = AsyncMock(return_value=mock_result)
        
        result = await toolkit.quick_search("test query")
        
        assert result["success"] is True
        assert result["data"]["query"] == "test"
        assert result["error"] is None
    
    @pytest.mark.asyncio
    async def test_batch_search(self, setup_toolkit):
        """测试批量搜索功能"""
        toolkit = await setup_toolkit
        
        # 模拟quick_search方法
        async def mock_quick_search(query, max_results):
            return {
                "success": True,
                "data": {"query": query, "total_results": 2},
                "error": None
            }
        
        toolkit.quick_search = mock_quick_search
        
        queries = ["Python", "JavaScript", "Java"]
        result = await toolkit.batch_search(queries, max_results_per_query=3)
        
        assert result["success"] is True
        assert result["total_queries"] == 3
        assert result["successful_count"] == 3
        assert result["failed_count"] == 0
        assert len(result["results"]) == 3


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
