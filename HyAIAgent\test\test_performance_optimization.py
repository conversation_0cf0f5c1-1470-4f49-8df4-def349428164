"""
性能优化模块测试

测试性能优化器和性能监控器的功能
"""

import asyncio
import sys
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from utils.performance_optimizer import PerformanceOptimizer, PerformanceMetrics, OptimizationSuggestion
from utils.performance_monitor import PerformanceMonitor, MonitoringConfig


class TestPerformanceOptimizer:
    """性能优化器测试类"""

    def __init__(self, temp_workspace):
        """初始化测试类"""
        self.temp_workspace = temp_workspace
        self.optimizer = PerformanceOptimizer(temp_workspace)

    async def test_collect_metrics(self):
        """测试性能指标收集"""
        print("测试性能指标收集...")

        # 收集性能指标
        metrics = await self.optimizer.collect_metrics()

        # 验证指标数据
        assert isinstance(metrics, PerformanceMetrics)
        assert isinstance(metrics.timestamp, datetime)
        assert 0 <= metrics.cpu_usage <= 100
        assert 0 <= metrics.memory_usage <= 100
        assert 0 <= metrics.disk_usage <= 100
        assert metrics.response_time >= 0
        assert metrics.active_tasks >= 0
        assert 0 <= metrics.cache_hit_rate <= 1
        assert 0 <= metrics.error_rate <= 1

        # 验证历史记录
        assert len(self.optimizer.metrics_history) == 1
        assert self.optimizer.metrics_history[0] == metrics

        print("✓ 性能指标收集正常")

    async def test_analyze_performance(self):
        """测试性能分析"""
        print("测试性能分析...")
        
        # 创建高负载的性能指标
        high_load_metrics = PerformanceMetrics(
            timestamp=datetime.now(),
            cpu_usage=95.0,
            memory_usage=90.0,
            disk_usage=85.0,
            response_time=6000.0,
            active_tasks=20,
            cache_hit_rate=0.3,
            error_rate=0.15
        )
        
        # 分析性能
        suggestions = await self.optimizer.analyze_performance(high_load_metrics)

        # 验证建议
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0

        # 检查建议类型
        suggestion_categories = [s.category for s in suggestions]
        assert "CPU" in suggestion_categories
        assert "Memory" in suggestion_categories
        assert "Performance" in suggestion_categories

        # 检查优先级
        high_priority_suggestions = [s for s in suggestions if s.priority == "high"]
        assert len(high_priority_suggestions) > 0

        print(f"✓ 性能分析正常，生成{len(suggestions)}个建议")

    async def test_apply_optimizations(self):
        """测试优化应用"""
        print("测试优化应用...")
        
        # 创建优化建议
        suggestions = [
            OptimizationSuggestion(
                category="Memory",
                priority="high",
                description="内存使用率过高",
                action="执行垃圾回收",
                expected_improvement="释放20-40%内存"
            ),
            OptimizationSuggestion(
                category="CPU",
                priority="medium",
                description="CPU使用率较高",
                action="减少并发任务",
                expected_improvement="降低CPU使用率10-20%"
            )
        ]
        
        # 应用优化
        result = await self.optimizer.apply_optimizations(suggestions)

        # 验证结果
        assert isinstance(result, dict)
        assert result['success'] is True
        assert 'applied_optimizations' in result
        assert 'skipped_optimizations' in result

        # 验证统计信息更新
        assert self.optimizer.stats['total_optimizations'] >= len(suggestions)

        print(f"✓ 优化应用正常，应用{len(result['applied_optimizations'])}个优化")

    async def test_optimize_system(self):
        """测试系统优化"""
        print("测试系统优化...")

        # 执行系统优化
        result = await self.optimizer.optimize_system()

        # 验证结果
        assert isinstance(result, dict)
        assert 'success' in result
        assert 'before_metrics' in result
        assert 'after_metrics' in result
        assert 'suggestions_count' in result
        assert 'applied_optimizations' in result

        if result['success']:
            assert 'performance_improvement' in result
            print(f"✓ 系统优化正常，性能改善: {result.get('performance_improvement', 0):.2%}")
        else:
            print(f"✓ 系统优化完成（无需优化）: {result.get('error', '')}")

    async def test_performance_report(self):
        """测试性能报告"""
        print("测试性能报告...")

        # 先收集一些性能数据
        for _ in range(3):
            await self.optimizer.collect_metrics()
            await asyncio.sleep(0.1)

        # 生成性能报告
        report = await self.optimizer.get_performance_report()

        # 验证报告
        assert isinstance(report, dict)
        assert report['success'] is True
        assert 'summary' in report
        assert 'trends' in report
        assert 'optimization_stats' in report
        assert 'health_status' in report
        assert 'recommendations' in report

        # 验证摘要数据
        summary = report['summary']
        assert 'avg_cpu_usage' in summary
        assert 'avg_memory_usage' in summary
        assert 'avg_response_time' in summary

        print(f"✓ 性能报告正常，健康状态: {report['health_status']}")


class TestPerformanceMonitor:
    """性能监控器测试类"""

    def __init__(self, temp_workspace):
        """初始化测试类"""
        self.temp_workspace = temp_workspace
        self.monitor_config = MonitoringConfig(
            collection_interval=0.1,  # 快速收集用于测试
            auto_optimization=True,
            export_enabled=False  # 测试时禁用导出
        )
        self.monitor = PerformanceMonitor(temp_workspace, self.monitor_config)
    
    async def test_start_stop_monitoring(self):
        """测试监控启动和停止"""
        print("测试监控启动和停止...")

        # 初始状态
        assert not self.monitor.is_monitoring

        # 启动监控
        success = await self.monitor.start_monitoring()
        assert success is True
        assert self.monitor.is_monitoring is True

        # 等待一段时间收集数据
        await asyncio.sleep(0.5)

        # 检查数据收集
        assert len(self.monitor.metrics_history) > 0
        assert self.monitor.current_metrics is not None

        # 停止监控
        success = await self.monitor.stop_monitoring()
        assert success is True
        assert self.monitor.is_monitoring is False

        print("✓ 监控启动和停止正常")
    
    async def test_alert_system(self):
        """测试告警系统"""
        print("测试告警系统...")

        # 设置低阈值以触发告警
        self.monitor.config.alert_thresholds = {
            'cpu_usage': 1.0,  # 很低的阈值
            'memory_usage': 1.0,
            'response_time': 0.1,
            'error_rate': 0.001
        }

        # 添加告警回调
        alerts_received = []

        async def alert_callback(alert):
            alerts_received.append(alert)

        self.monitor.add_alert_callback(alert_callback)

        # 启动监控
        await self.monitor.start_monitoring()

        # 等待收集数据和触发告警
        await asyncio.sleep(0.5)

        # 停止监控
        await self.monitor.stop_monitoring()

        # 验证告警
        assert len(alerts_received) > 0
        assert len(self.monitor.alerts_history) > 0

        # 检查告警内容
        alert = alerts_received[0]
        assert 'type' in alert
        assert 'level' in alert
        assert 'message' in alert
        assert 'timestamp' in alert

        print(f"✓ 告警系统正常，收到{len(alerts_received)}个告警")


async def run_performance_tests():
    """运行性能优化测试"""
    print("🚀 开始性能优化模块测试")
    print("=" * 60)
    
    # 创建临时工作空间
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 测试性能优化器
        print("\n🔧 测试性能优化器")
        print("-" * 40)

        optimizer_tests = TestPerformanceOptimizer(temp_dir)

        await optimizer_tests.test_collect_metrics()
        await optimizer_tests.test_analyze_performance()
        await optimizer_tests.test_apply_optimizations()
        await optimizer_tests.test_optimize_system()
        await optimizer_tests.test_performance_report()

        # 测试性能监控器
        print("\n📊 测试性能监控器")
        print("-" * 40)

        monitor_tests = TestPerformanceMonitor(temp_dir)

        await monitor_tests.test_start_stop_monitoring()
        await monitor_tests.test_alert_system()
        # 简化测试，只测试核心功能
        print("✓ 监控器核心功能测试完成")
        
        print("\n🎉 所有性能优化测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 性能优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(run_performance_tests())
    exit(0 if result else 1)
