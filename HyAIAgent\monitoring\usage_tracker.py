"""
使用跟踪器

提供用户行为跟踪、功能使用统计、性能瓶颈识别和使用模式分析功能。
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter, deque
import json
import logging

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class UsageEvent:
    """使用事件数据模型"""
    event_id: str
    user_id: str
    session_id: str
    event_type: str  # action, view, error, performance
    feature_name: str
    timestamp: datetime
    duration: Optional[float] = None
    metadata: Dict[str, Any] = None
    success: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        if self.metadata is None:
            data['metadata'] = {}
        return data


@dataclass
class UserSession:
    """用户会话数据模型"""
    session_id: str
    user_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_events: int = 0
    features_used: Set[str] = None
    total_duration: float = 0.0
    
    def __post_init__(self):
        if self.features_used is None:
            self.features_used = set()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['start_time'] = self.start_time.isoformat()
        data['end_time'] = self.end_time.isoformat() if self.end_time else None
        data['features_used'] = list(self.features_used)
        return data


@dataclass
class FeatureUsage:
    """功能使用统计数据模型"""
    feature_name: str
    total_uses: int
    unique_users: int
    avg_duration: float
    success_rate: float
    peak_usage_time: str
    last_used: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['last_used'] = self.last_used.isoformat()
        return data


class UsageTracker:
    """
    使用跟踪器
    
    提供用户行为跟踪、功能使用统计、性能瓶颈识别和使用模式分析功能。
    支持实时跟踪、会话管理、使用分析和性能监控。
    """
    
    def __init__(self, 
                 config_manager=None,
                 kv_store=None,
                 max_events_history: int = 10000,
                 session_timeout: int = 1800):  # 30分钟会话超时
        """
        初始化使用跟踪器
        
        Args:
            config_manager: 配置管理器
            kv_store: 键值存储
            max_events_history: 最大事件历史记录数
            session_timeout: 会话超时时间（秒）
        """
        self.config_manager = config_manager
        self.kv_store = kv_store
        self.max_events_history = max_events_history
        self.session_timeout = session_timeout
        
        # 数据存储
        self.events_history: deque = deque(maxlen=max_events_history)
        self.active_sessions: Dict[str, UserSession] = {}
        self.feature_stats: Dict[str, Dict[str, Any]] = defaultdict(lambda: {
            'total_uses': 0,
            'unique_users': set(),
            'durations': [],
            'success_count': 0,
            'error_count': 0,
            'hourly_usage': defaultdict(int),
            'first_used': None,
            'last_used': None
        })
        
        # 性能跟踪
        self.performance_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # 统计信息
        self.stats = {
            'total_events': 0,
            'total_sessions': 0,
            'active_sessions_count': 0,
            'total_users': set(),
            'most_used_features': [],
            'avg_session_duration': 0.0,
            'peak_concurrent_sessions': 0,
            'system_start_time': datetime.now()
        }
        
        logger.info("使用跟踪器初始化完成")
    
    async def track_event(self, user_id: str, session_id: str, event_type: str,
                         feature_name: str, duration: Optional[float] = None,
                         metadata: Dict[str, Any] = None, success: bool = True) -> str:
        """
        跟踪使用事件
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            event_type: 事件类型
            feature_name: 功能名称
            duration: 持续时间（秒）
            metadata: 元数据
            success: 是否成功
            
        Returns:
            str: 事件ID
        """
        try:
            # 创建事件
            event_id = f"event_{int(time.time() * 1000)}"
            event = UsageEvent(
                event_id=event_id,
                user_id=user_id,
                session_id=session_id,
                event_type=event_type,
                feature_name=feature_name,
                timestamp=datetime.now(),
                duration=duration,
                metadata=metadata or {},
                success=success
            )
            
            # 添加到历史记录
            self.events_history.append(event)
            
            # 更新会话信息
            await self._update_session(event)
            
            # 更新功能统计
            self._update_feature_stats(event)
            
            # 更新性能数据
            if duration is not None:
                self.performance_data[feature_name].append({
                    'timestamp': event.timestamp,
                    'duration': duration,
                    'success': success
                })
            
            # 更新总体统计
            self._update_global_stats(event)
            
            # 持久化（如果配置了存储）
            if self.kv_store:
                await self._persist_event(event)
            
            logger.debug(f"事件已跟踪: {event_id}")
            return event_id
            
        except Exception as e:
            logger.error(f"跟踪事件失败: {e}")
            return ""
    
    async def start_session(self, user_id: str, session_id: str) -> bool:
        """
        开始用户会话
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            
        Returns:
            bool: 开始是否成功
        """
        try:
            session = UserSession(
                session_id=session_id,
                user_id=user_id,
                start_time=datetime.now()
            )
            
            self.active_sessions[session_id] = session
            self.stats['total_sessions'] += 1
            self.stats['active_sessions_count'] = len(self.active_sessions)
            self.stats['total_users'].add(user_id)
            
            # 更新峰值并发会话数
            if len(self.active_sessions) > self.stats['peak_concurrent_sessions']:
                self.stats['peak_concurrent_sessions'] = len(self.active_sessions)
            
            logger.info(f"会话已开始: {session_id} (用户: {user_id})")
            return True
            
        except Exception as e:
            logger.error(f"开始会话失败: {e}")
            return False
    
    async def end_session(self, session_id: str) -> bool:
        """
        结束用户会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            bool: 结束是否成功
        """
        try:
            if session_id in self.active_sessions:
                session = self.active_sessions[session_id]
                session.end_time = datetime.now()
                session.total_duration = (session.end_time - session.start_time).total_seconds()
                
                # 更新平均会话时长
                total_sessions = self.stats['total_sessions']
                if total_sessions > 0:
                    current_avg = self.stats['avg_session_duration']
                    self.stats['avg_session_duration'] = (
                        (current_avg * (total_sessions - 1) + session.total_duration) / total_sessions
                    )
                
                # 持久化会话数据
                if self.kv_store:
                    await self._persist_session(session)
                
                # 从活跃会话中移除
                del self.active_sessions[session_id]
                self.stats['active_sessions_count'] = len(self.active_sessions)
                
                logger.info(f"会话已结束: {session_id} (时长: {session.total_duration:.1f}秒)")
                return True
            
            logger.warning(f"会话不存在: {session_id}")
            return False
            
        except Exception as e:
            logger.error(f"结束会话失败: {e}")
            return False
    
    async def _update_session(self, event: UsageEvent) -> None:
        """更新会话信息"""
        session_id = event.session_id
        
        if session_id not in self.active_sessions:
            # 自动创建会话
            await self.start_session(event.user_id, session_id)
        
        session = self.active_sessions[session_id]
        session.total_events += 1
        session.features_used.add(event.feature_name)
    
    def _update_feature_stats(self, event: UsageEvent) -> None:
        """更新功能统计"""
        feature_name = event.feature_name
        stats = self.feature_stats[feature_name]
        
        stats['total_uses'] += 1
        stats['unique_users'].add(event.user_id)
        
        if event.duration is not None:
            stats['durations'].append(event.duration)
        
        if event.success:
            stats['success_count'] += 1
        else:
            stats['error_count'] += 1
        
        # 按小时统计使用量
        hour_key = event.timestamp.strftime('%H')
        stats['hourly_usage'][hour_key] += 1
        
        # 更新首次和最后使用时间
        if stats['first_used'] is None:
            stats['first_used'] = event.timestamp
        stats['last_used'] = event.timestamp
    
    def _update_global_stats(self, event: UsageEvent) -> None:
        """更新全局统计"""
        self.stats['total_events'] += 1
        
        # 更新最常用功能
        feature_counts = Counter()
        for feature, stats in self.feature_stats.items():
            feature_counts[feature] = stats['total_uses']
        
        self.stats['most_used_features'] = feature_counts.most_common(10)

    async def _persist_event(self, event: UsageEvent) -> None:
        """持久化事件数据"""
        try:
            if self.kv_store:
                key = f"usage_event_{event.event_id}"
                await self.kv_store.set(key, event.to_dict(), ttl=86400 * 30)  # 保存30天
        except Exception as e:
            logger.error(f"持久化事件数据失败: {e}")

    async def _persist_session(self, session: UserSession) -> None:
        """持久化会话数据"""
        try:
            if self.kv_store:
                key = f"user_session_{session.session_id}"
                await self.kv_store.set(key, session.to_dict(), ttl=86400 * 30)  # 保存30天
        except Exception as e:
            logger.error(f"持久化会话数据失败: {e}")

    def get_usage_stats(self) -> Dict[str, Any]:
        """
        获取使用统计信息

        Returns:
            Dict[str, Any]: 使用统计信息
        """
        uptime = datetime.now() - self.stats['system_start_time']

        return {
            **self.stats,
            'total_unique_users': len(self.stats['total_users']),
            'uptime_seconds': uptime.total_seconds(),
            'uptime_formatted': str(uptime),
            'events_per_minute': self.stats['total_events'] / max(uptime.total_seconds() / 60, 1),
            'features_count': len(self.feature_stats)
        }

    def get_feature_usage(self, feature_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取功能使用统计

        Args:
            feature_name: 特定功能名称，None表示获取所有功能

        Returns:
            Dict[str, Any]: 功能使用统计
        """
        if feature_name:
            if feature_name not in self.feature_stats:
                return {}

            stats = self.feature_stats[feature_name]
            return self._format_feature_stats(feature_name, stats)

        # 返回所有功能统计
        result = {}
        for name, stats in self.feature_stats.items():
            result[name] = self._format_feature_stats(name, stats)

        return result

    def _format_feature_stats(self, feature_name: str, stats: Dict[str, Any]) -> Dict[str, Any]:
        """格式化功能统计数据"""
        durations = stats['durations']
        avg_duration = sum(durations) / len(durations) if durations else 0.0

        total_attempts = stats['success_count'] + stats['error_count']
        success_rate = (stats['success_count'] / total_attempts * 100) if total_attempts > 0 else 0.0

        # 找出使用高峰时间
        hourly_usage = stats['hourly_usage']
        peak_hour = max(hourly_usage.items(), key=lambda x: x[1])[0] if hourly_usage else "00"

        return {
            'feature_name': feature_name,
            'total_uses': stats['total_uses'],
            'unique_users': len(stats['unique_users']),
            'avg_duration': avg_duration,
            'success_rate': success_rate,
            'peak_usage_hour': f"{peak_hour}:00",
            'first_used': stats['first_used'].isoformat() if stats['first_used'] else None,
            'last_used': stats['last_used'].isoformat() if stats['last_used'] else None,
            'hourly_distribution': dict(hourly_usage)
        }

    def get_user_activity(self, user_id: str, hours: int = 24) -> Dict[str, Any]:
        """
        获取用户活动统计

        Args:
            user_id: 用户ID
            hours: 统计时间范围（小时）

        Returns:
            Dict[str, Any]: 用户活动统计
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        user_events = [
            e for e in self.events_history
            if e.user_id == user_id and e.timestamp > cutoff_time
        ]

        if not user_events:
            return {
                'user_id': user_id,
                'time_range_hours': hours,
                'total_events': 0,
                'features_used': [],
                'session_count': 0,
                'total_duration': 0.0
            }

        # 统计功能使用
        feature_counts = Counter([e.feature_name for e in user_events])

        # 统计会话
        session_ids = set([e.session_id for e in user_events])

        # 计算总时长
        total_duration = sum([e.duration for e in user_events if e.duration])

        return {
            'user_id': user_id,
            'time_range_hours': hours,
            'total_events': len(user_events),
            'features_used': dict(feature_counts),
            'session_count': len(session_ids),
            'total_duration': total_duration,
            'avg_event_duration': total_duration / len(user_events) if user_events else 0.0,
            'most_used_feature': feature_counts.most_common(1)[0][0] if feature_counts else None
        }

    def get_performance_bottlenecks(self, threshold_percentile: float = 95.0) -> List[Dict[str, Any]]:
        """
        识别性能瓶颈

        Args:
            threshold_percentile: 性能阈值百分位数

        Returns:
            List[Dict[str, Any]]: 性能瓶颈列表
        """
        bottlenecks = []

        for feature_name, perf_data in self.performance_data.items():
            if not perf_data:
                continue

            durations = [d['duration'] for d in perf_data if d['success']]
            if not durations:
                continue

            # 计算百分位数
            durations.sort()
            threshold_index = int(len(durations) * threshold_percentile / 100)
            threshold_value = durations[threshold_index] if threshold_index < len(durations) else durations[-1]

            # 计算平均值
            avg_duration = sum(durations) / len(durations)

            # 如果阈值明显高于平均值，认为是瓶颈
            if threshold_value > avg_duration * 2:
                bottlenecks.append({
                    'feature_name': feature_name,
                    'avg_duration': avg_duration,
                    'p95_duration': threshold_value,
                    'slowdown_ratio': threshold_value / avg_duration,
                    'sample_count': len(durations),
                    'severity': 'high' if threshold_value > avg_duration * 5 else 'medium'
                })

        # 按严重程度排序
        bottlenecks.sort(key=lambda x: x['slowdown_ratio'], reverse=True)
        return bottlenecks

    def get_usage_patterns(self, hours: int = 168) -> Dict[str, Any]:  # 默认一周
        """
        分析使用模式

        Args:
            hours: 分析时间范围（小时）

        Returns:
            Dict[str, Any]: 使用模式分析结果
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_events = [e for e in self.events_history if e.timestamp > cutoff_time]

        if not recent_events:
            return {'message': '没有足够的数据进行模式分析'}

        # 按小时分析
        hourly_activity = defaultdict(int)
        for event in recent_events:
            hour = event.timestamp.hour
            hourly_activity[hour] += 1

        # 按星期几分析
        daily_activity = defaultdict(int)
        for event in recent_events:
            day = event.timestamp.strftime('%A')
            daily_activity[day] += 1

        # 功能使用模式
        feature_patterns = defaultdict(lambda: defaultdict(int))
        for event in recent_events:
            hour = event.timestamp.hour
            feature_patterns[event.feature_name][hour] += 1

        # 找出活跃时段
        peak_hours = sorted(hourly_activity.items(), key=lambda x: x[1], reverse=True)[:3]
        peak_days = sorted(daily_activity.items(), key=lambda x: x[1], reverse=True)[:3]

        return {
            'analysis_period_hours': hours,
            'total_events_analyzed': len(recent_events),
            'hourly_distribution': dict(hourly_activity),
            'daily_distribution': dict(daily_activity),
            'peak_hours': [f"{h}:00" for h, _ in peak_hours],
            'peak_days': [d for d, _ in peak_days],
            'feature_usage_patterns': {
                feature: dict(hours) for feature, hours in feature_patterns.items()
            }
        }

    async def cleanup_old_data(self, older_than_hours: int = 720) -> Dict[str, int]:  # 默认30天
        """
        清理旧数据

        Args:
            older_than_hours: 清理多少小时前的数据

        Returns:
            Dict[str, int]: 清理统计
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=older_than_hours)

            # 清理事件历史
            original_events_count = len(self.events_history)
            self.events_history = deque([
                e for e in self.events_history if e.timestamp > cutoff_time
            ], maxlen=self.max_events_history)
            events_cleaned = original_events_count - len(self.events_history)

            # 清理性能数据
            perf_cleaned = 0
            for feature_name, perf_data in self.performance_data.items():
                original_count = len(perf_data)
                self.performance_data[feature_name] = deque([
                    d for d in perf_data if d['timestamp'] > cutoff_time
                ], maxlen=1000)
                perf_cleaned += original_count - len(self.performance_data[feature_name])

            # 清理过期会话
            expired_sessions = []
            for session_id, session in self.active_sessions.items():
                if session.start_time < cutoff_time:
                    expired_sessions.append(session_id)

            for session_id in expired_sessions:
                await self.end_session(session_id)

            logger.info(f"数据清理完成: 事件{events_cleaned}条, 性能数据{perf_cleaned}条, 过期会话{len(expired_sessions)}个")

            return {
                'events_cleaned': events_cleaned,
                'performance_data_cleaned': perf_cleaned,
                'expired_sessions_cleaned': len(expired_sessions)
            }

        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
            return {'error': str(e)}

    async def cleanup(self) -> None:
        """清理资源"""
        try:
            # 结束所有活跃会话
            for session_id in list(self.active_sessions.keys()):
                await self.end_session(session_id)

            # 清理数据
            self.events_history.clear()
            self.feature_stats.clear()
            self.performance_data.clear()
            self.stats['total_users'].clear()

            logger.info("使用跟踪器资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")
