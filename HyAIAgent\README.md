# HyAIAgent

<div align="center">

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![PyQt6](https://img.shields.io/badge/PyQt6-6.4+-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)
![Status](https://img.shields.io/badge/Status-Stable-brightgreen.svg)

**一个基于 PyQt6 的现代化 AI 聊天助手应用程序**

[功能特性](#-功能特性) • [快速开始](#-快速开始) • [使用说明](#-使用说明) • [开发文档](#-开发文档)

</div>

## 📋 项目简介

HyAIAgent 是一个功能完整的 AI 聊天助手桌面应用程序，采用 Python + PyQt6 开发，支持与 OpenAI 兼容的 API 接口进行智能对话。应用提供了直观的图形界面、灵活的配置管理、完善的数据存储和强大的提示词模板系统。

## ✨ 功能特性

### 🤖 AI 对话
- **智能对话**: 支持 OpenAI GPT 系列模型
- **上下文记忆**: 自动维护对话历史和上下文
- **多模型支持**: 支持 GPT-3.5、GPT-4 等多种模型
- **异步处理**: 非阻塞式 AI 响应处理

### 🎨 用户界面
- **现代化设计**: 基于 PyQt6 的美观界面
- **响应式布局**: 自适应窗口大小调整
- **主题支持**: Fusion 风格界面
- **高 DPI 支持**: 完美支持高分辨率显示器

### ⚙️ 配置管理
- **灵活配置**: JSON 配置文件 + 环境变量
- **多提供商**: 支持 OpenAI、DeepSeek 等多个 AI 服务
- **实时设置**: 图形界面配置，即时生效
- **配置验证**: 自动验证配置有效性

### 💾 数据存储
- **轻量级数据库**: 基于 TinyDB 的 KV 存储
- **对话保存**: 自动/手动保存对话历史
- **数据备份**: 支持数据备份和恢复
- **TTL 支持**: 数据过期自动清理

### 📝 提示词管理
- **模板系统**: 基于 Jinja2 的提示词模板
- **分类管理**: 全局提示词和模板提示词
- **动态渲染**: 支持变量替换和条件逻辑
- **文件管理**: 基于文件的提示词存储

### 📊 日志系统
- **分级日志**: DEBUG、INFO、WARNING、ERROR
- **文件轮转**: 自动轮转和压缩日志文件
- **双重输出**: 控制台 + 文件同时输出
- **详细追踪**: 完整的调用链追踪

## 🏗️ 项目结构

```
HyAIAgent/
├── main.py                    # 程序入口
├── config.json               # 配置文件
├── .env                       # 环境变量（API密钥）
├── requirements.txt           # 依赖包
├── README.md                  # 使用说明
├── core/                      # 核心模块
│   ├── __init__.py
│   ├── config_manager.py     # 配置管理
│   ├── ai_client.py          # AI客户端
│   ├── kv_store.py           # KV数据库
│   └── prompt_manager.py     # 提示词管理
├── prompts/                   # 提示词目录
│   ├── global/               # 全局提示词
│   │   └── basic_chat.md     # 基础聊天提示词
│   └── templates/            # 提示词模板
├── data/                      # 数据目录
│   └── kv_store.json         # KV数据库文件
└── ui/                        # 用户界面
    ├── __init__.py
    └── chat_window.py        # 聊天窗口
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

1. 复制 `.env` 文件并添加你的API密钥：
```bash
OPENAI_API_KEY=your_openai_api_key_here
```

2. 根据需要修改 `config.json` 中的配置

### 3. 启动应用

```bash
python main.py
```

## ⚙️ 配置说明

### config.json 主要配置项

- `application`: 应用基本信息
- `ai_providers`: AI服务商配置
- `kv_database`: 数据库配置
- `ui`: 界面配置
- `logging`: 日志配置

### 环境变量

在 `.env` 文件中设置以下环境变量：

- `OPENAI_API_KEY`: OpenAI API密钥
- `DEEPSEEK_API_KEY`: DeepSeek API密钥（可选）
- `ZHIPU_API_KEY`: 智谱AI API密钥（可选）

## 📝 使用说明

1. 启动应用后，会显示聊天窗口
2. 在输入框中输入你的问题
3. 点击"发送"按钮或按回车键发送消息
4. AI会在聊天区域回复你的问题
5. 使用"清空"按钮可以清除对话历史

## 🔧 开发说明

本项目采用模块化设计，主要模块包括：

- **ConfigManager**: 配置管理器
- **SimpleAIClient**: AI客户端
- **KVStore**: 轻量级数据库
- **PromptManager**: 提示词管理
- **ChatWindow**: 聊天界面

## 📄 许可证

本项目采用MIT许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**版本**: 1.0.0-stage1  
**更新时间**: 2025-01-27
