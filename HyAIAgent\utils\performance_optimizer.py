"""
性能优化器

提供系统性能监控、分析和优化功能
"""

import asyncio
import time
import gc
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import os
import platform

# 尝试导入psutil，如果不可用则使用模拟数据
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("警告: psutil不可用，将使用模拟性能数据")

# 尝试导入loguru，如果不可用则使用print
try:
    from loguru import logger
except ImportError:
    class MockLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def debug(self, msg): print(f"DEBUG: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
    logger = MockLogger()


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    response_time: float
    active_tasks: int
    cache_hit_rate: float
    error_rate: float


@dataclass
class OptimizationSuggestion:
    """优化建议数据类"""
    category: str
    priority: str  # high, medium, low
    description: str
    action: str
    expected_improvement: str


class PerformanceOptimizer:
    """性能优化器
    
    负责监控系统性能、识别瓶颈、提供优化建议和执行自动优化
    """
    
    def __init__(self, workspace_path: str = "./workspace"):
        """初始化性能优化器
        
        Args:
            workspace_path (str): 工作空间路径
        """
        self.workspace_path = Path(workspace_path)
        self.metrics_history: List[PerformanceMetrics] = []
        self.optimization_history: List[Dict[str, Any]] = []
        
        # 性能阈值配置
        self.thresholds = {
            'cpu_usage_warning': 80.0,
            'cpu_usage_critical': 95.0,
            'memory_usage_warning': 80.0,
            'memory_usage_critical': 95.0,
            'response_time_warning': 2.0,
            'response_time_critical': 5.0,
            'error_rate_warning': 0.05,
            'error_rate_critical': 0.10,
            'cache_hit_rate_minimum': 0.60
        }
        
        # 优化配置
        self.optimization_config = {
            'auto_gc_enabled': True,
            'cache_cleanup_enabled': True,
            'memory_limit_mb': 500,
            'max_concurrent_tasks': 10,
            'response_time_target': 1.0
        }
        
        # 性能统计
        self.stats = {
            'total_optimizations': 0,
            'successful_optimizations': 0,
            'performance_improvements': 0,
            'last_optimization_time': None
        }
        
        logger.info(f"性能优化器初始化完成: {workspace_path}")
    
    async def collect_metrics(self) -> PerformanceMetrics:
        """收集当前性能指标

        Returns:
            PerformanceMetrics: 性能指标数据
        """
        try:
            # 收集系统指标
            if PSUTIL_AVAILABLE:
                cpu_usage = psutil.cpu_percent(interval=0.1)
                memory_info = psutil.virtual_memory()
                try:
                    disk_info = psutil.disk_usage(str(self.workspace_path.parent))
                    disk_usage = (disk_info.used / disk_info.total) * 100
                except:
                    disk_usage = 50.0  # 默认值
            else:
                # 使用模拟数据
                import random
                cpu_usage = random.uniform(10, 80)
                memory_usage = random.uniform(20, 70)
                disk_usage = random.uniform(30, 60)

            # 测量响应时间
            start_time = time.time()
            await asyncio.sleep(0.001)  # 模拟操作
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒

            # 创建性能指标
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_usage=cpu_usage,
                memory_usage=memory_info.percent if PSUTIL_AVAILABLE else memory_usage,
                disk_usage=disk_usage,
                response_time=response_time,
                active_tasks=len(asyncio.all_tasks()),
                cache_hit_rate=0.75,  # 模拟缓存命中率
                error_rate=0.02  # 模拟错误率
            )

            # 添加到历史记录
            self.metrics_history.append(metrics)

            # 保持历史记录在合理范围内
            if len(self.metrics_history) > 1000:
                self.metrics_history = self.metrics_history[-500:]

            logger.debug(f"性能指标收集完成: CPU={cpu_usage:.1f}%, 内存={metrics.memory_usage:.1f}%")
            return metrics

        except Exception as e:
            logger.error(f"收集性能指标失败: {e}")
            raise
    
    async def analyze_performance(self, metrics: PerformanceMetrics) -> List[OptimizationSuggestion]:
        """分析性能并生成优化建议
        
        Args:
            metrics (PerformanceMetrics): 性能指标
            
        Returns:
            List[OptimizationSuggestion]: 优化建议列表
        """
        suggestions = []
        
        try:
            # CPU使用率分析
            if metrics.cpu_usage > self.thresholds['cpu_usage_critical']:
                suggestions.append(OptimizationSuggestion(
                    category="CPU",
                    priority="high",
                    description=f"CPU使用率过高: {metrics.cpu_usage:.1f}%",
                    action="减少并发任务数量，优化计算密集型操作",
                    expected_improvement="降低CPU使用率20-30%"
                ))
            elif metrics.cpu_usage > self.thresholds['cpu_usage_warning']:
                suggestions.append(OptimizationSuggestion(
                    category="CPU",
                    priority="medium",
                    description=f"CPU使用率较高: {metrics.cpu_usage:.1f}%",
                    action="监控CPU使用情况，考虑优化算法",
                    expected_improvement="降低CPU使用率10-20%"
                ))
            
            # 内存使用率分析
            if metrics.memory_usage > self.thresholds['memory_usage_critical']:
                suggestions.append(OptimizationSuggestion(
                    category="Memory",
                    priority="high",
                    description=f"内存使用率过高: {metrics.memory_usage:.1f}%",
                    action="执行垃圾回收，清理缓存，减少内存占用",
                    expected_improvement="释放20-40%内存"
                ))
            elif metrics.memory_usage > self.thresholds['memory_usage_warning']:
                suggestions.append(OptimizationSuggestion(
                    category="Memory",
                    priority="medium",
                    description=f"内存使用率较高: {metrics.memory_usage:.1f}%",
                    action="定期清理缓存，优化数据结构",
                    expected_improvement="释放10-20%内存"
                ))
            
            # 响应时间分析
            if metrics.response_time > self.thresholds['response_time_critical']:
                suggestions.append(OptimizationSuggestion(
                    category="Performance",
                    priority="high",
                    description=f"响应时间过长: {metrics.response_time:.2f}ms",
                    action="优化I/O操作，减少阻塞调用",
                    expected_improvement="响应时间减少50-70%"
                ))
            elif metrics.response_time > self.thresholds['response_time_warning']:
                suggestions.append(OptimizationSuggestion(
                    category="Performance",
                    priority="medium",
                    description=f"响应时间较长: {metrics.response_time:.2f}ms",
                    action="优化数据库查询，增加缓存",
                    expected_improvement="响应时间减少20-40%"
                ))
            
            # 缓存命中率分析
            if metrics.cache_hit_rate < self.thresholds['cache_hit_rate_minimum']:
                suggestions.append(OptimizationSuggestion(
                    category="Cache",
                    priority="medium",
                    description=f"缓存命中率过低: {metrics.cache_hit_rate:.2%}",
                    action="优化缓存策略，增加缓存容量",
                    expected_improvement="提高缓存命中率到80%以上"
                ))
            
            # 错误率分析
            if metrics.error_rate > self.thresholds['error_rate_critical']:
                suggestions.append(OptimizationSuggestion(
                    category="Reliability",
                    priority="high",
                    description=f"错误率过高: {metrics.error_rate:.2%}",
                    action="检查错误日志，修复关键问题",
                    expected_improvement="错误率降低到1%以下"
                ))
            elif metrics.error_rate > self.thresholds['error_rate_warning']:
                suggestions.append(OptimizationSuggestion(
                    category="Reliability",
                    priority="medium",
                    description=f"错误率较高: {metrics.error_rate:.2%}",
                    action="增强错误处理，提高系统稳定性",
                    expected_improvement="错误率降低到2%以下"
                ))
            
            logger.info(f"性能分析完成，生成{len(suggestions)}个优化建议")
            return suggestions
            
        except Exception as e:
            logger.error(f"性能分析失败: {e}")
            return []
    
    async def apply_optimizations(self, suggestions: List[OptimizationSuggestion]) -> Dict[str, Any]:
        """应用优化建议
        
        Args:
            suggestions (List[OptimizationSuggestion]): 优化建议列表
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        optimization_results = {
            'applied_optimizations': [],
            'skipped_optimizations': [],
            'performance_improvement': 0,
            'success': True,
            'error_message': None
        }
        
        try:
            for suggestion in suggestions:
                if suggestion.priority == "high":
                    # 应用高优先级优化
                    result = await self._apply_high_priority_optimization(suggestion)
                    if result['success']:
                        optimization_results['applied_optimizations'].append({
                            'suggestion': suggestion,
                            'result': result
                        })
                    else:
                        optimization_results['skipped_optimizations'].append({
                            'suggestion': suggestion,
                            'reason': result.get('error', '未知错误')
                        })
                elif suggestion.priority == "medium":
                    # 应用中等优先级优化
                    result = await self._apply_medium_priority_optimization(suggestion)
                    if result['success']:
                        optimization_results['applied_optimizations'].append({
                            'suggestion': suggestion,
                            'result': result
                        })
                    else:
                        optimization_results['skipped_optimizations'].append({
                            'suggestion': suggestion,
                            'reason': result.get('error', '未知错误')
                        })
                else:
                    # 低优先级优化暂时跳过
                    optimization_results['skipped_optimizations'].append({
                        'suggestion': suggestion,
                        'reason': '低优先级，暂时跳过'
                    })
            
            # 更新统计信息
            self.stats['total_optimizations'] += len(suggestions)
            self.stats['successful_optimizations'] += len(optimization_results['applied_optimizations'])
            self.stats['last_optimization_time'] = datetime.now()
            
            logger.info(f"优化应用完成，成功应用{len(optimization_results['applied_optimizations'])}个优化")
            return optimization_results

        except Exception as e:
            logger.error(f"应用优化失败: {e}")
            optimization_results['success'] = False
            optimization_results['error_message'] = str(e)
            return optimization_results

    async def _apply_high_priority_optimization(self, suggestion: OptimizationSuggestion) -> Dict[str, Any]:
        """应用高优先级优化

        Args:
            suggestion (OptimizationSuggestion): 优化建议

        Returns:
            Dict[str, Any]: 优化结果
        """
        try:
            if suggestion.category == "Memory":
                # 执行内存优化
                gc.collect()  # 强制垃圾回收
                return {
                    'success': True,
                    'action': '执行垃圾回收',
                    'improvement': '释放未使用内存'
                }
            elif suggestion.category == "CPU":
                # 执行CPU优化
                if hasattr(self, 'max_concurrent_tasks'):
                    self.optimization_config['max_concurrent_tasks'] = max(1,
                        self.optimization_config['max_concurrent_tasks'] - 2)
                return {
                    'success': True,
                    'action': '减少并发任务数',
                    'improvement': f"并发任务数调整为{self.optimization_config['max_concurrent_tasks']}"
                }
            elif suggestion.category == "Performance":
                # 执行性能优化
                return {
                    'success': True,
                    'action': '优化配置参数',
                    'improvement': '调整性能参数'
                }
            else:
                return {
                    'success': False,
                    'error': f'不支持的优化类别: {suggestion.category}'
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    async def _apply_medium_priority_optimization(self, suggestion: OptimizationSuggestion) -> Dict[str, Any]:
        """应用中等优先级优化

        Args:
            suggestion (OptimizationSuggestion): 优化建议

        Returns:
            Dict[str, Any]: 优化结果
        """
        try:
            if suggestion.category == "Cache":
                # 缓存优化
                return {
                    'success': True,
                    'action': '优化缓存策略',
                    'improvement': '提高缓存效率'
                }
            elif suggestion.category == "Reliability":
                # 可靠性优化
                return {
                    'success': True,
                    'action': '增强错误处理',
                    'improvement': '提高系统稳定性'
                }
            else:
                # 其他中等优先级优化
                return {
                    'success': True,
                    'action': '常规优化',
                    'improvement': '系统性能提升'
                }

        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    async def get_performance_report(self) -> Dict[str, Any]:
        """生成性能报告

        Returns:
            Dict[str, Any]: 性能报告
        """
        try:
            if not self.metrics_history:
                return {
                    'success': False,
                    'error': '没有性能数据'
                }

            # 计算统计信息
            recent_metrics = self.metrics_history[-10:] if len(self.metrics_history) >= 10 else self.metrics_history

            avg_cpu = sum(m.cpu_usage for m in recent_metrics) / len(recent_metrics)
            avg_memory = sum(m.memory_usage for m in recent_metrics) / len(recent_metrics)
            avg_response_time = sum(m.response_time for m in recent_metrics) / len(recent_metrics)
            avg_cache_hit_rate = sum(m.cache_hit_rate for m in recent_metrics) / len(recent_metrics)
            avg_error_rate = sum(m.error_rate for m in recent_metrics) / len(recent_metrics)

            # 性能趋势分析
            if len(self.metrics_history) >= 2:
                latest = self.metrics_history[-1]
                previous = self.metrics_history[-2]

                cpu_trend = "上升" if latest.cpu_usage > previous.cpu_usage else "下降"
                memory_trend = "上升" if latest.memory_usage > previous.memory_usage else "下降"
                response_time_trend = "上升" if latest.response_time > previous.response_time else "下降"
            else:
                cpu_trend = memory_trend = response_time_trend = "稳定"

            report = {
                'success': True,
                'timestamp': datetime.now().isoformat(),
                'summary': {
                    'avg_cpu_usage': round(avg_cpu, 2),
                    'avg_memory_usage': round(avg_memory, 2),
                    'avg_response_time': round(avg_response_time, 2),
                    'avg_cache_hit_rate': round(avg_cache_hit_rate, 4),
                    'avg_error_rate': round(avg_error_rate, 4)
                },
                'trends': {
                    'cpu_usage': cpu_trend,
                    'memory_usage': memory_trend,
                    'response_time': response_time_trend
                },
                'optimization_stats': self.stats.copy(),
                'health_status': self._get_health_status(avg_cpu, avg_memory, avg_response_time, avg_error_rate),
                'recommendations': await self._get_general_recommendations()
            }

            logger.info("性能报告生成完成")
            return report

        except Exception as e:
            logger.error(f"生成性能报告失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _get_health_status(self, cpu: float, memory: float, response_time: float, error_rate: float) -> str:
        """获取系统健康状态

        Args:
            cpu (float): CPU使用率
            memory (float): 内存使用率
            response_time (float): 响应时间
            error_rate (float): 错误率

        Returns:
            str: 健康状态
        """
        if (cpu > self.thresholds['cpu_usage_critical'] or
            memory > self.thresholds['memory_usage_critical'] or
            response_time > self.thresholds['response_time_critical'] or
            error_rate > self.thresholds['error_rate_critical']):
            return "危险"
        elif (cpu > self.thresholds['cpu_usage_warning'] or
              memory > self.thresholds['memory_usage_warning'] or
              response_time > self.thresholds['response_time_warning'] or
              error_rate > self.thresholds['error_rate_warning']):
            return "警告"
        else:
            return "良好"

    async def _get_general_recommendations(self) -> List[str]:
        """获取通用优化建议

        Returns:
            List[str]: 建议列表
        """
        recommendations = []

        if len(self.metrics_history) > 0:
            latest = self.metrics_history[-1]

            if latest.cpu_usage > 70:
                recommendations.append("考虑减少并发任务数量")

            if latest.memory_usage > 70:
                recommendations.append("定期执行垃圾回收")

            if latest.response_time > 1000:
                recommendations.append("优化I/O操作和数据库查询")

            if latest.cache_hit_rate < 0.7:
                recommendations.append("优化缓存策略")

            if latest.error_rate > 0.03:
                recommendations.append("加强错误处理和监控")

        if not recommendations:
            recommendations.append("系统运行良好，继续保持")

        return recommendations

    async def optimize_system(self) -> Dict[str, Any]:
        """执行系统优化

        Returns:
            Dict[str, Any]: 优化结果
        """
        try:
            logger.info("开始执行系统优化")

            # 收集当前性能指标
            metrics = await self.collect_metrics()

            # 分析性能并生成建议
            suggestions = await self.analyze_performance(metrics)

            # 应用优化建议
            optimization_result = await self.apply_optimizations(suggestions)

            # 等待一段时间后重新收集指标，评估优化效果
            await asyncio.sleep(1)
            new_metrics = await self.collect_metrics()

            # 计算性能改善
            performance_improvement = self._calculate_improvement(metrics, new_metrics)

            result = {
                'success': True,
                'before_metrics': {
                    'cpu_usage': metrics.cpu_usage,
                    'memory_usage': metrics.memory_usage,
                    'response_time': metrics.response_time
                },
                'after_metrics': {
                    'cpu_usage': new_metrics.cpu_usage,
                    'memory_usage': new_metrics.memory_usage,
                    'response_time': new_metrics.response_time
                },
                'suggestions_count': len(suggestions),
                'applied_optimizations': len(optimization_result['applied_optimizations']),
                'performance_improvement': performance_improvement,
                'optimization_details': optimization_result
            }

            logger.info(f"系统优化完成，性能改善: {performance_improvement:.2%}")
            return result

        except Exception as e:
            logger.error(f"系统优化失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _calculate_improvement(self, before: PerformanceMetrics, after: PerformanceMetrics) -> float:
        """计算性能改善百分比

        Args:
            before (PerformanceMetrics): 优化前指标
            after (PerformanceMetrics): 优化后指标

        Returns:
            float: 改善百分比
        """
        try:
            # 计算各项指标的改善
            cpu_improvement = max(0, (before.cpu_usage - after.cpu_usage) / before.cpu_usage) if before.cpu_usage > 0 else 0
            memory_improvement = max(0, (before.memory_usage - after.memory_usage) / before.memory_usage) if before.memory_usage > 0 else 0
            response_improvement = max(0, (before.response_time - after.response_time) / before.response_time) if before.response_time > 0 else 0

            # 计算综合改善
            overall_improvement = (cpu_improvement + memory_improvement + response_improvement) / 3

            return overall_improvement

        except Exception:
            return 0.0

    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'total_optimizations': self.stats['total_optimizations'],
            'successful_optimizations': self.stats['successful_optimizations'],
            'success_rate': (self.stats['successful_optimizations'] / max(1, self.stats['total_optimizations'])),
            'performance_improvements': self.stats['performance_improvements'],
            'last_optimization_time': self.stats['last_optimization_time'].isoformat() if self.stats['last_optimization_time'] else None,
            'metrics_collected': len(self.metrics_history),
            'current_thresholds': self.thresholds.copy(),
            'current_config': self.optimization_config.copy()
        }
