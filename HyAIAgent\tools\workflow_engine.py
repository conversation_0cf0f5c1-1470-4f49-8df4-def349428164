"""
工作流引擎模块

提供复杂工作流定义和执行、条件分支和循环控制、任务依赖管理和状态跟踪功能。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple, Callable, Set
from dataclasses import dataclass, field
from enum import Enum
import uuid


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"


class WorkflowStatus(Enum):
    """工作流状态枚举"""
    CREATED = "created"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class TaskType(Enum):
    """任务类型枚举"""
    FUNCTION = "function"
    HTTP_REQUEST = "http_request"
    FILE_OPERATION = "file_operation"
    DATA_PROCESSING = "data_processing"
    CONDITION = "condition"
    LOOP = "loop"
    DELAY = "delay"
    NOTIFICATION = "notification"


@dataclass
class TaskResult:
    """任务执行结果"""
    task_id: str
    status: TaskStatus
    result_data: Any = None
    error_message: Optional[str] = None
    execution_time: float = 0.0
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


@dataclass
class Task:
    """工作流任务"""
    task_id: str
    name: str
    task_type: TaskType
    config: Dict[str, Any]
    dependencies: List[str] = field(default_factory=list)
    conditions: List[Dict[str, Any]] = field(default_factory=list)
    retry_count: int = 0
    max_retries: int = 3
    timeout: int = 300  # 秒
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[TaskResult] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class Workflow:
    """工作流定义"""
    workflow_id: str
    name: str
    description: str
    tasks: Dict[str, Task] = field(default_factory=dict)
    variables: Dict[str, Any] = field(default_factory=dict)
    status: WorkflowStatus = WorkflowStatus.CREATED
    start_task_id: Optional[str] = None
    end_task_ids: List[str] = field(default_factory=list)
    max_execution_time: int = 3600  # 秒
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


@dataclass
class WorkflowExecution:
    """工作流执行实例"""
    execution_id: str
    workflow_id: str
    status: WorkflowStatus
    current_task_id: Optional[str] = None
    completed_tasks: Set[str] = field(default_factory=set)
    failed_tasks: Set[str] = field(default_factory=set)
    task_results: Dict[str, TaskResult] = field(default_factory=dict)
    execution_context: Dict[str, Any] = field(default_factory=dict)
    started_at: datetime = field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None


class WorkflowEngine:
    """工作流引擎"""
    
    def __init__(self):
        """初始化工作流引擎"""
        self.logger = logging.getLogger(__name__)
        self.workflows: Dict[str, Workflow] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        self.task_handlers: Dict[TaskType, Callable] = {}
        self.running_executions: Set[str] = set()
        
        # 注册默认任务处理器
        self._register_default_handlers()
        
        self.logger.info("工作流引擎初始化完成")
    
    def _register_default_handlers(self):
        """注册默认任务处理器"""
        self.task_handlers[TaskType.FUNCTION] = self._handle_function_task
        self.task_handlers[TaskType.HTTP_REQUEST] = self._handle_http_request_task
        self.task_handlers[TaskType.FILE_OPERATION] = self._handle_file_operation_task
        self.task_handlers[TaskType.DATA_PROCESSING] = self._handle_data_processing_task
        self.task_handlers[TaskType.CONDITION] = self._handle_condition_task
        self.task_handlers[TaskType.LOOP] = self._handle_loop_task
        self.task_handlers[TaskType.DELAY] = self._handle_delay_task
        self.task_handlers[TaskType.NOTIFICATION] = self._handle_notification_task
    
    async def create_workflow(self, workflow: Workflow) -> bool:
        """创建工作流"""
        try:
            # 验证工作流
            validation_result = await self.validate_workflow(workflow)
            if not validation_result["valid"]:
                self.logger.error(f"工作流验证失败: {validation_result['errors']}")
                return False
            
            self.workflows[workflow.workflow_id] = workflow
            self.logger.info(f"工作流创建成功: {workflow.workflow_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"工作流创建失败: {e}")
            return False
    
    async def get_workflow(self, workflow_id: str) -> Optional[Workflow]:
        """获取工作流"""
        return self.workflows.get(workflow_id)
    
    async def list_workflows(self) -> List[Workflow]:
        """列出所有工作流"""
        return list(self.workflows.values())
    
    async def add_task(self, workflow_id: str, task: Task) -> bool:
        """向工作流添加任务"""
        try:
            workflow = self.workflows.get(workflow_id)
            if not workflow:
                return False
            
            workflow.tasks[task.task_id] = task
            workflow.updated_at = datetime.now()
            
            self.logger.info(f"任务添加成功: {task.task_id} -> {workflow_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"任务添加失败: {e}")
            return False
    
    async def remove_task(self, workflow_id: str, task_id: str) -> bool:
        """从工作流移除任务"""
        try:
            workflow = self.workflows.get(workflow_id)
            if not workflow or task_id not in workflow.tasks:
                return False
            
            # 检查是否有其他任务依赖此任务
            for other_task in workflow.tasks.values():
                if task_id in other_task.dependencies:
                    self.logger.warning(f"任务 {task_id} 被其他任务依赖，无法删除")
                    return False
            
            del workflow.tasks[task_id]
            workflow.updated_at = datetime.now()
            
            self.logger.info(f"任务移除成功: {task_id} <- {workflow_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"任务移除失败: {e}")
            return False
    
    async def execute_workflow(self, workflow_id: str, 
                             initial_context: Optional[Dict[str, Any]] = None) -> str:
        """执行工作流"""
        try:
            workflow = self.workflows.get(workflow_id)
            if not workflow:
                raise ValueError(f"工作流不存在: {workflow_id}")
            
            # 创建执行实例
            execution_id = str(uuid.uuid4())
            execution = WorkflowExecution(
                execution_id=execution_id,
                workflow_id=workflow_id,
                status=WorkflowStatus.RUNNING,
                execution_context=initial_context or {}
            )
            
            self.executions[execution_id] = execution
            self.running_executions.add(execution_id)
            
            # 更新工作流状态
            workflow.status = WorkflowStatus.RUNNING
            workflow.started_at = datetime.now()
            
            # 异步执行工作流
            asyncio.create_task(self._execute_workflow_async(execution_id))
            
            self.logger.info(f"工作流执行开始: {workflow_id} -> {execution_id}")
            return execution_id
            
        except Exception as e:
            self.logger.error(f"工作流执行失败: {e}")
            raise
    
    async def _execute_workflow_async(self, execution_id: str):
        """异步执行工作流"""
        try:
            execution = self.executions[execution_id]
            workflow = self.workflows[execution.workflow_id]
            
            # 找到起始任务
            start_tasks = self._find_start_tasks(workflow)
            if not start_tasks:
                raise ValueError("没有找到起始任务")
            
            # 执行任务
            await self._execute_tasks(execution, workflow, start_tasks)
            
            # 检查执行结果
            if execution.failed_tasks:
                execution.status = WorkflowStatus.FAILED
                workflow.status = WorkflowStatus.FAILED
                execution.error_message = f"任务执行失败: {list(execution.failed_tasks)}"
            else:
                execution.status = WorkflowStatus.COMPLETED
                workflow.status = WorkflowStatus.COMPLETED
            
            execution.completed_at = datetime.now()
            workflow.completed_at = datetime.now()
            
            self.running_executions.discard(execution_id)
            
            self.logger.info(f"工作流执行完成: {execution_id}, 状态: {execution.status.value}")
            
        except Exception as e:
            execution = self.executions.get(execution_id)
            if execution:
                execution.status = WorkflowStatus.FAILED
                execution.error_message = str(e)
                execution.completed_at = datetime.now()
            
            workflow = self.workflows.get(execution.workflow_id) if execution else None
            if workflow:
                workflow.status = WorkflowStatus.FAILED
                workflow.completed_at = datetime.now()
            
            self.running_executions.discard(execution_id)
            self.logger.error(f"工作流执行异常: {execution_id}, 错误: {e}")
    
    def _find_start_tasks(self, workflow: Workflow) -> List[str]:
        """找到起始任务"""
        if workflow.start_task_id:
            return [workflow.start_task_id]
        
        # 找到没有依赖的任务
        start_tasks = []
        for task_id, task in workflow.tasks.items():
            if not task.dependencies:
                start_tasks.append(task_id)
        
        return start_tasks
    
    async def _execute_tasks(self, execution: WorkflowExecution, 
                           workflow: Workflow, task_ids: List[str]):
        """执行任务列表"""
        for task_id in task_ids:
            if execution.status != WorkflowStatus.RUNNING:
                break
            
            task = workflow.tasks.get(task_id)
            if not task:
                continue
            
            # 检查依赖是否完成
            if not self._check_dependencies(execution, task):
                continue
            
            # 检查条件
            if not await self._check_conditions(execution, task):
                task.status = TaskStatus.SKIPPED
                execution.completed_tasks.add(task_id)
                continue
            
            # 执行任务
            await self._execute_single_task(execution, workflow, task)
            
            # 找到下一个可执行的任务
            next_tasks = self._find_next_tasks(workflow, task_id)
            if next_tasks:
                await self._execute_tasks(execution, workflow, next_tasks)
    
    def _check_dependencies(self, execution: WorkflowExecution, task: Task) -> bool:
        """检查任务依赖"""
        for dep_task_id in task.dependencies:
            if dep_task_id not in execution.completed_tasks:
                return False
            
            # 检查依赖任务是否成功完成
            dep_result = execution.task_results.get(dep_task_id)
            if dep_result and dep_result.status == TaskStatus.FAILED:
                return False
        
        return True
    
    async def _check_conditions(self, execution: WorkflowExecution, task: Task) -> bool:
        """检查任务条件"""
        if not task.conditions:
            return True
        
        for condition in task.conditions:
            # 简单的条件检查实现
            condition_type = condition.get("type", "")
            if condition_type == "variable_equals":
                var_name = condition.get("variable")
                expected_value = condition.get("value")
                actual_value = execution.execution_context.get(var_name)
                if actual_value != expected_value:
                    return False
            elif condition_type == "task_result":
                task_id = condition.get("task_id")
                expected_status = condition.get("status")
                task_result = execution.task_results.get(task_id)
                if not task_result or task_result.status.value != expected_status:
                    return False
        
        return True
    
    def _find_next_tasks(self, workflow: Workflow, completed_task_id: str) -> List[str]:
        """找到下一个可执行的任务"""
        next_tasks = []
        for task_id, task in workflow.tasks.items():
            if (completed_task_id in task.dependencies and 
                task.status == TaskStatus.PENDING):
                next_tasks.append(task_id)
        
        return next_tasks

    async def _execute_single_task(self, execution: WorkflowExecution,
                                 workflow: Workflow, task: Task):
        """执行单个任务"""
        try:
            task.status = TaskStatus.RUNNING
            execution.current_task_id = task.task_id
            start_time = datetime.now()

            # 获取任务处理器
            handler = self.task_handlers.get(task.task_type)
            if not handler:
                raise ValueError(f"不支持的任务类型: {task.task_type}")

            # 执行任务
            result_data = await handler(task, execution.execution_context)

            # 创建任务结果
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            task_result = TaskResult(
                task_id=task.task_id,
                status=TaskStatus.COMPLETED,
                result_data=result_data,
                execution_time=execution_time,
                started_at=start_time,
                completed_at=end_time
            )

            task.status = TaskStatus.COMPLETED
            task.result = task_result
            execution.task_results[task.task_id] = task_result
            execution.completed_tasks.add(task.task_id)

            self.logger.info(f"任务执行成功: {task.task_id}")

        except Exception as e:
            # 任务执行失败
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            task_result = TaskResult(
                task_id=task.task_id,
                status=TaskStatus.FAILED,
                error_message=str(e),
                execution_time=execution_time,
                started_at=start_time,
                completed_at=end_time
            )

            task.status = TaskStatus.FAILED
            task.result = task_result
            execution.task_results[task.task_id] = task_result
            execution.failed_tasks.add(task.task_id)

            # 检查是否需要重试
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.PENDING
                self.logger.warning(f"任务执行失败，准备重试: {task.task_id}, 重试次数: {task.retry_count}")
                # 重新执行任务
                await asyncio.sleep(1)  # 等待1秒后重试
                await self._execute_single_task(execution, workflow, task)
            else:
                self.logger.error(f"任务执行失败: {task.task_id}, 错误: {e}")

    # 任务处理器实现
    async def _handle_function_task(self, task: Task, context: Dict[str, Any]) -> Any:
        """处理函数任务"""
        function_name = task.config.get("function_name")
        parameters = task.config.get("parameters", {})

        # 替换参数中的上下文变量
        resolved_params = self._resolve_parameters(parameters, context)

        # 这里应该调用实际的函数，暂时返回模拟结果
        result = {
            "function_name": function_name,
            "parameters": resolved_params,
            "result": f"函数 {function_name} 执行完成"
        }

        # 更新上下文
        output_var = task.config.get("output_variable")
        if output_var:
            context[output_var] = result

        return result

    async def _handle_http_request_task(self, task: Task, context: Dict[str, Any]) -> Any:
        """处理HTTP请求任务"""
        url = task.config.get("url")
        method = task.config.get("method", "GET")
        headers = task.config.get("headers", {})
        data = task.config.get("data", {})

        # 解析参数中的上下文变量
        resolved_url = self._resolve_string(url, context)
        resolved_headers = self._resolve_parameters(headers, context)
        resolved_data = self._resolve_parameters(data, context)

        # 模拟HTTP请求
        result = {
            "url": resolved_url,
            "method": method,
            "status_code": 200,
            "response": f"HTTP {method} 请求到 {resolved_url} 完成"
        }

        # 更新上下文
        output_var = task.config.get("output_variable")
        if output_var:
            context[output_var] = result

        return result

    async def _handle_file_operation_task(self, task: Task, context: Dict[str, Any]) -> Any:
        """处理文件操作任务"""
        operation = task.config.get("operation")  # read, write, delete, copy, move
        file_path = task.config.get("file_path")

        resolved_path = self._resolve_string(file_path, context)

        if operation == "read":
            # 模拟文件读取
            result = {
                "operation": "read",
                "file_path": resolved_path,
                "content": f"文件 {resolved_path} 的内容",
                "size": 1024
            }
        elif operation == "write":
            content = task.config.get("content", "")
            resolved_content = self._resolve_string(content, context)
            result = {
                "operation": "write",
                "file_path": resolved_path,
                "content_length": len(resolved_content),
                "success": True
            }
        else:
            result = {
                "operation": operation,
                "file_path": resolved_path,
                "success": True
            }

        # 更新上下文
        output_var = task.config.get("output_variable")
        if output_var:
            context[output_var] = result

        return result

    async def _handle_data_processing_task(self, task: Task, context: Dict[str, Any]) -> Any:
        """处理数据处理任务"""
        operation = task.config.get("operation")  # filter, transform, aggregate
        input_data = task.config.get("input_data")

        # 从上下文获取输入数据
        if isinstance(input_data, str) and input_data.startswith("$"):
            var_name = input_data[1:]
            input_data = context.get(var_name, [])

        if operation == "filter":
            # 模拟数据过滤
            filter_condition = task.config.get("filter_condition", {})
            result = {
                "operation": "filter",
                "input_count": len(input_data) if isinstance(input_data, list) else 0,
                "output_count": len(input_data) // 2 if isinstance(input_data, list) else 0,
                "filtered_data": input_data[:len(input_data)//2] if isinstance(input_data, list) else []
            }
        elif operation == "transform":
            # 模拟数据转换
            result = {
                "operation": "transform",
                "input_count": len(input_data) if isinstance(input_data, list) else 0,
                "transformed_data": [f"transformed_{item}" for item in input_data] if isinstance(input_data, list) else []
            }
        else:
            result = {
                "operation": operation,
                "input_data": input_data,
                "result": f"数据处理操作 {operation} 完成"
            }

        # 更新上下文
        output_var = task.config.get("output_variable")
        if output_var:
            context[output_var] = result

        return result

    async def _handle_condition_task(self, task: Task, context: Dict[str, Any]) -> Any:
        """处理条件任务"""
        condition = task.config.get("condition")
        true_value = task.config.get("true_value", True)
        false_value = task.config.get("false_value", False)

        # 简单的条件评估
        condition_result = self._evaluate_condition(condition, context)

        result = {
            "condition": condition,
            "result": condition_result,
            "value": true_value if condition_result else false_value
        }

        # 更新上下文
        output_var = task.config.get("output_variable")
        if output_var:
            context[output_var] = result["value"]

        return result

    async def _handle_loop_task(self, task: Task, context: Dict[str, Any]) -> Any:
        """处理循环任务"""
        loop_type = task.config.get("loop_type", "for")  # for, while
        iterations = task.config.get("iterations", 1)

        results = []

        if loop_type == "for":
            for i in range(iterations):
                iteration_context = context.copy()
                iteration_context["loop_index"] = i
                iteration_context["loop_count"] = iterations

                # 执行循环体（这里简化处理）
                iteration_result = {
                    "iteration": i,
                    "result": f"循环迭代 {i} 完成"
                }
                results.append(iteration_result)

        result = {
            "loop_type": loop_type,
            "iterations": len(results),
            "results": results
        }

        # 更新上下文
        output_var = task.config.get("output_variable")
        if output_var:
            context[output_var] = result

        return result

    async def _handle_delay_task(self, task: Task, context: Dict[str, Any]) -> Any:
        """处理延迟任务"""
        delay_seconds = task.config.get("delay_seconds", 1)

        await asyncio.sleep(delay_seconds)

        result = {
            "delay_seconds": delay_seconds,
            "completed_at": datetime.now().isoformat()
        }

        return result

    async def _handle_notification_task(self, task: Task, context: Dict[str, Any]) -> Any:
        """处理通知任务"""
        message = task.config.get("message", "")
        notification_type = task.config.get("type", "info")

        resolved_message = self._resolve_string(message, context)

        # 模拟发送通知
        result = {
            "type": notification_type,
            "message": resolved_message,
            "sent_at": datetime.now().isoformat(),
            "status": "sent"
        }

        self.logger.info(f"通知发送: {notification_type} - {resolved_message}")

        return result

    # 辅助方法
    def _resolve_parameters(self, parameters: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """解析参数中的上下文变量"""
        resolved = {}
        for key, value in parameters.items():
            if isinstance(value, str):
                resolved[key] = self._resolve_string(value, context)
            elif isinstance(value, dict):
                resolved[key] = self._resolve_parameters(value, context)
            elif isinstance(value, list):
                resolved[key] = [self._resolve_string(item, context) if isinstance(item, str) else item for item in value]
            else:
                resolved[key] = value
        return resolved

    def _resolve_string(self, text: str, context: Dict[str, Any]) -> str:
        """解析字符串中的上下文变量"""
        if not isinstance(text, str):
            return text

        # 简单的变量替换 ${variable_name}
        import re
        def replace_var(match):
            var_name = match.group(1)
            return str(context.get(var_name, match.group(0)))

        return re.sub(r'\$\{(\w+)\}', replace_var, text)

    def _evaluate_condition(self, condition: Dict[str, Any], context: Dict[str, Any]) -> bool:
        """评估条件表达式"""
        condition_type = condition.get("type", "")

        if condition_type == "equals":
            left = condition.get("left")
            right = condition.get("right")

            # 解析变量
            if isinstance(left, str) and left.startswith("$"):
                left = context.get(left[1:])
            if isinstance(right, str) and right.startswith("$"):
                right = context.get(right[1:])

            return left == right

        elif condition_type == "greater_than":
            left = condition.get("left")
            right = condition.get("right")

            if isinstance(left, str) and left.startswith("$"):
                left = context.get(left[1:])
            if isinstance(right, str) and right.startswith("$"):
                right = context.get(right[1:])

            try:
                return float(left) > float(right)
            except (ValueError, TypeError):
                return False

        elif condition_type == "contains":
            container = condition.get("container")
            item = condition.get("item")

            if isinstance(container, str) and container.startswith("$"):
                container = context.get(container[1:])
            if isinstance(item, str) and item.startswith("$"):
                item = context.get(item[1:])

            return item in container if container else False

        return True  # 默认返回True

    async def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """获取执行状态"""
        execution = self.executions.get(execution_id)
        if not execution:
            return None

        workflow = self.workflows.get(execution.workflow_id)

        return {
            "execution_id": execution_id,
            "workflow_id": execution.workflow_id,
            "workflow_name": workflow.name if workflow else "Unknown",
            "status": execution.status.value,
            "current_task_id": execution.current_task_id,
            "completed_tasks": len(execution.completed_tasks),
            "failed_tasks": len(execution.failed_tasks),
            "total_tasks": len(workflow.tasks) if workflow else 0,
            "started_at": execution.started_at.isoformat(),
            "completed_at": execution.completed_at.isoformat() if execution.completed_at else None,
            "error_message": execution.error_message
        }

    async def cancel_execution(self, execution_id: str) -> bool:
        """取消执行"""
        try:
            execution = self.executions.get(execution_id)
            if not execution:
                return False

            if execution.status == WorkflowStatus.RUNNING:
                execution.status = WorkflowStatus.CANCELLED
                execution.completed_at = datetime.now()

                workflow = self.workflows.get(execution.workflow_id)
                if workflow:
                    workflow.status = WorkflowStatus.CANCELLED
                    workflow.completed_at = datetime.now()

                self.running_executions.discard(execution_id)

                self.logger.info(f"工作流执行已取消: {execution_id}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"取消执行失败: {e}")
            return False

    async def pause_execution(self, execution_id: str) -> bool:
        """暂停执行"""
        try:
            execution = self.executions.get(execution_id)
            if not execution:
                return False

            if execution.status == WorkflowStatus.RUNNING:
                execution.status = WorkflowStatus.PAUSED

                workflow = self.workflows.get(execution.workflow_id)
                if workflow:
                    workflow.status = WorkflowStatus.PAUSED

                self.logger.info(f"工作流执行已暂停: {execution_id}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"暂停执行失败: {e}")
            return False

    async def resume_execution(self, execution_id: str) -> bool:
        """恢复执行"""
        try:
            execution = self.executions.get(execution_id)
            if not execution:
                return False

            if execution.status == WorkflowStatus.PAUSED:
                execution.status = WorkflowStatus.RUNNING

                workflow = self.workflows.get(execution.workflow_id)
                if workflow:
                    workflow.status = WorkflowStatus.RUNNING

                # 重新启动执行
                asyncio.create_task(self._execute_workflow_async(execution_id))

                self.logger.info(f"工作流执行已恢复: {execution_id}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"恢复执行失败: {e}")
            return False

    async def validate_workflow(self, workflow: Workflow) -> Dict[str, Any]:
        """验证工作流"""
        try:
            validation_result = {
                "valid": True,
                "errors": [],
                "warnings": []
            }

            # 检查是否有任务
            if not workflow.tasks:
                validation_result["valid"] = False
                validation_result["errors"].append("工作流必须包含至少一个任务")
                return validation_result

            # 检查任务依赖
            task_ids = set(workflow.tasks.keys())
            for task_id, task in workflow.tasks.items():
                for dep_id in task.dependencies:
                    if dep_id not in task_ids:
                        validation_result["valid"] = False
                        validation_result["errors"].append(f"任务 {task_id} 依赖不存在的任务 {dep_id}")

            # 检查循环依赖
            if self._has_circular_dependency(workflow):
                validation_result["valid"] = False
                validation_result["errors"].append("工作流存在循环依赖")

            # 检查起始任务
            start_tasks = self._find_start_tasks(workflow)
            if not start_tasks:
                validation_result["warnings"].append("没有找到起始任务（无依赖的任务）")

            return validation_result

        except Exception as e:
            return {
                "valid": False,
                "errors": [f"工作流验证失败: {e}"],
                "warnings": []
            }

    def _has_circular_dependency(self, workflow: Workflow) -> bool:
        """检查是否存在循环依赖"""
        def has_cycle(task_id: str, visited: Set[str], rec_stack: Set[str]) -> bool:
            visited.add(task_id)
            rec_stack.add(task_id)

            task = workflow.tasks.get(task_id)
            if task:
                for dep_id in task.dependencies:
                    if dep_id not in visited:
                        if has_cycle(dep_id, visited, rec_stack):
                            return True
                    elif dep_id in rec_stack:
                        return True

            rec_stack.remove(task_id)
            return False

        visited = set()
        for task_id in workflow.tasks:
            if task_id not in visited:
                if has_cycle(task_id, visited, set()):
                    return True

        return False

    async def get_workflow_statistics(self, workflow_id: str) -> Dict[str, Any]:
        """获取工作流统计信息"""
        workflow = self.workflows.get(workflow_id)
        if not workflow:
            return {}

        # 统计执行次数
        executions = [e for e in self.executions.values() if e.workflow_id == workflow_id]

        completed_executions = [e for e in executions if e.status == WorkflowStatus.COMPLETED]
        failed_executions = [e for e in executions if e.status == WorkflowStatus.FAILED]

        # 计算平均执行时间
        avg_execution_time = 0
        if completed_executions:
            total_time = sum(
                (e.completed_at - e.started_at).total_seconds()
                for e in completed_executions
                if e.completed_at
            )
            avg_execution_time = total_time / len(completed_executions)

        return {
            "workflow_id": workflow_id,
            "workflow_name": workflow.name,
            "total_tasks": len(workflow.tasks),
            "total_executions": len(executions),
            "completed_executions": len(completed_executions),
            "failed_executions": len(failed_executions),
            "success_rate": len(completed_executions) / len(executions) if executions else 0,
            "average_execution_time": avg_execution_time,
            "created_at": workflow.created_at.isoformat(),
            "last_updated": workflow.updated_at.isoformat()
        }
