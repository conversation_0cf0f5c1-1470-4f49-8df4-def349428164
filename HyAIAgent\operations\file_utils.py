"""
文件工具模块
提供文件处理和格式化工具
"""

import os
import hashlib
import mimetypes
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class FileUtils:
    """
    文件处理工具类
    
    提供以下功能：
    - 文件大小格式化
    - 文件类型检测和判断
    - 文件哈希计算
    - 文件时间处理
    - 扩展名处理
    """
    
    # 文件类型分类
    FILE_CATEGORIES = {
        'text': ['.txt', '.md', '.rst', '.log', '.csv', '.tsv'],
        'code': ['.py', '.js', '.html', '.css', '.json', '.xml', '.yaml', '.yml', '.sql'],
        'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.ico', '.webp'],
        'document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.odt', '.ods'],
        'archive': ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz'],
        'audio': ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a'],
        'video': ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'],
        'executable': ['.exe', '.msi', '.deb', '.rpm', '.dmg', '.app'],
        'config': ['.ini', '.conf', '.cfg', '.properties', '.toml']
    }
    
    # 危险文件扩展名
    DANGEROUS_EXTENSIONS = [
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
        '.sh', '.bash', '.zsh', '.fish', '.ps1', '.psm1', '.psd1',
        '.msi', '.msp', '.mst', '.deb', '.rpm', '.dmg', '.pkg'
    ]
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小为人类可读格式
        
        Args:
            size_bytes (int): 文件大小（字节）
            
        Returns:
            str: 格式化后的文件大小
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB", "PB"]
        size_index = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and size_index < len(size_names) - 1:
            size /= 1024.0
            size_index += 1
        
        if size_index == 0:
            return f"{int(size)} {size_names[size_index]}"
        else:
            return f"{size:.1f} {size_names[size_index]}"
    
    @staticmethod
    def get_file_extension(file_path: Union[str, Path]) -> str:
        """
        获取文件扩展名（小写）
        
        Args:
            file_path (Union[str, Path]): 文件路径
            
        Returns:
            str: 文件扩展名（包含点号）
        """
        try:
            return Path(file_path).suffix.lower()
        except Exception:
            return ""
    
    @staticmethod
    def get_file_category(file_path: Union[str, Path]) -> str:
        """
        获取文件类别
        
        Args:
            file_path (Union[str, Path]): 文件路径
            
        Returns:
            str: 文件类别
        """
        extension = FileUtils.get_file_extension(file_path)
        
        for category, extensions in FileUtils.FILE_CATEGORIES.items():
            if extension in extensions:
                return category
        
        return "other"
    
    @staticmethod
    def is_text_file(file_path: Union[str, Path]) -> bool:
        """
        判断是否为文本文件
        
        Args:
            file_path (Union[str, Path]): 文件路径
            
        Returns:
            bool: 是否为文本文件
        """
        extension = FileUtils.get_file_extension(file_path)
        text_extensions = FileUtils.FILE_CATEGORIES['text'] + FileUtils.FILE_CATEGORIES['code'] + FileUtils.FILE_CATEGORIES['config']
        return extension in text_extensions
    
    @staticmethod
    def is_image_file(file_path: Union[str, Path]) -> bool:
        """
        判断是否为图片文件
        
        Args:
            file_path (Union[str, Path]): 文件路径
            
        Returns:
            bool: 是否为图片文件
        """
        extension = FileUtils.get_file_extension(file_path)
        return extension in FileUtils.FILE_CATEGORIES['image']
    
    @staticmethod
    def is_dangerous_file(file_path: Union[str, Path]) -> bool:
        """
        判断是否为危险文件类型
        
        Args:
            file_path (Union[str, Path]): 文件路径
            
        Returns:
            bool: 是否为危险文件
        """
        extension = FileUtils.get_file_extension(file_path)
        return extension in FileUtils.DANGEROUS_EXTENSIONS
    
    @staticmethod
    def get_mime_type(file_path: Union[str, Path]) -> str:
        """
        获取文件MIME类型
        
        Args:
            file_path (Union[str, Path]): 文件路径
            
        Returns:
            str: MIME类型
        """
        try:
            mime_type, _ = mimetypes.guess_type(str(file_path))
            return mime_type or "application/octet-stream"
        except Exception:
            return "application/octet-stream"
    
    @staticmethod
    def calculate_file_hash(file_path: Union[str, Path], algorithm: str = "md5") -> Optional[str]:
        """
        计算文件哈希值
        
        Args:
            file_path (Union[str, Path]): 文件路径
            algorithm (str): 哈希算法 (md5, sha1, sha256)
            
        Returns:
            Optional[str]: 文件哈希值，失败时返回None
        """
        try:
            if algorithm.lower() == "md5":
                hash_obj = hashlib.md5()
            elif algorithm.lower() == "sha1":
                hash_obj = hashlib.sha1()
            elif algorithm.lower() == "sha256":
                hash_obj = hashlib.sha256()
            else:
                logger.warning(f"Unsupported hash algorithm: {algorithm}")
                return None
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            logger.error(f"Failed to calculate hash for {file_path}: {e}")
            return None
    
    @staticmethod
    def get_file_info(file_path: Union[str, Path]) -> Dict[str, any]:
        """
        获取文件详细信息
        
        Args:
            file_path (Union[str, Path]): 文件路径
            
        Returns:
            Dict[str, any]: 文件信息字典
        """
        try:
            path_obj = Path(file_path)
            
            if not path_obj.exists():
                return {"error": "File does not exist"}
            
            stat = path_obj.stat()
            
            info = {
                "name": path_obj.name,
                "path": str(path_obj.resolve()),
                "size": stat.st_size,
                "size_formatted": FileUtils.format_file_size(stat.st_size),
                "extension": FileUtils.get_file_extension(path_obj),
                "category": FileUtils.get_file_category(path_obj),
                "mime_type": FileUtils.get_mime_type(path_obj),
                "is_text": FileUtils.is_text_file(path_obj),
                "is_image": FileUtils.is_image_file(path_obj),
                "is_dangerous": FileUtils.is_dangerous_file(path_obj),
                "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "accessed": datetime.fromtimestamp(stat.st_atime).isoformat(),
                "is_readable": os.access(path_obj, os.R_OK),
                "is_writable": os.access(path_obj, os.W_OK),
                "is_executable": os.access(path_obj, os.X_OK)
            }
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {e}")
            return {"error": str(e)}
    
    @staticmethod
    def compare_files(file1: Union[str, Path], file2: Union[str, Path]) -> Dict[str, any]:
        """
        比较两个文件
        
        Args:
            file1 (Union[str, Path]): 第一个文件路径
            file2 (Union[str, Path]): 第二个文件路径
            
        Returns:
            Dict[str, any]: 比较结果
        """
        try:
            path1 = Path(file1)
            path2 = Path(file2)
            
            if not path1.exists() or not path2.exists():
                return {"error": "One or both files do not exist"}
            
            stat1 = path1.stat()
            stat2 = path2.stat()
            
            # 基本比较
            result = {
                "files": [str(path1), str(path2)],
                "same_size": stat1.st_size == stat2.st_size,
                "size_difference": abs(stat1.st_size - stat2.st_size),
                "same_modified_time": stat1.st_mtime == stat2.st_mtime,
                "time_difference": abs(stat1.st_mtime - stat2.st_mtime)
            }
            
            # 如果大小相同，计算哈希值比较内容
            if result["same_size"]:
                hash1 = FileUtils.calculate_file_hash(path1)
                hash2 = FileUtils.calculate_file_hash(path2)
                
                if hash1 and hash2:
                    result["same_content"] = hash1 == hash2
                    result["hash1"] = hash1
                    result["hash2"] = hash2
                else:
                    result["same_content"] = None
                    result["hash_error"] = "Failed to calculate hash"
            else:
                result["same_content"] = False
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to compare files {file1} and {file2}: {e}")
            return {"error": str(e)}
    
    @staticmethod
    def get_files_by_extension(directory: Union[str, Path], extension: str, recursive: bool = False) -> List[str]:
        """
        获取指定扩展名的所有文件
        
        Args:
            directory (Union[str, Path]): 目录路径
            extension (str): 文件扩展名（包含点号）
            recursive (bool): 是否递归搜索
            
        Returns:
            List[str]: 文件路径列表
        """
        try:
            dir_path = Path(directory)
            
            if not dir_path.exists() or not dir_path.is_dir():
                return []
            
            extension = extension.lower()
            if not extension.startswith('.'):
                extension = '.' + extension
            
            files = []
            
            if recursive:
                pattern = f"**/*{extension}"
                for file_path in dir_path.glob(pattern):
                    if file_path.is_file():
                        files.append(str(file_path))
            else:
                pattern = f"*{extension}"
                for file_path in dir_path.glob(pattern):
                    if file_path.is_file():
                        files.append(str(file_path))
            
            return sorted(files)
            
        except Exception as e:
            logger.error(f"Failed to get files by extension in {directory}: {e}")
            return []
    
    @staticmethod
    def get_directory_size(directory: Union[str, Path]) -> int:
        """
        计算目录总大小
        
        Args:
            directory (Union[str, Path]): 目录路径
            
        Returns:
            int: 目录大小（字节）
        """
        try:
            dir_path = Path(directory)
            
            if not dir_path.exists() or not dir_path.is_dir():
                return 0
            
            total_size = 0
            for file_path in dir_path.rglob('*'):
                if file_path.is_file():
                    try:
                        total_size += file_path.stat().st_size
                    except (OSError, IOError):
                        # 跳过无法访问的文件
                        continue
            
            return total_size
            
        except Exception as e:
            logger.error(f"Failed to calculate directory size for {directory}: {e}")
            return 0
    
    @staticmethod
    def clean_filename_for_url(filename: str) -> str:
        """
        清理文件名以适用于URL
        
        Args:
            filename (str): 原始文件名
            
        Returns:
            str: 清理后的文件名
        """
        if not filename:
            return "unnamed"
        
        # 移除或替换URL不安全字符
        import urllib.parse
        
        # 先进行基本清理
        cleaned = filename.replace(' ', '_')
        cleaned = ''.join(c for c in cleaned if c.isalnum() or c in '._-')
        
        # URL编码
        cleaned = urllib.parse.quote(cleaned, safe='._-')
        
        # 确保不为空
        if not cleaned:
            cleaned = "unnamed"
        
        return cleaned
