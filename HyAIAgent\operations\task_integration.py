"""
任务系统集成模块

本模块实现了文件操作功能与任务管理系统的集成，
提供基于任务的文件操作执行器和智能文件处理任务。
"""

import asyncio
import logging
import json
from typing import Dict, Any, List, Optional, Union, Callable
from datetime import datetime
from pathlib import Path

from core.task_models import Task, ExecutionResult, TaskStatus, TaskType
from core.task_manager import TaskManager
from core.execution_engine import ExecutionEngine
from .security_manager import SecurityManager
from .file_operations import FileOperations
from .batch_processor import BatchProcessor
from .document_processor import DocumentProcessor
from .text_analyzer import TextAnalyzer
from .config_processor import ConfigProcessor

logger = logging.getLogger(__name__)


class FileOperationTaskExecutor:
    """文件操作任务执行器
    
    将文件操作功能集成到任务系统中，支持基于任务的文件处理。
    """
    
    def __init__(self, workspace_path: str = "./workspace"):
        """初始化文件操作任务执行器
        
        Args:
            workspace_path: 工作目录路径
        """
        self.workspace_path = Path(workspace_path)
        
        # 初始化核心组件
        self.security_manager = SecurityManager(str(self.workspace_path))
        self.file_operations = FileOperations(str(self.workspace_path), self.security_manager)
        self.batch_processor = BatchProcessor(str(self.workspace_path), self.security_manager, self.file_operations)
        self.document_processor = DocumentProcessor(str(self.workspace_path), self.security_manager, self.file_operations)
        self.text_analyzer = TextAnalyzer(str(self.workspace_path), self.security_manager, self.file_operations)
        self.config_processor = ConfigProcessor(str(self.workspace_path))
        
        # 任务执行器映射
        self.task_executors = {
            "file_read": self._execute_file_read_task,
            "file_write": self._execute_file_write_task,
            "file_delete": self._execute_file_delete_task,
            "file_search": self._execute_file_search_task,
            "file_list": self._execute_file_list_task,
            "batch_rename": self._execute_batch_rename_task,
            "batch_copy": self._execute_batch_copy_task,
            "batch_move": self._execute_batch_move_task,
            "batch_delete": self._execute_batch_delete_task,
            "batch_process_content": self._execute_batch_process_content_task,
            "batch_checksum": self._execute_batch_checksum_task,
            "document_extract": self._execute_document_extract_task,
            "document_convert": self._execute_document_convert_task,
            "text_analyze": self._execute_text_analyze_task,
            "config_read": self._execute_config_read_task,
            "config_write": self._execute_config_write_task,
        }
        
        logger.info(f"FileOperationTaskExecutor initialized with workspace: {self.workspace_path}")
    
    async def execute_task(self, task: Task) -> ExecutionResult:
        """执行文件操作任务
        
        Args:
            task: 要执行的任务
            
        Returns:
            ExecutionResult: 执行结果
        """
        start_time = datetime.now()
        
        try:
            # 解析任务参数
            task_params = task.input_data or {}
            operation_type = task_params.get("operation_type")
            
            if not operation_type:
                raise ValueError("Missing operation_type in task parameters")
            
            if operation_type not in self.task_executors:
                raise ValueError(f"Unsupported operation type: {operation_type}")
            
            # 执行对应的操作
            executor = self.task_executors[operation_type]
            result_data = await executor(task_params)

            # 检查操作是否成功
            operation_success = result_data.get("success", False)

            if operation_success:
                # 创建成功结果
                execution_result = ExecutionResult(
                    task_id=task.id,
                    status=TaskStatus.COMPLETED,
                    success=True,
                    result_data=result_data,
                    error_message=None
                )
                logger.info(f"Task {task.id} executed successfully: {operation_type}")
            else:
                # 创建失败结果
                error_msg = result_data.get("error", "Operation failed")
                execution_result = ExecutionResult(
                    task_id=task.id,
                    status=TaskStatus.FAILED,
                    success=False,
                    result_data=result_data,
                    error_message=error_msg
                )
                logger.warning(f"Task {task.id} failed: {error_msg}")

            return execution_result
            
        except Exception as e:
            error_msg = f"Failed to execute task {task.id}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            
            # 创建失败结果
            execution_result = ExecutionResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                success=False,
                result_data={},
                error_message=error_msg
            )
            
            return execution_result
    
    async def _execute_file_read_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行文件读取任务"""
        file_path = params.get("file_path")
        encoding = params.get("encoding")
        
        if not file_path:
            raise ValueError("Missing file_path parameter")
        
        result = await self.file_operations.read_file(file_path, encoding)
        return result
    
    async def _execute_file_write_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行文件写入任务"""
        file_path = params.get("file_path")
        content = params.get("content")
        encoding = params.get("encoding", "utf-8")
        mode = params.get("mode", "w")
        
        if not file_path or content is None:
            raise ValueError("Missing file_path or content parameter")
        
        result = await self.file_operations.write_file(file_path, content, encoding, mode)
        return result
    
    async def _execute_file_delete_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行文件删除任务"""
        file_path = params.get("file_path")
        
        if not file_path:
            raise ValueError("Missing file_path parameter")
        
        result = await self.file_operations.delete_file(file_path)
        return result
    
    async def _execute_file_search_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行文件搜索任务"""
        query = params.get("query")
        search_options = params.get("search_options", {})
        
        if not query:
            raise ValueError("Missing query parameter")
        
        result = await self.file_operations.advanced_search_files(query, search_options)
        return result
    
    async def _execute_file_list_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行文件列表任务"""
        directory = params.get("directory", "")
        pattern = params.get("pattern", "*")
        recursive = params.get("recursive", False)
        
        result = await self.file_operations.list_files(directory, pattern, recursive)
        return result
    
    async def _execute_batch_rename_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行批量重命名任务"""
        file_patterns = params.get("file_patterns", [])
        rename_rule = params.get("rename_rule", {})
        
        if not file_patterns or not rename_rule:
            raise ValueError("Missing file_patterns or rename_rule parameter")
        
        result = await self.batch_processor.batch_rename_files(file_patterns, rename_rule)
        return result
    
    async def _execute_batch_copy_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行批量复制任务"""
        source_patterns = params.get("source_patterns", [])
        destination_dir = params.get("destination_dir")
        copy_options = params.get("copy_options", {})
        
        if not source_patterns or not destination_dir:
            raise ValueError("Missing source_patterns or destination_dir parameter")
        
        result = await self.batch_processor.batch_copy_files(source_patterns, destination_dir, copy_options)
        return result
    
    async def _execute_batch_move_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行批量移动任务"""
        source_patterns = params.get("source_patterns", [])
        destination_dir = params.get("destination_dir")
        move_options = params.get("move_options", {})
        
        if not source_patterns or not destination_dir:
            raise ValueError("Missing source_patterns or destination_dir parameter")
        
        result = await self.batch_processor.batch_move_files(source_patterns, destination_dir, move_options)
        return result
    
    async def _execute_batch_delete_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行批量删除任务"""
        file_patterns = params.get("file_patterns", [])
        delete_options = params.get("delete_options", {})
        
        if not file_patterns:
            raise ValueError("Missing file_patterns parameter")
        
        result = await self.batch_processor.batch_delete_files(file_patterns, delete_options)
        return result
    
    async def _execute_batch_process_content_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行批量内容处理任务"""
        file_patterns = params.get("file_patterns", [])
        processor_name = params.get("processor_name")
        process_options = params.get("process_options", {})
        
        if not file_patterns or not processor_name:
            raise ValueError("Missing file_patterns or processor_name parameter")
        
        # 根据处理器名称获取处理函数
        processor_func = self._get_content_processor(processor_name, params)
        
        result = await self.batch_processor.batch_process_content(file_patterns, processor_func, process_options)
        return result
    
    async def _execute_batch_checksum_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行批量校验和计算任务"""
        file_patterns = params.get("file_patterns", [])
        algorithm = params.get("algorithm", "md5")
        
        if not file_patterns:
            raise ValueError("Missing file_patterns parameter")
        
        result = await self.batch_processor.batch_calculate_checksums(file_patterns, algorithm)
        return result
    
    async def _execute_document_extract_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行文档提取任务"""
        file_path = params.get("file_path")
        extract_options = params.get("extract_options", {})
        
        if not file_path:
            raise ValueError("Missing file_path parameter")
        
        result = await self.document_processor.extract_content(file_path, extract_options)
        return result
    
    async def _execute_document_convert_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行文档转换任务"""
        source_path = params.get("source_path")
        target_format = params.get("target_format")
        convert_options = params.get("convert_options", {})
        
        if not source_path or not target_format:
            raise ValueError("Missing source_path or target_format parameter")
        
        result = await self.document_processor.convert_format(source_path, target_format, convert_options)
        return result
    
    async def _execute_text_analyze_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行文本分析任务"""
        text_input = params.get("text_input")
        file_path = params.get("file_path")
        analysis_options = params.get("analysis_options", {})
        
        if not text_input and not file_path:
            raise ValueError("Missing text_input or file_path parameter")
        
        if text_input:
            result = await self.text_analyzer.analyze_text(text_input, analysis_options)
        else:
            result = await self.text_analyzer.analyze_file(file_path, analysis_options)
        
        return result
    
    async def _execute_config_read_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行配置读取任务"""
        config_path = params.get("config_path")
        config_format = params.get("config_format")
        
        if not config_path:
            raise ValueError("Missing config_path parameter")
        
        result = await self.config_processor.read_config(config_path, config_format)
        return result
    
    async def _execute_config_write_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行配置写入任务"""
        config_path = params.get("config_path")
        config_data = params.get("config_data")
        config_format = params.get("config_format")
        write_options = params.get("write_options", {})
        
        if not config_path or config_data is None:
            raise ValueError("Missing config_path or config_data parameter")
        
        result = await self.config_processor.write_config(config_path, config_data, config_format, write_options)
        return result
    
    def _get_content_processor(self, processor_name: str, params: Dict[str, Any]) -> Callable[[str], str]:
        """获取内容处理函数
        
        Args:
            processor_name: 处理器名称
            params: 任务参数
            
        Returns:
            Callable: 内容处理函数
        """
        if processor_name == "uppercase":
            return lambda content: content.upper()
        elif processor_name == "lowercase":
            return lambda content: content.lower()
        elif processor_name == "replace":
            old_text = params.get("old_text", "")
            new_text = params.get("new_text", "")
            return lambda content: content.replace(old_text, new_text)
        elif processor_name == "strip":
            return lambda content: content.strip()
        elif processor_name == "add_prefix":
            prefix = params.get("prefix", "")
            return lambda content: prefix + content
        elif processor_name == "add_suffix":
            suffix = params.get("suffix", "")
            return lambda content: content + suffix
        else:
            raise ValueError(f"Unknown processor: {processor_name}")


class TaskSystemIntegrator:
    """任务系统集成器

    提供文件操作功能与任务管理系统的完整集成，
    支持智能任务分解、执行计划创建和进度监控。
    """

    def __init__(self, task_manager: TaskManager, execution_engine: ExecutionEngine,
                 workspace_path: str = "./workspace"):
        """初始化任务系统集成器

        Args:
            task_manager: 任务管理器实例
            execution_engine: 执行引擎实例
            workspace_path: 工作目录路径
        """
        self.task_manager = task_manager
        self.execution_engine = execution_engine
        self.workspace_path = Path(workspace_path)

        # 初始化文件操作任务执行器
        self.file_task_executor = FileOperationTaskExecutor(str(self.workspace_path))

        # 注册文件操作任务执行器到执行引擎
        self._register_file_operation_executors()

        logger.info("TaskSystemIntegrator initialized successfully")

    def _register_file_operation_executors(self):
        """注册文件操作任务执行器到执行引擎"""
        # 扩展执行引擎的任务执行器
        if hasattr(self.execution_engine, 'task_executors'):
            # 添加文件操作类型
            self.execution_engine.task_executors[TaskType.FILE_OPERATION] = self._execute_file_operation_task

        logger.info("File operation executors registered to execution engine")

    async def _execute_file_operation_task(self, task: Task) -> ExecutionResult:
        """执行文件操作任务的包装器"""
        return await self.file_task_executor.execute_task(task)

    async def create_file_operation_task(self, operation_type: str, parameters: Dict[str, Any],
                                       description: str = "", priority: str = "NORMAL") -> Task:
        """创建文件操作任务

        Args:
            operation_type: 操作类型
            parameters: 操作参数
            description: 任务描述
            priority: 任务优先级

        Returns:
            Task: 创建的任务
        """
        from core.task_models import TaskPriority

        # 构建任务参数
        task_params = {
            "operation_type": operation_type,
            **parameters
        }

        # 创建任务
        task = Task(
            description=description or f"Execute {operation_type} operation",
            task_type=TaskType.FILE_OPERATION,
            priority=getattr(TaskPriority, priority.upper(), TaskPriority.NORMAL),
            input_data=task_params,
            dependencies=[],
            estimated_duration=5 * 60  # 默认估计5分钟（秒）
        )

        return task

    async def create_batch_file_operation_plan(self, operations: List[Dict[str, Any]],
                                             plan_name: str = "Batch File Operations",
                                             parallel_execution: bool = True) -> str:
        """创建批量文件操作执行计划

        Args:
            operations: 操作列表，每个操作包含operation_type和parameters
            plan_name: 计划名称
            parallel_execution: 是否并行执行

        Returns:
            str: 执行计划ID
        """
        tasks = []

        # 为每个操作创建任务
        for i, operation in enumerate(operations):
            operation_type = operation.get("operation_type")
            parameters = operation.get("parameters", {})
            description = operation.get("description", f"Operation {i+1}: {operation_type}")
            priority = operation.get("priority", "NORMAL")

            task = await self.create_file_operation_task(
                operation_type, parameters, description, priority
            )
            tasks.append(task)

        # 创建执行计划
        execution_plan = await self.task_manager.create_execution_plan(
            tasks, plan_name, parallel_execution
        )

        return execution_plan.id

    async def execute_file_operation_plan(self, plan_id: str) -> ExecutionResult:
        """执行文件操作计划

        Args:
            plan_id: 执行计划ID

        Returns:
            ExecutionResult: 执行结果
        """
        # 获取执行计划
        if plan_id not in self.task_manager.active_plans:
            raise ValueError(f"Plan {plan_id} not found")

        plan = self.task_manager.active_plans[plan_id]

        # 实际执行计划中的所有任务
        try:
            logger.info(f"开始执行文件操作计划: {plan_id}")

            # 执行所有任务
            task_results = []
            for task in plan.tasks:
                task_result = await self.file_task_executor.execute_task(task)
                task_results.append(task_result)

            # 检查所有任务是否成功
            all_success = all(result.success for result in task_results)

            # 创建总体结果
            overall_result = ExecutionResult(
                plan_id=plan_id,
                status=TaskStatus.COMPLETED if all_success else TaskStatus.FAILED,
                success=all_success,
                result_data={
                    "plan_id": plan_id,
                    "task_count": len(plan.tasks),
                    "successful_tasks": sum(1 for r in task_results if r.success),
                    "failed_tasks": sum(1 for r in task_results if not r.success),
                    "task_results": [r.result_data for r in task_results]
                },
                error_message=None if all_success else "Some tasks failed"
            )

            logger.info(f"文件操作计划执行完成: {plan_id}, 成功: {all_success}")
            return overall_result

        except Exception as e:
            logger.error(f"执行文件操作计划失败: {plan_id}, 错误: {str(e)}")
            return ExecutionResult(
                plan_id=plan_id,
                status=TaskStatus.FAILED,
                success=False,
                result_data={},
                error_message=str(e)
            )

    async def monitor_file_operation_progress(self, plan_id: str) -> Dict[str, Any]:
        """监控文件操作进度

        Args:
            plan_id: 执行计划ID

        Returns:
            Dict[str, Any]: 进度信息
        """
        progress_info = await self.task_manager.monitor_progress(plan_id)

        # 转换为字典格式
        progress_dict = {
            "plan_id": plan_id,
            "total_tasks": progress_info.total_tasks,
            "completed_tasks": progress_info.completed_tasks,
            "failed_tasks": progress_info.failed_tasks,
            "progress_percentage": progress_info.overall_progress,
            "estimated_remaining_time": progress_info.estimated_remaining_time,
            "current_task": progress_info.current_task.to_dict() if progress_info.current_task else None,
            "start_time": progress_info.start_time.isoformat() if progress_info.start_time else None,
            "last_update": progress_info.last_update_time.isoformat() if progress_info.last_update_time else None
        }

        return progress_dict

    async def get_file_operation_stats(self) -> Dict[str, Any]:
        """获取文件操作统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        # 获取文件操作统计
        file_stats = self.file_task_executor.file_operations.get_operation_stats()
        batch_stats = self.file_task_executor.batch_processor.get_processing_stats()

        # 合并统计信息
        combined_stats = {
            "file_operations": file_stats,
            "batch_processing": batch_stats,
            "workspace_path": str(self.workspace_path),
            "total_registered_executors": len(self.file_task_executor.task_executors),
            "available_operations": list(self.file_task_executor.task_executors.keys())
        }

        return combined_stats

    def get_supported_operations(self) -> List[Dict[str, Any]]:
        """获取支持的操作列表

        Returns:
            List[Dict[str, Any]]: 支持的操作列表
        """
        operations = []

        operation_descriptions = {
            "file_read": "读取文件内容",
            "file_write": "写入文件内容",
            "file_delete": "删除文件",
            "file_search": "搜索文件",
            "file_list": "列出文件",
            "batch_rename": "批量重命名文件",
            "batch_copy": "批量复制文件",
            "batch_move": "批量移动文件",
            "batch_delete": "批量删除文件",
            "batch_process_content": "批量处理文件内容",
            "batch_checksum": "批量计算文件校验和",
            "document_extract": "提取文档内容",
            "document_convert": "转换文档格式",
            "text_analyze": "分析文本内容",
            "config_read": "读取配置文件",
            "config_write": "写入配置文件"
        }

        for operation_type, description in operation_descriptions.items():
            operations.append({
                "operation_type": operation_type,
                "description": description,
                "category": self._get_operation_category(operation_type)
            })

        return operations

    def _get_operation_category(self, operation_type: str) -> str:
        """获取操作类别"""
        if operation_type.startswith("file_"):
            return "基础文件操作"
        elif operation_type.startswith("batch_"):
            return "批量文件处理"
        elif operation_type.startswith("document_"):
            return "文档处理"
        elif operation_type.startswith("text_"):
            return "文本分析"
        elif operation_type.startswith("config_"):
            return "配置管理"
        else:
            return "其他操作"
