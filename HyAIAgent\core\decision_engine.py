"""
决策引擎模块

本模块实现了AI决策引擎，负责执行决策、策略调整和智能判断。
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
from enum import Enum

from .task_models import Task, ExecutionResult, TaskStatus, TaskType
from .ai_client import Simple<PERSON><PERSON><PERSON>
from .prompt_manager import PromptManager

logger = logging.getLogger(__name__)


class DecisionType(Enum):
    """决策类型枚举"""
    CONTINUE_EXECUTION = "continue_execution"
    PAUSE_EXECUTION = "pause_execution"
    RETRY_TASK = "retry_task"
    SKIP_TASK = "skip_task"
    ADJUST_STRATEGY = "adjust_strategy"
    ESCALATE_ISSUE = "escalate_issue"


class Action(Enum):
    """行动类型枚举"""
    EXECUTE_NEXT_TASK = "execute_next_task"
    RETRY_CURRENT_TASK = "retry_current_task"
    PAUSE_EXECUTION = "pause_execution"
    ABORT_EXECUTION = "abort_execution"
    REQUEST_USER_INPUT = "request_user_input"
    ADJUST_PARAMETERS = "adjust_parameters"


class ExecutionContext:
    """执行上下文类"""
    
    def __init__(self):
        self.current_task: Optional[Task] = None
        self.completed_tasks: List[Task] = []
        self.failed_tasks: List[Task] = []
        self.execution_results: List[ExecutionResult] = []
        self.global_variables: Dict[str, Any] = {}
        self.error_history: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, float] = {}
        self.user_feedback: List[Dict[str, Any]] = []
        self.start_time: datetime = datetime.now()
        self.last_update: datetime = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'current_task': self.current_task.to_dict() if self.current_task else None,
            'completed_tasks_count': len(self.completed_tasks),
            'failed_tasks_count': len(self.failed_tasks),
            'execution_results_count': len(self.execution_results),
            'global_variables': self.global_variables,
            'error_history': self.error_history,
            'performance_metrics': self.performance_metrics,
            'user_feedback': self.user_feedback,
            'start_time': self.start_time.isoformat(),
            'last_update': self.last_update.isoformat()
        }


class ExecutionFeedback:
    """执行反馈类"""
    
    def __init__(self, task_id: str, success: bool, message: str, 
                 metrics: Optional[Dict[str, float]] = None):
        self.task_id = task_id
        self.success = success
        self.message = message
        self.metrics = metrics or {}
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'task_id': self.task_id,
            'success': self.success,
            'message': self.message,
            'metrics': self.metrics,
            'timestamp': self.timestamp.isoformat()
        }


class DecisionEngine:
    """AI决策引擎
    
    负责执行过程中的智能决策，包括是否继续执行、选择下一步行动、
    策略调整等关键决策。
    """
    
    def __init__(self, ai_client: SimpleAIClient, prompt_manager: PromptManager):
        """初始化决策引擎
        
        Args:
            ai_client: AI客户端实例
            prompt_manager: 提示词管理器实例
        """
        self.ai_client = ai_client
        self.prompt_manager = prompt_manager
        
        # 决策历史
        self.decision_history: List[Dict[str, Any]] = []
        
        # 决策规则配置
        self.decision_rules = {
            'max_consecutive_failures': 3,
            'max_execution_time': 3600,  # 1小时
            'min_success_rate': 0.5,
            'auto_retry_enabled': True,
            'escalation_threshold': 5
        }
        
        # 性能指标
        self.performance_tracker = {
            'decisions_made': 0,
            'successful_decisions': 0,
            'strategy_adjustments': 0,
            'escalations': 0
        }
        
        logger.info("DecisionEngine initialized successfully")
    
    async def should_continue(self, context: ExecutionContext) -> bool:
        """判断是否继续执行
        
        Args:
            context: 执行上下文
            
        Returns:
            bool: 是否应该继续执行
        """
        try:
            logger.info("开始执行继续判断决策")
            
            # 基础规则检查
            if not self._check_basic_continuation_rules(context):
                logger.info("基础规则检查未通过，停止执行")
                return False
            
            # AI智能决策
            decision_prompt = self.prompt_manager.get_global_prompt("decision_maker")
            if not decision_prompt:
                decision_prompt = self._get_default_decision_prompt()
            
            # 构建决策请求
            decision_request = f"""
当前执行上下文：
{json.dumps(context.to_dict(), ensure_ascii=False, indent=2)}

请分析当前情况并决定是否应该继续执行。考虑因素包括：
1. 任务完成情况
2. 错误发生频率
3. 执行效率
4. 资源使用情况

请返回JSON格式的决策结果：
{{
    "should_continue": true/false,
    "reason": "决策原因",
    "confidence": 0.0-1.0,
    "recommendations": ["建议1", "建议2"]
}}
"""
            
            # 调用AI进行决策
            response = await asyncio.to_thread(
                self.ai_client.chat,
                decision_request,
                decision_prompt
            )
            
            # 解析AI决策
            decision = await self._parse_decision_response(response)
            
            # 记录决策
            self._record_decision("should_continue", decision, context)
            
            # 更新性能指标
            self.performance_tracker['decisions_made'] += 1
            if decision.get('should_continue', False):
                self.performance_tracker['successful_decisions'] += 1
            
            logger.info(f"继续执行决策: {decision.get('should_continue', False)}")
            return decision.get('should_continue', False)
            
        except Exception as e:
            logger.error(f"继续执行决策失败: {str(e)}")
            # 默认保守策略：如果决策失败，停止执行
            return False
    
    async def select_next_action(self, context: ExecutionContext) -> Action:
        """选择下一步行动
        
        Args:
            context: 执行上下文
            
        Returns:
            Action: 下一步行动
        """
        try:
            logger.info("开始选择下一步行动")
            
            # 分析当前状态
            if context.current_task and context.current_task.status == TaskStatus.FAILED:
                if context.current_task.retry_count < context.current_task.max_retries:
                    return Action.RETRY_CURRENT_TASK
                else:
                    return Action.EXECUTE_NEXT_TASK
            
            # 检查是否有待执行的任务
            if context.current_task and context.current_task.status == TaskStatus.PENDING:
                return Action.EXECUTE_NEXT_TASK
            
            # AI智能选择
            action_prompt = self._get_action_selection_prompt()
            action_request = f"""
当前执行上下文：
{json.dumps(context.to_dict(), ensure_ascii=False, indent=2)}

请分析当前情况并选择最佳的下一步行动。

可选行动：
- execute_next_task: 执行下一个任务
- retry_current_task: 重试当前任务
- pause_execution: 暂停执行
- abort_execution: 中止执行
- request_user_input: 请求用户输入
- adjust_parameters: 调整参数

请返回JSON格式：
{{
    "action": "action_name",
    "reason": "选择原因",
    "parameters": {{}}
}}
"""
            
            response = await asyncio.to_thread(
                self.ai_client.chat,
                action_request,
                action_prompt
            )
            
            # 解析行动选择
            action_data = await self._parse_action_response(response)
            action = Action(action_data.get('action', 'execute_next_task'))
            
            # 记录决策
            self._record_decision("select_next_action", action_data, context)
            
            logger.info(f"选择的下一步行动: {action.value}")
            return action
            
        except Exception as e:
            logger.error(f"选择下一步行动失败: {str(e)}")
            # 默认行动
            return Action.EXECUTE_NEXT_TASK
    
    async def adjust_strategy(self, feedback: ExecutionFeedback) -> Dict[str, Any]:
        """根据反馈调整策略
        
        Args:
            feedback: 执行反馈
            
        Returns:
            Dict[str, Any]: 策略调整结果
        """
        try:
            logger.info(f"开始策略调整，基于任务 {feedback.task_id} 的反馈")
            
            # 分析反馈
            adjustment = {
                'adjusted': False,
                'changes': [],
                'new_parameters': {},
                'reason': ''
            }
            
            # 基于成功率调整
            if not feedback.success:
                # 增加重试次数
                if 'max_retries' not in self.decision_rules:
                    self.decision_rules['max_retries'] = 3
                else:
                    self.decision_rules['max_retries'] = min(5, self.decision_rules['max_retries'] + 1)
                
                adjustment['adjusted'] = True
                adjustment['changes'].append('增加最大重试次数')
                adjustment['new_parameters']['max_retries'] = self.decision_rules['max_retries']
            
            # 基于性能指标调整
            if feedback.metrics:
                if feedback.metrics.get('execution_time', 0) > 60:  # 超过1分钟
                    # 调整超时时间
                    self.decision_rules['max_execution_time'] = max(
                        self.decision_rules.get('max_execution_time', 3600),
                        int(feedback.metrics['execution_time'] * 1.5)
                    )
                    adjustment['adjusted'] = True
                    adjustment['changes'].append('调整最大执行时间')
            
            # AI智能策略调整
            if adjustment['adjusted']:
                strategy_prompt = self._get_strategy_adjustment_prompt()
                strategy_request = f"""
执行反馈：
{json.dumps(feedback.to_dict(), ensure_ascii=False, indent=2)}

当前决策规则：
{json.dumps(self.decision_rules, ensure_ascii=False, indent=2)}

请分析反馈并提供策略调整建议。
"""
                
                response = await asyncio.to_thread(
                    self.ai_client.chat,
                    strategy_request,
                    strategy_prompt
                )
                
                adjustment['reason'] = response
            
            # 更新性能指标
            if adjustment['adjusted']:
                self.performance_tracker['strategy_adjustments'] += 1
            
            logger.info(f"策略调整完成: {adjustment['adjusted']}")
            return adjustment
            
        except Exception as e:
            logger.error(f"策略调整失败: {str(e)}")
            return {
                'adjusted': False,
                'error': str(e)
            }
    
    def get_decision_stats(self) -> Dict[str, Any]:
        """获取决策统计信息"""
        stats = self.performance_tracker.copy()
        
        if stats['decisions_made'] > 0:
            stats['decision_success_rate'] = stats['successful_decisions'] / stats['decisions_made']
        else:
            stats['decision_success_rate'] = 0.0
        
        stats['total_decisions'] = len(self.decision_history)
        stats['decision_rules'] = self.decision_rules.copy()
        
        return stats
    
    # 私有方法
    
    def _check_basic_continuation_rules(self, context: ExecutionContext) -> bool:
        """检查基础继续执行规则"""
        # 检查连续失败次数
        recent_failures = sum(1 for task in context.failed_tasks[-3:])
        if recent_failures >= self.decision_rules['max_consecutive_failures']:
            return False
        
        # 检查执行时间
        elapsed = (datetime.now() - context.start_time).total_seconds()
        if elapsed > self.decision_rules['max_execution_time']:
            return False
        
        # 检查成功率
        total_tasks = len(context.completed_tasks) + len(context.failed_tasks)
        if total_tasks > 0:
            success_rate = len(context.completed_tasks) / total_tasks
            if success_rate < self.decision_rules['min_success_rate']:
                return False
        
        return True
    
    def _get_default_decision_prompt(self) -> str:
        """获取默认决策提示词"""
        return """
你是一个智能决策助手，负责分析执行上下文并做出是否继续执行的决策。

决策原则：
1. 优先考虑任务完成质量
2. 平衡执行效率和资源消耗
3. 及时识别和处理异常情况
4. 确保系统稳定性

请基于提供的上下文信息做出明智的决策。
"""
    
    def _get_action_selection_prompt(self) -> str:
        """获取行动选择提示词"""
        return """
你是一个智能行动选择助手，负责根据当前执行状态选择最佳的下一步行动。

选择原则：
1. 优先完成当前任务
2. 合理处理失败情况
3. 避免无限循环
4. 及时寻求帮助

请选择最合适的行动方案。
"""
    
    def _get_strategy_adjustment_prompt(self) -> str:
        """获取策略调整提示词"""
        return """
你是一个智能策略调整助手，负责根据执行反馈优化决策策略。

调整原则：
1. 基于实际执行结果
2. 渐进式优化
3. 避免过度调整
4. 保持系统稳定性

请提供合理的策略调整建议。
"""
    
    async def _parse_decision_response(self, response: str) -> Dict[str, Any]:
        """解析决策响应"""
        try:
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
            else:
                json_str = response.strip()
            
            return json.loads(json_str)
        except:
            return {
                'should_continue': True,
                'reason': '解析失败，使用默认决策',
                'confidence': 0.5
            }
    
    async def _parse_action_response(self, response: str) -> Dict[str, Any]:
        """解析行动响应"""
        try:
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
            else:
                json_str = response.strip()
            
            return json.loads(json_str)
        except:
            return {
                'action': 'execute_next_task',
                'reason': '解析失败，使用默认行动'
            }
    
    def _record_decision(self, decision_type: str, decision_data: Dict[str, Any], 
                        context: ExecutionContext):
        """记录决策历史"""
        record = {
            'timestamp': datetime.now().isoformat(),
            'decision_type': decision_type,
            'decision_data': decision_data,
            'context_summary': {
                'completed_tasks': len(context.completed_tasks),
                'failed_tasks': len(context.failed_tasks),
                'current_task_id': context.current_task.id if context.current_task else None
            }
        }
        
        self.decision_history.append(record)
        
        # 保持历史记录在合理范围内
        if len(self.decision_history) > 100:
            self.decision_history = self.decision_history[-50:]
