"""
PathUtils测试模块
测试路径处理工具功能
"""

import unittest
import tempfile
import shutil
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from operations.path_utils import PathUtils


class TestPathUtils(unittest.TestCase):
    """PathUtils测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录作为测试环境
        self.temp_dir = tempfile.mkdtemp()
        self.base_path = os.path.join(self.temp_dir, "workspace")
        os.makedirs(self.base_path, exist_ok=True)
        
        # 创建测试文件和目录
        self.test_file = os.path.join(self.base_path, "test.txt")
        with open(self.test_file, 'w') as f:
            f.write("test content")
        
        self.test_subdir = os.path.join(self.base_path, "subdir")
        os.makedirs(self.test_subdir, exist_ok=True)
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_normalize_path(self):
        """测试路径规范化"""
        # 正常路径
        path = os.path.join(self.base_path, "test.txt")
        normalized = PathUtils.normalize_path(path)
        self.assertTrue(os.path.isabs(normalized))

        # 绝对路径
        abs_path = os.path.abspath("test.txt")
        normalized = PathUtils.normalize_path(abs_path)
        self.assertTrue(os.path.isabs(normalized))

        # 空路径
        self.assertEqual(PathUtils.normalize_path(""), "")
        self.assertEqual(PathUtils.normalize_path(None), "")
    
    def test_is_safe_path(self):
        """测试路径安全检查"""
        # 安全路径
        safe_path = os.path.join(self.base_path, "test.txt")
        self.assertTrue(PathUtils.is_safe_path(safe_path, self.base_path))
        
        # 子目录中的文件
        subdir_file = os.path.join(self.test_subdir, "file.txt")
        self.assertTrue(PathUtils.is_safe_path(subdir_file, self.base_path))
        
        # 不安全路径（工作目录外）
        outside_path = os.path.join(self.temp_dir, "outside.txt")
        self.assertFalse(PathUtils.is_safe_path(outside_path, self.base_path))
        
        # 路径遍历攻击
        traversal_path = os.path.join(self.base_path, "..", "outside.txt")
        self.assertFalse(PathUtils.is_safe_path(traversal_path, self.base_path))
    
    def test_contains_dangerous_patterns(self):
        """测试危险模式检测"""
        # 路径遍历模式
        self.assertTrue(PathUtils.contains_dangerous_patterns("../test.txt"))
        self.assertTrue(PathUtils.contains_dangerous_patterns("dir/../test.txt"))
        self.assertTrue(PathUtils.contains_dangerous_patterns("dir/.."))
        self.assertTrue(PathUtils.contains_dangerous_patterns("~/test.txt"))
        
        # 安全路径
        self.assertFalse(PathUtils.contains_dangerous_patterns("test.txt"))
        self.assertFalse(PathUtils.contains_dangerous_patterns("dir/test.txt"))
        self.assertFalse(PathUtils.contains_dangerous_patterns("subdir/file.txt"))
    
    def test_is_sensitive_path(self):
        """测试敏感路径检测"""
        # Windows敏感路径
        self.assertTrue(PathUtils.is_sensitive_path("C:\\Windows\\system32"))
        self.assertTrue(PathUtils.is_sensitive_path("C:\\Program Files\\test"))
        
        # Unix敏感路径
        self.assertTrue(PathUtils.is_sensitive_path("/etc/passwd"))
        self.assertTrue(PathUtils.is_sensitive_path("/bin/bash"))
        
        # 普通路径
        self.assertFalse(PathUtils.is_sensitive_path("/home/<USER>/test.txt"))
        self.assertFalse(PathUtils.is_sensitive_path("C:\\Users\\<USER>\\file.txt"))
    
    def test_get_relative_path(self):
        """测试相对路径获取"""
        # 基础目录内的文件
        file_path = os.path.join(self.base_path, "test.txt")
        relative = PathUtils.get_relative_path(file_path, self.base_path)
        self.assertEqual(relative, "test.txt")
        
        # 子目录中的文件
        subdir_file = os.path.join(self.test_subdir, "file.txt")
        relative = PathUtils.get_relative_path(subdir_file, self.base_path)
        expected = os.path.join("subdir", "file.txt")
        self.assertEqual(relative, expected)
        
        # 基础目录外的文件
        outside_file = os.path.join(self.temp_dir, "outside.txt")
        relative = PathUtils.get_relative_path(outside_file, self.base_path)
        self.assertTrue(os.path.isabs(relative))  # 应该返回绝对路径
    
    def test_join_paths(self):
        """测试路径连接"""
        # 正常连接
        joined = PathUtils.join_paths("dir", "subdir", "file.txt")
        expected = os.path.join("dir", "subdir", "file.txt")
        self.assertEqual(joined, expected)
        
        # 空路径处理
        joined = PathUtils.join_paths("dir", "", "file.txt")
        expected = os.path.join("dir", "file.txt")
        self.assertEqual(joined, expected)
        
        # 单个路径
        joined = PathUtils.join_paths("file.txt")
        self.assertEqual(joined, "file.txt")
        
        # 空参数
        joined = PathUtils.join_paths()
        self.assertEqual(joined, "")
    
    def test_split_path(self):
        """测试路径分割"""
        # 正常文件路径
        directory, filename = PathUtils.split_path(os.path.join("dir", "subdir", "file.txt"))
        self.assertTrue(directory.endswith("subdir"))
        self.assertEqual(filename, "file.txt")

        # 只有文件名
        directory, filename = PathUtils.split_path("file.txt")
        self.assertEqual(filename, "file.txt")

        # 目录路径（Windows和Unix处理不同）
        directory, filename = PathUtils.split_path(os.path.join("dir", "subdir") + os.sep)
        # 在Windows上，路径末尾的分隔符会被保留为目录名的一部分
        self.assertIn(filename, ["", "subdir"])
    
    def test_get_path_depth(self):
        """测试路径深度计算"""
        # 绝对路径
        depth = PathUtils.get_path_depth("/dir/subdir/file.txt")
        self.assertGreater(depth, 0)
        
        # 相对路径
        depth = PathUtils.get_path_depth("dir/subdir/file.txt")
        self.assertEqual(depth, 3)
        
        # 单个文件
        depth = PathUtils.get_path_depth("file.txt")
        self.assertEqual(depth, 1)
        
        # 根路径
        depth = PathUtils.get_path_depth("/")
        self.assertGreater(depth, 0)
    
    def test_is_absolute_path(self):
        """测试绝对路径判断"""
        # 绝对路径（根据操作系统调整）
        if os.name == 'nt':  # Windows
            self.assertTrue(PathUtils.is_absolute_path("C:\\dir\\file.txt"))
            self.assertTrue(PathUtils.is_absolute_path("D:\\dir\\file.txt"))
        else:  # Unix/Linux
            self.assertTrue(PathUtils.is_absolute_path("/dir/file.txt"))
            self.assertTrue(PathUtils.is_absolute_path("/home/<USER>/file.txt"))

        # 相对路径
        self.assertFalse(PathUtils.is_absolute_path("dir/file.txt"))
        self.assertFalse(PathUtils.is_absolute_path("./file.txt"))
        self.assertFalse(PathUtils.is_absolute_path("../file.txt"))
    
    def test_ensure_directory_exists(self):
        """测试目录创建"""
        # 创建新目录
        new_dir = os.path.join(self.temp_dir, "new_directory")
        self.assertTrue(PathUtils.ensure_directory_exists(new_dir))
        self.assertTrue(os.path.exists(new_dir))
        
        # 已存在的目录
        self.assertTrue(PathUtils.ensure_directory_exists(self.base_path))
        
        # 嵌套目录创建
        nested_dir = os.path.join(self.temp_dir, "level1", "level2", "level3")
        self.assertTrue(PathUtils.ensure_directory_exists(nested_dir))
        self.assertTrue(os.path.exists(nested_dir))
    
    def test_get_common_path(self):
        """测试公共路径获取"""
        # 多个路径有公共父目录
        paths = [
            os.path.join(self.base_path, "dir1", "file1.txt"),
            os.path.join(self.base_path, "dir2", "file2.txt"),
            os.path.join(self.base_path, "dir3", "file3.txt")
        ]
        common = PathUtils.get_common_path(paths)
        self.assertTrue(common.endswith("workspace"))
        
        # 单个路径
        single_path = [os.path.join(self.base_path, "file.txt")]
        common = PathUtils.get_common_path(single_path)
        self.assertTrue(common.endswith("workspace"))
        
        # 空列表
        common = PathUtils.get_common_path([])
        self.assertEqual(common, "")
    
    def test_validate_filename(self):
        """测试文件名验证"""
        # 合法文件名
        self.assertTrue(PathUtils.validate_filename("test.txt"))
        self.assertTrue(PathUtils.validate_filename("file_name.ext"))
        self.assertTrue(PathUtils.validate_filename("123.txt"))
        
        # 非法文件名
        self.assertFalse(PathUtils.validate_filename(""))
        self.assertFalse(PathUtils.validate_filename("   "))
        self.assertFalse(PathUtils.validate_filename("file<name.txt"))
        self.assertFalse(PathUtils.validate_filename("file>name.txt"))
        self.assertFalse(PathUtils.validate_filename("file:name.txt"))
        self.assertFalse(PathUtils.validate_filename("file|name.txt"))
        
        # Windows保留名称
        self.assertFalse(PathUtils.validate_filename("CON.txt"))
        self.assertFalse(PathUtils.validate_filename("PRN.txt"))
        self.assertFalse(PathUtils.validate_filename("AUX.txt"))
        self.assertFalse(PathUtils.validate_filename("COM1.txt"))
        
        # 过长文件名
        long_name = "a" * 300 + ".txt"
        self.assertFalse(PathUtils.validate_filename(long_name))
    
    def test_sanitize_filename(self):
        """测试文件名清理"""
        # 包含非法字符的文件名
        sanitized = PathUtils.sanitize_filename("file<name>test.txt")
        self.assertEqual(sanitized, "file_name_test.txt")
        
        # 包含多种非法字符
        sanitized = PathUtils.sanitize_filename("file:name|test?.txt")
        self.assertEqual(sanitized, "file_name_test_.txt")
        
        # 空文件名
        sanitized = PathUtils.sanitize_filename("")
        self.assertEqual(sanitized, "unnamed")
        
        # 只有空格的文件名
        sanitized = PathUtils.sanitize_filename("   ")
        self.assertEqual(sanitized, "unnamed")
        
        # 过长文件名
        long_name = "a" * 300 + ".txt"
        sanitized = PathUtils.sanitize_filename(long_name)
        self.assertLessEqual(len(sanitized), 255)
        self.assertTrue(sanitized.endswith(".txt"))


def run_path_utils_tests():
    """运行PathUtils测试"""
    print("🧪 开始PathUtils测试...")
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestPathUtils)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print(f"\n📊 测试结果:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n{'✅ 所有测试通过!' if success else '❌ 存在测试失败!'}")
    
    return success


if __name__ == "__main__":
    run_path_utils_tests()
