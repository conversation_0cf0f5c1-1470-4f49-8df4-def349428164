"""
任务特定提示词模块

本模块定义了不同类型任务的专门提示词模板。
"""

from typing import Dict, Any, Optional
from enum import Enum

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.task_models import TaskType


class PromptCategory(Enum):
    """提示词类别"""
    TASK_ANALYSIS = "task_analysis"
    TASK_EXECUTION = "task_execution"
    RESULT_EVALUATION = "result_evaluation"
    ERROR_HANDLING = "error_handling"
    DECISION_MAKING = "decision_making"


class TaskPrompts:
    """任务特定提示词管理器"""
    
    def __init__(self):
        """初始化提示词模板"""
        self.prompts = self._initialize_prompts()
    
    def get_prompt(self, task_type: TaskType, category: PromptCategory, 
                   context: Optional[Dict[str, Any]] = None) -> str:
        """获取特定任务类型和类别的提示词
        
        Args:
            task_type: 任务类型
            category: 提示词类别
            context: 上下文信息
            
        Returns:
            str: 提示词模板
        """
        prompt_key = f"{task_type.value}_{category.value}"
        base_prompt = self.prompts.get(prompt_key, self._get_default_prompt(category))
        
        # 如果有上下文，进行模板替换
        if context:
            try:
                return base_prompt.format(**context)
            except KeyError:
                # 如果模板变量不匹配，返回原始提示词
                return base_prompt
        
        return base_prompt
    
    def _initialize_prompts(self) -> Dict[str, str]:
        """初始化所有提示词模板"""
        return {
            # 分析任务提示词
            "analysis_task_analysis": """
你是一个专业的分析师。请对以下内容进行深入分析：

分析目标：{task_description}

分析要求：
1. 识别关键信息和模式
2. 提供客观的分析结果
3. 指出潜在的问题和机会
4. 给出基于数据的结论

上下文信息：
{context}

请提供结构化的分析报告，包括：
- 主要发现
- 关键指标
- 风险评估
- 建议措施
""",
            
            "analysis_task_execution": """
你是一个数据分析专家。请执行以下分析任务：

任务描述：{task_description}

执行步骤：
1. 理解分析需求
2. 收集和整理相关信息
3. 应用适当的分析方法
4. 得出有意义的结论

请确保分析过程严谨，结果准确可靠。
""",
            
            # 规划任务提示词
            "planning_task_analysis": """
你是一个专业的项目规划师。请为以下需求制定详细计划：

规划目标：{task_description}

规划要求：
1. 明确目标和里程碑
2. 分解具体的执行步骤
3. 估算时间和资源需求
4. 识别风险和应对措施

上下文信息：
{context}

请提供完整的规划方案，包括：
- 目标定义
- 工作分解结构(WBS)
- 时间安排
- 资源配置
- 风险管理
""",
            
            "planning_task_execution": """
你是一个执行规划专家。请制定以下计划：

计划内容：{task_description}

制定原则：
1. 目标明确，可衡量
2. 步骤具体，可执行
3. 时间合理，有弹性
4. 资源充足，可获得

请确保计划的可行性和有效性。
""",
            
            # 执行任务提示词
            "execution_task_analysis": """
你是一个执行专家。请分析以下执行任务：

执行目标：{task_description}

分析重点：
1. 执行的可行性
2. 所需的资源和条件
3. 可能遇到的障碍
4. 成功的关键因素

上下文信息：
{context}

请提供执行分析报告。
""",
            
            "execution_task_execution": """
你是一个任务执行者。请执行以下任务：

任务内容：{task_description}

执行要求：
1. 严格按照要求执行
2. 记录执行过程
3. 及时反馈问题
4. 确保质量标准

请开始执行并报告结果。
""",
            
            # 沟通任务提示词
            "communication_task_analysis": """
你是一个沟通专家。请分析以下沟通需求：

沟通目标：{task_description}

分析要点：
1. 沟通对象和背景
2. 沟通目的和期望
3. 最佳沟通方式
4. 可能的沟通障碍

上下文信息：
{context}

请提供沟通策略建议。
""",
            
            "communication_task_execution": """
你是一个专业的沟通者。请处理以下沟通任务：

沟通内容：{task_description}

沟通原则：
1. 清晰准确地表达
2. 考虑对方的感受
3. 选择合适的语调
4. 确保信息传达到位

请进行有效的沟通。
""",
            
            # 研究任务提示词
            "research_task_analysis": """
你是一个研究专家。请分析以下研究需求：

研究主题：{task_description}

分析内容：
1. 研究的范围和深度
2. 所需的研究方法
3. 信息来源和可靠性
4. 预期的研究成果

上下文信息：
{context}

请提供研究方案建议。
""",
            
            "research_task_execution": """
你是一个专业研究员。请进行以下研究：

研究题目：{task_description}

研究要求：
1. 全面收集相关信息
2. 使用可靠的信息源
3. 进行客观的分析
4. 提供有价值的见解

请开始研究并提供详细报告。
""",
            
            # 通用任务提示词
            "general_task_analysis": """
你是一个多能助手。请分析以下任务：

任务描述：{task_description}

分析要求：
1. 理解任务的本质和目标
2. 识别完成任务的关键要素
3. 评估任务的复杂度和难点
4. 提出合理的解决思路

上下文信息：
{context}

请提供任务分析结果。
""",
            
            "general_task_execution": """
你是一个通用助手。请处理以下任务：

任务内容：{task_description}

处理原则：
1. 认真理解任务要求
2. 选择合适的处理方法
3. 确保结果的质量
4. 及时反馈处理状态

请开始处理并提供结果。
""",
            
            # 结果评估提示词
            "analysis_result_evaluation": """
你是一个质量评估专家。请评估以下分析结果：

分析结果：{result_data}
原始任务：{task_description}

评估标准：
1. 分析的深度和广度
2. 结论的准确性和可靠性
3. 建议的实用性和可行性
4. 报告的清晰度和完整性

请提供详细的质量评估报告。
""",
            
            "planning_result_evaluation": """
你是一个规划评估专家。请评估以下规划结果：

规划结果：{result_data}
原始需求：{task_description}

评估要点：
1. 目标设定的合理性
2. 计划的可执行性
3. 时间安排的现实性
4. 风险考虑的充分性

请提供规划质量评估。
""",
            
            # 错误处理提示词
            "analysis_error_handling": """
分析任务执行出现错误。

错误信息：{error_message}
任务描述：{task_description}

请分析错误原因并提供解决方案：
1. 错误的根本原因
2. 可能的解决方法
3. 预防类似错误的措施
4. 是否需要重新执行

请提供错误处理建议。
""",
            
            "planning_error_handling": """
规划任务执行失败。

错误详情：{error_message}
规划目标：{task_description}

请提供错误处理方案：
1. 分析失败原因
2. 调整规划策略
3. 修正执行方法
4. 重新制定计划

请给出具体的改进建议。
""",
            
            # 决策制定提示词
            "analysis_decision_making": """
你是一个决策顾问。基于分析任务的执行情况，请做出以下决策：

当前状况：{current_status}
任务信息：{task_description}
执行历史：{execution_history}

决策要点：
1. 是否继续当前分析方向
2. 是否需要调整分析方法
3. 是否需要额外的信息
4. 下一步的最佳行动

请提供明确的决策建议。
""",
            
            "planning_decision_making": """
你是一个规划决策专家。基于规划任务的进展，请做出决策：

进展状况：{current_status}
规划目标：{task_description}
执行反馈：{execution_history}

决策考虑：
1. 当前规划是否需要调整
2. 执行策略是否有效
3. 资源配置是否合理
4. 时间安排是否现实

请提供决策建议和调整方案。
"""
        }
    
    def _get_default_prompt(self, category: PromptCategory) -> str:
        """获取默认提示词"""
        default_prompts = {
            PromptCategory.TASK_ANALYSIS: """
请分析以下任务：

任务描述：{task_description}

请提供详细的任务分析，包括目标、要求、方法和预期结果。
""",
            
            PromptCategory.TASK_EXECUTION: """
请执行以下任务：

任务内容：{task_description}

请按照要求完成任务并提供结果。
""",
            
            PromptCategory.RESULT_EVALUATION: """
请评估以下执行结果：

执行结果：{result_data}
原始任务：{task_description}

请提供质量评估和改进建议。
""",
            
            PromptCategory.ERROR_HANDLING: """
任务执行出现错误：

错误信息：{error_message}
任务描述：{task_description}

请分析错误原因并提供解决方案。
""",
            
            PromptCategory.DECISION_MAKING: """
请基于当前情况做出决策：

当前状况：{current_status}
任务信息：{task_description}

请提供明确的决策建议。
"""
        }
        
        return default_prompts.get(category, "请处理以下任务：{task_description}")
    
    def add_custom_prompt(self, task_type: TaskType, category: PromptCategory, 
                         prompt_template: str) -> bool:
        """添加自定义提示词模板
        
        Args:
            task_type: 任务类型
            category: 提示词类别
            prompt_template: 提示词模板
            
        Returns:
            bool: 是否添加成功
        """
        try:
            prompt_key = f"{task_type.value}_{category.value}"
            self.prompts[prompt_key] = prompt_template
            return True
        except Exception:
            return False
    
    def get_available_prompts(self) -> Dict[str, list]:
        """获取可用的提示词列表"""
        available = {}
        
        for task_type in TaskType:
            available[task_type.value] = []
            for category in PromptCategory:
                prompt_key = f"{task_type.value}_{category.value}"
                if prompt_key in self.prompts:
                    available[task_type.value].append(category.value)
        
        return available
