"""
上下文管理器模块

本模块实现了执行上下文管理，负责状态维护、数据共享和历史记录。
"""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import threading

from .task_models import Task, ExecutionResult, ExecutionPlan, ProgressInfo
from .kv_store import KVStore

logger = logging.getLogger(__name__)


@dataclass
class ContextSnapshot:
    """上下文快照"""
    timestamp: datetime
    plan_id: str
    current_task_id: Optional[str]
    completed_tasks: int
    failed_tasks: int
    global_variables: Dict[str, Any]
    performance_metrics: Dict[str, float]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class ContextManager:
    """执行上下文管理器
    
    负责维护执行过程中的状态信息、全局变量、任务历史等。
    提供上下文的保存、恢复、查询和分析功能。
    """
    
    def __init__(self, kv_store: KVStore):
        """初始化上下文管理器
        
        Args:
            kv_store: KV数据库实例
        """
        self.kv_store = kv_store
        
        # 当前执行上下文
        self.current_context: Dict[str, Any] = {
            'plan_id': None,
            'current_task': None,
            'completed_tasks': [],
            'failed_tasks': [],
            'pending_tasks': [],
            'global_variables': {},
            'execution_results': [],
            'performance_metrics': {},
            'error_history': [],
            'user_feedback': [],
            'start_time': None,
            'last_update': datetime.now(),
            'metadata': {}
        }
        
        # 上下文历史快照
        self.context_snapshots: List[ContextSnapshot] = []
        
        # 线程锁，确保并发安全
        self._lock = threading.RLock()
        
        # 自动保存配置
        self.auto_save_enabled = True
        self.auto_save_interval = 30  # 30秒
        self.last_save_time = datetime.now()
        
        # 性能统计
        self.stats = {
            'context_updates': 0,
            'snapshots_created': 0,
            'variables_set': 0,
            'variables_retrieved': 0
        }
        
        logger.info("ContextManager initialized successfully")
    
    async def initialize_context(self, plan: ExecutionPlan) -> bool:
        """初始化执行上下文
        
        Args:
            plan: 执行计划
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            with self._lock:
                logger.info(f"初始化执行上下文: {plan.id}")
                
                # 重置上下文
                self.current_context = {
                    'plan_id': plan.id,
                    'plan_name': plan.name,
                    'current_task': None,
                    'completed_tasks': [],
                    'failed_tasks': [],
                    'pending_tasks': [task.to_dict() for task in plan.tasks],
                    'global_variables': {},
                    'execution_results': [],
                    'performance_metrics': {
                        'total_tasks': len(plan.tasks),
                        'start_time': datetime.now().timestamp(),
                        'estimated_duration': plan.estimated_duration
                    },
                    'error_history': [],
                    'user_feedback': [],
                    'start_time': datetime.now(),
                    'last_update': datetime.now(),
                    'metadata': {
                        'parallel_execution': plan.parallel_execution,
                        'max_concurrent_tasks': plan.max_concurrent_tasks,
                        'task_order': plan.task_order
                    }
                }
                
                # 保存初始上下文
                await self._save_context()
                
                # 创建初始快照
                await self.create_snapshot()
                
                logger.info("执行上下文初始化完成")
                return True
                
        except Exception as e:
            logger.error(f"初始化执行上下文失败: {str(e)}")
            return False
    
    async def update_current_task(self, task: Task) -> bool:
        """更新当前任务
        
        Args:
            task: 当前任务
            
        Returns:
            bool: 更新是否成功
        """
        try:
            with self._lock:
                logger.debug(f"更新当前任务: {task.id}")
                
                self.current_context['current_task'] = task.to_dict()
                self.current_context['last_update'] = datetime.now()
                
                # 更新统计
                self.stats['context_updates'] += 1
                
                # 自动保存检查
                await self._check_auto_save()
                
                return True
                
        except Exception as e:
            logger.error(f"更新当前任务失败: {str(e)}")
            return False
    
    async def add_completed_task(self, task: Task, result: ExecutionResult) -> bool:
        """添加已完成任务
        
        Args:
            task: 已完成的任务
            result: 执行结果
            
        Returns:
            bool: 添加是否成功
        """
        try:
            with self._lock:
                logger.debug(f"添加已完成任务: {task.id}")
                
                # 添加到已完成列表
                self.current_context['completed_tasks'].append(task.to_dict())
                self.current_context['execution_results'].append(result.to_dict())
                
                # 从待执行列表移除
                self.current_context['pending_tasks'] = [
                    t for t in self.current_context['pending_tasks'] 
                    if t['id'] != task.id
                ]
                
                # 更新性能指标
                self._update_performance_metrics(task, result)
                
                # 更新时间戳
                self.current_context['last_update'] = datetime.now()
                
                # 统计更新
                self.stats['context_updates'] += 1
                
                # 自动保存检查
                await self._check_auto_save()
                
                return True
                
        except Exception as e:
            logger.error(f"添加已完成任务失败: {str(e)}")
            return False
    
    async def add_failed_task(self, task: Task, error: str) -> bool:
        """添加失败任务
        
        Args:
            task: 失败的任务
            error: 错误信息
            
        Returns:
            bool: 添加是否成功
        """
        try:
            with self._lock:
                logger.debug(f"添加失败任务: {task.id}")
                
                # 添加到失败列表
                task_dict = task.to_dict()
                task_dict['error_message'] = error
                task_dict['failed_at'] = datetime.now().isoformat()
                
                self.current_context['failed_tasks'].append(task_dict)
                
                # 添加错误历史
                error_record = {
                    'task_id': task.id,
                    'error_message': error,
                    'timestamp': datetime.now().isoformat(),
                    'retry_count': task.retry_count
                }
                self.current_context['error_history'].append(error_record)
                
                # 从待执行列表移除（如果重试次数已达上限）
                if task.retry_count >= task.max_retries:
                    self.current_context['pending_tasks'] = [
                        t for t in self.current_context['pending_tasks'] 
                        if t['id'] != task.id
                    ]
                
                # 更新时间戳
                self.current_context['last_update'] = datetime.now()
                
                # 统计更新
                self.stats['context_updates'] += 1
                
                # 自动保存检查
                await self._check_auto_save()
                
                return True
                
        except Exception as e:
            logger.error(f"添加失败任务失败: {str(e)}")
            return False
    
    async def set_global_variable(self, key: str, value: Any) -> bool:
        """设置全局变量
        
        Args:
            key: 变量名
            value: 变量值
            
        Returns:
            bool: 设置是否成功
        """
        try:
            with self._lock:
                logger.debug(f"设置全局变量: {key}")
                
                self.current_context['global_variables'][key] = value
                self.current_context['last_update'] = datetime.now()
                
                # 统计更新
                self.stats['variables_set'] += 1
                
                # 自动保存检查
                await self._check_auto_save()
                
                return True
                
        except Exception as e:
            logger.error(f"设置全局变量失败: {str(e)}")
            return False
    
    def get_global_variable(self, key: str, default: Any = None) -> Any:
        """获取全局变量
        
        Args:
            key: 变量名
            default: 默认值
            
        Returns:
            Any: 变量值
        """
        try:
            with self._lock:
                value = self.current_context['global_variables'].get(key, default)
                
                # 统计更新
                self.stats['variables_retrieved'] += 1
                
                return value
                
        except Exception as e:
            logger.error(f"获取全局变量失败: {str(e)}")
            return default
    
    async def create_snapshot(self) -> bool:
        """创建上下文快照
        
        Returns:
            bool: 创建是否成功
        """
        try:
            with self._lock:
                logger.debug("创建上下文快照")
                
                snapshot = ContextSnapshot(
                    timestamp=datetime.now(),
                    plan_id=self.current_context.get('plan_id', ''),
                    current_task_id=self.current_context.get('current_task', {}).get('id'),
                    completed_tasks=len(self.current_context.get('completed_tasks', [])),
                    failed_tasks=len(self.current_context.get('failed_tasks', [])),
                    global_variables=self.current_context.get('global_variables', {}).copy(),
                    performance_metrics=self.current_context.get('performance_metrics', {}).copy()
                )
                
                self.context_snapshots.append(snapshot)
                
                # 保持快照数量在合理范围内
                if len(self.context_snapshots) > 50:
                    self.context_snapshots = self.context_snapshots[-25:]
                
                # 保存快照到存储
                await self._save_snapshot(snapshot)
                
                # 统计更新
                self.stats['snapshots_created'] += 1
                
                logger.debug("上下文快照创建完成")
                return True
                
        except Exception as e:
            logger.error(f"创建上下文快照失败: {str(e)}")
            return False
    
    def get_context_summary(self) -> Dict[str, Any]:
        """获取上下文摘要
        
        Returns:
            Dict[str, Any]: 上下文摘要
        """
        try:
            with self._lock:
                summary = {
                    'plan_id': self.current_context.get('plan_id'),
                    'plan_name': self.current_context.get('plan_name'),
                    'current_task': self.current_context.get('current_task'),
                    'progress': {
                        'total_tasks': self.current_context.get('performance_metrics', {}).get('total_tasks', 0),
                        'completed': len(self.current_context.get('completed_tasks', [])),
                        'failed': len(self.current_context.get('failed_tasks', [])),
                        'pending': len(self.current_context.get('pending_tasks', []))
                    },
                    'performance_metrics': self.current_context.get('performance_metrics', {}),
                    'error_count': len(self.current_context.get('error_history', [])),
                    'start_time': self.current_context.get('start_time'),
                    'last_update': self.current_context.get('last_update'),
                    'snapshots_count': len(self.context_snapshots)
                }
                
                # 计算进度百分比
                if summary['progress']['total_tasks'] > 0:
                    summary['progress']['percentage'] = (
                        summary['progress']['completed'] / summary['progress']['total_tasks'] * 100
                    )
                else:
                    summary['progress']['percentage'] = 0.0
                
                return summary
                
        except Exception as e:
            logger.error(f"获取上下文摘要失败: {str(e)}")
            return {}
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标
        
        Returns:
            Dict[str, Any]: 性能指标
        """
        try:
            with self._lock:
                metrics = self.current_context.get('performance_metrics', {}).copy()
                
                # 计算运行时间
                if self.current_context.get('start_time'):
                    elapsed = (datetime.now() - self.current_context['start_time']).total_seconds()
                    metrics['elapsed_time'] = elapsed
                
                # 计算成功率
                completed = len(self.current_context.get('completed_tasks', []))
                failed = len(self.current_context.get('failed_tasks', []))
                total = completed + failed
                
                if total > 0:
                    metrics['success_rate'] = completed / total
                    metrics['failure_rate'] = failed / total
                else:
                    metrics['success_rate'] = 0.0
                    metrics['failure_rate'] = 0.0
                
                # 添加管理器统计
                metrics['manager_stats'] = self.stats.copy()
                
                return metrics
                
        except Exception as e:
            logger.error(f"获取性能指标失败: {str(e)}")
            return {}
    
    async def restore_context(self, plan_id: str) -> bool:
        """恢复执行上下文
        
        Args:
            plan_id: 执行计划ID
            
        Returns:
            bool: 恢复是否成功
        """
        try:
            logger.info(f"恢复执行上下文: {plan_id}")
            
            # 从存储加载上下文
            context_key = f"context:{plan_id}"
            context_data = await asyncio.to_thread(self.kv_store.get, context_key)
            
            if context_data:
                with self._lock:
                    self.current_context = context_data
                    self.current_context['last_update'] = datetime.now()
                
                logger.info("执行上下文恢复成功")
                return True
            else:
                logger.warning(f"未找到执行上下文: {plan_id}")
                return False
                
        except Exception as e:
            logger.error(f"恢复执行上下文失败: {str(e)}")
            return False
    
    # 私有方法
    
    async def _save_context(self):
        """保存当前上下文到存储"""
        try:
            if self.current_context.get('plan_id'):
                context_key = f"context:{self.current_context['plan_id']}"
                
                # 序列化上下文（处理datetime对象）
                serializable_context = self._make_serializable(self.current_context.copy())
                
                await asyncio.to_thread(self.kv_store.set, context_key, serializable_context)
                self.last_save_time = datetime.now()
                
        except Exception as e:
            logger.error(f"保存上下文失败: {str(e)}")
    
    async def _save_snapshot(self, snapshot: ContextSnapshot):
        """保存快照到存储"""
        try:
            snapshot_key = f"snapshot:{snapshot.plan_id}:{snapshot.timestamp.isoformat()}"
            snapshot_data = self._make_serializable(snapshot.to_dict())
            
            await asyncio.to_thread(self.kv_store.set, snapshot_key, snapshot_data)
            
        except Exception as e:
            logger.error(f"保存快照失败: {str(e)}")
    
    async def _check_auto_save(self):
        """检查是否需要自动保存"""
        if (self.auto_save_enabled and 
            (datetime.now() - self.last_save_time).total_seconds() > self.auto_save_interval):
            await self._save_context()
    
    def _update_performance_metrics(self, task: Task, result: ExecutionResult):
        """更新性能指标"""
        metrics = self.current_context['performance_metrics']
        
        # 更新执行时间统计
        if result.execution_time:
            if 'total_execution_time' not in metrics:
                metrics['total_execution_time'] = 0
            metrics['total_execution_time'] += result.execution_time
            
            if 'avg_execution_time' not in metrics:
                metrics['avg_execution_time'] = result.execution_time
            else:
                completed_count = len(self.current_context['completed_tasks'])
                metrics['avg_execution_time'] = (
                    (metrics['avg_execution_time'] * (completed_count - 1) + result.execution_time) / completed_count
                )
    
    def _make_serializable(self, obj: Any) -> Any:
        """将对象转换为可序列化格式"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        else:
            return obj
