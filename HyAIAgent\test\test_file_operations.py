"""
FileOperations测试模块
测试文件操作核心功能
"""

import unittest
import asyncio
import tempfile
import shutil
import json
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from operations.file_operations import FileOperations
from operations.security_manager import SecurityManager


class TestFileOperations(unittest.TestCase):
    """FileOperations测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录作为工作空间
        self.temp_dir = tempfile.mkdtemp()
        self.workspace_path = os.path.join(self.temp_dir, "workspace")
        self.config_path = os.path.join(self.temp_dir, "config", "file_security.json")
        
        # 创建测试配置
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        test_config = {
            "allowed_extensions": [".txt", ".md", ".json", ".py", ".log"],
            "forbidden_extensions": [".exe", ".bat", ".sh"],
            "max_file_size": 10240,  # 10KB for testing
            "max_path_length": 200,
            "allow_hidden_files": False,
            "audit_enabled": True
        }
        
        with open(self.config_path, 'w') as f:
            json.dump(test_config, f)
        
        # 初始化SecurityManager和FileOperations
        self.security_manager = SecurityManager(
            workspace_path=self.workspace_path,
            config_path=self.config_path
        )
        
        self.file_ops = FileOperations(
            workspace_path=self.workspace_path,
            security_manager=self.security_manager
        )
        
        # 创建测试文件
        os.makedirs(self.workspace_path, exist_ok=True)
        self.test_file = "test.txt"
        self.test_content = "Hello, World!\nThis is a test file."
        
        # 使用同步方式创建测试文件
        with open(os.path.join(self.workspace_path, self.test_file), 'w', encoding='utf-8') as f:
            f.write(self.test_content)
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def run_async_test(self, coro):
        """运行异步测试的辅助方法"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()
    
    def test_init(self):
        """测试初始化"""
        self.assertIsInstance(self.file_ops, FileOperations)
        self.assertTrue(Path(self.workspace_path).exists())
        self.assertIsNotNone(self.file_ops.security_manager)
        self.assertEqual(self.file_ops.operation_stats["total_operations"], 0)
    
    def test_read_file_success(self):
        """测试文件读取成功案例"""
        async def test():
            result = await self.file_ops.read_file(self.test_file)
            
            self.assertTrue(result["success"])
            self.assertEqual(result["content"], self.test_content)
            self.assertEqual(result["file_path"], self.test_file)
            self.assertIn("encoding", result)
            self.assertIn("size", result)
            self.assertIn("last_modified", result)
        
        self.run_async_test(test())
    
    def test_read_file_not_exists(self):
        """测试读取不存在的文件"""
        async def test():
            result = await self.file_ops.read_file("nonexistent.txt")
            
            self.assertFalse(result["success"])
            self.assertIn("error", result)
        
        self.run_async_test(test())
    
    def test_read_file_security_failure(self):
        """测试读取文件安全检查失败"""
        async def test():
            # 尝试读取禁止的文件类型
            result = await self.file_ops.read_file("malware.exe")
            
            self.assertFalse(result["success"])
            self.assertEqual(result["error"], "Security validation failed")
            self.assertIn("details", result)
        
        self.run_async_test(test())
    
    def test_write_file_success(self):
        """测试文件写入成功案例"""
        async def test():
            new_file = "new_test.txt"
            new_content = "This is new content."
            
            result = await self.file_ops.write_file(new_file, new_content)
            
            self.assertTrue(result["success"])
            self.assertEqual(result["file_path"], new_file)
            self.assertIn("size", result)
            self.assertIn("timestamp", result)
            
            # 验证文件确实被创建
            file_path = os.path.join(self.workspace_path, new_file)
            self.assertTrue(os.path.exists(file_path))
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            self.assertEqual(content, new_content)
        
        self.run_async_test(test())
    
    def test_write_file_append_mode(self):
        """测试文件追加写入"""
        async def test():
            append_content = "\nAppended content."
            
            result = await self.file_ops.write_file(self.test_file, append_content, mode='a')
            
            self.assertTrue(result["success"])
            self.assertEqual(result["mode"], 'a')
            
            # 验证内容被追加
            read_result = await self.file_ops.read_file(self.test_file)
            expected_content = self.test_content + append_content
            self.assertEqual(read_result["content"], expected_content)
        
        self.run_async_test(test())
    
    def test_write_file_security_failure(self):
        """测试写入文件安全检查失败"""
        async def test():
            result = await self.file_ops.write_file("malware.exe", "malicious content")
            
            self.assertFalse(result["success"])
            self.assertEqual(result["error"], "Security validation failed")
        
        self.run_async_test(test())
    
    def test_delete_file_success(self):
        """测试文件删除成功案例"""
        async def test():
            result = await self.file_ops.delete_file(self.test_file)
            
            self.assertTrue(result["success"])
            self.assertEqual(result["file_path"], self.test_file)
            self.assertIn("deleted_size", result)
            self.assertIn("timestamp", result)
            
            # 验证文件确实被删除
            file_path = os.path.join(self.workspace_path, self.test_file)
            self.assertFalse(os.path.exists(file_path))
        
        self.run_async_test(test())
    
    def test_delete_file_not_exists(self):
        """测试删除不存在的文件"""
        async def test():
            result = await self.file_ops.delete_file("nonexistent.txt")
            
            self.assertFalse(result["success"])
            self.assertEqual(result["error"], "File does not exist")
        
        self.run_async_test(test())
    
    def test_list_files_success(self):
        """测试列出文件成功案例"""
        async def test():
            # 创建更多测试文件
            await self.file_ops.write_file("test2.txt", "content2")
            await self.file_ops.write_file("test.md", "# Markdown content")
            
            result = await self.file_ops.list_files()
            
            self.assertTrue(result["success"])
            self.assertIn("files", result)
            self.assertIn("count", result)
            self.assertGreaterEqual(result["count"], 3)  # 至少3个文件
            
            # 检查文件信息格式
            for file_info in result["files"]:
                self.assertIn("path", file_info)
                self.assertIn("name", file_info)
                self.assertIn("size", file_info)
                self.assertIn("extension", file_info)
        
        self.run_async_test(test())
    
    def test_list_files_with_pattern(self):
        """测试按模式列出文件"""
        async def test():
            await self.file_ops.write_file("test2.txt", "content2")
            await self.file_ops.write_file("test.md", "# Markdown content")
            
            result = await self.file_ops.list_files(pattern="*.txt")
            
            self.assertTrue(result["success"])
            self.assertEqual(result["pattern"], "*.txt")
            
            # 所有返回的文件都应该是.txt文件
            for file_info in result["files"]:
                self.assertTrue(file_info["name"].endswith(".txt"))
        
        self.run_async_test(test())
    
    def test_search_files_by_name(self):
        """测试按文件名搜索"""
        async def test():
            await self.file_ops.write_file("search_test.txt", "content")
            await self.file_ops.write_file("another.txt", "content")
            
            result = await self.file_ops.search_files("search")
            
            self.assertTrue(result["success"])
            self.assertIn("results", result)
            self.assertGreater(result["count"], 0)
            
            # 检查搜索结果
            found_search_file = False
            for file_info in result["results"]:
                if "search" in file_info["name"].lower():
                    found_search_file = True
                    self.assertIn("filename", file_info["match_type"])
            
            self.assertTrue(found_search_file)
        
        self.run_async_test(test())
    
    def test_search_files_by_content(self):
        """测试按文件内容搜索"""
        async def test():
            await self.file_ops.write_file("content_test.txt", "This file contains special keyword.")
            await self.file_ops.write_file("normal.txt", "Normal content here.")
            
            result = await self.file_ops.search_files("special keyword", search_content=True)
            
            self.assertTrue(result["success"])
            self.assertTrue(result["search_content"])
            self.assertGreater(result["count"], 0)
            
            # 检查搜索结果
            found_content_match = False
            for file_info in result["results"]:
                if "content" in file_info["match_type"]:
                    found_content_match = True
            
            self.assertTrue(found_content_match)
        
        self.run_async_test(test())
    
    def test_operation_stats(self):
        """测试操作统计"""
        async def test():
            # 执行一些操作
            await self.file_ops.read_file(self.test_file)
            await self.file_ops.write_file("stats_test.txt", "test content")
            await self.file_ops.read_file("nonexistent.txt")  # 这个会失败
            
            stats = self.file_ops.get_operation_stats()
            
            self.assertIn("total_operations", stats)
            self.assertIn("successful_operations", stats)
            self.assertIn("failed_operations", stats)
            self.assertIn("success_rate", stats)
            self.assertIn("bytes_read", stats)
            self.assertIn("bytes_written", stats)
            
            self.assertEqual(stats["total_operations"], 3)
            self.assertEqual(stats["successful_operations"], 2)
            self.assertEqual(stats["failed_operations"], 1)
            self.assertAlmostEqual(stats["success_rate"], 2/3, places=2)
        
        self.run_async_test(test())
    
    def test_reset_stats(self):
        """测试重置统计"""
        async def test():
            # 执行一些操作
            await self.file_ops.read_file(self.test_file)
            
            # 重置统计
            self.file_ops.reset_stats()
            
            stats = self.file_ops.get_operation_stats()
            self.assertEqual(stats["total_operations"], 0)
            self.assertEqual(stats["successful_operations"], 0)
            self.assertEqual(stats["failed_operations"], 0)
        
        self.run_async_test(test())
    
    def test_encoding_detection(self):
        """测试编码检测"""
        async def test():
            # 创建不同编码的文件
            utf8_content = "UTF-8 content with 中文"
            await self.file_ops.write_file("utf8_test.txt", utf8_content, encoding='utf-8')
            
            result = await self.file_ops.read_file("utf8_test.txt")
            
            self.assertTrue(result["success"])
            self.assertEqual(result["content"], utf8_content)
            self.assertIn("encoding", result)
        
        self.run_async_test(test())


def run_file_operations_tests():
    """运行FileOperations测试"""
    print("🧪 开始FileOperations测试...")
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestFileOperations)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print(f"\n📊 测试结果:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n{'✅ 所有测试通过!' if success else '❌ 存在测试失败!'}")
    
    return success


if __name__ == "__main__":
    run_file_operations_tests()
