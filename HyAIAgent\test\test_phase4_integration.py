"""
第四阶段网络搜索集成测试
测试步骤4.19 - 验证网络搜索相关组件的集成功能
"""

import pytest
import asyncio
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

# 导入需要测试的组件
import sys
sys.path.append(str(Path(__file__).parent.parent))

from operations.search_operations import SearchOperations
from operations.content_processor import ContentProcessor
from operations.information_analyzer import InformationAnalyzer
from operations.data_integrator import DataIntegrator
from operations.hybrid_task_executor import HybridTaskExecutor, HybridTaskType, TaskStepType
from core.task_manager import TaskManager
from core.execution_engine import ExecutionEngine

class TestPhase4Integration:
    """第四阶段网络搜索集成测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            'search': {
                'max_results': 10,
                'timeout': 30,
                'engines': ['tavily', 'web_search'],
                'tavily': {
                    'api_key': 'test_api_key_for_testing'
                }
            },
            'processing': {
                'max_content_length': 10000,
                'chunk_size': 1000,
                'supported_formats': ['text', 'html', 'json']
            },
            'analysis': {
                'max_analysis_depth': 3,
                'confidence_threshold': 0.7
            },
            'storage': {
                'base_path': self.temp_dir,
                'max_file_size': 50000000
            }
        }

        # 使用模拟对象初始化组件
        with patch('operations.search_operations.TavilyClient'):
            self.search_ops = SearchOperations(self.config)
            self.content_processor = ContentProcessor(self.config)
            self.info_analyzer = InformationAnalyzer(self.config)
            self.data_integrator = DataIntegrator(self.config)
            self.task_manager = TaskManager(self.config)
            self.execution_engine = ExecutionEngine(self.config)
            self.hybrid_executor = HybridTaskExecutor(
                self.task_manager,
                self.execution_engine,
                self.search_ops,
                self.data_integrator,
                self.content_processor
            )
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_search_to_processing_pipeline(self):
        """测试1: 搜索到内容处理的完整管道"""
        # 模拟搜索结果
        mock_search_results = [
            {
                'title': 'AI技术发展报告',
                'url': 'https://example.com/ai-report',
                'content': '人工智能技术在2024年取得了重大突破，包括大语言模型、计算机视觉等领域。',
                'snippet': 'AI技术突破...',
                'metadata': {'source': 'tech_news', 'date': '2024-01-15'}
            },
            {
                'title': '机器学习应用案例',
                'url': 'https://example.com/ml-cases',
                'content': '机器学习在医疗、金融、教育等行业的应用案例分析。',
                'snippet': '机器学习应用...',
                'metadata': {'source': 'industry_report', 'date': '2024-01-20'}
            }
        ]
        
        # 模拟搜索操作
        with patch.object(self.search_ops, 'search', new_callable=AsyncMock) as mock_search:
            mock_search.return_value = mock_search_results
            
            # 执行搜索
            search_results = await self.search_ops.search("人工智能技术发展")
            assert len(search_results) == 2
            
            # 处理每个搜索结果
            processed_results = []
            for result in search_results:
                processed = await self.content_processor.process_content(
                    result['content'],
                    content_type='text',
                    metadata=result.get('metadata', {})
                )
                processed_results.append(processed)
            
            # 验证处理结果
            assert len(processed_results) == 2
            for processed in processed_results:
                assert 'processed_content' in processed
                assert 'metadata' in processed
                assert 'content_stats' in processed
                assert processed['metadata']['content_type'] == 'text'
    
    @pytest.mark.asyncio
    async def test_processing_to_analysis_workflow(self):
        """测试2: 内容处理到信息分析的工作流程"""
        # 准备测试内容
        test_content = """
        人工智能技术发展报告
        
        摘要：本报告分析了2024年人工智能技术的最新发展趋势，包括：
        1. 大语言模型的突破性进展
        2. 多模态AI技术的成熟应用
        3. AI在各行业的深度融合
        
        关键发现：
        - GPT-4和Claude等模型在推理能力上有显著提升
        - 图像生成和视频生成技术达到商用水平
        - AI辅助编程工具大幅提高开发效率
        
        结论：AI技术正在从实验室走向大规模商业应用。
        """
        
        # 内容处理
        processed_result = await self.content_processor.process_content(
            test_content,
            content_type='text',
            metadata={'source': 'ai_report', 'language': 'zh'}
        )
        
        # 信息分析
        analysis_result = await self.info_analyzer.analyze_information(
            processed_result['processed_content'],
            analysis_type='comprehensive_analysis',
            metadata=processed_result['metadata']
        )
        
        # 验证分析结果
        assert 'analysis_result' in analysis_result
        assert 'key_topics' in analysis_result['analysis_result']
        assert 'sentiment' in analysis_result['analysis_result']
        assert 'summary' in analysis_result['analysis_result']
        assert analysis_result['metadata']['analysis_type'] == 'comprehensive_analysis'
        
        # 验证关键主题提取
        key_topics = analysis_result['analysis_result']['key_topics']
        assert any('人工智能' in topic for topic in key_topics)
        assert any('大语言模型' in topic for topic in key_topics)
    
    @pytest.mark.asyncio
    async def test_multi_source_data_integration(self):
        """测试3: 多源数据整合"""
        # 准备多个分析结果
        analysis_results = [
            {
                'analysis_result': {
                    'key_topics': ['人工智能', '深度学习', '自然语言处理'],
                    'sentiment': 'positive',
                    'confidence': 0.85,
                    'summary': 'AI技术发展迅速'
                },
                'metadata': {
                    'source': 'tech_report_1',
                    'timestamp': datetime.now().isoformat(),
                    'language': 'zh'
                }
            },
            {
                'analysis_result': {
                    'key_topics': ['机器学习', '计算机视觉', '算法优化'],
                    'sentiment': 'neutral',
                    'confidence': 0.78,
                    'summary': '机器学习应用广泛'
                },
                'metadata': {
                    'source': 'industry_report_1',
                    'timestamp': datetime.now().isoformat(),
                    'language': 'zh'
                }
            },
            {
                'analysis_result': {
                    'key_topics': ['AI应用', '商业化', '技术落地'],
                    'sentiment': 'positive',
                    'confidence': 0.82,
                    'summary': 'AI商业化进程加速'
                },
                'metadata': {
                    'source': 'business_analysis_1',
                    'timestamp': datetime.now().isoformat(),
                    'language': 'zh'
                }
            }
        ]
        
        # 数据整合
        integration_result = await self.data_integrator.integrate_data(
            analysis_results,
            integration_type='topic_aggregation',
            integration_config={
                'merge_similar_topics': True,
                'calculate_overall_sentiment': True,
                'generate_comprehensive_summary': True
            }
        )
        
        # 验证整合结果
        assert 'integrated_data' in integration_result
        assert 'aggregated_topics' in integration_result['integrated_data']
        assert 'overall_sentiment' in integration_result['integrated_data']
        assert 'comprehensive_summary' in integration_result['integrated_data']
        
        # 验证主题聚合
        aggregated_topics = integration_result['integrated_data']['aggregated_topics']
        assert len(aggregated_topics) > 0
        
        # 验证情感分析整合
        overall_sentiment = integration_result['integrated_data']['overall_sentiment']
        assert 'sentiment_score' in overall_sentiment
        assert 'confidence' in overall_sentiment
    
    @pytest.mark.asyncio
    async def test_hybrid_task_research_workflow(self):
        """测试4: 混合任务调研工作流程"""
        # 创建调研分析任务
        task_config = {
            'research_topic': '人工智能在教育领域的应用',
            'search_queries': [
                'AI教育应用案例',
                '人工智能教学工具',
                '智能教育平台发展'
            ],
            'analysis_depth': 'comprehensive',
            'output_format': 'research_report',
            'save_path': os.path.join(self.temp_dir, 'ai_education_research.json')
        }
        
        # 模拟各组件的返回结果
        mock_search_results = [
            {
                'title': 'AI教育应用案例分析',
                'content': 'AI在个性化学习、智能辅导、自动评估等方面有重要应用。',
                'url': 'https://example.com/ai-education-cases',
                'metadata': {'source': 'education_tech', 'relevance': 0.9}
            },
            {
                'title': '智能教育平台发展报告',
                'content': '智能教育平台通过AI技术提供个性化学习体验。',
                'url': 'https://example.com/smart-education-platforms',
                'metadata': {'source': 'industry_report', 'relevance': 0.85}
            }
        ]
        
        with patch.object(self.search_ops, 'search', new_callable=AsyncMock) as mock_search, \
             patch.object(self.content_processor, 'process_content', new_callable=AsyncMock) as mock_process, \
             patch.object(self.info_analyzer, 'analyze_information', new_callable=AsyncMock) as mock_analyze:
            
            mock_search.return_value = mock_search_results
            mock_process.return_value = {
                'processed_content': '处理后的教育AI内容',
                'metadata': {'content_type': 'text', 'word_count': 150},
                'content_stats': {'readability': 0.8, 'complexity': 0.6}
            }
            mock_analyze.return_value = {
                'analysis_result': {
                    'key_topics': ['AI教育', '个性化学习', '智能辅导'],
                    'sentiment': 'positive',
                    'insights': ['AI提高学习效率', '个性化教学成为趋势'],
                    'recommendations': ['加强AI教师培训', '完善技术标准']
                },
                'metadata': {'analysis_type': 'comprehensive_analysis', 'confidence': 0.88}
            }
            
            # 创建并执行混合任务
            task = await self.hybrid_executor.create_hybrid_task(
                HybridTaskType.RESEARCH_AND_ANALYZE,
                task_config
            )
            
            execution_result = await self.hybrid_executor.execute_hybrid_task(task.task_id)
            
            # 验证执行结果
            assert execution_result['status'] == 'completed'
            assert execution_result['total_steps'] > 0
            assert execution_result['completed_steps'] == execution_result['total_steps']
            assert 'research_findings' in execution_result
    
    @pytest.mark.asyncio
    async def test_concurrent_hybrid_tasks(self):
        """测试5: 并发混合任务执行"""
        # 创建多个不同类型的混合任务
        task_configs = [
            {
                'type': HybridTaskType.SEARCH_AND_SAVE,
                'config': {
                    'query': '机器学习最新进展',
                    'max_results': 5,
                    'save_path': os.path.join(self.temp_dir, 'ml_progress.json')
                }
            },
            {
                'type': HybridTaskType.COLLECT_AND_INTEGRATE,
                'config': {
                    'query': '深度学习应用',
                    'collection_scope': 'comprehensive',
                    'integration_type': 'topic_clustering',
                    'output_path': os.path.join(self.temp_dir, 'dl_applications.json')
                }
            },
            {
                'type': HybridTaskType.RESEARCH_AND_ANALYZE,
                'config': {
                    'research_topic': '计算机视觉发展',
                    'analysis_depth': 'detailed',
                    'save_path': os.path.join(self.temp_dir, 'cv_research.json')
                }
            }
        ]
        
        # 模拟搜索结果
        with patch.object(self.search_ops, 'search', new_callable=AsyncMock) as mock_search:
            mock_search.return_value = [
                {
                    'title': '测试文章',
                    'content': '这是一篇测试文章内容。',
                    'url': 'https://example.com/test',
                    'metadata': {'source': 'test', 'relevance': 0.8}
                }
            ]
            
            # 并发创建任务
            tasks = []
            for task_config in task_configs:
                task = await self.hybrid_executor.create_hybrid_task(
                    task_config['type'],
                    task_config['config']
                )
                tasks.append(task)
            
            # 并发执行任务
            execution_tasks = [
                self.hybrid_executor.execute_hybrid_task(task.task_id)
                for task in tasks
            ]
            
            results = await asyncio.gather(*execution_tasks, return_exceptions=True)
            
            # 验证所有任务都成功执行
            for i, result in enumerate(results):
                assert not isinstance(result, Exception), f"Task {i} failed with exception: {result}"
                assert result['status'] == 'completed', f"Task {i} status: {result['status']}"
    
    @pytest.mark.asyncio
    async def test_error_recovery_integration(self):
        """测试6: 错误恢复集成"""
        # 模拟部分组件失败的情况
        task_config = {
            'query': '测试查询',
            'max_results': 5,
            'save_path': os.path.join(self.temp_dir, 'error_test.json'),
            'retry_config': {
                'max_retries': 2,
                'retry_delay': 0.1
            }
        }
        
        # 模拟搜索成功但处理失败
        with patch.object(self.search_ops, 'search', new_callable=AsyncMock) as mock_search, \
             patch.object(self.content_processor, 'process_content', new_callable=AsyncMock) as mock_process:
            
            mock_search.return_value = [
                {'title': '测试', 'content': '测试内容', 'url': 'https://example.com'}
            ]
            
            # 第一次调用失败，第二次成功
            mock_process.side_effect = [
                Exception("处理失败"),
                {
                    'processed_content': '成功处理的内容',
                    'metadata': {'content_type': 'text'},
                    'content_stats': {'word_count': 10}
                }
            ]
            
            # 创建任务
            task = await self.hybrid_executor.create_hybrid_task(
                HybridTaskType.SEARCH_AND_SAVE,
                task_config
            )
            
            # 执行任务（应该重试并成功）
            execution_result = await self.hybrid_executor.execute_hybrid_task(task.task_id)
            
            # 验证错误恢复
            assert execution_result['status'] == 'completed'
            assert 'retry_attempts' in execution_result
    
    @pytest.mark.asyncio
    async def test_prompt_template_integration(self):
        """测试7: 提示词模板集成"""
        # 验证提示词模板文件存在
        prompts_dir = Path("HyAIAgent/prompts/tasks")
        template_files = [
            "web_search.md",
            "information_analysis.md",
            "content_summary.md",
            "research_planning.md"
        ]
        
        for template_file in template_files:
            template_path = prompts_dir / template_file
            assert template_path.exists(), f"提示词模板文件不存在: {template_file}"
            
            # 验证模板内容
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
                assert len(content) > 0, f"模板文件为空: {template_file}"
                assert '{' in content and '}' in content, f"模板文件缺少变量占位符: {template_file}"
    
    @pytest.mark.asyncio
    async def test_end_to_end_performance(self):
        """测试8: 端到端性能测试"""
        import time
        
        # 测试完整的搜索-处理-分析-整合流程性能
        start_time = time.time()
        
        # 模拟所有组件
        with patch.object(self.search_ops, 'search', new_callable=AsyncMock) as mock_search, \
             patch.object(self.content_processor, 'process_content', new_callable=AsyncMock) as mock_process, \
             patch.object(self.info_analyzer, 'analyze_information', new_callable=AsyncMock) as mock_analyze, \
             patch.object(self.data_integrator, 'integrate_data', new_callable=AsyncMock) as mock_integrate:
            
            # 设置快速响应的模拟
            mock_search.return_value = [
                {'title': '性能测试', 'content': '测试内容', 'url': 'https://example.com'}
            ]
            mock_process.return_value = {
                'processed_content': '处理后内容',
                'metadata': {'content_type': 'text'}
            }
            mock_analyze.return_value = {
                'analysis_result': {'key_topics': ['测试'], 'sentiment': 'neutral'},
                'metadata': {'analysis_type': 'quick'}
            }
            mock_integrate.return_value = {
                'integrated_data': {'summary': '整合结果'},
                'metadata': {'integration_type': 'simple'}
            }
            
            # 执行完整流程
            task_config = {
                'query': '性能测试查询',
                'max_results': 3,
                'save_path': os.path.join(self.temp_dir, 'performance_test.json')
            }
            
            task = await self.hybrid_executor.create_hybrid_task(
                HybridTaskType.COLLECT_AND_INTEGRATE,
                task_config
            )
            
            result = await self.hybrid_executor.execute_hybrid_task(task.task_id)
            
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证性能（应该在合理时间内完成）
        assert execution_time < 2.0, f"端到端执行时间过长: {execution_time}秒"
        assert result['status'] == 'completed'

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
