"""
测试数据整合器模块
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from operations.data_integrator import (
    DataIntegrator, DataSource, IntegratedData, IntegrationReport,
    DataSourceType, IntegrationType
)


class TestDataIntegrator:
    """测试DataIntegrator类"""
    
    @pytest.fixture
    def integrator(self):
        """创建数据整合器实例"""
        return DataIntegrator()
    
    @pytest.fixture
    def sample_sources(self):
        """创建示例数据源"""
        sources = []
        
        # Web搜索源
        web_source = DataSource(
            source_id="web_001",
            source_type=DataSourceType.WEB_SEARCH,
            source_url="https://example.com/article1",
            source_path=None,
            metadata={"title": "AI技术发展", "content": "人工智能技术正在快速发展..."},
            timestamp=datetime.now(),
            reliability_score=0.8
        )
        sources.append(web_source)
        
        # 本地文件源
        file_source = DataSource(
            source_id="file_001",
            source_type=DataSourceType.LOCAL_FILE,
            source_url=None,
            source_path="/path/to/document.txt",
            metadata={"title": "AI研究报告", "content": "根据最新研究，AI技术..."},
            timestamp=datetime.now() - timedelta(hours=1),
            reliability_score=0.9
        )
        sources.append(file_source)
        
        # 处理后内容源
        processed_source = DataSource(
            source_id="processed_001",
            source_type=DataSourceType.PROCESSED_CONTENT,
            source_url=None,
            source_path=None,
            metadata={"title": "AI技术摘要", "summary": "AI技术发展迅速，应用广泛"},
            timestamp=datetime.now() - timedelta(minutes=30),
            reliability_score=0.95
        )
        sources.append(processed_source)
        
        return sources
    
    @pytest.mark.asyncio
    async def test_register_data_source(self, integrator):
        """测试注册数据源"""
        source_data = {"title": "测试文档", "content": "这是测试内容"}
        
        source_id = await integrator.register_data_source(
            source_data=source_data,
            source_type=DataSourceType.LOCAL_FILE,
            source_path="/test/path.txt",
            metadata={"author": "测试作者"}
        )
        
        assert source_id is not None
        assert source_id in integrator.data_sources
        
        registered_source = integrator.data_sources[source_id]
        assert registered_source.source_type == DataSourceType.LOCAL_FILE
        assert registered_source.source_path == "/test/path.txt"
        assert registered_source.metadata["author"] == "测试作者"
        assert 0 <= registered_source.reliability_score <= 1
    
    @pytest.mark.asyncio
    async def test_integrate_data_merge(self, integrator, sample_sources):
        """测试数据合并整合"""
        # 注册数据源
        source_ids = []
        for source in sample_sources:
            integrator.data_sources[source.source_id] = source
            source_ids.append(source.source_id)
        
        # 执行合并整合
        integration_id = await integrator.integrate_data(
            source_ids=source_ids,
            integration_type=IntegrationType.MERGE,
            title="AI技术综合分析"
        )
        
        assert integration_id is not None
        assert integration_id in integrator.integrated_data
        
        integrated_data = integrator.integrated_data[integration_id]
        assert integrated_data.title == "AI技术综合分析"
        assert integrated_data.integration_type == IntegrationType.MERGE
        assert len(integrated_data.sources) == 3
        assert 0 <= integrated_data.confidence_score <= 1
        assert "sources" in integrated_data.content
    
    @pytest.mark.asyncio
    async def test_integrate_data_combine(self, integrator, sample_sources):
        """测试数据组合整合"""
        # 注册数据源
        source_ids = []
        for source in sample_sources:
            integrator.data_sources[source.source_id] = source
            source_ids.append(source.source_id)
        
        # 执行组合整合
        integration_id = await integrator.integrate_data(
            source_ids=source_ids,
            integration_type=IntegrationType.COMBINE,
            title="AI技术组合视图"
        )
        
        assert integration_id is not None
        integrated_data = integrator.integrated_data[integration_id]
        assert integrated_data.integration_type == IntegrationType.COMBINE
        assert "combined_sources" in integrated_data.content
        assert "relationships" in integrated_data.content
    
    @pytest.mark.asyncio
    async def test_integrate_data_correlate(self, integrator, sample_sources):
        """测试数据关联整合"""
        # 注册数据源
        source_ids = []
        for source in sample_sources:
            integrator.data_sources[source.source_id] = source
            source_ids.append(source.source_id)
        
        # 执行关联整合
        integration_id = await integrator.integrate_data(
            source_ids=source_ids,
            integration_type=IntegrationType.CORRELATE,
            title="AI技术关联分析"
        )
        
        assert integration_id is not None
        integrated_data = integrator.integrated_data[integration_id]
        assert integrated_data.integration_type == IntegrationType.CORRELATE
        assert "correlation_matrix" in integrated_data.content
        assert "strong_correlations" in integrated_data.content
    
    @pytest.mark.asyncio
    async def test_integrate_data_aggregate(self, integrator, sample_sources):
        """测试数据聚合整合"""
        # 注册数据源
        source_ids = []
        for source in sample_sources:
            integrator.data_sources[source.source_id] = source
            source_ids.append(source.source_id)
        
        # 执行聚合整合
        integration_id = await integrator.integrate_data(
            source_ids=source_ids,
            integration_type=IntegrationType.AGGREGATE,
            title="AI技术聚合统计"
        )
        
        assert integration_id is not None
        integrated_data = integrator.integrated_data[integration_id]
        assert integrated_data.integration_type == IntegrationType.AGGREGATE
        assert "total_sources" in integrated_data.content
        assert "source_types" in integrated_data.content
        assert "reliability_stats" in integrated_data.content
    
    @pytest.mark.asyncio
    async def test_integrate_data_deduplicate(self, integrator, sample_sources):
        """测试数据去重整合"""
        # 添加重复源
        duplicate_source = DataSource(
            source_id="web_002",
            source_type=DataSourceType.WEB_SEARCH,
            source_url="https://example.com/article2",
            source_path=None,
            metadata={"title": "AI技术发展", "content": "人工智能技术正在快速发展..."},  # 相似内容
            timestamp=datetime.now(),
            reliability_score=0.7
        )
        sample_sources.append(duplicate_source)
        
        # 注册数据源
        source_ids = []
        for source in sample_sources:
            integrator.data_sources[source.source_id] = source
            source_ids.append(source.source_id)
        
        # 执行去重整合
        integration_id = await integrator.integrate_data(
            source_ids=source_ids,
            integration_type=IntegrationType.DEDUPLICATE,
            title="AI技术去重分析"
        )
        
        assert integration_id is not None
        integrated_data = integrator.integrated_data[integration_id]
        assert integrated_data.integration_type == IntegrationType.DEDUPLICATE
        assert "unique_sources" in integrated_data.content
        assert "duplicates_removed" in integrated_data.content
    
    @pytest.mark.asyncio
    async def test_get_integrated_data(self, integrator, sample_sources):
        """测试获取整合数据"""
        # 先创建整合数据
        source_ids = []
        for source in sample_sources:
            integrator.data_sources[source.source_id] = source
            source_ids.append(source.source_id)
        
        integration_id = await integrator.integrate_data(
            source_ids=source_ids,
            integration_type=IntegrationType.MERGE
        )
        
        # 测试获取
        retrieved_data = await integrator.get_integrated_data(integration_id)
        assert retrieved_data is not None
        assert retrieved_data.integration_id == integration_id
        
        # 测试获取不存在的数据
        non_existent_data = await integrator.get_integrated_data("non_existent_id")
        assert non_existent_data is None
    
    @pytest.mark.asyncio
    async def test_search_integrated_data(self, integrator, sample_sources):
        """测试搜索整合数据"""
        # 创建多个整合数据
        source_ids = []
        for source in sample_sources:
            integrator.data_sources[source.source_id] = source
            source_ids.append(source.source_id)
        
        # 创建AI相关的整合
        ai_integration_id = await integrator.integrate_data(
            source_ids=source_ids,
            integration_type=IntegrationType.MERGE,
            title="AI技术分析"
        )
        
        # 创建另一个整合（不相关）
        other_source = DataSource(
            source_id="other_001",
            source_type=DataSourceType.LOCAL_FILE,
            source_url=None,
            source_path="/other/path.txt",
            metadata={"title": "其他主题", "content": "这是其他内容"},
            timestamp=datetime.now(),
            reliability_score=0.8
        )
        integrator.data_sources[other_source.source_id] = other_source
        
        other_integration_id = await integrator.integrate_data(
            source_ids=[other_source.source_id],
            integration_type=IntegrationType.MERGE,
            title="其他主题分析"
        )
        
        # 搜索AI相关内容
        ai_results = await integrator.search_integrated_data("AI")
        assert len(ai_results) >= 1
        assert any(result.integration_id == ai_integration_id for result in ai_results)
        
        # 搜索其他内容
        other_results = await integrator.search_integrated_data("其他")
        assert len(other_results) >= 1
        assert any(result.integration_id == other_integration_id for result in other_results)
        
        # 搜索不存在的内容
        empty_results = await integrator.search_integrated_data("不存在的内容")
        assert len(empty_results) == 0
    
    @pytest.mark.asyncio
    async def test_search_with_filters(self, integrator, sample_sources):
        """测试带过滤器的搜索"""
        # 创建整合数据
        source_ids = []
        for source in sample_sources:
            integrator.data_sources[source.source_id] = source
            source_ids.append(source.source_id)
        
        integration_id = await integrator.integrate_data(
            source_ids=source_ids,
            integration_type=IntegrationType.MERGE,
            title="AI技术分析"
        )
        
        # 测试按整合类型过滤
        merge_results = await integrator.search_integrated_data(
            "AI",
            filters={"integration_type": "merge"}
        )
        assert len(merge_results) >= 1
        assert all(result.integration_type == IntegrationType.MERGE for result in merge_results)
        
        # 测试按置信度过滤
        high_confidence_results = await integrator.search_integrated_data(
            "AI",
            filters={"min_confidence": 0.9}
        )
        # 结果可能为空，这是正常的
        assert all(result.confidence_score >= 0.9 for result in high_confidence_results)
    
    @pytest.mark.asyncio
    async def test_remove_duplicates(self, integrator):
        """测试移除重复数据"""
        # 创建相似的整合数据
        source1 = DataSource(
            source_id="dup_001",
            source_type=DataSourceType.WEB_SEARCH,
            source_url="https://example.com/dup1",
            source_path=None,
            metadata={"title": "重复内容", "content": "这是重复的内容"},
            timestamp=datetime.now(),
            reliability_score=0.8
        )
        
        source2 = DataSource(
            source_id="dup_002",
            source_type=DataSourceType.WEB_SEARCH,
            source_url="https://example.com/dup2",
            source_path=None,
            metadata={"title": "重复内容", "content": "这是重复的内容"},  # 相同内容
            timestamp=datetime.now(),
            reliability_score=0.7  # 较低质量
        )
        
        integrator.data_sources[source1.source_id] = source1
        integrator.data_sources[source2.source_id] = source2
        
        # 创建两个相似的整合
        integration1_id = await integrator.integrate_data(
            source_ids=[source1.source_id],
            integration_type=IntegrationType.MERGE,
            title="重复内容分析1"
        )
        
        integration2_id = await integrator.integrate_data(
            source_ids=[source2.source_id],
            integration_type=IntegrationType.MERGE,
            title="重复内容分析2"
        )
        
        # 验证两个整合都存在
        assert len(integrator.integrated_data) == 2
        
        # 移除重复
        removed_count = await integrator.remove_duplicates(threshold=0.8)
        
        # 验证移除了重复项
        assert removed_count >= 0  # 可能没有足够相似的项被移除
        assert len(integrator.integrated_data) <= 2

    @pytest.mark.asyncio
    async def test_export_integrated_data(self, integrator, sample_sources):
        """测试导出整合数据"""
        # 创建整合数据
        source_ids = []
        for source in sample_sources:
            integrator.data_sources[source.source_id] = source
            source_ids.append(source.source_id)

        integration_id = await integrator.integrate_data(
            source_ids=source_ids,
            integration_type=IntegrationType.MERGE,
            title="导出测试数据"
        )

        # 测试导出全部数据
        export_data = await integrator.export_integrated_data()
        assert "metadata" in export_data
        assert "integrations" in export_data
        assert export_data["metadata"]["total_integrations"] == 1
        assert integration_id in export_data["integrations"]

        # 测试导出指定数据
        specific_export = await integrator.export_integrated_data(
            integration_ids=[integration_id]
        )
        assert specific_export["metadata"]["total_integrations"] == 1
        assert integration_id in specific_export["integrations"]

        # 测试导出不存在的数据
        empty_export = await integrator.export_integrated_data(
            integration_ids=["non_existent_id"]
        )
        assert empty_export["metadata"]["total_integrations"] == 0

    @pytest.mark.asyncio
    async def test_get_integration_statistics(self, integrator, sample_sources):
        """测试获取整合统计信息"""
        # 初始统计
        initial_stats = await integrator.get_integration_statistics()
        assert initial_stats["total_integrations"] == 0
        assert initial_stats["total_sources"] == 0

        # 添加数据源和整合
        source_ids = []
        for source in sample_sources:
            integrator.data_sources[source.source_id] = source
            source_ids.append(source.source_id)

        # 创建不同类型的整合
        merge_id = await integrator.integrate_data(
            source_ids=source_ids,
            integration_type=IntegrationType.MERGE,
            title="合并测试"
        )

        combine_id = await integrator.integrate_data(
            source_ids=source_ids[:2],
            integration_type=IntegrationType.COMBINE,
            title="组合测试"
        )

        # 获取统计信息
        stats = await integrator.get_integration_statistics()
        assert stats["total_integrations"] == 2
        assert stats["total_sources"] == 3
        assert "merge" in stats["integration_types"]
        assert "combine" in stats["integration_types"]
        assert stats["integration_types"]["merge"] == 1
        assert stats["integration_types"]["combine"] == 1

        # 验证源类型统计
        assert "web_search" in stats["source_types"]
        assert "local_file" in stats["source_types"]
        assert "processed_content" in stats["source_types"]

        # 验证质量和置信度统计
        assert 0 <= stats["average_quality"] <= 1
        assert 0 <= stats["average_confidence"] <= 1

    @pytest.mark.asyncio
    async def test_source_reliability_evaluation(self, integrator):
        """测试数据源可靠性评估"""
        # 测试不同类型源的可靠性评估
        test_cases = [
            (DataSourceType.WEB_SEARCH, "https://wikipedia.org/article", 0.9),
            (DataSourceType.WEB_SEARCH, "https://example.com/article", 0.7),
            (DataSourceType.LOCAL_FILE, None, 0.8),
            (DataSourceType.PROCESSED_CONTENT, None, 0.9),
            (DataSourceType.DATABASE, None, 0.9),
            (DataSourceType.API, None, 0.8),
            (DataSourceType.CACHE, None, 0.6)
        ]

        for source_type, url, expected_min_score in test_cases:
            reliability = await integrator._evaluate_source_reliability(
                source_data="test data",
                source_type=source_type,
                source_url=url
            )
            assert 0 <= reliability <= 1
            # 对于已知高质量源，验证评分合理
            if url and "wikipedia.org" in url:
                assert reliability >= 0.8

    @pytest.mark.asyncio
    async def test_quality_metrics_calculation(self, integrator, sample_sources):
        """测试质量指标计算"""
        # 创建测试内容
        test_content = {
            "sources": [source.to_dict() for source in sample_sources],
            "content_summary": "这是一个测试摘要",
            "key_points": ["要点1", "要点2"]
        }

        # 计算质量指标
        metrics = await integrator._calculate_quality_metrics(test_content, sample_sources)

        # 验证指标存在且合理
        assert "completeness" in metrics
        assert "consistency" in metrics
        assert "freshness" in metrics
        assert "diversity" in metrics
        assert "overall" in metrics

        for metric_name, value in metrics.items():
            assert 0 <= value <= 1, f"Metric {metric_name} value {value} out of range"

        # 验证完整性指标
        assert metrics["completeness"] > 0.5  # 包含必要字段

        # 验证多样性指标
        assert metrics["diversity"] > 0  # 有多种源类型

    @pytest.mark.asyncio
    async def test_relationship_analysis(self, integrator, sample_sources):
        """测试关系分析"""
        # 分析关系
        relationships = await integrator._analyze_relationships(sample_sources)

        # 验证关系结构
        for relationship in relationships:
            assert "source1_id" in relationship
            assert "source2_id" in relationship
            assert "relationship_type" in relationship
            assert "strength" in relationship
            assert "description" in relationship
            assert 0 <= relationship["strength"] <= 1

    @pytest.mark.asyncio
    async def test_confidence_score_calculation(self, integrator, sample_sources):
        """测试置信度评分计算"""
        # 创建质量指标
        quality_metrics = {
            "completeness": 0.8,
            "consistency": 0.7,
            "freshness": 0.9,
            "diversity": 0.6,
            "overall": 0.75
        }

        # 计算置信度
        confidence = await integrator._calculate_confidence_score(sample_sources, quality_metrics)

        # 验证置信度合理
        assert 0 <= confidence <= 1
        assert confidence > 0.5  # 基于高质量源应该有较高置信度

    @pytest.mark.asyncio
    async def test_text_similarity_calculation(self, integrator):
        """测试文本相似度计算"""
        # 测试相同文本
        similarity1 = integrator._calculate_text_similarity("hello world", "hello world")
        assert similarity1 == 1.0

        # 测试完全不同文本
        similarity2 = integrator._calculate_text_similarity("hello world", "goodbye universe")
        assert similarity2 == 0.0

        # 测试部分相似文本
        similarity3 = integrator._calculate_text_similarity("hello world test", "hello world example")
        assert 0 < similarity3 < 1

        # 测试空文本
        similarity4 = integrator._calculate_text_similarity("", "hello")
        assert similarity4 == 0.0

        similarity5 = integrator._calculate_text_similarity("hello", "")
        assert similarity5 == 0.0

    @pytest.mark.asyncio
    async def test_integration_with_invalid_sources(self, integrator):
        """测试使用无效数据源的整合"""
        # 尝试使用不存在的数据源ID
        with pytest.raises(ValueError, match="No valid data sources found"):
            await integrator.integrate_data(
                source_ids=["non_existent_1", "non_existent_2"],
                integration_type=IntegrationType.MERGE
            )

        # 混合有效和无效的数据源ID
        valid_source = DataSource(
            source_id="valid_001",
            source_type=DataSourceType.LOCAL_FILE,
            source_url=None,
            source_path="/valid/path.txt",
            metadata={"title": "有效源"},
            timestamp=datetime.now(),
            reliability_score=0.8
        )
        integrator.data_sources[valid_source.source_id] = valid_source

        # 应该只使用有效的数据源
        integration_id = await integrator.integrate_data(
            source_ids=["valid_001", "invalid_001"],
            integration_type=IntegrationType.MERGE
        )

        assert integration_id is not None
        integrated_data = integrator.integrated_data[integration_id]
        assert len(integrated_data.sources) == 1  # 只有一个有效源
        assert integrated_data.sources[0].source_id == "valid_001"


def test_data_source_to_dict():
    """测试DataSource转字典功能"""
    source = DataSource(
        source_id="test_001",
        source_type=DataSourceType.WEB_SEARCH,
        source_url="https://example.com",
        source_path=None,
        metadata={"title": "测试"},
        timestamp=datetime.now(),
        reliability_score=0.8
    )

    data_dict = source.to_dict()
    assert data_dict["source_id"] == "test_001"
    assert data_dict["source_type"] == "web_search"
    assert data_dict["source_url"] == "https://example.com"
    assert data_dict["reliability_score"] == 0.8
    assert "timestamp" in data_dict


def test_integrated_data_to_dict():
    """测试IntegratedData转字典功能"""
    source = DataSource(
        source_id="test_001",
        source_type=DataSourceType.WEB_SEARCH,
        source_url="https://example.com",
        source_path=None,
        metadata={"title": "测试"},
        timestamp=datetime.now(),
        reliability_score=0.8
    )

    integrated_data = IntegratedData(
        integration_id="integration_001",
        title="测试整合",
        content={"test": "data"},
        sources=[source],
        integration_type=IntegrationType.MERGE,
        confidence_score=0.9,
        quality_metrics={"overall": 0.8},
        relationships=[],
        timestamp=datetime.now()
    )

    data_dict = integrated_data.to_dict()
    assert data_dict["integration_id"] == "integration_001"
    assert data_dict["title"] == "测试整合"
    assert data_dict["integration_type"] == "merge"
    assert data_dict["confidence_score"] == 0.9
    assert len(data_dict["sources"]) == 1
    assert "timestamp" in data_dict


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
