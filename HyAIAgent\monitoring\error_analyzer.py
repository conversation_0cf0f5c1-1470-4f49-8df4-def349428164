"""
错误分析器

提供智能错误诊断、错误模式识别、解决方案推荐和错误趋势分析功能。
"""

import asyncio
import re
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import json
import logging

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class ErrorRecord:
    """错误记录数据模型"""
    error_id: str
    error_type: str
    error_message: str
    stack_trace: str
    timestamp: datetime
    context: Dict[str, Any]
    severity: str  # low, medium, high, critical
    category: str  # system, network, logic, data, etc.
    resolved: bool = False
    resolution: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class ErrorPattern:
    """错误模式数据模型"""
    pattern_id: str
    pattern_name: str
    error_regex: str
    category: str
    severity: str
    description: str
    common_causes: List[str]
    solutions: List[str]
    occurrence_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


@dataclass
class DiagnosisResult:
    """诊断结果数据模型"""
    error_id: str
    matched_patterns: List[str]
    severity_assessment: str
    root_cause_analysis: str
    recommended_solutions: List[str]
    confidence_score: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class ErrorAnalyzer:
    """
    错误分析器
    
    提供智能错误诊断、错误模式识别、解决方案推荐和错误趋势分析功能。
    支持错误分类、模式匹配、趋势分析和自动化诊断。
    """
    
    def __init__(self, 
                 config_manager=None,
                 kv_store=None,
                 ai_client=None,
                 max_error_history: int = 1000):
        """
        初始化错误分析器
        
        Args:
            config_manager: 配置管理器
            kv_store: 键值存储
            ai_client: AI客户端（用于智能分析）
            max_error_history: 最大错误历史记录数
        """
        self.config_manager = config_manager
        self.kv_store = kv_store
        self.ai_client = ai_client
        self.max_error_history = max_error_history
        
        # 错误数据存储
        self.error_history: List[ErrorRecord] = []
        self.error_patterns: Dict[str, ErrorPattern] = {}
        self.diagnosis_cache: Dict[str, DiagnosisResult] = {}
        
        # 统计信息
        self.stats = {
            'total_errors': 0,
            'errors_by_type': defaultdict(int),
            'errors_by_category': defaultdict(int),
            'errors_by_severity': defaultdict(int),
            'resolved_errors': 0,
            'avg_resolution_time': 0.0,
            'most_common_errors': [],
            'error_trends': {}
        }
        
        # 初始化内置错误模式
        self._initialize_error_patterns()
        
        logger.info("错误分析器初始化完成")
    
    def _initialize_error_patterns(self) -> None:
        """初始化内置错误模式"""
        patterns = [
            ErrorPattern(
                pattern_id="connection_error",
                pattern_name="连接错误",
                error_regex=r"(connection|connect|timeout|refused|unreachable)",
                category="network",
                severity="high",
                description="网络连接相关错误",
                common_causes=["网络不稳定", "服务器不可达", "防火墙阻拦", "DNS解析失败"],
                solutions=["检查网络连接", "验证服务器状态", "检查防火墙设置", "重试连接"]
            ),
            ErrorPattern(
                pattern_id="file_not_found",
                pattern_name="文件未找到",
                error_regex=r"(file not found|no such file|cannot find)",
                category="system",
                severity="medium",
                description="文件系统相关错误",
                common_causes=["文件路径错误", "文件被删除", "权限不足", "磁盘空间不足"],
                solutions=["检查文件路径", "验证文件存在", "检查文件权限", "清理磁盘空间"]
            ),
            ErrorPattern(
                pattern_id="memory_error",
                pattern_name="内存错误",
                error_regex=r"(memory|out of memory|allocation failed)",
                category="system",
                severity="critical",
                description="内存相关错误",
                common_causes=["内存不足", "内存泄漏", "大数据处理", "递归调用过深"],
                solutions=["增加内存", "优化内存使用", "分批处理数据", "检查内存泄漏"]
            ),
            ErrorPattern(
                pattern_id="permission_error",
                pattern_name="权限错误",
                error_regex=r"(permission|access denied|forbidden|unauthorized)",
                category="security",
                severity="high",
                description="权限和安全相关错误",
                common_causes=["权限不足", "用户认证失败", "文件权限错误", "API权限限制"],
                solutions=["检查用户权限", "更新认证信息", "修改文件权限", "联系管理员"]
            ),
            ErrorPattern(
                pattern_id="data_error",
                pattern_name="数据错误",
                error_regex=r"(invalid data|parse error|format error|validation)",
                category="data",
                severity="medium",
                description="数据格式和验证错误",
                common_causes=["数据格式错误", "数据类型不匹配", "数据验证失败", "编码问题"],
                solutions=["检查数据格式", "验证数据类型", "更新验证规则", "处理编码问题"]
            )
        ]
        
        for pattern in patterns:
            self.error_patterns[pattern.pattern_id] = pattern
    
    async def analyze_error(self, error: Exception, context: Dict[str, Any] = None) -> DiagnosisResult:
        """
        分析错误并提供诊断结果
        
        Args:
            error: 异常对象
            context: 错误上下文信息
            
        Returns:
            DiagnosisResult: 诊断结果
        """
        try:
            # 创建错误记录
            error_record = self._create_error_record(error, context or {})
            
            # 添加到历史记录
            self._add_error_record(error_record)
            
            # 模式匹配
            matched_patterns = self._match_error_patterns(error_record)
            
            # 严重性评估
            severity = self._assess_severity(error_record, matched_patterns)
            
            # 根因分析
            root_cause = await self._analyze_root_cause(error_record, matched_patterns)
            
            # 解决方案推荐
            solutions = self._recommend_solutions(matched_patterns)
            
            # 计算置信度
            confidence = self._calculate_confidence(matched_patterns, error_record)
            
            # 创建诊断结果
            diagnosis = DiagnosisResult(
                error_id=error_record.error_id,
                matched_patterns=[p.pattern_id for p in matched_patterns],
                severity_assessment=severity,
                root_cause_analysis=root_cause,
                recommended_solutions=solutions,
                confidence_score=confidence,
                timestamp=datetime.now()
            )
            
            # 缓存诊断结果
            self.diagnosis_cache[error_record.error_id] = diagnosis
            
            # 更新统计信息
            self._update_stats(error_record)
            
            logger.info(f"错误分析完成: {error_record.error_id}")
            return diagnosis
            
        except Exception as e:
            logger.error(f"错误分析失败: {e}")
            # 返回基础诊断结果
            return DiagnosisResult(
                error_id="unknown",
                matched_patterns=[],
                severity_assessment="unknown",
                root_cause_analysis="分析失败",
                recommended_solutions=["请联系技术支持"],
                confidence_score=0.0,
                timestamp=datetime.now()
            )

    def _create_error_record(self, error: Exception, context: Dict[str, Any]) -> ErrorRecord:
        """创建错误记录"""
        error_id = f"error_{int(datetime.now().timestamp() * 1000)}"
        error_type = type(error).__name__
        error_message = str(error)
        stack_trace = traceback.format_exc()

        # 基础分类
        category = self._classify_error(error_type, error_message)
        severity = self._initial_severity_assessment(error_type, error_message)

        return ErrorRecord(
            error_id=error_id,
            error_type=error_type,
            error_message=error_message,
            stack_trace=stack_trace,
            timestamp=datetime.now(),
            context=context,
            severity=severity,
            category=category
        )

    def _add_error_record(self, error_record: ErrorRecord) -> None:
        """添加错误记录到历史"""
        self.error_history.append(error_record)

        # 保持历史记录大小限制
        if len(self.error_history) > self.max_error_history:
            self.error_history.pop(0)

    def _classify_error(self, error_type: str, error_message: str) -> str:
        """错误分类"""
        error_text = f"{error_type} {error_message}".lower()

        if any(keyword in error_text for keyword in ['connection', 'network', 'timeout', 'http']):
            return 'network'
        elif any(keyword in error_text for keyword in ['file', 'directory', 'path', 'io']):
            return 'system'
        elif any(keyword in error_text for keyword in ['permission', 'access', 'auth', 'forbidden']):
            return 'security'
        elif any(keyword in error_text for keyword in ['data', 'parse', 'format', 'json', 'xml']):
            return 'data'
        elif any(keyword in error_text for keyword in ['memory', 'allocation', 'resource']):
            return 'resource'
        else:
            return 'logic'

    def _initial_severity_assessment(self, error_type: str, error_message: str) -> str:
        """初始严重性评估"""
        error_text = f"{error_type} {error_message}".lower()

        # 关键错误
        if any(keyword in error_text for keyword in ['critical', 'fatal', 'memory', 'system']):
            return 'critical'
        # 高级错误
        elif any(keyword in error_text for keyword in ['error', 'failed', 'exception', 'timeout']):
            return 'high'
        # 中级错误
        elif any(keyword in error_text for keyword in ['warning', 'invalid', 'not found']):
            return 'medium'
        else:
            return 'low'

    def _match_error_patterns(self, error_record: ErrorRecord) -> List[ErrorPattern]:
        """匹配错误模式"""
        matched_patterns = []
        error_text = f"{error_record.error_message} {error_record.stack_trace}".lower()

        for pattern in self.error_patterns.values():
            if re.search(pattern.error_regex, error_text, re.IGNORECASE):
                matched_patterns.append(pattern)
                pattern.occurrence_count += 1

        return matched_patterns

    def _assess_severity(self, error_record: ErrorRecord, matched_patterns: List[ErrorPattern]) -> str:
        """评估错误严重性"""
        if not matched_patterns:
            return error_record.severity

        # 取最高严重性
        severity_levels = {'low': 1, 'medium': 2, 'high': 3, 'critical': 4}
        max_severity = max([severity_levels.get(p.severity, 1) for p in matched_patterns])

        for level, value in severity_levels.items():
            if value == max_severity:
                return level

        return error_record.severity

    async def _analyze_root_cause(self, error_record: ErrorRecord,
                                 matched_patterns: List[ErrorPattern]) -> str:
        """分析根本原因"""
        if not matched_patterns:
            return "未找到匹配的错误模式，需要进一步分析"

        # 基于模式的根因分析
        common_causes = []
        for pattern in matched_patterns:
            common_causes.extend(pattern.common_causes)

        # 去重并排序
        cause_counts = Counter(common_causes)
        most_likely_causes = [cause for cause, _ in cause_counts.most_common(3)]

        root_cause = f"可能的根本原因：{', '.join(most_likely_causes)}"

        # 如果有AI客户端，进行智能分析
        if self.ai_client:
            try:
                ai_analysis = await self._ai_root_cause_analysis(error_record, matched_patterns)
                if ai_analysis:
                    root_cause += f"\n\nAI分析：{ai_analysis}"
            except Exception as e:
                logger.warning(f"AI根因分析失败: {e}")

        return root_cause

    async def _ai_root_cause_analysis(self, error_record: ErrorRecord,
                                     matched_patterns: List[ErrorPattern]) -> Optional[str]:
        """AI根因分析"""
        try:
            prompt = f"""
            请分析以下错误信息并提供根本原因分析：

            错误类型：{error_record.error_type}
            错误消息：{error_record.error_message}
            错误上下文：{json.dumps(error_record.context, ensure_ascii=False, indent=2)}
            匹配的错误模式：{[p.pattern_name for p in matched_patterns]}

            请提供简洁的根本原因分析（不超过200字）：
            """

            response = await self.ai_client.chat(prompt)
            return response.strip()

        except Exception as e:
            logger.error(f"AI根因分析失败: {e}")
            return None

    def _recommend_solutions(self, matched_patterns: List[ErrorPattern]) -> List[str]:
        """推荐解决方案"""
        if not matched_patterns:
            return ["请检查错误日志", "联系技术支持", "重试操作"]

        all_solutions = []
        for pattern in matched_patterns:
            all_solutions.extend(pattern.solutions)

        # 去重并保持顺序
        unique_solutions = []
        seen = set()
        for solution in all_solutions:
            if solution not in seen:
                unique_solutions.append(solution)
                seen.add(solution)

        return unique_solutions[:5]  # 返回前5个解决方案

    def _calculate_confidence(self, matched_patterns: List[ErrorPattern],
                            error_record: ErrorRecord) -> float:
        """计算诊断置信度"""
        if not matched_patterns:
            return 0.1

        # 基础置信度
        base_confidence = 0.5

        # 模式匹配加分
        pattern_bonus = min(len(matched_patterns) * 0.2, 0.4)

        # 历史数据加分
        similar_errors = self._find_similar_errors(error_record)
        history_bonus = min(len(similar_errors) * 0.1, 0.3)

        total_confidence = base_confidence + pattern_bonus + history_bonus
        return min(total_confidence, 1.0)

    def _find_similar_errors(self, error_record: ErrorRecord) -> List[ErrorRecord]:
        """查找相似错误"""
        similar_errors = []

        for record in self.error_history:
            if (record.error_type == error_record.error_type or
                record.category == error_record.category):
                similar_errors.append(record)

        return similar_errors

    def _update_stats(self, error_record: ErrorRecord) -> None:
        """更新统计信息"""
        self.stats['total_errors'] += 1
        self.stats['errors_by_type'][error_record.error_type] += 1
        self.stats['errors_by_category'][error_record.category] += 1
        self.stats['errors_by_severity'][error_record.severity] += 1

        # 更新最常见错误
        error_counts = Counter([r.error_type for r in self.error_history])
        self.stats['most_common_errors'] = error_counts.most_common(10)

    def get_error_history(self, limit: Optional[int] = None,
                         category: Optional[str] = None,
                         severity: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取错误历史记录

        Args:
            limit: 限制返回数量
            category: 错误类别过滤
            severity: 严重性过滤

        Returns:
            List[Dict[str, Any]]: 错误历史记录列表
        """
        errors = self.error_history

        # 应用过滤条件
        if category:
            errors = [e for e in errors if e.category == category]
        if severity:
            errors = [e for e in errors if e.severity == severity]

        # 应用数量限制
        if limit:
            errors = errors[-limit:]

        return [error.to_dict() for error in errors]

    def get_error_stats(self) -> Dict[str, Any]:
        """
        获取错误统计信息

        Returns:
            Dict[str, Any]: 错误统计信息
        """
        return {
            **self.stats,
            'error_patterns_count': len(self.error_patterns),
            'diagnosis_cache_size': len(self.diagnosis_cache),
            'recent_errors_24h': len([
                e for e in self.error_history
                if e.timestamp > datetime.now() - timedelta(hours=24)
            ])
        }

    def get_error_trends(self, hours: int = 24) -> Dict[str, Any]:
        """
        获取错误趋势分析

        Args:
            hours: 分析时间范围（小时）

        Returns:
            Dict[str, Any]: 错误趋势数据
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_errors = [e for e in self.error_history if e.timestamp > cutoff_time]

        # 按小时分组
        hourly_counts = defaultdict(int)
        for error in recent_errors:
            hour_key = error.timestamp.strftime('%Y-%m-%d %H:00')
            hourly_counts[hour_key] += 1

        # 按类别统计
        category_counts = Counter([e.category for e in recent_errors])

        # 按严重性统计
        severity_counts = Counter([e.severity for e in recent_errors])

        return {
            'time_range_hours': hours,
            'total_errors': len(recent_errors),
            'hourly_distribution': dict(hourly_counts),
            'category_distribution': dict(category_counts),
            'severity_distribution': dict(severity_counts),
            'error_rate_per_hour': len(recent_errors) / hours if hours > 0 else 0
        }

    def get_diagnosis_result(self, error_id: str) -> Optional[Dict[str, Any]]:
        """
        获取诊断结果

        Args:
            error_id: 错误ID

        Returns:
            Optional[Dict[str, Any]]: 诊断结果，如果不存在返回None
        """
        diagnosis = self.diagnosis_cache.get(error_id)
        return diagnosis.to_dict() if diagnosis else None

    def add_error_pattern(self, pattern: ErrorPattern) -> bool:
        """
        添加错误模式

        Args:
            pattern: 错误模式

        Returns:
            bool: 添加是否成功
        """
        try:
            self.error_patterns[pattern.pattern_id] = pattern
            logger.info(f"错误模式已添加: {pattern.pattern_id}")
            return True
        except Exception as e:
            logger.error(f"添加错误模式失败: {e}")
            return False

    def remove_error_pattern(self, pattern_id: str) -> bool:
        """
        移除错误模式

        Args:
            pattern_id: 模式ID

        Returns:
            bool: 移除是否成功
        """
        try:
            if pattern_id in self.error_patterns:
                del self.error_patterns[pattern_id]
                logger.info(f"错误模式已移除: {pattern_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"移除错误模式失败: {e}")
            return False

    def get_error_patterns(self) -> List[Dict[str, Any]]:
        """
        获取所有错误模式

        Returns:
            List[Dict[str, Any]]: 错误模式列表
        """
        return [pattern.to_dict() for pattern in self.error_patterns.values()]

    async def resolve_error(self, error_id: str, resolution: str) -> bool:
        """
        标记错误为已解决

        Args:
            error_id: 错误ID
            resolution: 解决方案描述

        Returns:
            bool: 标记是否成功
        """
        try:
            for error in self.error_history:
                if error.error_id == error_id:
                    error.resolved = True
                    error.resolution = resolution
                    self.stats['resolved_errors'] += 1
                    logger.info(f"错误已标记为解决: {error_id}")
                    return True

            logger.warning(f"未找到错误记录: {error_id}")
            return False

        except Exception as e:
            logger.error(f"标记错误解决失败: {e}")
            return False

    def clear_error_history(self, older_than_hours: Optional[int] = None) -> int:
        """
        清理错误历史记录

        Args:
            older_than_hours: 清理多少小时前的记录，None表示清理全部

        Returns:
            int: 清理的记录数量
        """
        try:
            if older_than_hours is None:
                count = len(self.error_history)
                self.error_history.clear()
                self.diagnosis_cache.clear()
                logger.info(f"已清理所有错误历史记录: {count}条")
                return count

            cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
            original_count = len(self.error_history)

            # 保留较新的记录
            self.error_history = [e for e in self.error_history if e.timestamp > cutoff_time]

            # 清理对应的诊断缓存
            valid_error_ids = {e.error_id for e in self.error_history}
            self.diagnosis_cache = {
                k: v for k, v in self.diagnosis_cache.items()
                if k in valid_error_ids
            }

            cleared_count = original_count - len(self.error_history)
            logger.info(f"已清理{older_than_hours}小时前的错误记录: {cleared_count}条")
            return cleared_count

        except Exception as e:
            logger.error(f"清理错误历史失败: {e}")
            return 0

    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.error_history.clear()
            self.diagnosis_cache.clear()
            self.stats.clear()
            logger.info("错误分析器资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")
