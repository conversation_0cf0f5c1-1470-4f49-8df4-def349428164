"""
多步推理引擎模块

提供复杂问题的多步骤逻辑推理、因果关系分析和假设验证功能。
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from enum import Enum

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.ai_client import SimpleAIClient
from core.prompt_manager import PromptManager
from core.kv_store import KVStore


class ReasoningType(Enum):
    """推理类型枚举"""
    DEDUCTIVE = "deductive"  # 演绎推理
    INDUCTIVE = "inductive"  # 归纳推理
    ABDUCTIVE = "abductive"  # 溯因推理
    CAUSAL = "causal"       # 因果推理
    ANALOGICAL = "analogical"  # 类比推理


class ConfidenceLevel(Enum):
    """置信度等级"""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.95


@dataclass
class ReasoningStep:
    """推理步骤数据模型"""
    step_id: str
    premise: str
    rule: str
    conclusion: str
    reasoning_type: ReasoningType
    confidence: float = 0.0
    evidence: List[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.evidence is None:
            self.evidence = []
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'step_id': self.step_id,
            'premise': self.premise,
            'rule': self.rule,
            'conclusion': self.conclusion,
            'reasoning_type': self.reasoning_type.value,
            'confidence': self.confidence,
            'evidence': self.evidence,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class ReasoningChain:
    """推理链数据模型"""
    chain_id: str
    problem: str
    steps: List[ReasoningStep]
    final_conclusion: str
    overall_confidence: float = 0.0
    reasoning_path: List[str] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.reasoning_path is None:
            self.reasoning_path = []
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'chain_id': self.chain_id,
            'problem': self.problem,
            'steps': [step.to_dict() for step in self.steps],
            'final_conclusion': self.final_conclusion,
            'overall_confidence': self.overall_confidence,
            'reasoning_path': self.reasoning_path,
            'created_at': self.created_at.isoformat()
        }


@dataclass
class ReasoningResult:
    """推理结果数据模型"""
    success: bool
    reasoning_chain: Optional[ReasoningChain] = None
    error: Optional[str] = None
    execution_time: float = 0.0
    steps_count: int = 0
    confidence_distribution: Dict[str, float] = None
    
    def __post_init__(self):
        if self.confidence_distribution is None:
            self.confidence_distribution = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            'success': self.success,
            'error': self.error,
            'execution_time': self.execution_time,
            'steps_count': self.steps_count,
            'confidence_distribution': self.confidence_distribution
        }
        if self.reasoning_chain:
            result['reasoning_chain'] = self.reasoning_chain.to_dict()
        return result


class ReasoningEngine:
    """多步推理引擎"""
    
    def __init__(self, ai_client: SimpleAIClient, prompt_manager: PromptManager, 
                 kv_store: Optional[KVStore] = None):
        """
        初始化推理引擎
        
        Args:
            ai_client: AI客户端实例
            prompt_manager: 提示词管理器实例
            kv_store: 可选的KV存储实例，用于缓存推理结果
        """
        self.ai_client = ai_client
        self.prompt_manager = prompt_manager
        self.kv_store = kv_store
        self.logger = logging.getLogger(__name__)
        
        # 推理统计信息
        self.reasoning_stats = {
            'total_reasoning_chains': 0,
            'successful_chains': 0,
            'failed_chains': 0,
            'average_steps_per_chain': 0.0,
            'average_confidence': 0.0,
            'reasoning_type_distribution': {},
            'total_execution_time': 0.0
        }
        
        # 推理规则库
        self.reasoning_rules = {
            ReasoningType.DEDUCTIVE: [
                "如果前提为真，且推理规则有效，则结论必然为真",
                "从一般性原理推导出特定结论",
                "基于已知事实和逻辑规则进行推导"
            ],
            ReasoningType.INDUCTIVE: [
                "从特定观察中归纳出一般性规律",
                "基于样本数据推断总体特征",
                "从经验事实中提取模式和规律"
            ],
            ReasoningType.ABDUCTIVE: [
                "寻找最佳解释来说明观察到的现象",
                "从结果推断最可能的原因",
                "基于不完整信息进行最合理的推测"
            ],
            ReasoningType.CAUSAL: [
                "分析事件之间的因果关系",
                "识别原因和结果的链条",
                "评估因果关系的强度和方向"
            ],
            ReasoningType.ANALOGICAL: [
                "基于相似性进行推理",
                "从已知情况类推到新情况",
                "利用结构相似性进行推导"
            ]
        }
    
    async def multi_step_reasoning(self, problem: str, max_steps: int = 10,
                                 reasoning_types: List[ReasoningType] = None) -> ReasoningResult:
        """
        执行多步推理
        
        Args:
            problem: 需要推理的问题
            max_steps: 最大推理步数
            reasoning_types: 允许的推理类型列表
            
        Returns:
            ReasoningResult: 推理结果
        """
        start_time = time.time()
        chain_id = f"reasoning_{int(time.time() * 1000)}"
        
        try:
            self.logger.info(f"开始多步推理: {problem}")
            
            # 默认推理类型
            if reasoning_types is None:
                reasoning_types = [ReasoningType.DEDUCTIVE, ReasoningType.INDUCTIVE, 
                                 ReasoningType.ABDUCTIVE]
            
            # 初始化推理链
            reasoning_chain = ReasoningChain(
                chain_id=chain_id,
                problem=problem,
                steps=[],
                final_conclusion=""
            )
            
            # 执行推理步骤
            current_context = problem
            for step_num in range(max_steps):
                step_result = await self._execute_reasoning_step(
                    step_num + 1, current_context, reasoning_types, reasoning_chain
                )

                if not step_result:
                    self.logger.debug(f"推理步骤 {step_num + 1} 执行失败，停止推理")
                    break

                reasoning_chain.steps.append(step_result)
                current_context = step_result.conclusion

                # 检查是否达到最终结论
                if await self._is_conclusion_reached(current_context, problem):
                    self.logger.debug(f"在步骤 {step_num + 1} 达到最终结论")
                    break
            
            # 检查是否有有效的推理步骤
            if not reasoning_chain.steps:
                self.logger.warning("没有生成有效的推理步骤")
                self._update_reasoning_stats(None, time.time() - start_time, False)
                return ReasoningResult(
                    success=False,
                    error="没有生成有效的推理步骤",
                    execution_time=time.time() - start_time
                )

            # 生成最终结论
            reasoning_chain.final_conclusion = await self._generate_final_conclusion(reasoning_chain)

            # 计算整体置信度
            reasoning_chain.overall_confidence = self._calculate_chain_confidence(reasoning_chain.steps)

            # 生成推理路径
            reasoning_chain.reasoning_path = [step.conclusion for step in reasoning_chain.steps]

            # 缓存结果
            if self.kv_store:
                await self._cache_reasoning_result(reasoning_chain)

            # 更新统计信息
            self._update_reasoning_stats(reasoning_chain, time.time() - start_time, True)

            return ReasoningResult(
                success=True,
                reasoning_chain=reasoning_chain,
                execution_time=time.time() - start_time,
                steps_count=len(reasoning_chain.steps),
                confidence_distribution=self._get_confidence_distribution(reasoning_chain.steps)
            )
            
        except Exception as e:
            self.logger.error(f"推理执行失败: {str(e)}")
            self._update_reasoning_stats(None, time.time() - start_time, False)

            return ReasoningResult(
                success=False,
                error=str(e),
                execution_time=time.time() - start_time
            )

    async def logical_deduction(self, premises: List[str], rules: List[str] = None) -> ReasoningResult:
        """
        执行逻辑推理和演绎

        Args:
            premises: 前提条件列表
            rules: 推理规则列表

        Returns:
            ReasoningResult: 推理结果
        """
        start_time = time.time()
        chain_id = f"deduction_{int(time.time() * 1000)}"

        try:
            self.logger.info(f"开始逻辑演绎推理，前提数量: {len(premises)}")

            # 构建推理提示词
            prompt = await self._build_deduction_prompt(premises, rules)

            # 调用AI进行推理
            response = await self.ai_client.chat(prompt)

            # 解析推理结果
            reasoning_chain = await self._parse_deduction_response(response, premises, chain_id)

            # 验证推理有效性
            is_valid = await self._validate_reasoning_chain(reasoning_chain)

            if not is_valid:
                return ReasoningResult(
                    success=False,
                    error="推理链验证失败",
                    execution_time=time.time() - start_time
                )

            # 缓存结果
            if self.kv_store:
                await self._cache_reasoning_result(reasoning_chain)

            self._update_reasoning_stats(reasoning_chain, time.time() - start_time, True)

            return ReasoningResult(
                success=True,
                reasoning_chain=reasoning_chain,
                execution_time=time.time() - start_time,
                steps_count=len(reasoning_chain.steps),
                confidence_distribution=self._get_confidence_distribution(reasoning_chain.steps)
            )

        except Exception as e:
            self.logger.error(f"逻辑演绎推理失败: {str(e)}")
            self._update_reasoning_stats(None, time.time() - start_time, False)

            return ReasoningResult(
                success=False,
                error=str(e),
                execution_time=time.time() - start_time
            )

    async def causal_analysis(self, events: List[Dict[str, Any]],
                            analysis_depth: str = "medium") -> ReasoningResult:
        """
        执行因果关系分析

        Args:
            events: 事件列表，每个事件包含时间、描述等信息
            analysis_depth: 分析深度 (shallow/medium/deep)

        Returns:
            ReasoningResult: 因果分析结果
        """
        start_time = time.time()
        chain_id = f"causal_{int(time.time() * 1000)}"

        try:
            self.logger.info(f"开始因果关系分析，事件数量: {len(events)}")

            # 构建因果分析提示词
            prompt = await self._build_causal_analysis_prompt(events, analysis_depth)

            # 调用AI进行分析
            response = await self.ai_client.chat(prompt)

            # 解析因果关系
            reasoning_chain = await self._parse_causal_response(response, events, chain_id)

            # 评估因果关系强度
            await self._evaluate_causal_strength(reasoning_chain)

            # 缓存结果
            if self.kv_store:
                await self._cache_reasoning_result(reasoning_chain)

            self._update_reasoning_stats(reasoning_chain, time.time() - start_time, True)

            return ReasoningResult(
                success=True,
                reasoning_chain=reasoning_chain,
                execution_time=time.time() - start_time,
                steps_count=len(reasoning_chain.steps),
                confidence_distribution=self._get_confidence_distribution(reasoning_chain.steps)
            )

        except Exception as e:
            self.logger.error(f"因果关系分析失败: {str(e)}")
            self._update_reasoning_stats(None, time.time() - start_time, False)

            return ReasoningResult(
                success=False,
                error=str(e),
                execution_time=time.time() - start_time
            )

    async def hypothesis_testing(self, hypothesis: str, evidence: List[str],
                                confidence_threshold: float = 0.7) -> ReasoningResult:
        """
        执行假设验证

        Args:
            hypothesis: 待验证的假设
            evidence: 证据列表
            confidence_threshold: 置信度阈值

        Returns:
            ReasoningResult: 假设验证结果
        """
        start_time = time.time()
        chain_id = f"hypothesis_{int(time.time() * 1000)}"

        try:
            self.logger.info(f"开始假设验证: {hypothesis}")

            # 构建假设验证提示词
            prompt = await self._build_hypothesis_testing_prompt(hypothesis, evidence)

            # 调用AI进行验证
            response = await self.ai_client.chat(prompt)

            # 解析验证结果
            reasoning_chain = await self._parse_hypothesis_response(response, hypothesis, evidence, chain_id)

            # 计算假设支持度
            support_score = await self._calculate_hypothesis_support(reasoning_chain, evidence)

            # 判断假设是否通过验证
            hypothesis_accepted = support_score >= confidence_threshold

            # 更新结论
            reasoning_chain.final_conclusion = f"假设{'接受' if hypothesis_accepted else '拒绝'}，支持度: {support_score:.2f}"

            # 缓存结果
            if self.kv_store:
                await self._cache_reasoning_result(reasoning_chain)

            self._update_reasoning_stats(reasoning_chain, time.time() - start_time, True)

            return ReasoningResult(
                success=True,
                reasoning_chain=reasoning_chain,
                execution_time=time.time() - start_time,
                steps_count=len(reasoning_chain.steps),
                confidence_distribution=self._get_confidence_distribution(reasoning_chain.steps)
            )

        except Exception as e:
            self.logger.error(f"假设验证失败: {str(e)}")
            self._update_reasoning_stats(None, time.time() - start_time, False)

            return ReasoningResult(
                success=False,
                error=str(e),
                execution_time=time.time() - start_time
            )

    def get_reasoning_stats(self) -> Dict[str, Any]:
        """获取推理统计信息"""
        return self.reasoning_stats.copy()

    def reset_stats(self) -> None:
        """重置推理统计信息"""
        self.reasoning_stats = {
            'total_reasoning_chains': 0,
            'successful_chains': 0,
            'failed_chains': 0,
            'average_steps_per_chain': 0.0,
            'average_confidence': 0.0,
            'reasoning_type_distribution': {},
            'total_execution_time': 0.0
        }

    # 私有辅助方法
    async def _execute_reasoning_step(self, step_num: int, context: str,
                                    reasoning_types: List[ReasoningType],
                                    reasoning_chain: ReasoningChain) -> Optional[ReasoningStep]:
        """执行单个推理步骤"""
        try:
            # 选择推理类型
            reasoning_type = await self._select_reasoning_type(context, reasoning_types)

            # 构建推理提示词
            prompt = await self._build_step_prompt(step_num, context, reasoning_type, reasoning_chain)

            # 调用AI进行推理
            response = await self.ai_client.chat(prompt)

            # 解析推理步骤
            step = await self._parse_reasoning_step(response, step_num, reasoning_type)

            # 验证推理步骤
            if await self._validate_reasoning_step(step):
                # 计算置信度
                step.confidence = await self._calculate_step_confidence(step, context)
                return step
            else:
                self.logger.warning(f"推理步骤验证失败: {step.step_id}")
                return None

        except Exception as e:
            self.logger.error(f"推理步骤执行失败: {str(e)}")
            # 重新抛出异常，让上层处理
            raise

    async def _select_reasoning_type(self, context: str,
                                   available_types: List[ReasoningType]) -> ReasoningType:
        """选择最适合的推理类型"""
        # 简化实现：基于上下文关键词选择推理类型
        context_lower = context.lower()

        if any(word in context_lower for word in ['因为', '导致', '原因', '结果']):
            return ReasoningType.CAUSAL
        elif any(word in context_lower for word in ['所有', '每个', '必然', '一定']):
            return ReasoningType.DEDUCTIVE
        elif any(word in context_lower for word in ['通常', '大多数', '可能', '倾向于']):
            return ReasoningType.INDUCTIVE
        elif any(word in context_lower for word in ['最可能', '解释', '假设']):
            return ReasoningType.ABDUCTIVE
        elif any(word in context_lower for word in ['类似', '相似', '像']):
            return ReasoningType.ANALOGICAL
        else:
            # 默认使用演绎推理
            return ReasoningType.DEDUCTIVE

    async def _build_step_prompt(self, step_num: int, context: str,
                               reasoning_type: ReasoningType,
                               reasoning_chain: ReasoningChain) -> str:
        """构建推理步骤提示词"""
        rules = self.reasoning_rules.get(reasoning_type, [])
        rule_text = "\\n".join(rules)

        previous_steps = ""
        if reasoning_chain.steps:
            previous_steps = "\n".join([
                f"步骤{i+1}: {step.premise} -> {step.conclusion}"
                for i, step in enumerate(reasoning_chain.steps)
            ])

        prompt = f"""
你是一个专业的逻辑推理专家。请基于以下信息进行第{step_num}步推理：

问题背景: {reasoning_chain.problem}

当前上下文: {context}

推理类型: {reasoning_type.value}
推理规则: {rule_text}

之前的推理步骤:
{previous_steps}

请按照以下格式输出推理步骤：
前提: [明确的前提条件]
规则: [应用的推理规则]
结论: [得出的结论]
证据: [支持结论的证据]

要求：
1. 推理逻辑清晰、严谨
2. 结论必须从前提和规则中合理推导
3. 提供充分的证据支持
4. 避免循环论证和逻辑谬误
"""
        return prompt

    async def _parse_reasoning_step(self, response: str, step_num: int,
                                  reasoning_type: ReasoningType) -> ReasoningStep:
        """解析AI响应为推理步骤"""
        try:
            lines = response.strip().split('\n')  # 修复：使用单个反斜杠
            premise = ""
            rule = ""
            conclusion = ""
            evidence = []

            for line in lines:
                line = line.strip()
                if line.startswith('前提:') or line.startswith('前提：'):
                    premise = line[3:].strip()
                elif line.startswith('规则:') or line.startswith('规则：'):
                    rule = line[3:].strip()
                elif line.startswith('结论:') or line.startswith('结论：'):
                    conclusion = line[3:].strip()
                elif line.startswith('证据:') or line.startswith('证据：'):
                    evidence_text = line[3:].strip()
                    evidence = [e.strip() for e in evidence_text.split(',') if e.strip()]

            # 如果解析失败，提供默认值
            if not premise:
                premise = f"步骤{step_num}的前提"
            if not rule:
                rule = f"{reasoning_type.value}推理规则"
            if not conclusion:
                conclusion = f"步骤{step_num}的结论"

            step = ReasoningStep(
                step_id=f"step_{step_num}_{int(time.time() * 1000)}",
                premise=premise,
                rule=rule,
                conclusion=conclusion,
                reasoning_type=reasoning_type,
                evidence=evidence
            )

            self.logger.debug(f"解析推理步骤: premise='{premise}', rule='{rule}', conclusion='{conclusion}'")
            return step

        except Exception as e:
            self.logger.error(f"解析推理步骤失败: {str(e)}")
            # 返回一个基本的推理步骤而不是抛出异常
            return ReasoningStep(
                step_id=f"step_{step_num}_{int(time.time() * 1000)}",
                premise=f"步骤{step_num}的前提",
                rule=f"{reasoning_type.value}推理规则",
                conclusion=f"步骤{step_num}的结论",
                reasoning_type=reasoning_type,
                evidence=[]
            )

    async def _validate_reasoning_step(self, step: ReasoningStep) -> bool:
        """验证推理步骤的有效性"""
        # 基本验证：检查必要字段是否存在
        if not step.premise or not step.rule or not step.conclusion:
            self.logger.debug(f"推理步骤缺少必要字段: premise={bool(step.premise)}, rule={bool(step.rule)}, conclusion={bool(step.conclusion)}")
            return False

        # 检查字段长度
        if len(step.premise.strip()) < 3 or len(step.conclusion.strip()) < 3:
            self.logger.debug(f"推理步骤字段过短")
            return False

        # 简化的逻辑一致性检查
        # 避免完全相同的前提和结论
        if step.premise.strip().lower() == step.conclusion.strip().lower():
            self.logger.debug(f"前提和结论完全相同")
            return False

        return True

    async def _calculate_step_confidence(self, step: ReasoningStep, context: str) -> float:
        """计算推理步骤的置信度"""
        confidence = 0.5  # 基础置信度

        # 基于证据数量调整
        if step.evidence:
            confidence += min(len(step.evidence) * 0.1, 0.3)

        # 基于推理类型调整
        type_confidence = {
            ReasoningType.DEDUCTIVE: 0.9,
            ReasoningType.INDUCTIVE: 0.7,
            ReasoningType.ABDUCTIVE: 0.6,
            ReasoningType.CAUSAL: 0.8,
            ReasoningType.ANALOGICAL: 0.6
        }
        confidence *= type_confidence.get(step.reasoning_type, 0.7)

        # 确保置信度在合理范围内
        return max(0.1, min(0.95, confidence))

    def _calculate_chain_confidence(self, steps: List[ReasoningStep]) -> float:
        """计算推理链的整体置信度"""
        if not steps:
            return 0.0

        # 使用加权平均数计算整体置信度，而不是几何平均数
        # 几何平均数会让置信度过低
        total_confidence = sum(step.confidence for step in steps)
        average_confidence = total_confidence / len(steps)

        # 对于多步推理，适当降低整体置信度
        penalty_factor = 0.95 ** (len(steps) - 1)  # 每增加一步，降低5%

        return average_confidence * penalty_factor

    def _get_confidence_distribution(self, steps: List[ReasoningStep]) -> Dict[str, float]:
        """获取置信度分布"""
        if not steps:
            return {}

        distribution = {}
        for level in ConfidenceLevel:
            count = sum(1 for step in steps if abs(step.confidence - level.value) < 0.1)
            distribution[level.name] = count / len(steps)

        return distribution

    async def _is_conclusion_reached(self, current_context: str, original_problem: str) -> bool:
        """判断是否达到最终结论"""
        # 简化实现：检查是否包含结论性词汇
        conclusion_indicators = ['因此', '所以', '综上', '结论是', '可以得出']
        return any(indicator in current_context for indicator in conclusion_indicators)

    async def _generate_final_conclusion(self, reasoning_chain: ReasoningChain) -> str:
        """生成最终结论"""
        if not reasoning_chain.steps:
            return "无法得出结论"

        last_step = reasoning_chain.steps[-1]
        return f"基于{len(reasoning_chain.steps)}步推理，得出结论：{last_step.conclusion}"

    async def _cache_reasoning_result(self, reasoning_chain: ReasoningChain) -> None:
        """缓存推理结果"""
        if self.kv_store:
            cache_key = f"reasoning_chain_{reasoning_chain.chain_id}"
            await self.kv_store.set(cache_key, reasoning_chain.to_dict(), ttl=3600)  # 缓存1小时

    def _update_reasoning_stats(self, reasoning_chain: Optional[ReasoningChain],
                              execution_time: float, success: bool) -> None:
        """更新推理统计信息"""
        self.reasoning_stats['total_reasoning_chains'] += 1
        self.reasoning_stats['total_execution_time'] += execution_time

        if success and reasoning_chain:
            self.reasoning_stats['successful_chains'] += 1

            # 更新平均步数
            total_steps = (self.reasoning_stats['average_steps_per_chain'] *
                          (self.reasoning_stats['successful_chains'] - 1) +
                          len(reasoning_chain.steps))
            self.reasoning_stats['average_steps_per_chain'] = total_steps / self.reasoning_stats['successful_chains']

            # 更新平均置信度
            total_confidence = (self.reasoning_stats['average_confidence'] *
                              (self.reasoning_stats['successful_chains'] - 1) +
                              reasoning_chain.overall_confidence)
            self.reasoning_stats['average_confidence'] = total_confidence / self.reasoning_stats['successful_chains']

            # 更新推理类型分布
            for step in reasoning_chain.steps:
                reasoning_type = step.reasoning_type.value
                self.reasoning_stats['reasoning_type_distribution'][reasoning_type] = (
                    self.reasoning_stats['reasoning_type_distribution'].get(reasoning_type, 0) + 1
                )
        else:
            self.reasoning_stats['failed_chains'] += 1

    # 专门方法的辅助函数
    async def _build_deduction_prompt(self, premises: List[str], rules: List[str] = None) -> str:
        """构建演绎推理提示词"""
        premises_text = "\n".join([f"{i+1}. {premise}" for i, premise in enumerate(premises)])
        rules_text = "\n".join(rules) if rules else "使用标准逻辑推理规则"

        return f"""
请基于以下前提进行逻辑演绎推理：

前提条件：
{premises_text}

推理规则：
{rules_text}

请按照严格的逻辑推理步骤，从前提条件推导出结论。
每一步都要明确说明使用的推理规则和逻辑依据。

输出格式：
步骤1: [前提] + [规则] -> [中间结论]
步骤2: [中间结论] + [规则] -> [进一步结论]
...
最终结论: [最终推导结果]
"""

    async def _parse_deduction_response(self, response: str, premises: List[str],
                                      chain_id: str) -> ReasoningChain:
        """解析演绎推理响应"""
        lines = response.strip().split('\n')  # 修复换行符
        steps = []
        final_conclusion = ""

        step_num = 1
        for line in lines:
            line = line.strip()
            if line.startswith('步骤') and '->' in line:
                # 解析推理步骤
                parts = line.split('->')
                if len(parts) == 2:
                    premise_rule = parts[0].strip()
                    conclusion = parts[1].strip()

                    step = ReasoningStep(
                        step_id=f"deduction_step_{step_num}",
                        premise=premise_rule,
                        rule="演绎推理规则",
                        conclusion=conclusion,
                        reasoning_type=ReasoningType.DEDUCTIVE,
                        confidence=0.8  # 设置默认置信度
                    )
                    steps.append(step)
                    step_num += 1
            elif line.startswith('最终结论:') or line.startswith('最终结论：'):
                final_conclusion = line[5:].strip()

        # 如果没有解析到最终结论，生成一个
        if not final_conclusion and steps:
            final_conclusion = steps[-1].conclusion
        elif not final_conclusion:
            final_conclusion = "基于前提的演绎推理结论"

        return ReasoningChain(
            chain_id=chain_id,
            problem=f"基于{len(premises)}个前提的演绎推理",
            steps=steps,
            final_conclusion=final_conclusion
        )

    async def _validate_reasoning_chain(self, reasoning_chain: ReasoningChain) -> bool:
        """验证推理链的有效性"""
        if not reasoning_chain.steps:
            self.logger.debug("推理链没有步骤")
            return False

        # 检查每个步骤的有效性
        for i, step in enumerate(reasoning_chain.steps):
            if not await self._validate_reasoning_step(step):
                self.logger.debug(f"推理链第{i+1}步验证失败")
                return False

        # 检查是否有最终结论
        if not reasoning_chain.final_conclusion or len(reasoning_chain.final_conclusion.strip()) < 3:
            self.logger.debug("推理链缺少有效的最终结论")
            return False

        return True

    async def _build_causal_analysis_prompt(self, events: List[Dict[str, Any]],
                                          analysis_depth: str) -> str:
        """构建因果分析提示词"""
        events_text = "\\n".join([
            f"事件{i+1}: {event.get('description', '')} (时间: {event.get('timestamp', '未知')})"
            for i, event in enumerate(events)
        ])

        depth_instructions = {
            'shallow': "进行基础的因果关系识别",
            'medium': "进行详细的因果链分析，包括直接和间接因果关系",
            'deep': "进行深度因果分析，包括多层次因果关系、反馈循环和系统性影响"
        }

        return f"""
请对以下事件序列进行因果关系分析：

事件列表：
{events_text}

分析要求：{depth_instructions.get(analysis_depth, '进行标准因果分析')}

请按照以下格式输出分析结果：
1. 直接因果关系：A导致B
2. 间接因果关系：A通过C导致B
3. 因果强度评估：强/中/弱
4. 置信度评估：0.0-1.0

要求：
- 基于时间顺序分析因果关系
- 区分相关性和因果性
- 考虑可能的混淆变量
- 评估因果关系的强度和可信度
"""

    async def _parse_causal_response(self, response: str, events: List[Dict[str, Any]],
                                   chain_id: str) -> ReasoningChain:
        """解析因果分析响应"""
        lines = response.strip().split('\\n')
        steps = []

        step_num = 1
        for line in lines:
            line = line.strip()
            if '导致' in line:
                # 解析因果关系
                if '通过' in line:
                    # 间接因果关系
                    reasoning_type = ReasoningType.CAUSAL
                    rule = "间接因果关系分析"
                else:
                    # 直接因果关系
                    reasoning_type = ReasoningType.CAUSAL
                    rule = "直接因果关系分析"

                step = ReasoningStep(
                    step_id=f"causal_step_{step_num}",
                    premise=f"基于事件序列的观察",
                    rule=rule,
                    conclusion=line,
                    reasoning_type=reasoning_type
                )
                steps.append(step)
                step_num += 1

        return ReasoningChain(
            chain_id=chain_id,
            problem=f"对{len(events)}个事件的因果关系分析",
            steps=steps,
            final_conclusion=f"识别出{len(steps)}个因果关系"
        )

    async def _evaluate_causal_strength(self, reasoning_chain: ReasoningChain) -> None:
        """评估因果关系强度"""
        for step in reasoning_chain.steps:
            # 基于关键词评估因果强度
            conclusion = step.conclusion.lower()
            if any(word in conclusion for word in ['直接导致', '必然导致', '强烈影响']):
                step.confidence = 0.9
            elif any(word in conclusion for word in ['可能导致', '倾向于', '影响']):
                step.confidence = 0.7
            elif any(word in conclusion for word in ['相关', '关联', '可能相关']):
                step.confidence = 0.5
            else:
                step.confidence = 0.6

    async def _build_hypothesis_testing_prompt(self, hypothesis: str,
                                             evidence: List[str]) -> str:
        """构建假设验证提示词"""
        evidence_text = "\\n".join([f"证据{i+1}: {ev}" for i, ev in enumerate(evidence)])

        return f"""
请对以下假设进行科学验证：

假设：{hypothesis}

可用证据：
{evidence_text}

请按照以下步骤进行验证：
1. 分析假设的可验证性
2. 评估每个证据对假设的支持程度
3. 识别支持和反对假设的证据
4. 考虑替代解释的可能性
5. 给出验证结论和置信度

输出格式：
支持证据：[列出支持假设的证据]
反对证据：[列出反对假设的证据]
替代解释：[可能的其他解释]
验证结论：[接受/拒绝假设]
置信度：[0.0-1.0]
"""

    async def _parse_hypothesis_response(self, response: str, hypothesis: str,
                                       evidence: List[str], chain_id: str) -> ReasoningChain:
        """解析假设验证响应"""
        lines = response.strip().split('\\n')
        steps = []

        support_evidence = []
        against_evidence = []
        alternatives = []

        current_section = None
        for line in lines:
            line = line.strip()
            if line.startswith('支持证据：'):
                current_section = 'support'
                support_evidence.append(line[5:].strip())
            elif line.startswith('反对证据：'):
                current_section = 'against'
                against_evidence.append(line[5:].strip())
            elif line.startswith('替代解释：'):
                current_section = 'alternatives'
                alternatives.append(line[5:].strip())
            elif current_section and line:
                if current_section == 'support':
                    support_evidence.append(line)
                elif current_section == 'against':
                    against_evidence.append(line)
                elif current_section == 'alternatives':
                    alternatives.append(line)

        # 创建验证步骤
        if support_evidence:
            step = ReasoningStep(
                step_id=f"hypothesis_support",
                premise=f"假设：{hypothesis}",
                rule="证据支持分析",
                conclusion=f"支持证据：{'; '.join(support_evidence)}",
                reasoning_type=ReasoningType.ABDUCTIVE,
                evidence=support_evidence
            )
            steps.append(step)

        if against_evidence:
            step = ReasoningStep(
                step_id=f"hypothesis_against",
                premise=f"假设：{hypothesis}",
                rule="反证分析",
                conclusion=f"反对证据：{'; '.join(against_evidence)}",
                reasoning_type=ReasoningType.ABDUCTIVE,
                evidence=against_evidence
            )
            steps.append(step)

        return ReasoningChain(
            chain_id=chain_id,
            problem=f"验证假设：{hypothesis}",
            steps=steps,
            final_conclusion="假设验证完成"
        )

    async def _calculate_hypothesis_support(self, reasoning_chain: ReasoningChain,
                                          evidence: List[str]) -> float:
        """计算假设支持度"""
        if not reasoning_chain.steps:
            return 0.0

        support_score = 0.0
        total_evidence = len(evidence)

        for step in reasoning_chain.steps:
            if '支持证据' in step.conclusion:
                # 支持证据增加分数
                support_count = len(step.evidence)
                support_score += (support_count / total_evidence) * 0.8
            elif '反对证据' in step.conclusion:
                # 反对证据减少分数
                against_count = len(step.evidence)
                support_score -= (against_count / total_evidence) * 0.6

        # 确保分数在0-1范围内
        return max(0.0, min(1.0, support_score))
