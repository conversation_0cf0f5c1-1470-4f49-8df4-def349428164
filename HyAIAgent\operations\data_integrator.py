"""
HyAIAgent 第四阶段 - 数据整合器模块
负责整合来自不同源的数据，包括搜索结果、本地文件和处理后的内容
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Set, Union
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib
from pathlib import Path

# 配置日志
logger = logging.getLogger(__name__)


class DataSourceType(Enum):
    """数据源类型枚举"""
    WEB_SEARCH = "web_search"
    LOCAL_FILE = "local_file"
    PROCESSED_CONTENT = "processed_content"
    DATABASE = "database"
    API = "api"
    CACHE = "cache"


class IntegrationType(Enum):
    """整合类型枚举"""
    MERGE = "merge"  # 合并相似数据
    COMBINE = "combine"  # 组合不同数据
    CORRELATE = "correlate"  # 关联分析
    AGGREGATE = "aggregate"  # 聚合统计
    DEDUPLICATE = "deduplicate"  # 去重处理


@dataclass
class DataSource:
    """数据源信息"""
    source_id: str
    source_type: DataSourceType
    source_url: Optional[str]
    source_path: Optional[str]
    metadata: Dict[str, Any]
    timestamp: datetime
    reliability_score: float  # 可靠性评分 0-1
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['source_type'] = self.source_type.value
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class IntegratedData:
    """整合后的数据"""
    integration_id: str
    title: str
    content: Dict[str, Any]
    sources: List[DataSource]
    integration_type: IntegrationType
    confidence_score: float  # 置信度评分 0-1
    quality_metrics: Dict[str, float]
    relationships: List[Dict[str, Any]]  # 数据间关系
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['integration_type'] = self.integration_type.value
        data['timestamp'] = self.timestamp.isoformat()
        data['sources'] = [source.to_dict() for source in self.sources]
        return data


@dataclass
class IntegrationReport:
    """整合报告"""
    total_sources: int
    successful_integrations: int
    failed_integrations: int
    duplicate_removed: int
    quality_score: float
    processing_time: float
    recommendations: List[str]
    timestamp: datetime


class DataIntegrator:
    """数据整合器
    
    负责整合来自不同源的数据，提供统一的数据访问接口
    """
    
    def __init__(self, config_manager=None):
        """初始化数据整合器"""
        self.config_manager = config_manager
        self.data_sources: Dict[str, DataSource] = {}
        self.integrated_data: Dict[str, IntegratedData] = {}
        self.integration_history: List[IntegrationReport] = []
        
        # 整合配置
        self.similarity_threshold = 0.8  # 相似度阈值
        self.quality_threshold = 0.6  # 质量阈值
        self.max_sources_per_integration = 10  # 每次整合最大数据源数量
        
        logger.info("DataIntegrator initialized")
    
    async def register_data_source(self, 
                                 source_data: Any,
                                 source_type: DataSourceType,
                                 source_url: Optional[str] = None,
                                 source_path: Optional[str] = None,
                                 metadata: Optional[Dict[str, Any]] = None) -> str:
        """注册数据源
        
        Args:
            source_data: 源数据
            source_type: 数据源类型
            source_url: 源URL（可选）
            source_path: 源路径（可选）
            metadata: 元数据（可选）
            
        Returns:
            str: 数据源ID
        """
        try:
            # 生成数据源ID
            source_id = self._generate_source_id(source_data, source_type)
            
            # 评估数据可靠性
            reliability_score = await self._evaluate_source_reliability(
                source_data, source_type, source_url
            )
            
            # 创建数据源对象
            data_source = DataSource(
                source_id=source_id,
                source_type=source_type,
                source_url=source_url,
                source_path=source_path,
                metadata=metadata or {},
                timestamp=datetime.now(),
                reliability_score=reliability_score
            )
            
            # 存储数据源
            self.data_sources[source_id] = data_source
            
            logger.info(f"Data source registered: {source_id}")
            return source_id
            
        except Exception as e:
            logger.error(f"Failed to register data source: {e}")
            raise
    
    async def integrate_data(self,
                           source_ids: List[str],
                           integration_type: IntegrationType,
                           title: Optional[str] = None) -> str:
        """整合数据
        
        Args:
            source_ids: 数据源ID列表
            integration_type: 整合类型
            title: 整合标题（可选）
            
        Returns:
            str: 整合数据ID
        """
        try:
            start_time = datetime.now()
            
            # 验证数据源
            valid_sources = []
            for source_id in source_ids:
                if source_id in self.data_sources:
                    valid_sources.append(self.data_sources[source_id])
                else:
                    logger.warning(f"Data source not found: {source_id}")
            
            if not valid_sources:
                raise ValueError("No valid data sources found")
            
            # 执行整合
            integrated_content = await self._perform_integration(
                valid_sources, integration_type
            )
            
            # 计算质量指标
            quality_metrics = await self._calculate_quality_metrics(
                integrated_content, valid_sources
            )
            
            # 分析数据关系
            relationships = await self._analyze_relationships(valid_sources)
            
            # 计算置信度
            confidence_score = await self._calculate_confidence_score(
                valid_sources, quality_metrics
            )
            
            # 生成整合ID
            integration_id = self._generate_integration_id(
                valid_sources, integration_type
            )
            
            # 创建整合数据对象
            integrated_data = IntegratedData(
                integration_id=integration_id,
                title=title or f"Integration_{integration_id[:8]}",
                content=integrated_content,
                sources=valid_sources,
                integration_type=integration_type,
                confidence_score=confidence_score,
                quality_metrics=quality_metrics,
                relationships=relationships,
                timestamp=datetime.now()
            )
            
            # 存储整合数据
            self.integrated_data[integration_id] = integrated_data
            
            # 记录整合报告
            processing_time = (datetime.now() - start_time).total_seconds()
            await self._record_integration_report(
                len(source_ids), 1, 0, processing_time, quality_metrics
            )
            
            logger.info(f"Data integration completed: {integration_id}")
            return integration_id
            
        except Exception as e:
            logger.error(f"Failed to integrate data: {e}")
            raise
    
    async def get_integrated_data(self, integration_id: str) -> Optional[IntegratedData]:
        """获取整合数据
        
        Args:
            integration_id: 整合数据ID
            
        Returns:
            IntegratedData: 整合数据对象
        """
        return self.integrated_data.get(integration_id)
    
    async def search_integrated_data(self,
                                   query: str,
                                   filters: Optional[Dict[str, Any]] = None) -> List[IntegratedData]:
        """搜索整合数据
        
        Args:
            query: 搜索查询
            filters: 过滤条件（可选）
            
        Returns:
            List[IntegratedData]: 匹配的整合数据列表
        """
        try:
            results = []
            query_lower = query.lower()
            
            for integrated_data in self.integrated_data.values():
                # 检查标题匹配
                if query_lower in integrated_data.title.lower():
                    results.append(integrated_data)
                    continue
                
                # 检查内容匹配
                content_str = json.dumps(integrated_data.content).lower()
                if query_lower in content_str:
                    results.append(integrated_data)
                    continue
                
                # 检查源数据匹配
                for source in integrated_data.sources:
                    if query_lower in json.dumps(source.metadata).lower():
                        results.append(integrated_data)
                        break
            
            # 应用过滤器
            if filters:
                results = await self._apply_filters(results, filters)
            
            # 按置信度排序
            results.sort(key=lambda x: x.confidence_score, reverse=True)
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to search integrated data: {e}")
            return []
    
    async def remove_duplicates(self, threshold: float = 0.9) -> int:
        """移除重复数据
        
        Args:
            threshold: 相似度阈值
            
        Returns:
            int: 移除的重复数据数量
        """
        try:
            removed_count = 0
            integration_ids = list(self.integrated_data.keys())
            
            for i in range(len(integration_ids)):
                for j in range(i + 1, len(integration_ids)):
                    id1, id2 = integration_ids[i], integration_ids[j]
                    
                    if id1 not in self.integrated_data or id2 not in self.integrated_data:
                        continue
                    
                    data1 = self.integrated_data[id1]
                    data2 = self.integrated_data[id2]
                    
                    # 计算相似度
                    similarity = await self._calculate_similarity(data1, data2)
                    
                    if similarity >= threshold:
                        # 保留质量更高的数据
                        if data1.quality_metrics.get('overall', 0) >= data2.quality_metrics.get('overall', 0):
                            del self.integrated_data[id2]
                        else:
                            del self.integrated_data[id1]
                        
                        removed_count += 1
                        break
            
            logger.info(f"Removed {removed_count} duplicate integrations")
            return removed_count

        except Exception as e:
            logger.error(f"Failed to remove duplicates: {e}")
            return 0

    async def export_integrated_data(self,
                                   integration_ids: Optional[List[str]] = None,
                                   format_type: str = "json") -> Dict[str, Any]:
        """导出整合数据

        Args:
            integration_ids: 要导出的整合数据ID列表（可选，默认导出全部）
            format_type: 导出格式（json, csv, xml）

        Returns:
            Dict[str, Any]: 导出的数据
        """
        try:
            # 确定要导出的数据
            if integration_ids:
                data_to_export = {
                    id: self.integrated_data[id]
                    for id in integration_ids
                    if id in self.integrated_data
                }
            else:
                data_to_export = self.integrated_data.copy()

            # 转换为字典格式
            export_data = {
                "metadata": {
                    "export_timestamp": datetime.now().isoformat(),
                    "total_integrations": len(data_to_export),
                    "format": format_type
                },
                "integrations": {
                    id: data.to_dict()
                    for id, data in data_to_export.items()
                }
            }

            logger.info(f"Exported {len(data_to_export)} integrations in {format_type} format")
            return export_data

        except Exception as e:
            logger.error(f"Failed to export integrated data: {e}")
            return {}

    async def get_integration_statistics(self) -> Dict[str, Any]:
        """获取整合统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            total_integrations = len(self.integrated_data)
            total_sources = len(self.data_sources)

            # 按类型统计
            type_stats = {}
            for data in self.integrated_data.values():
                type_name = data.integration_type.value
                type_stats[type_name] = type_stats.get(type_name, 0) + 1

            # 质量统计
            quality_scores = [
                data.quality_metrics.get('overall', 0)
                for data in self.integrated_data.values()
            ]
            avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0

            # 置信度统计
            confidence_scores = [data.confidence_score for data in self.integrated_data.values()]
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0

            # 源类型统计
            source_type_stats = {}
            for source in self.data_sources.values():
                type_name = source.source_type.value
                source_type_stats[type_name] = source_type_stats.get(type_name, 0) + 1

            return {
                "total_integrations": total_integrations,
                "total_sources": total_sources,
                "integration_types": type_stats,
                "source_types": source_type_stats,
                "average_quality": round(avg_quality, 3),
                "average_confidence": round(avg_confidence, 3),
                "integration_history_count": len(self.integration_history)
            }

        except Exception as e:
            logger.error(f"Failed to get integration statistics: {e}")
            return {}

    # 私有辅助方法

    def _generate_source_id(self, source_data: Any, source_type: DataSourceType) -> str:
        """生成数据源ID"""
        content_str = str(source_data) + source_type.value + str(datetime.now().timestamp())
        return hashlib.md5(content_str.encode()).hexdigest()

    def _generate_integration_id(self, sources: List[DataSource], integration_type: IntegrationType) -> str:
        """生成整合ID"""
        source_ids = [source.source_id for source in sources]
        content_str = "".join(sorted(source_ids)) + integration_type.value + str(datetime.now().timestamp())
        return hashlib.md5(content_str.encode()).hexdigest()

    async def _evaluate_source_reliability(self,
                                         source_data: Any,
                                         source_type: DataSourceType,
                                         source_url: Optional[str]) -> float:
        """评估数据源可靠性"""
        try:
            base_score = 0.5

            # 根据源类型调整评分
            type_scores = {
                DataSourceType.WEB_SEARCH: 0.7,
                DataSourceType.LOCAL_FILE: 0.8,
                DataSourceType.PROCESSED_CONTENT: 0.9,
                DataSourceType.DATABASE: 0.9,
                DataSourceType.API: 0.8,
                DataSourceType.CACHE: 0.6
            }

            base_score = type_scores.get(source_type, 0.5)

            # 根据URL域名调整评分（如果有）
            if source_url:
                domain_scores = {
                    'wikipedia.org': 0.9,
                    'gov': 0.95,
                    'edu': 0.9,
                    'org': 0.8
                }

                for domain, score in domain_scores.items():
                    if domain in source_url:
                        base_score = max(base_score, score)
                        break

            # 根据数据完整性调整评分
            if hasattr(source_data, '__len__') and len(str(source_data)) > 100:
                base_score += 0.1

            return min(1.0, base_score)

        except Exception as e:
            logger.error(f"Failed to evaluate source reliability: {e}")
            return 0.5

    async def _perform_integration(self,
                                 sources: List[DataSource],
                                 integration_type: IntegrationType) -> Dict[str, Any]:
        """执行数据整合"""
        try:
            if integration_type == IntegrationType.MERGE:
                return await self._merge_sources(sources)
            elif integration_type == IntegrationType.COMBINE:
                return await self._combine_sources(sources)
            elif integration_type == IntegrationType.CORRELATE:
                return await self._correlate_sources(sources)
            elif integration_type == IntegrationType.AGGREGATE:
                return await self._aggregate_sources(sources)
            elif integration_type == IntegrationType.DEDUPLICATE:
                return await self._deduplicate_sources(sources)
            else:
                # 默认合并
                return await self._merge_sources(sources)

        except Exception as e:
            logger.error(f"Failed to perform integration: {e}")
            return {}

    async def _merge_sources(self, sources: List[DataSource]) -> Dict[str, Any]:
        """合并数据源"""
        merged_content = {
            "sources": [source.to_dict() for source in sources],
            "merged_metadata": {},
            "content_summary": "",
            "key_points": []
        }

        # 合并元数据
        for source in sources:
            for key, value in source.metadata.items():
                if key not in merged_content["merged_metadata"]:
                    merged_content["merged_metadata"][key] = []
                merged_content["merged_metadata"][key].append(value)

        return merged_content

    async def _combine_sources(self, sources: List[DataSource]) -> Dict[str, Any]:
        """组合数据源"""
        combined_content = {
            "combined_sources": [],
            "relationships": [],
            "unified_view": {}
        }

        for i, source in enumerate(sources):
            combined_content["combined_sources"].append({
                "index": i,
                "source_info": source.to_dict(),
                "weight": source.reliability_score
            })

        return combined_content

    async def _correlate_sources(self, sources: List[DataSource]) -> Dict[str, Any]:
        """关联分析数据源"""
        correlation_content = {
            "correlation_matrix": {},
            "strong_correlations": [],
            "weak_correlations": [],
            "insights": []
        }

        # 简单的关联分析（基于元数据相似性）
        for i in range(len(sources)):
            for j in range(i + 1, len(sources)):
                correlation = await self._calculate_source_correlation(sources[i], sources[j])
                correlation_content["correlation_matrix"][f"{i}-{j}"] = correlation

                if correlation > 0.7:
                    correlation_content["strong_correlations"].append({
                        "source1": i,
                        "source2": j,
                        "correlation": correlation
                    })
                elif correlation > 0.3:
                    correlation_content["weak_correlations"].append({
                        "source1": i,
                        "source2": j,
                        "correlation": correlation
                    })

        return correlation_content

    async def _aggregate_sources(self, sources: List[DataSource]) -> Dict[str, Any]:
        """聚合数据源"""
        aggregate_content = {
            "total_sources": len(sources),
            "source_types": {},
            "reliability_stats": {},
            "temporal_distribution": {},
            "aggregated_metadata": {}
        }

        # 统计源类型
        for source in sources:
            type_name = source.source_type.value
            aggregate_content["source_types"][type_name] = \
                aggregate_content["source_types"].get(type_name, 0) + 1

        # 可靠性统计
        reliability_scores = [source.reliability_score for source in sources]
        aggregate_content["reliability_stats"] = {
            "average": sum(reliability_scores) / len(reliability_scores),
            "min": min(reliability_scores),
            "max": max(reliability_scores),
            "count": len(reliability_scores)
        }

        # 时间分布
        timestamps = [source.timestamp for source in sources]
        if timestamps:
            aggregate_content["temporal_distribution"] = {
                "earliest": min(timestamps).isoformat(),
                "latest": max(timestamps).isoformat(),
                "span_hours": (max(timestamps) - min(timestamps)).total_seconds() / 3600
            }

        return aggregate_content

    async def _deduplicate_sources(self, sources: List[DataSource]) -> Dict[str, Any]:
        """去重数据源"""
        unique_sources = []
        duplicates_found = []

        for i, source in enumerate(sources):
            is_duplicate = False
            for j, unique_source in enumerate(unique_sources):
                similarity = await self._calculate_source_similarity(source, unique_source)
                if similarity > self.similarity_threshold:
                    is_duplicate = True
                    duplicates_found.append({
                        "duplicate_index": i,
                        "original_index": j,
                        "similarity": similarity
                    })
                    break

            if not is_duplicate:
                unique_sources.append(source)

        deduplicate_content = {
            "unique_sources": [source.to_dict() for source in unique_sources],
            "duplicates_removed": len(sources) - len(unique_sources),
            "duplicate_details": duplicates_found,
            "deduplication_ratio": len(unique_sources) / len(sources) if sources else 0
        }

        return deduplicate_content

    async def _calculate_quality_metrics(self,
                                       integrated_content: Dict[str, Any],
                                       sources: List[DataSource]) -> Dict[str, float]:
        """计算质量指标"""
        try:
            metrics = {}

            # 数据完整性
            completeness = 1.0 if integrated_content else 0.0
            if integrated_content:
                required_fields = ['sources', 'content_summary']
                present_fields = sum(1 for field in required_fields if field in integrated_content)
                completeness = present_fields / len(required_fields)

            metrics['completeness'] = completeness

            # 数据一致性
            consistency = sum(source.reliability_score for source in sources) / len(sources) if sources else 0
            metrics['consistency'] = consistency

            # 数据新鲜度
            if sources:
                now = datetime.now()
                avg_age_hours = sum(
                    (now - source.timestamp).total_seconds() / 3600
                    for source in sources
                ) / len(sources)
                freshness = max(0, 1 - (avg_age_hours / 168))  # 一周内为新鲜
                metrics['freshness'] = freshness
            else:
                metrics['freshness'] = 0

            # 数据多样性
            source_types = set(source.source_type for source in sources)
            diversity = len(source_types) / len(DataSourceType) if sources else 0
            metrics['diversity'] = diversity

            # 总体质量
            metrics['overall'] = (
                metrics['completeness'] * 0.3 +
                metrics['consistency'] * 0.3 +
                metrics['freshness'] * 0.2 +
                metrics['diversity'] * 0.2
            )

            return metrics

        except Exception as e:
            logger.error(f"Failed to calculate quality metrics: {e}")
            return {'overall': 0.0}

    async def _analyze_relationships(self, sources: List[DataSource]) -> List[Dict[str, Any]]:
        """分析数据关系"""
        try:
            relationships = []

            for i in range(len(sources)):
                for j in range(i + 1, len(sources)):
                    source1, source2 = sources[i], sources[j]

                    # 计算关系强度
                    relationship_strength = await self._calculate_relationship_strength(source1, source2)

                    if relationship_strength > 0.3:  # 只记录有意义的关系
                        relationship = {
                            "source1_id": source1.source_id,
                            "source2_id": source2.source_id,
                            "relationship_type": await self._determine_relationship_type(source1, source2),
                            "strength": relationship_strength,
                            "description": await self._generate_relationship_description(source1, source2)
                        }
                        relationships.append(relationship)

            return relationships

        except Exception as e:
            logger.error(f"Failed to analyze relationships: {e}")
            return []

    async def _calculate_confidence_score(self,
                                        sources: List[DataSource],
                                        quality_metrics: Dict[str, float]) -> float:
        """计算置信度评分"""
        try:
            # 基于源可靠性的置信度
            reliability_confidence = sum(source.reliability_score for source in sources) / len(sources) if sources else 0

            # 基于质量指标的置信度
            quality_confidence = quality_metrics.get('overall', 0)

            # 基于源数量的置信度（更多源通常意味着更高置信度）
            source_count_confidence = min(1.0, len(sources) / 5)  # 5个源为满分

            # 综合置信度
            confidence = (
                reliability_confidence * 0.4 +
                quality_confidence * 0.4 +
                source_count_confidence * 0.2
            )

            return min(1.0, confidence)

        except Exception as e:
            logger.error(f"Failed to calculate confidence score: {e}")
            return 0.5

    async def _apply_filters(self,
                           results: List[IntegratedData],
                           filters: Dict[str, Any]) -> List[IntegratedData]:
        """应用过滤器"""
        try:
            filtered_results = results.copy()

            # 按整合类型过滤
            if 'integration_type' in filters:
                integration_type = filters['integration_type']
                filtered_results = [
                    data for data in filtered_results
                    if data.integration_type.value == integration_type
                ]

            # 按置信度过滤
            if 'min_confidence' in filters:
                min_confidence = filters['min_confidence']
                filtered_results = [
                    data for data in filtered_results
                    if data.confidence_score >= min_confidence
                ]

            # 按时间范围过滤
            if 'start_time' in filters and 'end_time' in filters:
                start_time = datetime.fromisoformat(filters['start_time'])
                end_time = datetime.fromisoformat(filters['end_time'])
                filtered_results = [
                    data for data in filtered_results
                    if start_time <= data.timestamp <= end_time
                ]

            # 按源类型过滤
            if 'source_types' in filters:
                allowed_types = set(filters['source_types'])
                filtered_results = [
                    data for data in filtered_results
                    if any(source.source_type.value in allowed_types for source in data.sources)
                ]

            return filtered_results

        except Exception as e:
            logger.error(f"Failed to apply filters: {e}")
            return results

    async def _calculate_similarity(self, data1: IntegratedData, data2: IntegratedData) -> float:
        """计算两个整合数据的相似度"""
        try:
            # 标题相似度
            title_similarity = self._calculate_text_similarity(data1.title, data2.title)

            # 内容相似度
            content1_str = json.dumps(data1.content, sort_keys=True)
            content2_str = json.dumps(data2.content, sort_keys=True)
            content_similarity = self._calculate_text_similarity(content1_str, content2_str)

            # 源相似度
            sources1_ids = set(source.source_id for source in data1.sources)
            sources2_ids = set(source.source_id for source in data2.sources)
            source_similarity = len(sources1_ids & sources2_ids) / len(sources1_ids | sources2_ids) if sources1_ids | sources2_ids else 0

            # 综合相似度
            similarity = (
                title_similarity * 0.3 +
                content_similarity * 0.5 +
                source_similarity * 0.2
            )

            return similarity

        except Exception as e:
            logger.error(f"Failed to calculate similarity: {e}")
            return 0.0

    async def _calculate_source_correlation(self, source1: DataSource, source2: DataSource) -> float:
        """计算两个数据源的关联度"""
        try:
            correlation = 0.0

            # 类型相关性
            if source1.source_type == source2.source_type:
                correlation += 0.3

            # 时间相关性
            time_diff = abs((source1.timestamp - source2.timestamp).total_seconds())
            if time_diff < 3600:  # 1小时内
                correlation += 0.3
            elif time_diff < 86400:  # 1天内
                correlation += 0.2

            # 元数据相关性
            metadata1_str = json.dumps(source1.metadata, sort_keys=True)
            metadata2_str = json.dumps(source2.metadata, sort_keys=True)
            metadata_similarity = self._calculate_text_similarity(metadata1_str, metadata2_str)
            correlation += metadata_similarity * 0.4

            return min(1.0, correlation)

        except Exception as e:
            logger.error(f"Failed to calculate source correlation: {e}")
            return 0.0

    async def _calculate_source_similarity(self, source1: DataSource, source2: DataSource) -> float:
        """计算两个数据源的相似度"""
        try:
            similarity = 0.0

            # URL相似度
            if source1.source_url and source2.source_url:
                url_similarity = self._calculate_text_similarity(source1.source_url, source2.source_url)
                similarity += url_similarity * 0.4

            # 路径相似度
            if source1.source_path and source2.source_path:
                path_similarity = self._calculate_text_similarity(source1.source_path, source2.source_path)
                similarity += path_similarity * 0.3

            # 元数据相似度
            metadata1_str = json.dumps(source1.metadata, sort_keys=True)
            metadata2_str = json.dumps(source2.metadata, sort_keys=True)
            metadata_similarity = self._calculate_text_similarity(metadata1_str, metadata2_str)
            similarity += metadata_similarity * 0.3

            return similarity

        except Exception as e:
            logger.error(f"Failed to calculate source similarity: {e}")
            return 0.0

    async def _calculate_relationship_strength(self, source1: DataSource, source2: DataSource) -> float:
        """计算关系强度"""
        try:
            strength = 0.0

            # 基于相似度的关系强度
            similarity = await self._calculate_source_similarity(source1, source2)
            strength += similarity * 0.5

            # 基于关联度的关系强度
            correlation = await self._calculate_source_correlation(source1, source2)
            strength += correlation * 0.5

            return strength

        except Exception as e:
            logger.error(f"Failed to calculate relationship strength: {e}")
            return 0.0

    async def _determine_relationship_type(self, source1: DataSource, source2: DataSource) -> str:
        """确定关系类型"""
        try:
            # 基于源类型确定关系
            if source1.source_type == source2.source_type:
                return "同类型源"

            # 基于时间确定关系
            time_diff = abs((source1.timestamp - source2.timestamp).total_seconds())
            if time_diff < 3600:
                return "时间相关"

            # 基于URL确定关系
            if source1.source_url and source2.source_url:
                if source1.source_url.split('/')[2] == source2.source_url.split('/')[2]:  # 同域名
                    return "同源网站"

            return "一般关联"

        except Exception as e:
            logger.error(f"Failed to determine relationship type: {e}")
            return "未知关系"

    async def _generate_relationship_description(self, source1: DataSource, source2: DataSource) -> str:
        """生成关系描述"""
        try:
            relationship_type = await self._determine_relationship_type(source1, source2)
            strength = await self._calculate_relationship_strength(source1, source2)

            if strength > 0.8:
                strength_desc = "强"
            elif strength > 0.5:
                strength_desc = "中等"
            else:
                strength_desc = "弱"

            return f"{relationship_type}，关系强度：{strength_desc}（{strength:.2f}）"

        except Exception as e:
            logger.error(f"Failed to generate relationship description: {e}")
            return "关系描述生成失败"

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单实现）"""
        try:
            if not text1 or not text2:
                return 0.0

            # 转换为小写并分词
            words1 = set(text1.lower().split())
            words2 = set(text2.lower().split())

            # 计算Jaccard相似度
            intersection = len(words1 & words2)
            union = len(words1 | words2)

            return intersection / union if union > 0 else 0.0

        except Exception as e:
            logger.error(f"Failed to calculate text similarity: {e}")
            return 0.0

    async def _record_integration_report(self,
                                       total_sources: int,
                                       successful: int,
                                       failed: int,
                                       processing_time: float,
                                       quality_metrics: Dict[str, float]) -> None:
        """记录整合报告"""
        try:
            report = IntegrationReport(
                total_sources=total_sources,
                successful_integrations=successful,
                failed_integrations=failed,
                duplicate_removed=0,  # 在去重时更新
                quality_score=quality_metrics.get('overall', 0),
                processing_time=processing_time,
                recommendations=await self._generate_recommendations(quality_metrics),
                timestamp=datetime.now()
            )

            self.integration_history.append(report)

            # 保持历史记录数量限制
            if len(self.integration_history) > 100:
                self.integration_history = self.integration_history[-100:]

        except Exception as e:
            logger.error(f"Failed to record integration report: {e}")

    async def _generate_recommendations(self, quality_metrics: Dict[str, float]) -> List[str]:
        """生成改进建议"""
        try:
            recommendations = []

            if quality_metrics.get('completeness', 0) < 0.7:
                recommendations.append("建议增加更多数据源以提高数据完整性")

            if quality_metrics.get('consistency', 0) < 0.6:
                recommendations.append("建议验证数据源的可靠性，移除低质量源")

            if quality_metrics.get('freshness', 0) < 0.5:
                recommendations.append("建议更新数据源，获取更新鲜的信息")

            if quality_metrics.get('diversity', 0) < 0.4:
                recommendations.append("建议增加不同类型的数据源以提高多样性")

            if quality_metrics.get('overall', 0) < 0.6:
                recommendations.append("整体质量偏低，建议重新评估整合策略")

            return recommendations

        except Exception as e:
            logger.error(f"Failed to generate recommendations: {e}")
            return []
