{"tavily": {"api_key": "${TAVILY_API_KEY}", "base_url": "https://api.tavily.com", "endpoints": {"search": "/search", "extract": "/extract"}, "default_params": {"search_depth": "basic", "max_results": 5, "timeout": 30, "include_answer": true, "include_raw_content": false, "include_images": false}, "rate_limiting": {"requests_per_minute": 60, "requests_per_hour": 1000, "delay_between_requests": 1.0}, "retry_config": {"max_retries": 3, "retry_delay": 2.0, "backoff_factor": 2.0}}, "search_cache": {"enabled": true, "ttl_seconds": 3600, "max_cache_size": 1000, "cleanup_interval": 300, "cache_key_prefix": "tavily_search_"}, "content_filtering": {"enabled": true, "min_content_length": 50, "max_content_length": 10000, "blocked_domains": ["spam-site.com", "malicious-site.com"], "allowed_content_types": ["text/html", "text/plain", "application/json"]}, "logging": {"log_requests": true, "log_responses": false, "log_level": "INFO", "log_file": "logs/tavily_search.log"}}