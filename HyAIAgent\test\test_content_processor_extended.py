"""
HyAIAgent 第四阶段 - 内容处理器扩展功能测试
步骤4.6: 内容处理器开发测试
"""

import asyncio
import pytest
import json
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from operations.content_processor import (
    ContentProcessor, ProcessedContent, ProcessedResponse
)
from operations.search_operations import SearchResult, SearchResponse
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager


class TestContentProcessorExtended:
    """内容处理器扩展功能测试类"""
    
    @pytest.fixture
    def setup_processor(self):
        """设置内容处理器"""
        # 创建模拟配置
        config_manager = Mock(spec=ConfigManager)
        config_manager.get.return_value = {
            "reading_speed": 200,
            "max_summary_length": 500,
            "similarity_threshold": 0.8
        }
        
        # 创建模拟安全管理器
        security_manager = Mock(spec=SecurityManager)
        
        # 创建处理器
        processor = ContentProcessor(config_manager, security_manager)
        
        return processor
    
    @pytest.mark.asyncio
    async def test_extract_key_information(self, setup_processor):
        """测试关键信息提取功能"""
        processor = setup_processor
        
        test_content = """
        人工智能技术在现代社会中发挥着重要作用。首先，AI技术能够提高工作效率。
        其次，机器学习算法可以处理大量数据。最后，深度学习在图像识别方面表现出色。
        研究表明，AI市场预计在2025年将达到1000亿美元。
        """
        
        result = await processor.extract_key_information(test_content)
        
        # 验证结果结构
        assert "keywords" in result
        assert "entities" in result
        assert "topics" in result
        assert "key_points" in result
        assert "statistics" in result
        assert "timestamp" in result
        
        # 验证内容
        assert isinstance(result["keywords"], list)
        assert isinstance(result["entities"], list)
        assert isinstance(result["topics"], list)
        assert isinstance(result["key_points"], list)
        assert isinstance(result["statistics"], dict)
        
        # 验证统计信息
        stats = result["statistics"]
        assert "word_count" in stats
        assert "language" in stats
        assert "content_type" in stats
        assert stats["word_count"] > 0
        assert stats["language"] in ["zh", "en", "unknown"]
    
    @pytest.mark.asyncio
    async def test_generate_summary(self, setup_processor):
        """测试摘要生成功能"""
        processor = setup_processor
        
        test_contents = [
            "Python是一种高级编程语言，易于学习和使用。它具有简洁的语法和强大的功能。",
            "Python在数据科学领域应用广泛，包括数据分析、机器学习和人工智能。",
            "许多大公司如Google、Netflix都在使用Python进行开发。Python社区活跃，生态系统丰富。"
        ]
        
        result = await processor.generate_summary(test_contents, max_length=200)
        
        # 验证结果结构
        assert "summary" in result
        assert "key_themes" in result
        assert "confidence" in result
        assert "source_count" in result
        assert "timestamp" in result
        
        # 验证内容
        assert isinstance(result["summary"], str)
        assert isinstance(result["key_themes"], list)
        assert isinstance(result["confidence"], float)
        assert result["source_count"] == 3
        assert 0.0 <= result["confidence"] <= 1.0
        assert len(result["summary"]) <= 200
        
        # 验证摘要质量
        assert len(result["summary"]) > 0
        assert len(result["key_themes"]) > 0
    
    @pytest.mark.asyncio
    async def test_structure_data_list(self, setup_processor):
        """测试列表数据结构化"""
        processor = setup_processor
        
        test_content = """
        编程语言排行榜：
        1. Python - 易学易用
        2. JavaScript - Web开发必备
        3. Java - 企业级应用
        4. C++ - 系统编程
        5. Go - 云原生开发
        """
        
        result = await processor.structure_data(test_content)
        
        # 验证结果结构
        assert "structured_data" in result
        assert "confidence" in result
        assert "data_type" in result
        
        # 验证数据类型
        assert result["data_type"] == "list"
        
        # 验证结构化数据
        structured = result["structured_data"]
        assert structured["type"] == "list"
        assert "items" in structured
        assert "count" in structured
        assert structured["count"] > 0
        assert len(structured["items"]) == structured["count"]
    
    @pytest.mark.asyncio
    async def test_structure_data_table(self, setup_processor):
        """测试表格数据结构化"""
        processor = setup_processor
        
        test_content = """
        语言 | 类型 | 难度
        Python | 解释型 | 简单
        Java | 编译型 | 中等
        C++ | 编译型 | 困难
        """
        
        result = await processor.structure_data(test_content)
        
        # 验证结果
        assert result["data_type"] == "table"
        
        structured = result["structured_data"]
        assert structured["type"] == "table"
        assert "headers" in structured
        assert "rows" in structured
        assert len(structured["headers"]) > 0
        assert len(structured["rows"]) > 0
    
    @pytest.mark.asyncio
    async def test_detect_duplicates(self, setup_processor):
        """测试重复内容检测"""
        processor = setup_processor
        
        test_contents = [
            "Python是一种编程语言，广泛用于数据科学。",
            "Java是面向对象的编程语言，适合企业开发。",
            "Python是编程语言，在数据科学领域应用广泛。",  # 与第一个相似
            "JavaScript用于Web前端开发，功能强大。",
            "Python语言在数据科学中使用很多。"  # 与第一个相似
        ]
        
        result = await processor.detect_duplicates(test_contents, similarity_threshold=0.3)
        
        # 验证结果结构
        assert "duplicates" in result
        assert "unique_count" in result
        assert "duplicate_count" in result
        assert "similarity_matrix" in result
        assert "threshold_used" in result
        
        # 验证检测结果
        assert isinstance(result["duplicates"], list)
        assert result["unique_count"] + result["duplicate_count"] == len(test_contents)
        assert result["threshold_used"] == 0.3
        assert len(result["similarity_matrix"]) == len(test_contents)
        
        # 应该检测到重复内容
        assert result["duplicate_count"] > 0
    
    @pytest.mark.asyncio
    async def test_batch_process_contents(self, setup_processor):
        """测试批量内容处理"""
        processor = setup_processor
        
        test_contents = [
            "人工智能是计算机科学的一个分支，致力于创建智能机器。",
            "机器学习是人工智能的子集，通过数据训练模型。",
            "深度学习使用神经网络进行复杂的模式识别。"
        ]
        
        operations = ['extract', 'summarize', 'structure', 'deduplicate']
        result = await processor.batch_process_contents(test_contents, operations)
        
        # 验证结果结构
        assert "total_contents" in result
        assert "operations_performed" in result
        assert "results" in result
        assert "processing_time" in result
        assert "timestamp" in result
        
        # 验证基本信息
        assert result["total_contents"] == 3
        assert result["operations_performed"] == operations
        assert result["processing_time"] > 0
        
        # 验证各操作结果
        results = result["results"]
        
        if 'extract' in operations:
            assert "extraction" in results
            assert len(results["extraction"]) == 3
        
        if 'summarize' in operations:
            assert "summary" in results
            assert "summary" in results["summary"]
        
        if 'structure' in operations:
            assert "structure" in results
            assert len(results["structure"]) == 3
        
        if 'deduplicate' in operations:
            assert "duplicates" in results
            assert "unique_count" in results["duplicates"]
    
    @pytest.mark.asyncio
    async def test_empty_content_handling(self, setup_processor):
        """测试空内容处理"""
        processor = setup_processor
        
        # 测试空字符串
        result = await processor.extract_key_information("")
        assert result["keywords"] == []
        assert result["entities"] == []
        
        # 测试空列表
        result = await processor.generate_summary([])
        assert result["summary"] == ""
        assert result["source_count"] == 0
        
        # 测试空内容结构化
        result = await processor.structure_data("")
        assert result["data_type"] == "empty"
        assert result["confidence"] == 0.0
    
    @pytest.mark.asyncio
    async def test_error_handling(self, setup_processor):
        """测试错误处理"""
        processor = setup_processor
        
        # 测试None输入
        result = await processor.extract_key_information(None)
        assert "error" in result or result["keywords"] == []
        
        # 测试无效数据类型
        with patch.object(processor, '_clean_content', side_effect=Exception("Test error")):
            result = await processor.extract_key_information("test content")
            assert "error" in result
    
    def test_similarity_calculation(self, setup_processor):
        """测试相似度计算"""
        processor = setup_processor
        
        content1 = "Python是一种编程语言"
        content2 = "Python是编程语言"
        content3 = "Java是面向对象语言"
        
        # 相似内容应该有较高相似度
        similarity1 = processor._calculate_content_similarity(content1, content2)
        assert similarity1 > 0.4  # 调整阈值为更合理的值
        
        # 不同内容应该有较低相似度
        similarity2 = processor._calculate_content_similarity(content1, content3)
        assert similarity2 < similarity1
        
        # 相同内容应该相似度为1
        similarity3 = processor._calculate_content_similarity(content1, content1)
        assert similarity3 == 1.0


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
