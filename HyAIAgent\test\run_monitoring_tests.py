"""
监控模块测试运行器

运行所有监控模块的测试，验证代码质量和功能正确性。
"""

import sys
import os
import subprocess
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

def run_test_file(test_file):
    """运行单个测试文件"""
    print(f"\n{'='*60}")
    print(f"运行测试: {test_file}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # 运行pytest
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            test_file, 
            '-v',  # 详细输出
            '--tb=short',  # 简短的错误回溯
            '--no-header',  # 不显示头部信息
            '--disable-warnings'  # 禁用警告
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"测试耗时: {duration:.2f}秒")
        
        if result.returncode == 0:
            print("✅ 测试通过")
            # 解析测试结果
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if '::' in line and ('PASSED' in line or 'FAILED' in line):
                    print(f"  {line}")
        else:
            print("❌ 测试失败")
            print("STDOUT:")
            print(result.stdout)
            print("STDERR:")
            print(result.stderr)
        
        return result.returncode == 0, duration
        
    except Exception as e:
        print(f"❌ 运行测试时发生错误: {e}")
        return False, 0

def check_dependencies():
    """检查测试依赖"""
    print("检查测试依赖...")
    
    required_packages = ['pytest', 'psutil']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    print("HyAIAgent 监控模块测试运行器")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 测试文件列表
    test_files = [
        'test_performance_monitor.py',
        'test_error_analyzer.py', 
        'test_usage_tracker.py',
        'test_optimization_engine.py'
    ]
    
    # 运行测试统计
    total_tests = len(test_files)
    passed_tests = 0
    total_duration = 0
    
    # 运行每个测试文件
    for test_file in test_files:
        test_path = os.path.join(os.path.dirname(__file__), test_file)
        
        if not os.path.exists(test_path):
            print(f"❌ 测试文件不存在: {test_file}")
            continue
        
        success, duration = run_test_file(test_path)
        total_duration += duration
        
        if success:
            passed_tests += 1
    
    # 输出总结
    print(f"\n{'='*60}")
    print("测试总结")
    print(f"{'='*60}")
    print(f"总测试文件数: {total_tests}")
    print(f"通过测试数: {passed_tests}")
    print(f"失败测试数: {total_tests - passed_tests}")
    print(f"总耗时: {total_duration:.2f}秒")
    print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试都通过了！")
        print("✅ 步骤5.3.1 - 性能监控系统开发完成，代码质量验证通过")
    else:
        print(f"\n⚠️  有 {total_tests - passed_tests} 个测试失败，请检查代码")
    
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == '__main__':
    main()
