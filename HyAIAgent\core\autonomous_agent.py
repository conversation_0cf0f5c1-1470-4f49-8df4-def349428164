"""
自主执行代理

本模块实现HyAIAgent的核心自主执行循环，整合所有组件。
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.task_manager import TaskManager
from core.execution_engine import ExecutionEngine
from core.decision_engine import DecisionEngine, DecisionType
from core.context_manager import ContextManager
from core.task_models import Task, TaskStatus, ExecutionPlan
from operations.system_operations import SystemOperations
from prompts.task_prompts import TaskPrompts
from core.config_manager import ConfigManager
from core.ai_client import SimpleAIClient
from core.kv_store import KVStore
from core.prompt_manager import PromptManager

logger = logging.getLogger(__name__)


class AgentState:
    """代理状态枚举"""
    IDLE = "idle"
    THINKING = "thinking"
    PLANNING = "planning"
    EXECUTING = "executing"
    EVALUATING = "evaluating"
    PAUSED = "paused"
    ERROR = "error"
    STOPPED = "stopped"


class AutonomousAgent:
    """自主执行代理
    
    HyAIAgent的核心类，实现完整的自主思考和执行循环。
    """
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """初始化自主代理
        
        Args:
            config_path: 配置文件路径
        """
        self.agent_id = str(uuid.uuid4())
        self.state = AgentState.IDLE
        self.created_at = datetime.now()
        
        # 初始化组件
        self.config_manager = ConfigManager(config_path)
        self.ai_client = SimpleAIClient(self.config_manager)
        self.kv_store = KVStore(self.config_manager.get('storage.kv_store_path', 'data/kv_store.json'))
        self.prompt_manager = PromptManager("prompts")
        
        # 核心组件
        self.task_manager = TaskManager(self.ai_client, self.prompt_manager, self.kv_store)
        self.execution_engine = ExecutionEngine(self.ai_client, self.prompt_manager)
        self.decision_engine = DecisionEngine(self.ai_client, self.prompt_manager)
        self.context_manager = ContextManager(self.kv_store)
        
        # 操作和提示词组件
        self.system_operations = SystemOperations()
        self.task_prompts = TaskPrompts()
        
        # 执行控制
        self.is_running = False
        self.current_session_id: Optional[str] = None
        self.execution_loop_task: Optional[asyncio.Task] = None
        
        # 性能统计
        self.stats = {
            'total_requests': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'total_execution_time': 0.0,
            'average_response_time': 0.0
        }
        
        logger.info(f"自主代理初始化完成 - ID: {self.agent_id}")
    
    async def start(self, initial_request: str = None) -> str:
        """启动代理
        
        Args:
            initial_request: 初始请求（可选）
            
        Returns:
            str: 会话ID
        """
        if self.is_running:
            logger.warning("代理已在运行中")
            return self.current_session_id
        
        try:
            # 创建新会话
            self.current_session_id = str(uuid.uuid4())
            self.is_running = True
            self.state = AgentState.THINKING
            
            # 创建临时执行计划用于初始化上下文
            from .task_models import ExecutionPlan
            temp_plan = ExecutionPlan(
                id=self.current_session_id,
                name=f"会话-{self.current_session_id[:8]}",
                description=f"初始请求: {initial_request or '无'}"
            )

            # 初始化执行上下文
            await self.context_manager.initialize_context(temp_plan)
            
            # 启动执行循环
            self.execution_loop_task = asyncio.create_task(
                self._execution_loop(initial_request)
            )
            
            logger.info(f"代理启动成功 - 会话ID: {self.current_session_id}")
            return self.current_session_id
            
        except Exception as e:
            logger.error(f"代理启动失败: {str(e)}")
            self.state = AgentState.ERROR
            self.is_running = False
            raise
    
    async def stop(self) -> bool:
        """停止代理
        
        Returns:
            bool: 是否成功停止
        """
        try:
            if not self.is_running:
                logger.info("代理未在运行")
                return True
            
            self.is_running = False
            self.state = AgentState.STOPPED
            
            # 取消执行循环
            if self.execution_loop_task and not self.execution_loop_task.done():
                self.execution_loop_task.cancel()
                try:
                    await self.execution_loop_task
                except asyncio.CancelledError:
                    pass
            
            # 保存上下文
            if self.current_session_id:
                await self.context_manager.create_snapshot(
                    f"session_end_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                )
            
            logger.info("代理已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止代理失败: {str(e)}")
            return False
    
    async def pause(self) -> bool:
        """暂停代理
        
        Returns:
            bool: 是否成功暂停
        """
        if self.state in [AgentState.IDLE, AgentState.PAUSED, AgentState.STOPPED]:
            return True
        
        self.state = AgentState.PAUSED
        logger.info("代理已暂停")
        return True
    
    async def resume(self) -> bool:
        """恢复代理
        
        Returns:
            bool: 是否成功恢复
        """
        if self.state != AgentState.PAUSED:
            return False
        
        self.state = AgentState.THINKING
        logger.info("代理已恢复")
        return True
    
    async def process_request(self, request: str) -> Dict[str, Any]:
        """处理用户请求
        
        Args:
            request: 用户请求
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        start_time = datetime.now()
        self.stats['total_requests'] += 1
        
        try:
            logger.info(f"处理用户请求: {request[:100]}...")
            
            # 如果代理未运行，启动它
            if not self.is_running:
                await self.start(request)
                return {
                    'success': True,
                    'message': '代理已启动，正在处理请求',
                    'session_id': self.current_session_id
                }
            
            # 更新上下文
            await self.context_manager.update_user_request(request)
            
            # 触发新的执行循环
            if self.state == AgentState.IDLE:
                self.state = AgentState.THINKING
            
            return {
                'success': True,
                'message': '请求已接收，正在处理',
                'session_id': self.current_session_id,
                'agent_state': self.state
            }
            
        except Exception as e:
            logger.error(f"处理请求失败: {str(e)}")
            self.stats['failed_executions'] += 1
            return {
                'success': False,
                'error': str(e),
                'session_id': self.current_session_id
            }
        finally:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.stats['total_execution_time'] += execution_time
            self._update_average_response_time()
    
    async def _execution_loop(self, initial_request: Optional[str] = None):
        """主执行循环"""
        try:
            logger.info("开始执行循环")
            
            # 处理初始请求
            if initial_request:
                await self._process_initial_request(initial_request)
            
            # 主循环
            while self.is_running:
                try:
                    # 检查暂停状态
                    if self.state == AgentState.PAUSED:
                        await asyncio.sleep(1)
                        continue
                    
                    # 获取当前上下文
                    context = await self.context_manager.get_current_context()
                    
                    # 根据状态执行相应操作
                    if self.state == AgentState.THINKING:
                        await self._thinking_phase(context)
                    elif self.state == AgentState.PLANNING:
                        await self._planning_phase(context)
                    elif self.state == AgentState.EXECUTING:
                        await self._execution_phase(context)
                    elif self.state == AgentState.EVALUATING:
                        await self._evaluation_phase(context)
                    elif self.state == AgentState.IDLE:
                        await asyncio.sleep(1)  # 空闲等待
                    
                    # 短暂休息，避免过度消耗资源
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"执行循环异常: {str(e)}")
                    self.state = AgentState.ERROR
                    await asyncio.sleep(5)  # 错误恢复等待
                    self.state = AgentState.THINKING
            
        except asyncio.CancelledError:
            logger.info("执行循环被取消")
        except Exception as e:
            logger.error(f"执行循环严重错误: {str(e)}")
            self.state = AgentState.ERROR
    
    async def _process_initial_request(self, request: str):
        """处理初始请求"""
        try:
            logger.info("处理初始请求")
            
            # 分析请求并创建任务
            tasks = await self.task_manager.decompose_task(request)
            
            if tasks:
                # 创建执行计划
                execution_plan = await self.task_manager.create_execution_plan(tasks)
                
                # 更新上下文
                await self.context_manager.update_execution_plan(execution_plan)
                
                # 转入规划阶段
                self.state = AgentState.PLANNING
            else:
                logger.warning("无法从初始请求创建任务")
                self.state = AgentState.IDLE
                
        except Exception as e:
            logger.error(f"处理初始请求失败: {str(e)}")
            self.state = AgentState.ERROR
    
    async def _thinking_phase(self, context: Dict[str, Any]):
        """思考阶段"""
        try:
            logger.debug("进入思考阶段")
            
            # 检查是否有待处理的用户请求
            pending_request = context.get('pending_user_request')
            if pending_request:
                await self._process_initial_request(pending_request)
                await self.context_manager.clear_pending_request()
                return
            
            # 检查是否有未完成的任务
            current_plan = context.get('execution_plan')
            if current_plan and current_plan.get('status') != 'completed':
                self.state = AgentState.EXECUTING
                return
            
            # 没有任务，进入空闲状态
            self.state = AgentState.IDLE
            
        except Exception as e:
            logger.error(f"思考阶段错误: {str(e)}")
            self.state = AgentState.ERROR
    
    async def _planning_phase(self, context: Dict[str, Any]):
        """规划阶段"""
        try:
            logger.debug("进入规划阶段")
            
            # 获取执行计划
            execution_plan = context.get('execution_plan')
            if not execution_plan:
                self.state = AgentState.THINKING
                return
            
            # 验证计划的可执行性
            plan_valid = await self._validate_execution_plan(execution_plan)
            if not plan_valid:
                logger.warning("执行计划验证失败，重新思考")
                self.state = AgentState.THINKING
                return
            
            # 计划有效，开始执行
            self.state = AgentState.EXECUTING
            
        except Exception as e:
            logger.error(f"规划阶段错误: {str(e)}")
            self.state = AgentState.ERROR
    
    async def _execution_phase(self, context: Dict[str, Any]):
        """执行阶段"""
        try:
            logger.debug("进入执行阶段")
            
            # 获取当前执行计划
            execution_plan = context.get('execution_plan')
            if not execution_plan:
                self.state = AgentState.THINKING
                return
            
            # 执行下一个任务
            result = await self.task_manager.execute_plan(execution_plan)
            
            # 更新上下文
            await self.context_manager.update_execution_result(result)
            
            # 转入评估阶段
            self.state = AgentState.EVALUATING
            
        except Exception as e:
            logger.error(f"执行阶段错误: {str(e)}")
            self.state = AgentState.ERROR
    
    async def _evaluation_phase(self, context: Dict[str, Any]):
        """评估阶段"""
        try:
            logger.debug("进入评估阶段")
            
            # 获取执行结果
            execution_result = context.get('last_execution_result')
            if not execution_result:
                self.state = AgentState.THINKING
                return
            
            # 使用决策引擎评估结果
            decision = await self.decision_engine.should_continue(context)
            
            # 根据决策结果确定下一步
            if decision.decision_type == DecisionType.CONTINUE:
                self.state = AgentState.EXECUTING
            elif decision.decision_type == DecisionType.REPLAN:
                self.state = AgentState.PLANNING
            elif decision.decision_type == DecisionType.COMPLETE:
                self.state = AgentState.IDLE
                self.stats['successful_executions'] += 1
            else:
                self.state = AgentState.THINKING
            
        except Exception as e:
            logger.error(f"评估阶段错误: {str(e)}")
            self.state = AgentState.ERROR
    
    async def _validate_execution_plan(self, execution_plan: Dict[str, Any]) -> bool:
        """验证执行计划"""
        try:
            # 检查计划结构
            if not execution_plan.get('tasks'):
                return False
            
            # 检查任务依赖关系
            tasks = execution_plan['tasks']
            for task in tasks:
                dependencies = task.get('dependencies', [])
                for dep_id in dependencies:
                    if not any(t.get('id') == dep_id for t in tasks):
                        logger.warning(f"任务依赖不存在: {dep_id}")
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证执行计划失败: {str(e)}")
            return False
    
    def _update_average_response_time(self):
        """更新平均响应时间"""
        if self.stats['total_requests'] > 0:
            self.stats['average_response_time'] = (
                self.stats['total_execution_time'] / self.stats['total_requests']
            )
    
    def get_status(self) -> Dict[str, Any]:
        """获取代理状态
        
        Returns:
            Dict[str, Any]: 代理状态信息
        """
        return {
            'agent_id': self.agent_id,
            'state': self.state,
            'is_running': self.is_running,
            'session_id': self.current_session_id,
            'created_at': self.created_at.isoformat(),
            'stats': self.stats.copy()
        }
    
    def get_detailed_status(self) -> Dict[str, Any]:
        """获取详细状态信息
        
        Returns:
            Dict[str, Any]: 详细状态信息
        """
        status = self.get_status()

        # 添加运行时间
        uptime_seconds = (datetime.now() - self.created_at).total_seconds()
        status['uptime'] = uptime_seconds

        # 添加组件状态
        status['components'] = {
            'task_manager': 'active',
            'execution_engine': 'active',
            'decision_engine': 'active',
            'context_manager': 'active',
            'system_operations': 'active'
        }

        return status


# 全局代理实例
_global_agent: Optional[AutonomousAgent] = None


def get_agent(config_path: str = "config/config.yaml") -> AutonomousAgent:
    """获取全局代理实例

    Args:
        config_path: 配置文件路径

    Returns:
        AutonomousAgent: 代理实例
    """
    global _global_agent

    if _global_agent is None:
        _global_agent = AutonomousAgent(config_path)

    return _global_agent


async def shutdown_agent():
    """关闭全局代理"""
    global _global_agent

    if _global_agent and _global_agent.is_running:
        await _global_agent.stop()

    _global_agent = None
