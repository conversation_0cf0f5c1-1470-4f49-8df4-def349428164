"""
HyAIAgent Advanced AI Capabilities Module

This module contains advanced AI capabilities including:
- Multi-step reasoning engine
- Knowledge graph management
- Learning system
- Creativity engine
"""

try:
    from .reasoning_engine import ReasoningEngine, ReasoningStep, ReasoningChain, ReasoningResult
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from reasoning_engine import ReasoningEngine, ReasoningStep, ReasoningChain, ReasoningResult

try:
    from .knowledge_graph import KnowledgeGraph, KnowledgeTriple, KnowledgeResults, InferredRelation
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from knowledge_graph import KnowledgeGraph, KnowledgeTriple, KnowledgeResults, InferredRelation

# 其他模块暂时注释，等开发完成后再启用
# from .learning_system import LearningSystem, UserInteraction, ResponseFeedback, UsagePatterns
# from .creativity_engine import CreativityEngine

__all__ = [
    'ReasoningEngine',
    'ReasoningStep',
    'ReasoningChain',
    'ReasoningResult',
    'KnowledgeGraph',
    'KnowledgeTriple',
    'KnowledgeResults',
    'InferredRelation'
    # 其他模块暂时注释
    # 'LearningSystem',
    # 'UserInteraction',
    # 'ResponseFeedback',
    # 'UsagePatterns',
    # 'CreativityEngine'
]

__version__ = "1.0.0"
__author__ = "HyAIAgent Development Team"
