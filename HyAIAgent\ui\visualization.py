"""
可视化展示模块

提供任务执行流程可视化、数据关系图表展示、实时性能监控图表、知识图谱可视化等功能。
支持matplotlib、plotly等图表库集成，提供交互式图表和导出功能。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import sys
import json
import asyncio
import io
import base64
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from pathlib import Path
from datetime import datetime, timedelta
import logging

# PyQt6 imports (必须先导入)
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QTabWidget,
    QLabel, QPushButton, QComboBox, QSpinBox, QSlider, QCheckBox,
    QGroupBox, QSplitter, QScrollArea, QTextEdit, QProgressBar,
    QFileDialog, QMessageBox, QFrame, QSizePolicy
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QObject
from PyQt6.QtGui import QFont, QPixmap, QPainter

# 数据处理库（可选依赖）
try:
    import numpy as np
    import pandas as pd
    HAS_NUMPY = True
    HAS_PANDAS = True
except ImportError:
    HAS_NUMPY = False
    HAS_PANDAS = False
    # 创建模拟对象
    class MockNumpy:
        def array(self, data):
            return data
        def arange(self, *args):
            return list(range(*args))
        def linspace(self, start, stop, num, endpoint=True):
            step = (stop - start) / (num - 1 if endpoint else num)
            return [start + i * step for i in range(num)]
        def cos(self, x):
            import math
            return [math.cos(i) for i in x] if isinstance(x, list) else math.cos(x)
        def sin(self, x):
            import math
            return [math.sin(i) for i in x] if isinstance(x, list) else math.sin(x)
        def diff(self, x):
            return [x[i+1] - x[i] for i in range(len(x)-1)]
        def random(self):
            import random
            return type('Random', (), {'seed': lambda x: None, 'randn': lambda *args: [random.random() for _ in range(args[0] if args else 1)]})()

    class MockPandas:
        def DataFrame(self, data=None):
            return type('DataFrame', (), {
                'data': data or {},
                'columns': list(data.keys()) if data else [],
                'corr': lambda: data,
                'describe': lambda: data,
                'dropna': lambda: data
            })()

    np = MockNumpy()
    pd = MockPandas()

# 图表库（可选依赖）
try:
    import matplotlib
    matplotlib.use('Qt5Agg')  # 使用Qt后端
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.dates as mdates
    from matplotlib.animation import FuncAnimation
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    # 创建模拟的matplotlib对象
    class MockFigure:
        def __init__(self, *args, **kwargs):
            pass
        def add_subplot(self, *args):
            return type('Axes', (), {
                'clear': lambda self: None,
                'text': lambda self, *args, **kwargs: None,
                'imshow': lambda self, *args, **kwargs: None,
                'set_xticks': lambda self, x: None,
                'set_yticks': lambda self, x: None,
                'set_xticklabels': lambda self, x: None,
                'set_yticklabels': lambda self, x: None,
                'set_title': lambda self, x: None,
                'set_xlabel': lambda self, x: None,
                'set_ylabel': lambda self, x: None,
                'set_xlim': lambda self, *args: None,
                'set_ylim': lambda self, *args: None,
                'set_aspect': lambda self, x: None,
                'axis': lambda self, x: None,
                'grid': lambda self, *args, **kwargs: None,
                'plot': lambda self, *args, **kwargs: None,
                'scatter': lambda self, *args, **kwargs: None,
                'barh': lambda self, *args, **kwargs: None,
                'boxplot': lambda self, *args, **kwargs: None,
                'hist': lambda self, *args, **kwargs: None,
                'legend': lambda self: None,
                'add_patch': lambda self, x: None,
                'arrow': lambda self, *args, **kwargs: None,
                'annotate': lambda self, *args, **kwargs: None,
                'tick_params': lambda self, *args, **kwargs: None,
                'xaxis': type('Axis', (), {'set_major_formatter': lambda self, x: None})(),
                'transAxes': None
            })()
        def tight_layout(self):
            pass
        def savefig(self, *args, **kwargs):
            pass
        def colorbar(self, *args, **kwargs):
            pass
        def clear(self):
            pass

    class MockCanvas(QWidget):
        def __init__(self, figure):
            super().__init__()
            self.figure = figure
        def draw(self):
            pass

    Figure = MockFigure
    FigureCanvas = MockCanvas
    plt = type('plt', (), {
        'Circle': lambda *args, **kwargs: None,
        'cm': type('cm', (), {'Set3': lambda x: ['#FF0000'] * len(x), 'coolwarm': 'coolwarm', 'viridis': 'viridis'})()
    })()
    mdates = type('mdates', (), {
        'date2num': lambda x: 0,
        'DateFormatter': lambda x: None
    })()
    FuncAnimation = lambda *args, **kwargs: None

# 导入项目模块
from core.config_manager import ConfigManager
from core.kv_store import KVStore

# 设置日志
logger = logging.getLogger(__name__)


class TaskFlowVisualizer(QWidget):
    """任务执行流程可视化器
    
    可视化显示任务的执行流程、状态变化和依赖关系
    """
    
    # 信号定义
    taskSelected = pyqtSignal(str)
    flowUpdated = pyqtSignal()
    
    def __init__(self, parent=None):
        """初始化任务流程可视化器"""
        super().__init__(parent)
        self.tasks = []
        self.connections = []
        self.current_task = None
        
        self.setup_ui()
        self.setup_matplotlib()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_layout = QHBoxLayout()
        
        # 视图控制
        self.view_combo = QComboBox()
        self.view_combo.addItems(["流程图", "甘特图", "网络图", "时间线"])
        control_layout.addWidget(QLabel("视图类型:"))
        control_layout.addWidget(self.view_combo)
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        control_layout.addWidget(self.refresh_button)
        
        # 导出按钮
        self.export_button = QPushButton("导出")
        control_layout.addWidget(self.export_button)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 图表区域
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # 信息面板
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(100)
        self.info_text.setPlaceholderText("任务详细信息将在这里显示...")
        layout.addWidget(self.info_text)
        
        # 连接信号
        self.view_combo.currentTextChanged.connect(self.on_view_changed)
        self.refresh_button.clicked.connect(self.refresh_visualization)
        self.export_button.clicked.connect(self.export_visualization)
        
    def setup_matplotlib(self):
        """设置matplotlib"""
        self.ax = self.figure.add_subplot(111)
        self.figure.tight_layout()
        
    def set_tasks(self, tasks: List[Dict[str, Any]]):
        """设置任务数据"""
        self.tasks = tasks
        self.refresh_visualization()
        
    def add_task(self, task: Dict[str, Any]):
        """添加任务"""
        self.tasks.append(task)
        self.refresh_visualization()
        
    def update_task_status(self, task_id: str, status: str):
        """更新任务状态"""
        for task in self.tasks:
            if task.get('id') == task_id:
                task['status'] = status
                break
        self.refresh_visualization()
        
    def set_connections(self, connections: List[Dict[str, str]]):
        """设置任务连接关系"""
        self.connections = connections
        self.refresh_visualization()
        
    def refresh_visualization(self):
        """刷新可视化"""
        self.ax.clear()
        
        view_type = self.view_combo.currentText()
        
        if view_type == "流程图":
            self._draw_flowchart()
        elif view_type == "甘特图":
            self._draw_gantt_chart()
        elif view_type == "网络图":
            self._draw_network_diagram()
        elif view_type == "时间线":
            self._draw_timeline()
            
        self.canvas.draw()
        self.flowUpdated.emit()
        
    def _draw_flowchart(self):
        """绘制流程图"""
        if not self.tasks:
            self.ax.text(0.5, 0.5, '暂无任务数据', ha='center', va='center', 
                        transform=self.ax.transAxes, fontsize=14)
            return
            
        # 状态颜色映射
        status_colors = {
            'pending': '#FFA500',
            'running': '#4CAF50',
            'completed': '#2196F3',
            'failed': '#F44336',
            'cancelled': '#9E9E9E'
        }
        
        # 计算布局
        n_tasks = len(self.tasks)
        cols = min(4, n_tasks)
        rows = (n_tasks + cols - 1) // cols
        
        for i, task in enumerate(self.tasks):
            row = i // cols
            col = i % cols
            
            x = col * 2
            y = (rows - row - 1) * 2
            
            # 绘制任务节点
            status = task.get('status', 'pending')
            color = status_colors.get(status, '#CCCCCC')
            
            circle = plt.Circle((x, y), 0.3, color=color, alpha=0.7)
            self.ax.add_patch(circle)
            
            # 添加任务名称
            task_name = task.get('name', f'Task {i+1}')
            self.ax.text(x, y, task_name[:8], ha='center', va='center', 
                        fontsize=8, weight='bold')
            
        # 绘制连接线
        for conn in self.connections:
            from_id = conn.get('from')
            to_id = conn.get('to')
            
            # 找到对应的任务位置
            from_pos = self._get_task_position(from_id)
            to_pos = self._get_task_position(to_id)
            
            if from_pos and to_pos:
                self.ax.arrow(from_pos[0], from_pos[1], 
                            to_pos[0] - from_pos[0], to_pos[1] - from_pos[1],
                            head_width=0.1, head_length=0.1, fc='black', ec='black')
                
        self.ax.set_xlim(-1, cols * 2)
        self.ax.set_ylim(-1, rows * 2)
        self.ax.set_aspect('equal')
        self.ax.set_title('任务执行流程图')
        self.ax.axis('off')
        
    def _draw_gantt_chart(self):
        """绘制甘特图"""
        if not self.tasks:
            self.ax.text(0.5, 0.5, '暂无任务数据', ha='center', va='center', 
                        transform=self.ax.transAxes, fontsize=14)
            return
            
        # 准备数据
        task_names = []
        start_times = []
        durations = []
        colors = []
        
        status_colors = {
            'pending': '#FFA500',
            'running': '#4CAF50',
            'completed': '#2196F3',
            'failed': '#F44336',
            'cancelled': '#9E9E9E'
        }
        
        base_time = datetime.now()
        
        for i, task in enumerate(self.tasks):
            task_names.append(task.get('name', f'Task {i+1}'))
            
            # 模拟时间数据
            start_time = base_time + timedelta(hours=i*2)
            duration = task.get('duration', 2)  # 默认2小时
            
            start_times.append(start_time)
            durations.append(duration)
            
            status = task.get('status', 'pending')
            colors.append(status_colors.get(status, '#CCCCCC'))
            
        # 绘制甘特图
        y_pos = np.arange(len(task_names))
        
        for i, (start, duration, color) in enumerate(zip(start_times, durations, colors)):
            self.ax.barh(y_pos[i], duration, left=mdates.date2num(start), 
                        color=color, alpha=0.7, height=0.6)
            
        self.ax.set_yticks(y_pos)
        self.ax.set_yticklabels(task_names)
        self.ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        self.ax.set_xlabel('时间')
        self.ax.set_title('任务甘特图')
        self.ax.grid(True, alpha=0.3)
        
    def _draw_network_diagram(self):
        """绘制网络图"""
        if not self.tasks:
            self.ax.text(0.5, 0.5, '暂无任务数据', ha='center', va='center', 
                        transform=self.ax.transAxes, fontsize=14)
            return
            
        # 简化的网络图实现
        import networkx as nx
        
        G = nx.DiGraph()
        
        # 添加节点
        for task in self.tasks:
            task_id = task.get('id', task.get('name', ''))
            G.add_node(task_id, **task)
            
        # 添加边
        for conn in self.connections:
            G.add_edge(conn.get('from'), conn.get('to'))
            
        # 布局
        pos = nx.spring_layout(G)
        
        # 绘制网络图
        nx.draw(G, pos, ax=self.ax, with_labels=True, 
               node_color='lightblue', node_size=1000, 
               font_size=8, font_weight='bold',
               arrows=True, arrowsize=20)
               
        self.ax.set_title('任务网络图')
        
    def _draw_timeline(self):
        """绘制时间线"""
        if not self.tasks:
            self.ax.text(0.5, 0.5, '暂无任务数据', ha='center', va='center', 
                        transform=self.ax.transAxes, fontsize=14)
            return
            
        # 时间线实现
        base_time = datetime.now()
        times = []
        events = []
        colors = []
        
        status_colors = {
            'pending': '#FFA500',
            'running': '#4CAF50',
            'completed': '#2196F3',
            'failed': '#F44336',
            'cancelled': '#9E9E9E'
        }
        
        for i, task in enumerate(self.tasks):
            time = base_time + timedelta(hours=i*2)
            times.append(time)
            events.append(task.get('name', f'Task {i+1}'))
            
            status = task.get('status', 'pending')
            colors.append(status_colors.get(status, '#CCCCCC'))
            
        # 绘制时间线
        self.ax.scatter(times, [1]*len(times), c=colors, s=100, alpha=0.7)
        
        for i, (time, event) in enumerate(zip(times, events)):
            self.ax.annotate(event, (time, 1), xytext=(0, 20), 
                           textcoords='offset points', ha='center',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor=colors[i], alpha=0.3))
                           
        self.ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        self.ax.set_ylim(0.5, 1.5)
        self.ax.set_ylabel('')
        self.ax.set_xlabel('时间')
        self.ax.set_title('任务时间线')
        self.ax.grid(True, alpha=0.3)
        
    def _get_task_position(self, task_id: str) -> Optional[Tuple[float, float]]:
        """获取任务在图中的位置"""
        for i, task in enumerate(self.tasks):
            if task.get('id') == task_id:
                cols = min(4, len(self.tasks))
                row = i // cols
                col = i % cols
                x = col * 2
                y = (len(self.tasks) // cols - row) * 2
                return (x, y)
        return None
        
    def on_view_changed(self, view_type: str):
        """处理视图类型变化"""
        self.refresh_visualization()
        
    def export_visualization(self):
        """导出可视化图表"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "导出图表", f"task_flow_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
            "PNG文件 (*.png);;PDF文件 (*.pdf);;SVG文件 (*.svg)"
        )
        
        if filename:
            self.figure.savefig(filename, dpi=300, bbox_inches='tight')
            QMessageBox.information(self, "导出成功", f"图表已导出为: {filename}")


class DataRelationshipChart(QWidget):
    """数据关系图表展示器
    
    展示数据之间的关系、相关性分析和统计图表
    """
    
    def __init__(self, parent=None):
        """初始化数据关系图表展示器"""
        super().__init__(parent)
        self.data = None
        self.chart_type = "correlation"
        
        self.setup_ui()
        self.setup_matplotlib()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_layout = QHBoxLayout()
        
        # 图表类型选择
        self.chart_combo = QComboBox()
        self.chart_combo.addItems(["相关性矩阵", "散点图", "箱线图", "直方图", "热力图"])
        control_layout.addWidget(QLabel("图表类型:"))
        control_layout.addWidget(self.chart_combo)
        
        # 数据加载按钮
        self.load_button = QPushButton("加载数据")
        control_layout.addWidget(self.load_button)
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        control_layout.addWidget(self.refresh_button)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 图表区域
        self.figure = Figure(figsize=(10, 8))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # 连接信号
        self.chart_combo.currentTextChanged.connect(self.on_chart_type_changed)
        self.load_button.clicked.connect(self.load_data)
        self.refresh_button.clicked.connect(self.refresh_chart)
        
    def setup_matplotlib(self):
        """设置matplotlib"""
        self.ax = self.figure.add_subplot(111)
        self.figure.tight_layout()
        
    def load_data(self):
        """加载数据"""
        # 这里可以实现数据加载逻辑
        # 暂时使用模拟数据
        np.random.seed(42)
        self.data = pd.DataFrame({
            'A': np.random.randn(100),
            'B': np.random.randn(100),
            'C': np.random.randn(100),
            'D': np.random.randn(100)
        })
        self.refresh_chart()
        
    def set_data(self, data: pd.DataFrame):
        """设置数据"""
        self.data = data
        self.refresh_chart()
        
    def refresh_chart(self):
        """刷新图表"""
        if self.data is None:
            self.ax.text(0.5, 0.5, '请先加载数据', ha='center', va='center', 
                        transform=self.ax.transAxes, fontsize=14)
            self.canvas.draw()
            return
            
        self.ax.clear()
        
        chart_type = self.chart_combo.currentText()
        
        if chart_type == "相关性矩阵":
            self._draw_correlation_matrix()
        elif chart_type == "散点图":
            self._draw_scatter_plot()
        elif chart_type == "箱线图":
            self._draw_box_plot()
        elif chart_type == "直方图":
            self._draw_histogram()
        elif chart_type == "热力图":
            self._draw_heatmap()
            
        self.canvas.draw()
        
    def _draw_correlation_matrix(self):
        """绘制相关性矩阵"""
        corr = self.data.corr()
        im = self.ax.imshow(corr, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)
        
        # 添加标签
        self.ax.set_xticks(range(len(corr.columns)))
        self.ax.set_yticks(range(len(corr.columns)))
        self.ax.set_xticklabels(corr.columns)
        self.ax.set_yticklabels(corr.columns)
        
        # 添加数值
        for i in range(len(corr.columns)):
            for j in range(len(corr.columns)):
                text = self.ax.text(j, i, f'{corr.iloc[i, j]:.2f}',
                                  ha="center", va="center", color="black")
                                  
        self.ax.set_title('数据相关性矩阵')
        self.figure.colorbar(im, ax=self.ax)
        
    def _draw_scatter_plot(self):
        """绘制散点图"""
        if len(self.data.columns) >= 2:
            x_col = self.data.columns[0]
            y_col = self.data.columns[1]
            
            self.ax.scatter(self.data[x_col], self.data[y_col], alpha=0.6)
            self.ax.set_xlabel(x_col)
            self.ax.set_ylabel(y_col)
            self.ax.set_title(f'{x_col} vs {y_col} 散点图')
            self.ax.grid(True, alpha=0.3)
            
    def _draw_box_plot(self):
        """绘制箱线图"""
        self.ax.boxplot([self.data[col].dropna() for col in self.data.columns],
                       labels=self.data.columns)
        self.ax.set_title('数据分布箱线图')
        self.ax.grid(True, alpha=0.3)
        
    def _draw_histogram(self):
        """绘制直方图"""
        n_cols = len(self.data.columns)
        colors = plt.cm.Set3(np.linspace(0, 1, n_cols))
        
        for i, col in enumerate(self.data.columns):
            self.ax.hist(self.data[col].dropna(), alpha=0.7, 
                        label=col, color=colors[i], bins=20)
                        
        self.ax.set_title('数据分布直方图')
        self.ax.legend()
        self.ax.grid(True, alpha=0.3)
        
    def _draw_heatmap(self):
        """绘制热力图"""
        # 使用数据的统计信息创建热力图
        stats = self.data.describe()
        im = self.ax.imshow(stats.values, cmap='viridis', aspect='auto')
        
        self.ax.set_xticks(range(len(stats.columns)))
        self.ax.set_yticks(range(len(stats.index)))
        self.ax.set_xticklabels(stats.columns)
        self.ax.set_yticklabels(stats.index)
        
        self.ax.set_title('数据统计热力图')
        self.figure.colorbar(im, ax=self.ax)
        
    def on_chart_type_changed(self, chart_type: str):
        """处理图表类型变化"""
        self.refresh_chart()


class PerformanceMonitor(QWidget):
    """实时性能监控图表

    监控系统性能指标，如CPU使用率、内存使用率、任务执行时间等
    """

    # 信号定义
    dataUpdated = pyqtSignal(dict)
    alertTriggered = pyqtSignal(str, float)

    def __init__(self, parent=None):
        """初始化性能监控器"""
        super().__init__(parent)
        self.monitoring = False
        self.data_history = {
            'cpu': [],
            'memory': [],
            'disk': [],
            'network': [],
            'timestamps': []
        }
        self.max_history = 100
        self.alert_thresholds = {
            'cpu': 80.0,
            'memory': 85.0,
            'disk': 90.0
        }

        self.setup_ui()
        self.setup_matplotlib()
        self.setup_timer()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 控制面板
        control_layout = QHBoxLayout()

        # 监控控制
        self.start_button = QPushButton("开始监控")
        self.stop_button = QPushButton("停止监控")
        self.clear_button = QPushButton("清除数据")

        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.stop_button)
        control_layout.addWidget(self.clear_button)

        # 更新间隔
        control_layout.addWidget(QLabel("更新间隔(秒):"))
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 60)
        self.interval_spin.setValue(5)
        control_layout.addWidget(self.interval_spin)

        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 指标显示
        metrics_layout = QHBoxLayout()

        # CPU指标
        cpu_group = QGroupBox("CPU使用率")
        cpu_layout = QVBoxLayout(cpu_group)
        self.cpu_label = QLabel("0%")
        self.cpu_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        self.cpu_progress = QProgressBar()
        cpu_layout.addWidget(self.cpu_label)
        cpu_layout.addWidget(self.cpu_progress)
        metrics_layout.addWidget(cpu_group)

        # 内存指标
        memory_group = QGroupBox("内存使用率")
        memory_layout = QVBoxLayout(memory_group)
        self.memory_label = QLabel("0%")
        self.memory_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        self.memory_progress = QProgressBar()
        memory_layout.addWidget(self.memory_label)
        memory_layout.addWidget(self.memory_progress)
        metrics_layout.addWidget(memory_group)

        # 磁盘指标
        disk_group = QGroupBox("磁盘使用率")
        disk_layout = QVBoxLayout(disk_group)
        self.disk_label = QLabel("0%")
        self.disk_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        self.disk_progress = QProgressBar()
        disk_layout.addWidget(self.disk_label)
        disk_layout.addWidget(self.disk_progress)
        metrics_layout.addWidget(disk_group)

        layout.addLayout(metrics_layout)

        # 图表区域
        self.figure = Figure(figsize=(12, 6))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        # 连接信号
        self.start_button.clicked.connect(self.start_monitoring)
        self.stop_button.clicked.connect(self.stop_monitoring)
        self.clear_button.clicked.connect(self.clear_data)
        self.interval_spin.valueChanged.connect(self.update_interval)

    def setup_matplotlib(self):
        """设置matplotlib"""
        self.figure.clear()

        # 创建子图
        self.ax_cpu = self.figure.add_subplot(2, 2, 1)
        self.ax_memory = self.figure.add_subplot(2, 2, 2)
        self.ax_disk = self.figure.add_subplot(2, 2, 3)
        self.ax_network = self.figure.add_subplot(2, 2, 4)

        self.figure.tight_layout()

    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_metrics)

    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        interval = self.interval_spin.value() * 1000  # 转换为毫秒
        self.timer.start(interval)

        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.timer.stop()

        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)

    def clear_data(self):
        """清除历史数据"""
        for key in self.data_history:
            self.data_history[key].clear()
        self.refresh_charts()

    def update_interval(self, interval: int):
        """更新监控间隔"""
        if self.monitoring:
            self.timer.setInterval(interval * 1000)

    def update_metrics(self):
        """更新性能指标"""
        try:
            # 获取系统性能数据（这里使用模拟数据）
            import psutil

            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            disk_percent = psutil.disk_usage('/').percent
            network_io = psutil.net_io_counters()

            # 更新显示
            self.cpu_label.setText(f"{cpu_percent:.1f}%")
            self.cpu_progress.setValue(int(cpu_percent))

            self.memory_label.setText(f"{memory_percent:.1f}%")
            self.memory_progress.setValue(int(memory_percent))

            self.disk_label.setText(f"{disk_percent:.1f}%")
            self.disk_progress.setValue(int(disk_percent))

            # 更新历史数据
            current_time = datetime.now()
            self.data_history['cpu'].append(cpu_percent)
            self.data_history['memory'].append(memory_percent)
            self.data_history['disk'].append(disk_percent)
            self.data_history['network'].append(network_io.bytes_sent + network_io.bytes_recv)
            self.data_history['timestamps'].append(current_time)

            # 限制历史数据长度
            if len(self.data_history['timestamps']) > self.max_history:
                for key in self.data_history:
                    self.data_history[key].pop(0)

            # 检查警报
            self._check_alerts(cpu_percent, memory_percent, disk_percent)

            # 刷新图表
            self.refresh_charts()

            # 发射信号
            self.dataUpdated.emit({
                'cpu': cpu_percent,
                'memory': memory_percent,
                'disk': disk_percent,
                'timestamp': current_time
            })

        except Exception as e:
            logger.error(f"更新性能指标失败: {e}")
            # 使用模拟数据
            self._update_with_mock_data()

    def _update_with_mock_data(self):
        """使用模拟数据更新"""
        import random

        cpu_percent = random.uniform(10, 90)
        memory_percent = random.uniform(20, 80)
        disk_percent = random.uniform(30, 70)

        self.cpu_label.setText(f"{cpu_percent:.1f}%")
        self.cpu_progress.setValue(int(cpu_percent))

        self.memory_label.setText(f"{memory_percent:.1f}%")
        self.memory_progress.setValue(int(memory_percent))

        self.disk_label.setText(f"{disk_percent:.1f}%")
        self.disk_progress.setValue(int(disk_percent))

        current_time = datetime.now()
        self.data_history['cpu'].append(cpu_percent)
        self.data_history['memory'].append(memory_percent)
        self.data_history['disk'].append(disk_percent)
        self.data_history['network'].append(random.uniform(1000, 10000))
        self.data_history['timestamps'].append(current_time)

        if len(self.data_history['timestamps']) > self.max_history:
            for key in self.data_history:
                self.data_history[key].pop(0)

        self.refresh_charts()

    def _check_alerts(self, cpu: float, memory: float, disk: float):
        """检查警报条件"""
        if cpu > self.alert_thresholds['cpu']:
            self.alertTriggered.emit("CPU使用率过高", cpu)

        if memory > self.alert_thresholds['memory']:
            self.alertTriggered.emit("内存使用率过高", memory)

        if disk > self.alert_thresholds['disk']:
            self.alertTriggered.emit("磁盘使用率过高", disk)

    def refresh_charts(self):
        """刷新图表"""
        if not self.data_history['timestamps']:
            return

        timestamps = self.data_history['timestamps']

        # CPU图表
        self.ax_cpu.clear()
        self.ax_cpu.plot(timestamps, self.data_history['cpu'], 'b-', linewidth=2)
        self.ax_cpu.set_title('CPU使用率')
        self.ax_cpu.set_ylabel('%')
        self.ax_cpu.grid(True, alpha=0.3)
        self.ax_cpu.set_ylim(0, 100)

        # 内存图表
        self.ax_memory.clear()
        self.ax_memory.plot(timestamps, self.data_history['memory'], 'g-', linewidth=2)
        self.ax_memory.set_title('内存使用率')
        self.ax_memory.set_ylabel('%')
        self.ax_memory.grid(True, alpha=0.3)
        self.ax_memory.set_ylim(0, 100)

        # 磁盘图表
        self.ax_disk.clear()
        self.ax_disk.plot(timestamps, self.data_history['disk'], 'r-', linewidth=2)
        self.ax_disk.set_title('磁盘使用率')
        self.ax_disk.set_ylabel('%')
        self.ax_disk.grid(True, alpha=0.3)
        self.ax_disk.set_ylim(0, 100)

        # 网络图表
        self.ax_network.clear()
        if len(self.data_history['network']) > 1:
            network_data = np.array(self.data_history['network'])
            network_diff = np.diff(network_data)
            self.ax_network.plot(timestamps[1:], network_diff, 'm-', linewidth=2)
        self.ax_network.set_title('网络流量')
        self.ax_network.set_ylabel('Bytes/s')
        self.ax_network.grid(True, alpha=0.3)

        # 格式化时间轴
        for ax in [self.ax_cpu, self.ax_memory, self.ax_disk, self.ax_network]:
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
            ax.tick_params(axis='x', rotation=45)

        self.figure.tight_layout()
        self.canvas.draw()

    def set_alert_threshold(self, metric: str, threshold: float):
        """设置警报阈值"""
        if metric in self.alert_thresholds:
            self.alert_thresholds[metric] = threshold

    def export_data(self, filename: str):
        """导出监控数据"""
        try:
            df = pd.DataFrame(self.data_history)
            df.to_csv(filename, index=False)
            return True
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return False


class KnowledgeGraphVisualizer(QWidget):
    """知识图谱可视化器

    可视化展示知识图谱、实体关系和概念层次结构
    """

    # 信号定义
    nodeSelected = pyqtSignal(str)
    edgeSelected = pyqtSignal(str, str)
    graphUpdated = pyqtSignal()

    def __init__(self, parent=None):
        """初始化知识图谱可视化器"""
        super().__init__(parent)
        self.nodes = []
        self.edges = []
        self.selected_node = None
        self.layout_type = "spring"

        self.setup_ui()
        self.setup_matplotlib()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 控制面板
        control_layout = QHBoxLayout()

        # 布局类型选择
        self.layout_combo = QComboBox()
        self.layout_combo.addItems(["spring", "circular", "random", "shell"])
        control_layout.addWidget(QLabel("布局类型:"))
        control_layout.addWidget(self.layout_combo)

        # 节点大小控制
        control_layout.addWidget(QLabel("节点大小:"))
        self.node_size_slider = QSlider(Qt.Orientation.Horizontal)
        self.node_size_slider.setRange(100, 2000)
        self.node_size_slider.setValue(500)
        control_layout.addWidget(self.node_size_slider)

        # 显示标签
        self.show_labels_check = QCheckBox("显示标签")
        self.show_labels_check.setChecked(True)
        control_layout.addWidget(self.show_labels_check)

        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        control_layout.addWidget(self.refresh_button)

        # 导出按钮
        self.export_button = QPushButton("导出")
        control_layout.addWidget(self.export_button)

        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 图表区域
        self.figure = Figure(figsize=(12, 8))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        # 信息面板
        info_layout = QHBoxLayout()

        # 节点信息
        node_info_group = QGroupBox("节点信息")
        node_info_layout = QVBoxLayout(node_info_group)
        self.node_info_text = QTextEdit()
        self.node_info_text.setMaximumHeight(100)
        node_info_layout.addWidget(self.node_info_text)
        info_layout.addWidget(node_info_group)

        # 图谱统计
        stats_group = QGroupBox("图谱统计")
        stats_layout = QVBoxLayout(stats_group)
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(100)
        stats_layout.addWidget(self.stats_text)
        info_layout.addWidget(stats_group)

        layout.addLayout(info_layout)

        # 连接信号
        self.layout_combo.currentTextChanged.connect(self.on_layout_changed)
        self.node_size_slider.valueChanged.connect(self.on_node_size_changed)
        self.show_labels_check.toggled.connect(self.refresh_visualization)
        self.refresh_button.clicked.connect(self.refresh_visualization)
        self.export_button.clicked.connect(self.export_graph)

    def setup_matplotlib(self):
        """设置matplotlib"""
        self.ax = self.figure.add_subplot(111)
        self.figure.tight_layout()

    def set_graph_data(self, nodes: List[Dict[str, Any]], edges: List[Dict[str, Any]]):
        """设置图谱数据"""
        self.nodes = nodes
        self.edges = edges
        self.refresh_visualization()
        self.update_stats()

    def add_node(self, node: Dict[str, Any]):
        """添加节点"""
        self.nodes.append(node)
        self.refresh_visualization()
        self.update_stats()

    def add_edge(self, edge: Dict[str, Any]):
        """添加边"""
        self.edges.append(edge)
        self.refresh_visualization()
        self.update_stats()

    def remove_node(self, node_id: str):
        """移除节点"""
        self.nodes = [n for n in self.nodes if n.get('id') != node_id]
        self.edges = [e for e in self.edges if e.get('source') != node_id and e.get('target') != node_id]
        self.refresh_visualization()
        self.update_stats()

    def refresh_visualization(self):
        """刷新可视化"""
        self.ax.clear()

        if not self.nodes:
            self.ax.text(0.5, 0.5, '暂无图谱数据', ha='center', va='center',
                        transform=self.ax.transAxes, fontsize=14)
            self.canvas.draw()
            return

        try:
            import networkx as nx

            # 创建图
            G = nx.Graph()

            # 添加节点
            for node in self.nodes:
                node_id = node.get('id', '')
                G.add_node(node_id, **node)

            # 添加边
            for edge in self.edges:
                source = edge.get('source', '')
                target = edge.get('target', '')
                if source and target:
                    G.add_edge(source, target, **edge)

            # 选择布局
            if self.layout_type == "spring":
                pos = nx.spring_layout(G, k=1, iterations=50)
            elif self.layout_type == "circular":
                pos = nx.circular_layout(G)
            elif self.layout_type == "random":
                pos = nx.random_layout(G)
            elif self.layout_type == "shell":
                pos = nx.shell_layout(G)
            else:
                pos = nx.spring_layout(G)

            # 绘制节点
            node_colors = []
            node_sizes = []

            for node in self.nodes:
                # 根据节点类型设置颜色
                node_type = node.get('type', 'default')
                color_map = {
                    'entity': '#FF6B6B',
                    'concept': '#4ECDC4',
                    'relation': '#45B7D1',
                    'attribute': '#96CEB4',
                    'default': '#FFEAA7'
                }
                node_colors.append(color_map.get(node_type, color_map['default']))

                # 根据重要性设置大小
                importance = node.get('importance', 1.0)
                base_size = self.node_size_slider.value()
                node_sizes.append(base_size * importance)

            # 绘制图
            nx.draw_networkx_nodes(G, pos, ax=self.ax,
                                 node_color=node_colors,
                                 node_size=node_sizes,
                                 alpha=0.8)

            nx.draw_networkx_edges(G, pos, ax=self.ax,
                                 edge_color='gray',
                                 alpha=0.6,
                                 width=1)

            # 绘制标签
            if self.show_labels_check.isChecked():
                labels = {}
                for node in self.nodes:
                    node_id = node.get('id', '')
                    label = node.get('label', node_id)
                    labels[node_id] = label[:10]  # 限制标签长度

                nx.draw_networkx_labels(G, pos, labels, ax=self.ax,
                                      font_size=8, font_weight='bold')

            self.ax.set_title('知识图谱可视化')
            self.ax.axis('off')

        except ImportError:
            # 如果没有networkx，使用简化的绘制方法
            self._draw_simple_graph()

        self.canvas.draw()
        self.graphUpdated.emit()

    def _draw_simple_graph(self):
        """简化的图谱绘制方法（不依赖networkx）"""
        if not self.nodes:
            return

        # 简单的圆形布局
        n_nodes = len(self.nodes)
        angles = np.linspace(0, 2*np.pi, n_nodes, endpoint=False)
        radius = 1.0

        positions = {}
        for i, node in enumerate(self.nodes):
            x = radius * np.cos(angles[i])
            y = radius * np.sin(angles[i])
            positions[node.get('id', '')] = (x, y)

        # 绘制边
        for edge in self.edges:
            source = edge.get('source', '')
            target = edge.get('target', '')

            if source in positions and target in positions:
                x1, y1 = positions[source]
                x2, y2 = positions[target]
                self.ax.plot([x1, x2], [y1, y2], 'gray', alpha=0.6, linewidth=1)

        # 绘制节点
        for node in self.nodes:
            node_id = node.get('id', '')
            if node_id in positions:
                x, y = positions[node_id]

                # 节点颜色
                node_type = node.get('type', 'default')
                color_map = {
                    'entity': '#FF6B6B',
                    'concept': '#4ECDC4',
                    'relation': '#45B7D1',
                    'attribute': '#96CEB4',
                    'default': '#FFEAA7'
                }
                color = color_map.get(node_type, color_map['default'])

                # 绘制节点
                circle = plt.Circle((x, y), 0.1, color=color, alpha=0.8)
                self.ax.add_patch(circle)

                # 绘制标签
                if self.show_labels_check.isChecked():
                    label = node.get('label', node_id)
                    self.ax.text(x, y-0.15, label[:10], ha='center', va='top',
                               fontsize=8, weight='bold')

        self.ax.set_xlim(-1.5, 1.5)
        self.ax.set_ylim(-1.5, 1.5)
        self.ax.set_aspect('equal')
        self.ax.set_title('知识图谱可视化')
        self.ax.axis('off')

    def update_stats(self):
        """更新图谱统计信息"""
        stats = {
            "节点数量": len(self.nodes),
            "边数量": len(self.edges),
            "节点类型": {}
        }

        # 统计节点类型
        for node in self.nodes:
            node_type = node.get('type', 'default')
            stats["节点类型"][node_type] = stats["节点类型"].get(node_type, 0) + 1

        # 格式化显示
        stats_text = f"节点数量: {stats['节点数量']}\n"
        stats_text += f"边数量: {stats['边数量']}\n"
        stats_text += "节点类型分布:\n"

        for node_type, count in stats["节点类型"].items():
            stats_text += f"  {node_type}: {count}\n"

        self.stats_text.setText(stats_text)

    def on_layout_changed(self, layout_type: str):
        """处理布局类型变化"""
        self.layout_type = layout_type
        self.refresh_visualization()

    def on_node_size_changed(self, size: int):
        """处理节点大小变化"""
        self.refresh_visualization()

    def export_graph(self):
        """导出图谱"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "导出知识图谱", f"knowledge_graph_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png",
            "PNG文件 (*.png);;PDF文件 (*.pdf);;JSON文件 (*.json)"
        )

        if filename:
            if filename.endswith('.json'):
                # 导出数据
                graph_data = {
                    'nodes': self.nodes,
                    'edges': self.edges
                }
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(graph_data, f, ensure_ascii=False, indent=2)
            else:
                # 导出图片
                self.figure.savefig(filename, dpi=300, bbox_inches='tight')

            QMessageBox.information(self, "导出成功", f"知识图谱已导出为: {filename}")

    def load_graph_from_file(self, filename: str):
        """从文件加载图谱"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                graph_data = json.load(f)

            self.nodes = graph_data.get('nodes', [])
            self.edges = graph_data.get('edges', [])

            self.refresh_visualization()
            self.update_stats()

            return True
        except Exception as e:
            logger.error(f"加载图谱文件失败: {e}")
            return False


class Visualization(QWidget):
    """可视化展示主类

    统一管理所有可视化组件，提供任务流程、数据关系、性能监控、知识图谱等可视化功能
    """

    # 信号定义
    visualizationChanged = pyqtSignal(str)
    dataExported = pyqtSignal(str)
    alertTriggered = pyqtSignal(str, str)

    def __init__(self, config_manager: Optional[ConfigManager] = None,
                 kv_store: Optional[KVStore] = None, parent=None):
        """初始化可视化展示主类

        Args:
            config_manager: 配置管理器
            kv_store: 键值存储
            parent: 父组件
        """
        super().__init__(parent)
        self.config_manager = config_manager
        self.kv_store = kv_store

        # 组件实例
        self.task_flow_visualizer = None
        self.data_relationship_chart = None
        self.performance_monitor = None
        self.knowledge_graph_visualizer = None

        # 设置日志
        self.logger = logging.getLogger(__name__)

        self.setup_ui()
        self.setup_components()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("可视化展示中心")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 选项卡组件
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        # 全局控制按钮
        self.export_all_button = QPushButton("导出所有")
        self.refresh_all_button = QPushButton("刷新所有")
        status_layout.addWidget(self.export_all_button)
        status_layout.addWidget(self.refresh_all_button)

        layout.addLayout(status_layout)

        # 连接信号
        self.export_all_button.clicked.connect(self.export_all_visualizations)
        self.refresh_all_button.clicked.connect(self.refresh_all_visualizations)

    def setup_components(self):
        """设置可视化组件"""
        # 任务流程可视化
        self.task_flow_visualizer = TaskFlowVisualizer()
        self.tab_widget.addTab(self.task_flow_visualizer, "任务流程")

        # 数据关系图表
        self.data_relationship_chart = DataRelationshipChart()
        self.tab_widget.addTab(self.data_relationship_chart, "数据关系")

        # 性能监控
        self.performance_monitor = PerformanceMonitor()
        self.tab_widget.addTab(self.performance_monitor, "性能监控")

        # 知识图谱
        self.knowledge_graph_visualizer = KnowledgeGraphVisualizer()
        self.tab_widget.addTab(self.knowledge_graph_visualizer, "知识图谱")

        # 连接信号
        self.task_flow_visualizer.taskSelected.connect(self.on_task_selected)
        self.task_flow_visualizer.flowUpdated.connect(self.on_flow_updated)

        self.performance_monitor.dataUpdated.connect(self.on_performance_data_updated)
        self.performance_monitor.alertTriggered.connect(self.on_alert_triggered)

        self.knowledge_graph_visualizer.nodeSelected.connect(self.on_node_selected)
        self.knowledge_graph_visualizer.graphUpdated.connect(self.on_graph_updated)

        # 选项卡变化信号
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def set_task_flow_data(self, tasks: List[Dict[str, Any]],
                          connections: Optional[List[Dict[str, str]]] = None):
        """设置任务流程数据"""
        if self.task_flow_visualizer:
            self.task_flow_visualizer.set_tasks(tasks)
            if connections:
                self.task_flow_visualizer.set_connections(connections)
            self.status_label.setText("任务流程数据已更新")

    def set_data_relationship_data(self, data: pd.DataFrame):
        """设置数据关系数据"""
        if self.data_relationship_chart:
            self.data_relationship_chart.set_data(data)
            self.status_label.setText("数据关系数据已更新")

    def set_knowledge_graph_data(self, nodes: List[Dict[str, Any]],
                                edges: List[Dict[str, Any]]):
        """设置知识图谱数据"""
        if self.knowledge_graph_visualizer:
            self.knowledge_graph_visualizer.set_graph_data(nodes, edges)
            self.status_label.setText("知识图谱数据已更新")

    def start_performance_monitoring(self):
        """开始性能监控"""
        if self.performance_monitor:
            self.performance_monitor.start_monitoring()
            self.status_label.setText("性能监控已开始")

    def stop_performance_monitoring(self):
        """停止性能监控"""
        if self.performance_monitor:
            self.performance_monitor.stop_monitoring()
            self.status_label.setText("性能监控已停止")

    def add_task_to_flow(self, task: Dict[str, Any]):
        """添加任务到流程图"""
        if self.task_flow_visualizer:
            self.task_flow_visualizer.add_task(task)

    def update_task_status(self, task_id: str, status: str):
        """更新任务状态"""
        if self.task_flow_visualizer:
            self.task_flow_visualizer.update_task_status(task_id, status)

    def add_knowledge_node(self, node: Dict[str, Any]):
        """添加知识图谱节点"""
        if self.knowledge_graph_visualizer:
            self.knowledge_graph_visualizer.add_node(node)

    def add_knowledge_edge(self, edge: Dict[str, Any]):
        """添加知识图谱边"""
        if self.knowledge_graph_visualizer:
            self.knowledge_graph_visualizer.add_edge(edge)

    def export_all_visualizations(self):
        """导出所有可视化内容"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            export_dir = Path(f"visualization_export_{timestamp}")
            export_dir.mkdir(exist_ok=True)

            # 导出任务流程
            if self.task_flow_visualizer:
                task_flow_file = export_dir / "task_flow.png"
                self.task_flow_visualizer.figure.savefig(task_flow_file, dpi=300, bbox_inches='tight')

            # 导出数据关系
            if self.data_relationship_chart:
                data_chart_file = export_dir / "data_relationship.png"
                self.data_relationship_chart.figure.savefig(data_chart_file, dpi=300, bbox_inches='tight')

            # 导出性能监控
            if self.performance_monitor:
                performance_file = export_dir / "performance_monitor.png"
                self.performance_monitor.figure.savefig(performance_file, dpi=300, bbox_inches='tight')

                # 导出性能数据
                performance_data_file = export_dir / "performance_data.csv"
                self.performance_monitor.export_data(str(performance_data_file))

            # 导出知识图谱
            if self.knowledge_graph_visualizer:
                knowledge_graph_file = export_dir / "knowledge_graph.png"
                self.knowledge_graph_visualizer.figure.savefig(knowledge_graph_file, dpi=300, bbox_inches='tight')

                # 导出图谱数据
                graph_data_file = export_dir / "knowledge_graph_data.json"
                graph_data = {
                    'nodes': self.knowledge_graph_visualizer.nodes,
                    'edges': self.knowledge_graph_visualizer.edges
                }
                with open(graph_data_file, 'w', encoding='utf-8') as f:
                    json.dump(graph_data, f, ensure_ascii=False, indent=2)

            self.status_label.setText(f"所有可视化内容已导出到: {export_dir}")
            self.dataExported.emit(str(export_dir))

            QMessageBox.information(self, "导出成功", f"所有可视化内容已导出到:\n{export_dir}")

        except Exception as e:
            error_msg = f"导出失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.logger.error(error_msg)
            QMessageBox.warning(self, "导出失败", error_msg)

    def refresh_all_visualizations(self):
        """刷新所有可视化内容"""
        try:
            if self.task_flow_visualizer:
                self.task_flow_visualizer.refresh_visualization()

            if self.data_relationship_chart:
                self.data_relationship_chart.refresh_chart()

            if self.performance_monitor:
                self.performance_monitor.refresh_charts()

            if self.knowledge_graph_visualizer:
                self.knowledge_graph_visualizer.refresh_visualization()

            self.status_label.setText("所有可视化内容已刷新")

        except Exception as e:
            error_msg = f"刷新失败: {str(e)}"
            self.status_label.setText(error_msg)
            self.logger.error(error_msg)

    def get_current_visualization_type(self) -> str:
        """获取当前可视化类型"""
        current_index = self.tab_widget.currentIndex()
        tab_names = ["task_flow", "data_relationship", "performance_monitor", "knowledge_graph"]
        return tab_names[current_index] if current_index < len(tab_names) else "unknown"

    def switch_to_visualization(self, visualization_type: str):
        """切换到指定的可视化类型"""
        type_mapping = {
            "task_flow": 0,
            "data_relationship": 1,
            "performance_monitor": 2,
            "knowledge_graph": 3
        }

        if visualization_type in type_mapping:
            self.tab_widget.setCurrentIndex(type_mapping[visualization_type])

    def get_visualization_stats(self) -> Dict[str, Any]:
        """获取可视化统计信息"""
        stats = {
            "current_tab": self.get_current_visualization_type(),
            "task_flow": {
                "tasks_count": len(self.task_flow_visualizer.tasks) if self.task_flow_visualizer else 0,
                "connections_count": len(self.task_flow_visualizer.connections) if self.task_flow_visualizer else 0
            },
            "performance_monitor": {
                "monitoring": self.performance_monitor.monitoring if self.performance_monitor else False,
                "data_points": len(self.performance_monitor.data_history['timestamps']) if self.performance_monitor else 0
            },
            "knowledge_graph": {
                "nodes_count": len(self.knowledge_graph_visualizer.nodes) if self.knowledge_graph_visualizer else 0,
                "edges_count": len(self.knowledge_graph_visualizer.edges) if self.knowledge_graph_visualizer else 0
            }
        }
        return stats

    # 信号处理方法
    def on_task_selected(self, task_id: str):
        """处理任务选择"""
        self.status_label.setText(f"已选择任务: {task_id}")

    def on_flow_updated(self):
        """处理流程更新"""
        self.status_label.setText("任务流程已更新")

    def on_performance_data_updated(self, data: Dict[str, Any]):
        """处理性能数据更新"""
        cpu = data.get('cpu', 0)
        memory = data.get('memory', 0)
        self.status_label.setText(f"性能监控 - CPU: {cpu:.1f}%, 内存: {memory:.1f}%")

    def on_alert_triggered(self, alert_type: str, value: float):
        """处理警报触发"""
        alert_msg = f"警报: {alert_type} - {value:.1f}%"
        self.status_label.setText(alert_msg)
        self.alertTriggered.emit(alert_type, alert_msg)

    def on_node_selected(self, node_id: str):
        """处理节点选择"""
        self.status_label.setText(f"已选择节点: {node_id}")

    def on_graph_updated(self):
        """处理图谱更新"""
        self.status_label.setText("知识图谱已更新")

    def on_tab_changed(self, index: int):
        """处理选项卡变化"""
        tab_names = ["任务流程", "数据关系", "性能监控", "知识图谱"]
        if index < len(tab_names):
            self.status_label.setText(f"当前视图: {tab_names[index]}")
            self.visualizationChanged.emit(tab_names[index])

    def cleanup(self):
        """清理资源"""
        if self.performance_monitor:
            self.performance_monitor.stop_monitoring()
        self.logger.info("可视化展示组件已清理")
