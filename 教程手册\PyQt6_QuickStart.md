# PyQt6 快速入门指南

## 🚀 安装和环境配置

### 安装PyQt6
```bash
# 基础安装
pip install PyQt6

# 包含开发工具
pip install PyQt6 PyQt6-tools

# 验证安装
python -c "from PyQt6.QtWidgets import QApplication; print('PyQt6 安装成功！')"
```

### 开发环境推荐
- **IDE**: VS Code, PyCharm, Qt Creator
- **Python版本**: 3.8+
- **操作系统**: Windows 10+, macOS 10.14+, Linux

## 📚 基础概念

### 1. 应用程序结构
```python
import sys
from PyQt6.QtWidgets import QApplication, QMainWindow

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("我的应用")
        
def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
```

### 2. 常用控件
```python
from PyQt6.QtWidgets import (
    QLabel,      # 标签
    QLineEdit,   # 单行输入框
    QPushButton, # 按钮
    QTextEdit,   # 多行文本框
    QComboBox,   # 下拉框
    QCheckBox,   # 复选框
    QRadioButton # 单选按钮
)
```

### 3. 布局管理器
```python
from PyQt6.QtWidgets import (
    QVBoxLayout,  # 垂直布局
    QHBoxLayout,  # 水平布局
    QGridLayout,  # 网格布局
    QFormLayout   # 表单布局
)
```

## 🎨 信号槽机制

### 基础用法
```python
# 连接信号到槽
button.clicked.connect(self.on_button_clicked)

# 槽函数定义
def on_button_clicked(self):
    print("按钮被点击了！")
```

### 自定义信号
```python
from PyQt6.QtCore import pyqtSignal

class MyWidget(QWidget):
    # 定义自定义信号
    data_changed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        # 连接信号
        self.data_changed.connect(self.handle_data_change)
    
    def emit_signal(self):
        # 发射信号
        self.data_changed.emit("新数据")
    
    def handle_data_change(self, data):
        print(f"数据变化: {data}")
```

## 🎨 样式表(QSS)

### 基础样式
```python
# 设置样式表
widget.setStyleSheet("""
    QPushButton {
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 16px;
        font-weight: bold;
    }
    
    QPushButton:hover {
        background-color: #2980b9;
    }
    
    QPushButton:pressed {
        background-color: #21618c;
    }
""")
```

### 全局样式
```python
app = QApplication(sys.argv)
app.setStyleSheet("""
    QMainWindow {
        background-color: #f8f9fa;
    }
    
    QLineEdit {
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        padding: 5px;
    }
    
    QLineEdit:focus {
        border-color: #3498db;
    }
""")
```

## 🔧 实用技巧

### 1. 窗口居中
```python
def center_window(self):
    screen = QApplication.primaryScreen().geometry()
    window = self.geometry()
    x = (screen.width() - window.width()) // 2
    y = (screen.height() - window.height()) // 2
    self.move(x, y)
```

### 2. 消息框
```python
from PyQt6.QtWidgets import QMessageBox

# 信息提示
QMessageBox.information(self, "标题", "消息内容")

# 警告提示
QMessageBox.warning(self, "警告", "警告内容")

# 错误提示
QMessageBox.critical(self, "错误", "错误内容")

# 确认对话框
reply = QMessageBox.question(self, "确认", "确定要继续吗？")
if reply == QMessageBox.StandardButton.Yes:
    print("用户选择了是")
```

### 3. 文件对话框
```python
from PyQt6.QtWidgets import QFileDialog

# 打开文件
file_path, _ = QFileDialog.getOpenFileName(
    self, "选择文件", "", "文本文件 (*.txt);;所有文件 (*)"
)

# 保存文件
file_path, _ = QFileDialog.getSaveFileName(
    self, "保存文件", "", "文本文件 (*.txt)"
)

# 选择目录
dir_path = QFileDialog.getExistingDirectory(self, "选择目录")
```

## 📱 响应式设计

### 布局权重
```python
layout = QHBoxLayout()
layout.addWidget(widget1, 1)  # 权重1
layout.addWidget(widget2, 2)  # 权重2，占用更多空间
```

### 尺寸策略
```python
from PyQt6.QtWidgets import QSizePolicy

widget.setSizePolicy(
    QSizePolicy.Policy.Expanding,  # 水平方向可扩展
    QSizePolicy.Policy.Fixed       # 垂直方向固定
)
```

## 🚀 最佳实践

### 1. 代码组织
```python
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()      # 初始化界面
        self.init_signals() # 初始化信号槽
        
    def init_ui(self):
        """初始化用户界面"""
        pass
        
    def init_signals(self):
        """初始化信号槽连接"""
        pass
```

### 2. 错误处理
```python
def safe_operation(self):
    try:
        # 可能出错的操作
        result = risky_function()
    except Exception as e:
        QMessageBox.critical(self, "错误", f"操作失败: {str(e)}")
        return False
    return True
```

### 3. 资源管理
```python
# 使用资源文件
from PyQt6 import QtCore

# 加载图标
icon = QtGui.QIcon(":/icons/app_icon.png")
self.setWindowIcon(icon)
```

## 📖 学习路径

### 初级阶段
1. 掌握基础控件使用
2. 学习布局管理器
3. 理解信号槽机制
4. 练习简单应用开发

### 中级阶段
1. 掌握QSS样式表
2. 学习自定义控件
3. 理解模型/视图架构
4. 掌握多线程编程

### 高级阶段
1. 插件系统开发
2. 国际化和本地化
3. 性能优化技巧
4. 跨平台部署

---

**祝您PyQt6学习愉快！** 🎉
