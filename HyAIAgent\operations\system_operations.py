"""
系统操作模块

本模块实现基础的系统级操作，如文件操作、进程管理等。
"""

import os
import sys
import asyncio
import subprocess
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from operations.base_operation import BaseOperation, OperationType, OperationResult

logger = logging.getLogger(__name__)


class FileOperation(BaseOperation):
    """文件操作类"""
    
    def __init__(self):
        super().__init__(
            operation_type=OperationType.FILE,
            name="file_operation",
            description="文件系统操作"
        )
    
    async def execute(self, **kwargs) -> OperationResult:
        """执行文件操作
        
        支持的操作:
        - read: 读取文件
        - write: 写入文件
        - delete: 删除文件
        - copy: 复制文件
        - move: 移动文件
        - exists: 检查文件是否存在
        - list: 列出目录内容
        """
        action = kwargs.get('action')
        file_path = kwargs.get('file_path')
        
        try:
            if action == 'read':
                return await self._read_file(file_path, kwargs.get('encoding', 'utf-8'))
            elif action == 'write':
                return await self._write_file(
                    file_path, 
                    kwargs.get('content', ''),
                    kwargs.get('encoding', 'utf-8'),
                    kwargs.get('append', False)
                )
            elif action == 'delete':
                return await self._delete_file(file_path)
            elif action == 'copy':
                return await self._copy_file(file_path, kwargs.get('destination'))
            elif action == 'move':
                return await self._move_file(file_path, kwargs.get('destination'))
            elif action == 'exists':
                return await self._check_exists(file_path)
            elif action == 'list':
                return await self._list_directory(file_path, kwargs.get('recursive', False))
            else:
                return OperationResult(
                    success=False,
                    error_message=f"不支持的文件操作: {action}"
                )
                
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"文件操作失败: {str(e)}"
            )
    
    def validate_parameters(self, **kwargs) -> bool:
        """验证参数"""
        action = kwargs.get('action')
        file_path = kwargs.get('file_path')
        
        if not action:
            logger.error("缺少action参数")
            return False
        
        if not file_path:
            logger.error("缺少file_path参数")
            return False
        
        # 特定操作的参数验证
        if action in ['copy', 'move'] and not kwargs.get('destination'):
            logger.error(f"{action}操作需要destination参数")
            return False
        
        if action == 'write' and 'content' not in kwargs:
            logger.error("write操作需要content参数")
            return False
        
        return True
    
    async def _read_file(self, file_path: str, encoding: str) -> OperationResult:
        """读取文件"""
        try:
            path = Path(file_path)
            if not path.exists():
                return OperationResult(
                    success=False,
                    error_message=f"文件不存在: {file_path}"
                )
            
            content = path.read_text(encoding=encoding)
            return OperationResult(
                success=True,
                data=content,
                metadata={'file_size': path.stat().st_size, 'encoding': encoding}
            )
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"读取文件失败: {str(e)}"
            )
    
    async def _write_file(self, file_path: str, content: str, encoding: str, append: bool) -> OperationResult:
        """写入文件"""
        try:
            path = Path(file_path)
            
            # 确保目录存在
            path.parent.mkdir(parents=True, exist_ok=True)
            
            if append:
                with open(path, 'a', encoding=encoding) as f:
                    f.write(content)
            else:
                path.write_text(content, encoding=encoding)
            
            return OperationResult(
                success=True,
                data=f"文件{'追加' if append else '写入'}成功",
                metadata={'file_path': str(path), 'content_length': len(content)}
            )
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"写入文件失败: {str(e)}"
            )
    
    async def _delete_file(self, file_path: str) -> OperationResult:
        """删除文件"""
        try:
            path = Path(file_path)
            if not path.exists():
                return OperationResult(
                    success=False,
                    error_message=f"文件不存在: {file_path}"
                )
            
            if path.is_file():
                path.unlink()
            elif path.is_dir():
                import shutil
                shutil.rmtree(path)
            
            return OperationResult(
                success=True,
                data=f"删除成功: {file_path}"
            )
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"删除文件失败: {str(e)}"
            )
    
    async def _copy_file(self, source: str, destination: str) -> OperationResult:
        """复制文件"""
        try:
            import shutil
            
            source_path = Path(source)
            dest_path = Path(destination)
            
            if not source_path.exists():
                return OperationResult(
                    success=False,
                    error_message=f"源文件不存在: {source}"
                )
            
            # 确保目标目录存在
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            if source_path.is_file():
                shutil.copy2(source_path, dest_path)
            else:
                shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
            
            return OperationResult(
                success=True,
                data=f"复制成功: {source} -> {destination}"
            )
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"复制文件失败: {str(e)}"
            )
    
    async def _move_file(self, source: str, destination: str) -> OperationResult:
        """移动文件"""
        try:
            import shutil
            
            source_path = Path(source)
            dest_path = Path(destination)
            
            if not source_path.exists():
                return OperationResult(
                    success=False,
                    error_message=f"源文件不存在: {source}"
                )
            
            # 确保目标目录存在
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(str(source_path), str(dest_path))
            
            return OperationResult(
                success=True,
                data=f"移动成功: {source} -> {destination}"
            )
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"移动文件失败: {str(e)}"
            )
    
    async def _check_exists(self, file_path: str) -> OperationResult:
        """检查文件是否存在"""
        try:
            path = Path(file_path)
            exists = path.exists()
            
            metadata = {'exists': exists}
            if exists:
                stat = path.stat()
                metadata.update({
                    'is_file': path.is_file(),
                    'is_dir': path.is_dir(),
                    'size': stat.st_size,
                    'modified_time': stat.st_mtime
                })
            
            return OperationResult(
                success=True,
                data=exists,
                metadata=metadata
            )
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"检查文件存在性失败: {str(e)}"
            )
    
    async def _list_directory(self, dir_path: str, recursive: bool) -> OperationResult:
        """列出目录内容"""
        try:
            path = Path(dir_path)
            if not path.exists():
                return OperationResult(
                    success=False,
                    error_message=f"目录不存在: {dir_path}"
                )
            
            if not path.is_dir():
                return OperationResult(
                    success=False,
                    error_message=f"不是目录: {dir_path}"
                )
            
            items = []
            if recursive:
                for item in path.rglob('*'):
                    items.append({
                        'path': str(item),
                        'name': item.name,
                        'is_file': item.is_file(),
                        'is_dir': item.is_dir(),
                        'size': item.stat().st_size if item.is_file() else 0
                    })
            else:
                for item in path.iterdir():
                    items.append({
                        'path': str(item),
                        'name': item.name,
                        'is_file': item.is_file(),
                        'is_dir': item.is_dir(),
                        'size': item.stat().st_size if item.is_file() else 0
                    })
            
            return OperationResult(
                success=True,
                data=items,
                metadata={'total_items': len(items), 'recursive': recursive}
            )
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"列出目录内容失败: {str(e)}"
            )


class ProcessOperation(BaseOperation):
    """进程操作类"""
    
    def __init__(self):
        super().__init__(
            operation_type=OperationType.SYSTEM,
            name="process_operation",
            description="进程管理操作"
        )
    
    async def execute(self, **kwargs) -> OperationResult:
        """执行进程操作
        
        支持的操作:
        - run: 运行命令
        - kill: 终止进程
        - list: 列出进程
        """
        action = kwargs.get('action')
        
        try:
            if action == 'run':
                return await self._run_command(
                    kwargs.get('command'),
                    kwargs.get('cwd'),
                    kwargs.get('env'),
                    kwargs.get('timeout', 30)
                )
            elif action == 'kill':
                return await self._kill_process(kwargs.get('pid'))
            elif action == 'list':
                return await self._list_processes()
            else:
                return OperationResult(
                    success=False,
                    error_message=f"不支持的进程操作: {action}"
                )
                
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"进程操作失败: {str(e)}"
            )
    
    def validate_parameters(self, **kwargs) -> bool:
        """验证参数"""
        action = kwargs.get('action')
        
        if not action:
            logger.error("缺少action参数")
            return False
        
        if action == 'run' and not kwargs.get('command'):
            logger.error("run操作需要command参数")
            return False
        
        if action == 'kill' and not kwargs.get('pid'):
            logger.error("kill操作需要pid参数")
            return False
        
        return True
    
    async def _run_command(self, command: str, cwd: Optional[str] = None, 
                          env: Optional[Dict[str, str]] = None, timeout: int = 30) -> OperationResult:
        """运行命令"""
        try:
            # 准备环境变量
            process_env = os.environ.copy()
            if env:
                process_env.update(env)
            
            # 运行命令
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=cwd,
                env=process_env
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
                
                return OperationResult(
                    success=process.returncode == 0,
                    data={
                        'stdout': stdout.decode('utf-8', errors='ignore'),
                        'stderr': stderr.decode('utf-8', errors='ignore'),
                        'returncode': process.returncode
                    },
                    metadata={
                        'command': command,
                        'cwd': cwd,
                        'pid': process.pid
                    }
                )
                
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return OperationResult(
                    success=False,
                    error_message=f"命令执行超时 ({timeout}秒): {command}"
                )
                
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"运行命令失败: {str(e)}"
            )
    
    async def _kill_process(self, pid: int) -> OperationResult:
        """终止进程"""
        try:
            import signal
            
            os.kill(pid, signal.SIGTERM)
            
            return OperationResult(
                success=True,
                data=f"进程 {pid} 已终止"
            )
        except ProcessLookupError:
            return OperationResult(
                success=False,
                error_message=f"进程不存在: {pid}"
            )
        except PermissionError:
            return OperationResult(
                success=False,
                error_message=f"没有权限终止进程: {pid}"
            )
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"终止进程失败: {str(e)}"
            )
    
    async def _list_processes(self) -> OperationResult:
        """列出进程"""
        try:
            import psutil
            
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return OperationResult(
                success=True,
                data=processes,
                metadata={'total_processes': len(processes)}
            )
        except ImportError:
            return OperationResult(
                success=False,
                error_message="需要安装psutil库来列出进程"
            )
        except Exception as e:
            return OperationResult(
                success=False,
                error_message=f"列出进程失败: {str(e)}"
            )


class SystemOperations:
    """系统操作管理器"""
    
    def __init__(self):
        self.file_op = FileOperation()
        self.process_op = ProcessOperation()
        
        logger.info("系统操作管理器初始化完成")
    
    async def execute_file_operation(self, **kwargs) -> OperationResult:
        """执行文件操作"""
        return await self.file_op.run(**kwargs)
    
    async def execute_process_operation(self, **kwargs) -> OperationResult:
        """执行进程操作"""
        return await self.process_op.run(**kwargs)
    
    def get_available_operations(self) -> Dict[str, List[str]]:
        """获取可用操作列表"""
        return {
            'file_operations': ['read', 'write', 'delete', 'copy', 'move', 'exists', 'list'],
            'process_operations': ['run', 'kill', 'list']
        }
