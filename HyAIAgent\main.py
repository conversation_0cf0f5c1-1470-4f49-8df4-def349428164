"""
HyAIAgent主程序入口
基于PyQt6的AI聊天助手应用程序
"""

import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入应用程序组件
from ui.chat_window import ChatWindow
from core.config_manager import ConfigManager


def setup_logging():
    """设置日志系统"""
    try:
        # 确保logs目录存在
        logs_dir = project_root / "logs"
        logs_dir.mkdir(exist_ok=True)
        
        # 配置loguru日志
        logger.remove()  # 移除默认处理器
        
        # 添加控制台输出
        logger.add(
            sys.stderr,
            level="INFO",
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
        
        # 添加文件输出
        logger.add(
            logs_dir / "hyaiagent.log",
            level="DEBUG",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="10 MB",
            retention="7 days",
            compression="zip"
        )
        
        logger.info("日志系统初始化完成")
        return True
        
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        return False


def check_dependencies():
    """检查依赖项"""
    try:
        logger.info("检查依赖项...")
        
        # 检查必要的Python包
        required_packages = [
            "PyQt6",
            "openai", 
            "loguru",
            "tinydb",
            "jinja2",
            "python-dotenv"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                # 特殊处理一些包名
                import_name = package
                if package == "python-dotenv":
                    import_name = "dotenv"
                elif package == "PyQt6":
                    import_name = "PyQt6"

                __import__(import_name)
                logger.debug(f"✅ {package} 已安装")
            except ImportError:
                missing_packages.append(package)
                logger.error(f"❌ {package} 未安装")
        
        if missing_packages:
            error_msg = f"缺少必要的依赖包: {', '.join(missing_packages)}\n请运行: pip install {' '.join(missing_packages)}"
            logger.error(error_msg)
            return False, error_msg
        
        logger.info("所有依赖项检查通过")
        return True, "依赖项检查通过"
        
    except Exception as e:
        error_msg = f"依赖项检查失败: {e}"
        logger.error(error_msg)
        return False, error_msg


def check_configuration():
    """检查配置文件"""
    try:
        logger.info("检查配置文件...")
        
        config_manager = ConfigManager()
        
        # 检查配置文件是否存在
        config_file = project_root / "config.json"
        if not config_file.exists():
            logger.warning("配置文件不存在，将使用默认配置")
        
        # 检查AI配置
        try:
            ai_config = config_manager.get_ai_config()
            if not ai_config.get("api_key"):
                logger.warning("未配置AI API密钥，请在.env文件中设置OPENAI_API_KEY")
            else:
                logger.info("AI配置检查通过")
        except Exception as e:
            logger.warning(f"AI配置检查失败: {e}")
        
        # 检查环境变量文件
        env_file = project_root / ".env"
        if not env_file.exists():
            logger.warning(".env文件不存在，请创建并配置API密钥")
        
        logger.info("配置检查完成")
        return True, "配置检查完成"
        
    except Exception as e:
        error_msg = f"配置检查失败: {e}"
        logger.error(error_msg)
        return False, error_msg


def create_directories():
    """创建必要的目录"""
    try:
        logger.info("创建必要的目录...")
        
        directories = [
            "data",
            "logs", 
            "prompts/global",
            "prompts/templates"
        ]
        
        for dir_path in directories:
            full_path = project_root / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            logger.debug(f"目录已创建: {full_path}")
        
        logger.info("目录创建完成")
        return True
        
    except Exception as e:
        logger.error(f"目录创建失败: {e}")
        return False


def initialize_application():
    """初始化应用程序"""
    try:
        logger.info("初始化HyAIAgent应用程序...")
        
        # 1. 设置日志系统
        if not setup_logging():
            return False, "日志系统初始化失败"
        
        # 2. 检查依赖项
        success, message = check_dependencies()
        if not success:
            return False, message
        
        # 3. 创建必要目录
        if not create_directories():
            return False, "目录创建失败"
        
        # 4. 检查配置
        success, message = check_configuration()
        if not success:
            return False, message
        
        logger.info("应用程序初始化完成")
        return True, "初始化成功"
        
    except Exception as e:
        error_msg = f"应用程序初始化失败: {e}"
        logger.error(error_msg)
        return False, error_msg


def main():
    """主函数"""
    try:
        # 创建QApplication实例
        app = QApplication(sys.argv)
        
        # 设置应用程序属性
        app.setApplicationName("HyAIAgent")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("HyDevelop")
        app.setOrganizationDomain("hydevelop.com")
        
        # 设置应用程序样式
        app.setStyle("Fusion")
        
        # 初始化应用程序
        success, message = initialize_application()
        if not success:
            QMessageBox.critical(None, "初始化错误", f"应用程序初始化失败:\n{message}")
            return 1
        
        # 创建并显示主窗口
        logger.info("启动主窗口...")
        main_window = ChatWindow()
        main_window.show()
        
        # 显示启动成功消息
        logger.info("HyAIAgent启动成功")
        
        # 运行应用程序事件循环
        exit_code = app.exec()
        
        logger.info(f"应用程序退出，退出码: {exit_code}")
        return exit_code
        
    except Exception as e:
        error_msg = f"应用程序运行失败: {e}"
        print(error_msg)
        
        # 尝试显示错误对话框
        try:
            if 'app' in locals():
                QMessageBox.critical(None, "运行错误", error_msg)
        except:
            pass
        
        return 1


if __name__ == "__main__":
    # 设置高DPI支持 (PyQt6兼容)
    os.environ["QT_AUTO_SCREEN_SCALE_FACTOR"] = "1"

    # PyQt6中AA_EnableHighDpiScaling已被移除，默认启用
    # 只需要设置AA_UseHighDpiPixmaps
    try:
        QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    except AttributeError:
        # 如果属性不存在，忽略错误
        pass

    # 运行主程序
    exit_code = main()
    sys.exit(exit_code)
