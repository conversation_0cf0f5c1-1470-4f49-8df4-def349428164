"""
InteractionOptimizer 测试模块

测试交互流程优化系统的各项功能，包括快捷操作、批量操作、
智能提示、性能优化等。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import sys
import os
import pytest
import asyncio
import time
from unittest.mock import Mock, patch, MagicMock, AsyncMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# PyQt6 imports
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from PyQt6.QtTest import QTest

# 导入被测试的模块
from ui.interaction_optimizer import (
    InteractionOptimizer, QuickAction, BatchOperation, SmartTip,
    ContextAnalyzer, QuickActionWidget
)
from core.config_manager import ConfigManager
from core.kv_store import KVStore
from ui.settings_manager import SettingsManager


class TestInteractionOptimizer:
    """InteractionOptimizer 测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
    
    @pytest.fixture
    def mock_config_manager(self):
        """创建模拟的ConfigManager"""
        mock_config = Mock(spec=ConfigManager)
        mock_config.get.return_value = {}
        mock_config.set.return_value = True
        return mock_config
    
    @pytest.fixture
    def mock_kv_store(self):
        """创建模拟的KVStore"""
        mock_kv = Mock(spec=KVStore)
        mock_kv.get.return_value = None
        mock_kv.set.return_value = True
        mock_kv.cleanup_expired = Mock()
        return mock_kv
    
    @pytest.fixture
    def mock_settings_manager(self):
        """创建模拟的SettingsManager"""
        mock_settings = Mock(spec=SettingsManager)
        return mock_settings
    
    @pytest.fixture
    def optimizer(self, app, mock_config_manager, mock_kv_store, mock_settings_manager):
        """创建InteractionOptimizer实例"""
        optimizer = InteractionOptimizer(
            config_manager=mock_config_manager,
            settings_manager=mock_settings_manager,
            kv_store=mock_kv_store
        )
        yield optimizer
    
    def test_initialization(self, optimizer):
        """测试初始化"""
        assert optimizer is not None
        assert isinstance(optimizer.quick_actions, dict)
        assert isinstance(optimizer.batch_operations, dict)
        assert isinstance(optimizer.smart_tips, dict)
        assert len(optimizer.quick_actions) > 0  # 应该有默认操作
    
    def test_add_quick_action(self, optimizer):
        """测试添加快捷操作"""
        callback = Mock()
        
        result = optimizer.add_quick_action(
            action_id="test_action",
            name="测试操作",
            description="这是一个测试操作",
            shortcut="Ctrl+T",
            category="test",
            callback=callback
        )
        
        assert result is True
        assert "test_action" in optimizer.quick_actions
        
        action = optimizer.quick_actions["test_action"]
        assert action.name == "测试操作"
        assert action.shortcut == "Ctrl+T"
        assert action.category == "test"
        assert action.callback == callback
        assert action.enabled is True
        assert action.usage_count == 0
    
    def test_trigger_quick_action(self, optimizer):
        """测试触发快捷操作"""
        callback = Mock()
        
        # 添加操作
        optimizer.add_quick_action(
            action_id="trigger_test",
            name="触发测试",
            description="测试触发功能",
            shortcut="Ctrl+X",
            category="test",
            callback=callback
        )
        
        # 触发操作
        optimizer._trigger_quick_action("trigger_test")
        
        # 验证回调被调用
        callback.assert_called_once()
        
        # 验证使用次数增加
        action = optimizer.quick_actions["trigger_test"]
        assert action.usage_count == 1
        
        # 验证统计信息更新
        assert optimizer.stats['quick_actions_used'] == 1
    
    @pytest.mark.asyncio
    async def test_start_batch_operation(self, optimizer):
        """测试启动批量操作"""
        targets = ["target1", "target2", "target3"]
        parameters = {"param1": "value1"}
        
        result = await optimizer.start_batch_operation(
            operation_id="batch_test",
            name="批量测试",
            description="测试批量操作",
            target_type="file",
            operation_type="copy",
            targets=targets,
            parameters=parameters
        )
        
        assert result is True
        assert "batch_test" in optimizer.batch_operations
        
        operation = optimizer.batch_operations["batch_test"]
        assert operation.name == "批量测试"
        assert operation.target_type == "file"
        assert operation.operation_type == "copy"
        assert operation.parameters == parameters
        assert operation.status in ["pending", "running"]
    
    def test_show_smart_tip(self, optimizer):
        """测试显示智能提示"""
        tip_id = optimizer.show_smart_tip(
            content="这是一个测试提示",
            tip_type="info",
            context="test",
            priority=2
        )
        
        assert tip_id != ""
        assert tip_id in optimizer.smart_tips
        
        tip = optimizer.smart_tips[tip_id]
        assert tip.content == "这是一个测试提示"
        assert tip.tip_type == "info"
        assert tip.context == "test"
        assert tip.priority == 2
        assert tip.shown_count == 1
        
        # 验证统计信息更新
        assert optimizer.stats['tips_shown'] == 1
    
    def test_optimize_performance(self, optimizer):
        """测试性能优化"""
        # 模拟性能数据
        optimizer.performance_metrics['memory_usage'].extend([85, 90, 95])  # 超过阈值
        optimizer.performance_metrics['response_times'].extend([3.0, 4.0, 5.0])  # 超过阈值
        
        result = optimizer.optimize_performance()
        
        assert isinstance(result, dict)
        assert 'memory_optimized' in result
        assert 'ui_optimized' in result
        assert 'cache_optimized' in result
        assert 'improvements' in result
        
        if result['improvements']:
            assert optimizer.stats['optimizations_applied'] > 0
    
    def test_analyze_user_behavior(self, optimizer):
        """测试用户行为分析"""
        # 模拟用户行为数据
        optimizer.user_behavior['click_patterns']['action1'] = 10
        optimizer.user_behavior['click_patterns']['action2'] = 5
        optimizer.user_behavior['navigation_paths'].extend(['page1', 'page2', 'page1', 'page3'])
        optimizer.user_behavior['error_patterns']['error1'] = 3
        optimizer.user_behavior['help_requests']['help1'] = 2
        
        analysis = optimizer.analyze_user_behavior()
        
        assert isinstance(analysis, dict)
        assert 'most_used_actions' in analysis
        assert 'common_navigation_paths' in analysis
        assert 'frequent_errors' in analysis
        assert 'help_topics' in analysis
        assert 'recommendations' in analysis
        
        # 验证最常用操作排序
        if analysis['most_used_actions']:
            assert analysis['most_used_actions'][0][0] == 'action1'
            assert analysis['most_used_actions'][0][1] == 10
    
    def test_get_optimization_stats(self, optimizer):
        """测试获取优化统计信息"""
        # 添加一些数据
        optimizer.stats['quick_actions_used'] = 5
        optimizer.stats['tips_shown'] = 3
        
        # 添加批量操作
        optimizer.batch_operations['op1'] = BatchOperation(
            operation_id='op1',
            name='操作1',
            description='描述1',
            target_type='file',
            operation_type='copy',
            parameters={},
            status='running'
        )
        
        # 添加智能提示
        optimizer.smart_tips['tip1'] = SmartTip(
            tip_id='tip1',
            content='提示1',
            tip_type='info',
            context='test',
            shown_count=1
        )
        
        # 添加性能数据
        optimizer.performance_metrics['response_times'].extend([1.0, 2.0, 1.5])
        optimizer.performance_metrics['memory_usage'].extend([60, 65, 70])
        
        stats = optimizer.get_optimization_stats()
        
        assert isinstance(stats, dict)
        assert stats['quick_actions_used'] == 5
        assert stats['tips_shown'] == 3
        assert stats['active_batch_operations'] == 1
        assert stats['active_tips'] == 1
        assert stats['quick_actions_count'] > 0  # 包含默认操作
        assert 'average_response_time' in stats
        assert 'current_memory_usage' in stats


class TestContextAnalyzer:
    """ContextAnalyzer 测试类"""
    
    @pytest.fixture
    def analyzer(self):
        """创建ContextAnalyzer实例"""
        return ContextAnalyzer()
    
    def test_initialization(self, analyzer):
        """测试初始化"""
        assert analyzer is not None
        assert analyzer.current_context == "general"
        assert len(analyzer.context_history) == 0
        assert isinstance(analyzer.context_patterns, dict)
    
    def test_analyze_current_context(self, analyzer):
        """测试分析当前上下文"""
        context = analyzer.analyze_current_context()
        assert isinstance(context, str)
        assert context == "general"  # 默认上下文
    
    def test_update_context(self, analyzer):
        """测试更新上下文"""
        # 更新到新上下文
        analyzer.update_context("file_operations")
        assert analyzer.current_context == "file_operations"
        assert len(analyzer.context_history) == 1
        assert analyzer.context_history[0] == "general"
        
        # 再次更新
        analyzer.update_context("task_management")
        assert analyzer.current_context == "task_management"
        assert len(analyzer.context_history) == 2
        assert analyzer.context_history[1] == "file_operations"
        
        # 更新到相同上下文（不应该添加到历史）
        analyzer.update_context("task_management")
        assert len(analyzer.context_history) == 2
    
    def test_get_context_history(self, analyzer):
        """测试获取上下文历史"""
        # 添加一些历史
        analyzer.update_context("context1")
        analyzer.update_context("context2")
        analyzer.update_context("context3")
        
        history = analyzer.get_context_history()
        assert isinstance(history, list)
        assert len(history) == 3
        assert history == ["general", "context1", "context2"]


class TestQuickActionWidget:
    """QuickActionWidget 测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
    
    @pytest.fixture
    def optimizer(self, app):
        """创建模拟的InteractionOptimizer"""
        mock_config = Mock()
        optimizer = InteractionOptimizer(mock_config)
        return optimizer
    
    def test_widget_initialization(self, app, optimizer):
        """测试小部件初始化"""
        widget = QuickActionWidget(optimizer)
        
        assert widget is not None
        assert widget.optimizer == optimizer
        assert hasattr(widget, 'actions_list')
        
        # 检查是否显示了默认操作
        assert widget.actions_list.count() > 0
    
    def test_refresh_actions(self, app, optimizer):
        """测试刷新操作列表"""
        widget = QuickActionWidget(optimizer)
        
        # 添加新操作
        optimizer.add_quick_action(
            action_id="widget_test",
            name="小部件测试",
            description="测试小部件",
            shortcut="Ctrl+W",
            category="test",
            callback=Mock()
        )
        
        # 刷新列表
        widget.refresh_actions()
        
        # 验证新操作是否显示
        items = [widget.actions_list.item(i).text() for i in range(widget.actions_list.count())]
        assert any("小部件测试" in item for item in items)
    
    def test_on_action_triggered(self, app, optimizer):
        """测试操作触发处理"""
        widget = QuickActionWidget(optimizer)
        initial_count = widget.actions_list.count()
        
        # 触发操作
        widget.on_action_triggered("new_task")  # 默认操作之一
        
        # 验证列表被刷新（项目数量应该相同，但内容可能更新）
        assert widget.actions_list.count() == initial_count


class TestDataModels:
    """数据模型测试类"""
    
    def test_quick_action_model(self):
        """测试QuickAction数据模型"""
        callback = Mock()
        action = QuickAction(
            action_id="test_id",
            name="测试操作",
            description="测试描述",
            shortcut="Ctrl+T",
            category="test",
            callback=callback
        )
        
        assert action.action_id == "test_id"
        assert action.name == "测试操作"
        assert action.description == "测试描述"
        assert action.shortcut == "Ctrl+T"
        assert action.category == "test"
        assert action.callback == callback
        assert action.enabled is True
        assert action.usage_count == 0
        assert action.last_used is not None
    
    def test_batch_operation_model(self):
        """测试BatchOperation数据模型"""
        operation = BatchOperation(
            operation_id="batch_id",
            name="批量操作",
            description="批量描述",
            target_type="file",
            operation_type="copy",
            parameters={"param": "value"}
        )
        
        assert operation.operation_id == "batch_id"
        assert operation.name == "批量操作"
        assert operation.description == "批量描述"
        assert operation.target_type == "file"
        assert operation.operation_type == "copy"
        assert operation.parameters == {"param": "value"}
        assert operation.progress == 0.0
        assert operation.status == "pending"
        assert operation.created_at is not None
    
    def test_smart_tip_model(self):
        """测试SmartTip数据模型"""
        tip = SmartTip(
            tip_id="tip_id",
            content="提示内容",
            tip_type="info",
            context="test"
        )
        
        assert tip.tip_id == "tip_id"
        assert tip.content == "提示内容"
        assert tip.tip_type == "info"
        assert tip.context == "test"
        assert tip.priority == 1
        assert tip.shown_count == 0
        assert tip.created_at is not None


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
