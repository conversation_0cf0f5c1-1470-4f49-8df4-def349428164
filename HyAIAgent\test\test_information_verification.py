"""
信息验证系统测试模块

该模块测试ContentProcessor类中的信息验证功能，包括：
- 信息验证
- 交叉验证
- 事实声明验证
- 信息质量评估

作者: HyAIAgent开发团队
创建时间: 2025-07-29
版本: 1.0.0
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from operations.content_processor import ContentProcessor
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager


class TestInformationVerification:
    """信息验证系统测试类"""
    
    @pytest.fixture
    def config_manager(self):
        """创建配置管理器"""
        config = ConfigManager()
        config.config = {
            "content_processor": {
                "max_content_length": 10000,
                "enable_caching": True,
                "processing_timeout": 30
            },
            "security": {
                "enable_content_filtering": True,
                "max_file_size": 10485760
            }
        }
        return config
    
    @pytest.fixture
    def security_manager(self, config_manager):
        """创建安全管理器"""
        return SecurityManager("./workspace", "config/file_security.json")
    
    @pytest.fixture
    def content_processor(self, config_manager, security_manager):
        """创建内容处理器"""
        return ContentProcessor(config_manager, security_manager)
    
    @pytest.mark.asyncio
    async def test_validate_information_comprehensive(self, content_processor):
        """测试综合信息验证"""
        information = """
        人工智能技术在2023年取得了重大突破。根据最新研究报告，
        AI模型的性能提升了40%，这主要归功于新的训练算法和更大的数据集。
        专家预测，到2025年，AI将在医疗、教育和金融领域得到广泛应用。
        """
        
        sources = [
            "https://example.com/ai-research-2023",
            "https://nature.com/ai-breakthrough-study"
        ]
        
        result = await content_processor.validate_information(
            information, sources, "comprehensive"
        )
        
        # 验证结果结构
        assert result["validation_status"] == "completed"
        assert result["validation_type"] == "comprehensive"
        assert "credibility_score" in result
        assert "validation_checks" in result
        assert "validation_report" in result
        
        # 验证分数合理性
        assert 0.0 <= result["credibility_score"] <= 1.0
        
        # 验证检查项目
        checks = result["validation_checks"]
        expected_checks = ["completeness", "consistency", "factual_accuracy"]
        for check in expected_checks:
            assert check in checks
            assert "score" in checks[check]
        
        print(f"✅ 综合信息验证测试通过，可信度: {result['credibility_score']:.2f}")
    
    @pytest.mark.asyncio
    async def test_validate_information_basic(self, content_processor):
        """测试基础信息验证"""
        information = "这是一个简单的测试信息。"
        
        result = await content_processor.validate_information(
            information, [], "basic"
        )
        
        # 验证结果结构
        assert result["validation_status"] == "completed"
        assert result["validation_type"] == "basic"
        assert "credibility_score" in result
        
        # 验证基础检查项目
        checks = result["validation_checks"]
        expected_checks = ["format", "length"]
        for check in expected_checks:
            assert check in checks
        
        print(f"✅ 基础信息验证测试通过，可信度: {result['credibility_score']:.2f}")
    
    @pytest.mark.asyncio
    async def test_cross_validate_sources(self, content_processor):
        """测试交叉验证多个信息源"""
        information = "人工智能技术正在快速发展"
        sources = [
            "人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
            "AI技术包括机器学习、深度学习和自然语言处理等多个领域。",
            "人工智能在医疗诊断、自动驾驶和语音识别等领域有广泛应用。"
        ]
        
        result = await content_processor.cross_validate_sources(information, sources)
        
        # 验证结果结构
        assert result["validation_status"] == "completed"
        assert result["source_count"] == len(sources)
        assert "consistency_score" in result
        assert "source_analyses" in result
        assert "conflicts" in result
        
        # 验证源分析
        assert len(result["source_analyses"]) == len(sources)
        for analysis in result["source_analyses"]:
            assert "source_index" in analysis
            assert "consistency_score" in analysis
            assert "authority_score" in analysis
        
        print(f"✅ 交叉验证测试通过，一致性分数: {result['consistency_score']:.2f}")
    
    @pytest.mark.asyncio
    async def test_cross_validate_insufficient_sources(self, content_processor):
        """测试信息源不足的交叉验证"""
        information = "测试信息"
        sources = ["单一信息源"]
        
        result = await content_processor.cross_validate_sources(information, sources)
        
        # 验证结果
        assert result["validation_status"] == "insufficient_sources"
        assert result["source_count"] == 1
        assert "message" in result
        
        print("✅ 信息源不足的交叉验证测试通过")
    
    @pytest.mark.asyncio
    async def test_verify_factual_claims(self, content_processor):
        """测试事实声明验证"""
        claims = [
            "地球是圆的",
            "2023年全球人口超过80亿",
            "人工智能可能在未来取代所有工作",
            "水的沸点是100摄氏度"
        ]
        
        context = "这是关于科学事实和技术发展的讨论"
        
        result = await content_processor.verify_factual_claims(claims, context)
        
        # 验证结果结构
        assert result["verification_status"] == "completed"
        assert result["claims_count"] == len(claims)
        assert "verified_claims" in result
        assert "overall_credibility" in result
        assert "verification_summary" in result
        
        # 验证声明分析
        verified_claims = result["verified_claims"]
        assert len(verified_claims) == len(claims)
        
        for claim_result in verified_claims:
            assert "claim_index" in claim_result
            assert "claim_type" in claim_result
            assert "credibility_score" in claim_result
            assert "risk_level" in claim_result
        
        print(f"✅ 事实声明验证测试通过，整体可信度: {result['overall_credibility']:.2f}")
    
    @pytest.mark.asyncio
    async def test_verify_empty_claims(self, content_processor):
        """测试空声明列表验证"""
        result = await content_processor.verify_factual_claims([])
        
        assert result["verification_status"] == "no_claims"
        assert result["verified_claims"] == []
        
        print("✅ 空声明列表验证测试通过")
    
    @pytest.mark.asyncio
    async def test_assess_information_quality(self, content_processor):
        """测试信息质量评估"""
        information = """
        根据2023年发布的研究报告，人工智能技术在多个领域取得了显著进展。
        该报告基于对1000家企业的调研数据，显示AI应用率提升了35%。
        研究团队采用了严格的统计方法，确保数据的准确性和可靠性。
        这些发现为未来AI发展提供了重要参考。
        """
        
        result = await content_processor.assess_information_quality(information)
        
        # 验证结果结构
        assert result["assessment_status"] == "completed"
        assert "quality_scores" in result
        assert "overall_score" in result
        assert "quality_level" in result
        assert "improvement_suggestions" in result
        
        # 验证质量标准
        quality_scores = result["quality_scores"]
        expected_criteria = ["completeness", "accuracy", "clarity", "relevance", "timeliness", "objectivity"]
        
        for criterion in expected_criteria:
            assert criterion in quality_scores
            assert 0.0 <= quality_scores[criterion] <= 1.0
        
        # 验证整体分数
        assert 0.0 <= result["overall_score"] <= 1.0
        
        print(f"✅ 信息质量评估测试通过，综合分数: {result['overall_score']:.2f}")
    
    @pytest.mark.asyncio
    async def test_assess_empty_information_quality(self, content_processor):
        """测试空信息质量评估"""
        result = await content_processor.assess_information_quality("")
        
        assert result["assessment_status"] == "empty_information"
        assert result["quality_score"] == 0.0
        
        print("✅ 空信息质量评估测试通过")
    
    @pytest.mark.asyncio
    async def test_validation_statistics_update(self, content_processor):
        """测试验证统计信息更新"""
        # 执行几次验证操作
        information = "测试信息用于统计更新"
        
        await content_processor.validate_information(information, [], "basic")
        await content_processor.validate_information(information, [], "comprehensive")
        
        # 检查统计信息
        stats = content_processor.processing_stats.get("validation_stats", {})
        
        assert stats["total_validations"] >= 2
        assert "by_type" in stats
        assert "average_credibility" in stats
        
        print("✅ 验证统计信息更新测试通过")


def main():
    """运行所有信息验证测试"""
    print("🚀 开始信息验证系统测试")
    print("=" * 50)
    
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])


if __name__ == "__main__":
    main()
