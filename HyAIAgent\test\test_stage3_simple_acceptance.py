"""
第三阶段简化验收测试

验证第三阶段核心模块的基本功能
"""

import asyncio
import sys
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

# 导入核心模块
from operations.security_manager import SecurityManager
from operations.file_operations import FileOperations
from operations.file_analyzer import FileAnalyzer
from utils.performance_optimizer import PerformanceOptimizer


class Stage3SimpleAcceptanceTest:
    """第三阶段简化验收测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.workspace_path = Path(self.temp_dir)
        self.test_results = []
        self.start_time = datetime.now()
        
        print(f"🚀 第三阶段简化验收测试开始")
        print(f"📁 测试工作空间: {self.temp_dir}")
        print("=" * 60)
    
    async def test_security_manager(self) -> bool:
        """测试安全管理器基本功能"""
        try:
            print("🔒 测试安全管理器...")
            
            security_manager = SecurityManager(str(self.workspace_path))
            
            # 创建测试文件
            test_file = self.workspace_path / "security_test.txt"
            test_file.write_text("安全测试内容", encoding='utf-8')
            
            # 测试路径验证
            path_valid = security_manager.validate_path(str(test_file))
            assert path_valid, "路径验证失败"
            
            # 测试文件类型检查
            type_valid = security_manager.check_file_type(str(test_file))
            assert type_valid, "文件类型检查失败"
            
            print("   ✓ 安全管理器基本功能正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 安全管理器测试失败: {e}")
            return False
    
    async def test_file_operations(self) -> bool:
        """测试文件操作基本功能"""
        try:
            print("📁 测试文件操作...")
            
            security_manager = SecurityManager(str(self.workspace_path))
            file_ops = FileOperations(str(self.workspace_path), security_manager)
            
            # 测试文件写入
            test_content = "这是文件操作测试内容"
            result = await file_ops.write_file("file_ops_test.txt", test_content)
            assert result.get('success', False), f"文件写入失败: {result.get('error', '')}"
            
            # 测试文件读取
            result = await file_ops.read_file("file_ops_test.txt")
            assert result.get('success', False), f"文件读取失败: {result.get('error', '')}"
            assert result.get('content') == test_content, "文件内容不匹配"
            
            print("   ✓ 文件操作基本功能正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 文件操作测试失败: {e}")
            return False
    
    async def test_file_analyzer(self) -> bool:
        """测试文件分析器基本功能"""
        try:
            print("🔬 测试文件分析器...")
            
            file_analyzer = FileAnalyzer(str(self.workspace_path))
            
            # 创建测试文件
            test_file = self.workspace_path / "analyzer_test.txt"
            test_file.write_text("这是用于分析的测试文件内容，包含多种信息。", encoding='utf-8')
            
            # 测试文件分析
            result = await file_analyzer.analyze_file(str(test_file), ["basic"])
            assert isinstance(result, dict), "分析结果应该是字典"
            
            print("   ✓ 文件分析器基本功能正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 文件分析器测试失败: {e}")
            return False
    
    async def test_performance_optimizer(self) -> bool:
        """测试性能优化器基本功能"""
        try:
            print("⚡ 测试性能优化器...")
            
            optimizer = PerformanceOptimizer(str(self.workspace_path))
            
            # 测试性能指标收集
            metrics = await optimizer.collect_metrics()
            assert metrics is not None, "性能指标收集失败"
            
            # 测试获取统计信息
            stats = optimizer.get_optimization_stats()
            assert isinstance(stats, dict), "统计信息应该是字典"
            
            print("   ✓ 性能优化器基本功能正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 性能优化器测试失败: {e}")
            return False
    
    async def test_integration_basic(self) -> bool:
        """测试基本集成功能"""
        try:
            print("🔄 测试基本集成...")
            
            # 创建组件
            security_manager = SecurityManager(str(self.workspace_path))
            file_ops = FileOperations(str(self.workspace_path), security_manager)
            file_analyzer = FileAnalyzer(str(self.workspace_path))
            
            # 1. 创建文件
            content = "集成测试文档内容，用于验证各模块协作。"
            result = await file_ops.write_file("integration_test.txt", content)
            assert result.get('success', False), "集成测试文件创建失败"
            
            # 2. 分析文件
            result = await file_analyzer.analyze_file("integration_test.txt", ["basic"])
            assert isinstance(result, dict), "集成测试文件分析失败"
            
            # 3. 读取文件
            result = await file_ops.read_file("integration_test.txt")
            assert result.get('success', False), "集成测试文件读取失败"
            assert result.get('content') == content, "集成测试内容不匹配"
            
            print("   ✓ 基本集成功能正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 基本集成测试失败: {e}")
            return False
    
    async def run_all_tests(self) -> bool:
        """运行所有简化验收测试"""
        tests = [
            ("安全管理器", self.test_security_manager),
            ("文件操作", self.test_file_operations),
            ("文件分析器", self.test_file_analyzer),
            ("性能优化器", self.test_performance_optimizer),
            ("基本集成", self.test_integration_basic)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                if result:
                    passed_tests += 1
                    self.test_results.append({"name": test_name, "status": "PASS"})
                else:
                    self.test_results.append({"name": test_name, "status": "FAIL"})
            except Exception as e:
                print(f"   ❌ {test_name}测试异常: {e}")
                self.test_results.append({"name": test_name, "status": "ERROR", "error": str(e)})
        
        # 生成测试报告
        self.generate_test_report(passed_tests, total_tests)
        
        return passed_tests == total_tests
    
    def generate_test_report(self, passed_tests: int, total_tests: int):
        """生成测试报告"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        print("\n" + "=" * 60)
        print("📊 第三阶段简化验收测试报告")
        print("=" * 60)
        print(f"⏱️  测试耗时: {duration:.2f}秒")
        print(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"📁 测试工作空间: {self.temp_dir}")
        
        print("\n📋 详细测试结果:")
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"   {status_icon} {result['name']}: {result['status']}")
            if "error" in result:
                print(f"      错误: {result['error']}")
        
        if passed_tests == total_tests:
            print("\n🎉 第三阶段核心功能验收测试全部通过！")
            print("✅ HyAIAgent第三阶段基础操作模块验证成功")
        else:
            failed_tests = total_tests - passed_tests
            print(f"\n❌ {failed_tests}个测试失败，但核心功能基本可用")
    
    def cleanup(self):
        """清理测试环境"""
        try:
            shutil.rmtree(self.temp_dir)
            print(f"\n🧹 测试环境已清理: {self.temp_dir}")
        except Exception as e:
            print(f"\n⚠️ 清理测试环境失败: {e}")


async def main():
    """主函数"""
    test_suite = Stage3SimpleAcceptanceTest()
    
    try:
        success = await test_suite.run_all_tests()
        return 0 if success else 1
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        test_suite.cleanup()


if __name__ == "__main__":
    # 运行简化验收测试
    result = asyncio.run(main())
    exit(result)
