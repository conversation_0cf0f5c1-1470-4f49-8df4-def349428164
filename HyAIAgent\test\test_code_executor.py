"""
代码执行器测试模块

测试CodeExecutor类的所有功能，包括代码执行、安全检查、资源管理等。
"""

import pytest
import asyncio
import time
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

from HyAIAgent.tools.code_executor import (
    CodeExecutor, ExecutionResult, SecurityPolicy, CodeAnalysisResult
)

class TestExecutionResult:
    """测试ExecutionResult数据模型"""
    
    def test_execution_result_creation(self):
        """测试执行结果创建"""
        result = ExecutionResult(
            execution_id="test-123",
            language="python",
            code="print('hello')",
            output="hello\n",
            error="",
            execution_time=0.1,
            timestamp=datetime.now(),
            success=True,
            resource_usage={"memory": 1024}
        )
        
        assert result.execution_id == "test-123"
        assert result.language == "python"
        assert result.code == "print('hello')"
        assert result.output == "hello\n"
        assert result.error == ""
        assert result.execution_time == 0.1
        assert result.success is True
        assert result.resource_usage == {"memory": 1024}
    
    def test_execution_result_to_dict(self):
        """测试执行结果转换为字典"""
        timestamp = datetime.now()
        result = ExecutionResult(
            execution_id="test-123",
            language="python",
            code="print('hello')",
            output="hello\n",
            error="",
            execution_time=0.1,
            timestamp=timestamp,
            success=True,
            resource_usage={"memory": 1024}
        )
        
        result_dict = result.to_dict()
        assert result_dict['execution_id'] == "test-123"
        assert result_dict['language'] == "python"
        assert result_dict['timestamp'] == timestamp.isoformat()
        assert result_dict['success'] is True

class TestSecurityPolicy:
    """测试SecurityPolicy数据模型"""
    
    def test_security_policy_creation(self):
        """测试安全策略创建"""
        policy = SecurityPolicy(
            max_execution_time=60,
            max_memory_mb=256,
            allow_file_access=True,
            allow_network_access=False
        )
        
        assert policy.max_execution_time == 60
        assert policy.max_memory_mb == 256
        assert policy.allow_file_access is True
        assert policy.allow_network_access is False
        assert isinstance(policy.allowed_modules, list)
        assert isinstance(policy.blocked_functions, list)
    
    def test_security_policy_defaults(self):
        """测试安全策略默认值"""
        policy = SecurityPolicy()
        
        assert policy.max_execution_time == 30
        assert policy.max_memory_mb == 128
        assert policy.allow_file_access is False
        assert policy.allow_network_access is False
        assert 'math' in policy.allowed_modules
        assert 'open' in policy.blocked_functions

class TestCodeAnalysisResult:
    """测试CodeAnalysisResult数据模型"""
    
    def test_code_analysis_result_creation(self):
        """测试代码分析结果创建"""
        result = CodeAnalysisResult(
            is_safe=True,
            issues=[],
            warnings=["潜在性能问题"],
            complexity_score=5,
            line_count=10
        )
        
        assert result.is_safe is True
        assert result.issues == []
        assert result.warnings == ["潜在性能问题"]
        assert result.complexity_score == 5
        assert result.line_count == 10
    
    def test_code_analysis_result_to_dict(self):
        """测试代码分析结果转换为字典"""
        result = CodeAnalysisResult(
            is_safe=False,
            issues=["危险操作"],
            warnings=[],
            complexity_score=8,
            line_count=15
        )
        
        result_dict = result.to_dict()
        assert result_dict['is_safe'] is False
        assert result_dict['issues'] == ["危险操作"]
        assert result_dict['complexity_score'] == 8

@pytest.fixture
def mock_config_manager():
    """模拟配置管理器"""
    return Mock()

@pytest.fixture
def mock_kv_store():
    """模拟键值存储"""
    return Mock()

@pytest.fixture
def security_policy():
    """测试安全策略"""
    return SecurityPolicy(
        max_execution_time=10,
        max_memory_mb=64,
        allow_file_access=False,
        allow_network_access=False
    )

@pytest.fixture
def code_executor(mock_config_manager, mock_kv_store, security_policy):
    """代码执行器实例"""
    return CodeExecutor(
        config_manager=mock_config_manager,
        kv_store=mock_kv_store,
        security_policy=security_policy
    )

class TestCodeExecutor:
    """测试CodeExecutor类"""
    
    def test_code_executor_initialization(self, code_executor):
        """测试代码执行器初始化"""
        assert code_executor.security_policy.max_execution_time == 10
        assert code_executor.security_policy.max_memory_mb == 64
        assert len(code_executor.execution_history) == 0
        assert len(code_executor.execution_callbacks) == 0
        assert code_executor.stats['total_executions'] == 0
        assert 'python' in code_executor.supported_languages
        assert 'javascript' in code_executor.supported_languages
        assert 'bash' in code_executor.supported_languages
    
    @pytest.mark.asyncio
    async def test_execute_python_code_success(self, code_executor):
        """测试成功执行Python代码"""
        code = "print('Hello, World!')"
        result = await code_executor.execute_code(code, 'python')
        
        assert result.success is True
        assert result.language == 'python'
        assert result.code == code
        assert 'Hello, World!' in result.output
        assert result.error == ""
        assert result.execution_time > 0
        assert len(code_executor.execution_history) == 1
    
    @pytest.mark.asyncio
    async def test_execute_python_code_with_error(self, code_executor):
        """测试执行有错误的Python代码"""
        code = "print(undefined_variable)"
        result = await code_executor.execute_code(code, 'python')
        
        assert result.success is False
        assert result.language == 'python'
        assert result.code == code
        assert result.output == ""
        assert "NameError" in result.error or "name 'undefined_variable' is not defined" in result.error
    
    @pytest.mark.asyncio
    async def test_execute_unsafe_python_code(self, code_executor):
        """测试执行不安全的Python代码"""
        code = "import os; os.system('ls')"
        result = await code_executor.execute_code(code, 'python')
        
        assert result.success is False
        assert "代码安全检查失败" in result.error
        assert "禁止导入模块: os" in result.error
    
    @pytest.mark.asyncio
    async def test_execute_code_with_context(self, code_executor):
        """测试带上下文执行代码"""
        code = "print(f'Hello, {name}!')"
        context = {"name": "Alice"}
        result = await code_executor.execute_code(code, 'python', context)
        
        assert result.success is True
        assert 'Hello, Alice!' in result.output
    
    @pytest.mark.asyncio
    async def test_execute_unsupported_language(self, code_executor):
        """测试执行不支持的语言"""
        code = "console.log('test');"
        result = await code_executor.execute_code(code, 'unsupported')
        
        assert result.success is False
        assert "不支持的编程语言" in result.error
    
    def test_analyze_python_security_safe_code(self, code_executor):
        """测试分析安全的Python代码"""
        code = "x = 1 + 2\nprint(x)"
        result = code_executor._analyze_python_security(code)
        
        assert result.is_safe is True
        assert len(result.issues) == 0
        assert result.line_count == 2
    
    def test_analyze_python_security_unsafe_code(self, code_executor):
        """测试分析不安全的Python代码"""
        code = "import os\nos.system('rm -rf /')"
        result = code_executor._analyze_python_security(code)
        
        assert result.is_safe is False
        assert len(result.issues) > 0
        assert any("禁止导入模块: os" in issue for issue in result.issues)
    
    def test_analyze_javascript_security(self, code_executor):
        """测试分析JavaScript代码安全性"""
        safe_code = "console.log('hello');"
        unsafe_code = "eval('malicious code');"
        
        safe_result = code_executor._analyze_javascript_security(safe_code)
        unsafe_result = code_executor._analyze_javascript_security(unsafe_code)
        
        assert safe_result.is_safe is True
        assert unsafe_result.is_safe is False
        assert any("eval(" in issue for issue in unsafe_result.issues)
    
    def test_get_execution_history(self, code_executor):
        """测试获取执行历史"""
        # 添加一些模拟历史记录
        result1 = ExecutionResult(
            execution_id="1", language="python", code="print(1)",
            output="1\n", error="", execution_time=0.1,
            timestamp=datetime.now(), success=True, resource_usage={}
        )
        result2 = ExecutionResult(
            execution_id="2", language="python", code="print(2)",
            output="2\n", error="", execution_time=0.1,
            timestamp=datetime.now(), success=True, resource_usage={}
        )
        
        code_executor.execution_history = [result1, result2]
        
        history = code_executor.get_execution_history()
        assert len(history) == 2
        assert history[0]['execution_id'] == "1"
        assert history[1]['execution_id'] == "2"
        
        # 测试限制数量
        limited_history = code_executor.get_execution_history(limit=1)
        assert len(limited_history) == 1
        assert limited_history[0]['execution_id'] == "2"
    
    def test_get_execution_stats(self, code_executor):
        """测试获取执行统计"""
        # 设置一些统计数据
        code_executor.stats = {
            'total_executions': 10,
            'successful_executions': 8,
            'failed_executions': 2,
            'blocked_executions': 1,
            'total_execution_time': 5.0,
            'average_execution_time': 0.5
        }
        
        stats = code_executor.get_execution_stats()
        assert stats['total_executions'] == 10
        assert stats['success_rate'] == 80.0
        assert stats['failure_rate'] == 20.0
        assert stats['block_rate'] == 10.0
        assert 'supported_languages' in stats
        assert 'security_policy' in stats
    
    def test_update_security_policy(self, code_executor):
        """测试更新安全策略"""
        new_policy = SecurityPolicy(
            max_execution_time=60,
            max_memory_mb=256
        )
        
        result = code_executor.update_security_policy(new_policy)
        assert result is True
        assert code_executor.security_policy.max_execution_time == 60
        assert code_executor.security_policy.max_memory_mb == 256
    
    def test_execution_callback_management(self, code_executor):
        """测试执行回调函数管理"""
        callback1 = Mock()
        callback2 = Mock()
        
        # 添加回调函数
        assert code_executor.add_execution_callback(callback1) is True
        assert code_executor.add_execution_callback(callback2) is True
        assert len(code_executor.execution_callbacks) == 2
        
        # 重复添加
        assert code_executor.add_execution_callback(callback1) is False
        assert len(code_executor.execution_callbacks) == 2
        
        # 移除回调函数
        assert code_executor.remove_execution_callback(callback1) is True
        assert len(code_executor.execution_callbacks) == 1
        
        # 移除不存在的回调函数
        assert code_executor.remove_execution_callback(callback1) is False
    
    def test_clear_execution_history(self, code_executor):
        """测试清空执行历史"""
        # 添加一些历史记录
        result = ExecutionResult(
            execution_id="1", language="python", code="print(1)",
            output="1\n", error="", execution_time=0.1,
            timestamp=datetime.now(), success=True, resource_usage={}
        )
        code_executor.execution_history = [result]
        
        assert len(code_executor.execution_history) == 1
        assert code_executor.clear_execution_history() is True
        assert len(code_executor.execution_history) == 0
    
    def test_get_language_support(self, code_executor):
        """测试获取语言支持信息"""
        support_info = code_executor.get_language_support()
        
        assert 'python' in support_info
        assert 'javascript' in support_info
        assert 'bash' in support_info
        
        assert support_info['python']['supported'] is True
        assert 'version' in support_info['python']
        assert 'features' in support_info['python']
        assert 'security_level' in support_info['python']
    
    @pytest.mark.asyncio
    async def test_validate_code(self, code_executor):
        """测试代码验证"""
        safe_code = "x = 1 + 2"
        unsafe_code = "import os"
        
        safe_result = await code_executor.validate_code(safe_code, 'python')
        unsafe_result = await code_executor.validate_code(unsafe_code, 'python')
        
        assert safe_result.is_safe is True
        assert unsafe_result.is_safe is False
    
    @pytest.mark.asyncio
    async def test_cleanup(self, code_executor):
        """测试清理资源"""
        # 添加一些数据
        callback = Mock()
        code_executor.add_execution_callback(callback)
        result = ExecutionResult(
            execution_id="1", language="python", code="print(1)",
            output="1\n", error="", execution_time=0.1,
            timestamp=datetime.now(), success=True, resource_usage={}
        )
        code_executor.execution_history = [result]
        
        await code_executor.cleanup()
        
        assert len(code_executor.execution_history) == 0
        assert len(code_executor.execution_callbacks) == 0
