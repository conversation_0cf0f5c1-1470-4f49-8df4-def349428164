# 第五阶段 API 文档

## 概述

本文档描述了HyAIAgent第五阶段开发完成的核心API接口和功能模块。第五阶段主要包含高级AI功能、用户界面组件、监控系统和工具模块。

## 版本信息

- **版本**: 1.0.0-stage5
- **发布日期**: 2025-07-30
- **兼容性**: Python 3.8+

## 核心模块

### 1. 配置管理 (ConfigManager)

#### 类: `ConfigManager`

配置管理器提供应用程序配置的加载、保存和管理功能。

**初始化**
```python
from core.config_manager import ConfigManager

config_manager = ConfigManager()
```

**主要方法**

##### `get_default_config() -> Dict[str, Any]`
获取默认配置信息。

**返回值**:
- `Dict[str, Any]`: 包含所有默认配置的字典

**示例**:
```python
config = config_manager.get_default_config()
print(config['ai_providers'])
```

##### `get(key: str, default: Any = None) -> Any`
获取指定配置项的值。

**参数**:
- `key`: 配置键名，支持点分隔的嵌套键
- `default`: 默认值，当键不存在时返回

**返回值**:
- `Any`: 配置值或默认值

**示例**:
```python
api_key = config_manager.get("ai_providers.openai.api_key")
```

##### `set(key: str, value: Any) -> bool`
设置配置项的值。

**参数**:
- `key`: 配置键名
- `value`: 要设置的值

**返回值**:
- `bool`: 设置成功返回True，失败返回False

**示例**:
```python
success = config_manager.set("custom.setting", "value")
```

### 2. 键值存储 (KVStore)

#### 类: `KVStore`

基于TinyDB的键值存储系统，提供数据持久化功能。

**初始化**
```python
from core.kv_store import KVStore

kv_store = KVStore(db_path="data/my_store.json")
```

**主要方法**

##### `set(key: str, value: Any, ttl: Optional[int] = None) -> bool`
设置键值对。

**参数**:
- `key`: 键名
- `value`: 值，支持任意可序列化的Python对象
- `ttl`: 生存时间（秒），可选

**返回值**:
- `bool`: 设置成功返回True

**示例**:
```python
success = kv_store.set("user:123", {"name": "张三", "age": 25})
```

##### `get(key: str, default: Any = None) -> Any`
获取键对应的值。

**参数**:
- `key`: 键名
- `default`: 默认值

**返回值**:
- `Any`: 键对应的值或默认值

**示例**:
```python
user_data = kv_store.get("user:123")
```

##### `exists(key: str) -> bool`
检查键是否存在。

**参数**:
- `key`: 键名

**返回值**:
- `bool`: 存在返回True，不存在返回False

##### `delete(key: str) -> bool`
删除指定的键。

**参数**:
- `key`: 键名

**返回值**:
- `bool`: 删除成功返回True

##### `close() -> None`
关闭数据库连接。

**示例**:
```python
await kv_store.close()
```

### 3. 性能监控 (PerformanceMonitor)

#### 类: `PerformanceMonitor`

系统性能监控器，提供CPU、内存、磁盘等系统指标的监控功能。

**初始化**
```python
from monitoring.performance_monitor import PerformanceMonitor

monitor = PerformanceMonitor()
```

**主要方法**

##### `async get_current_metrics() -> Optional[SystemMetrics]`
获取当前系统指标。

**返回值**:
- `SystemMetrics`: 系统指标对象，包含CPU、内存、磁盘使用率等信息

**示例**:
```python
metrics = await monitor.get_current_metrics()
print(f"CPU使用率: {metrics.cpu_usage}%")
print(f"内存使用率: {metrics.memory_usage}%")
```

##### `get_performance_stats() -> Dict[str, Any]`
获取性能统计信息。

**返回值**:
- `Dict[str, Any]`: 包含平均值、峰值等统计信息的字典

**示例**:
```python
stats = monitor.get_performance_stats()
print(f"平均CPU使用率: {stats['avg_cpu_usage']}")
```

##### `start_monitoring() -> None`
启动性能监控。

##### `stop_monitoring() -> None`
停止性能监控。

## 高级功能模块

### 1. 推理引擎 (ReasoningEngine)

#### 类: `ReasoningEngine`

多步推理引擎，提供复杂问题的逻辑推理功能。

**初始化**
```python
from advanced.reasoning_engine import ReasoningEngine
from core.ai_client import SimpleAIClient
from core.prompt_manager import PromptManager

reasoning_engine = ReasoningEngine(
    ai_client=ai_client,
    prompt_manager=prompt_manager,
    kv_store=kv_store
)
```

**主要方法**

##### `async multi_step_reasoning(problem: str) -> ReasoningResult`
执行多步推理。

**参数**:
- `problem`: 要解决的问题描述

**返回值**:
- `ReasoningResult`: 推理结果，包含推理链和置信度

### 2. 知识图谱 (KnowledgeGraph)

#### 类: `KnowledgeGraph`

知识图谱管理系统，提供知识三元组的存储和查询功能。

**初始化**
```python
from advanced.knowledge_graph import KnowledgeGraph

knowledge_graph = KnowledgeGraph(
    ai_client=ai_client,
    prompt_manager=prompt_manager,
    kv_store=kv_store
)
```

**主要方法**

##### `async add_knowledge(subject: str, predicate: str, object: str) -> bool`
添加知识三元组。

**参数**:
- `subject`: 主语
- `predicate`: 谓语
- `object`: 宾语

**返回值**:
- `bool`: 添加成功返回True

##### `async query_knowledge(query: str) -> KnowledgeQueryResult`
查询知识。

**参数**:
- `query`: 查询字符串

**返回值**:
- `KnowledgeQueryResult`: 查询结果

### 3. 学习系统 (LearningSystem)

#### 类: `LearningSystem`

自适应学习系统，从用户交互中学习和改进。

**初始化**
```python
from advanced.learning_system import LearningSystem

learning_system = LearningSystem(
    ai_client=ai_client,
    prompt_manager=prompt_manager,
    kv_store=kv_store
)
```

**主要方法**

##### `async learn_from_interaction(interaction: Dict[str, Any]) -> bool`
从用户交互中学习。

**参数**:
- `interaction`: 交互数据字典

**返回值**:
- `bool`: 学习成功返回True

##### `get_user_preferences(user_id: str) -> Optional[Dict[str, Any]]`
获取用户偏好。

**参数**:
- `user_id`: 用户ID

**返回值**:
- `Dict[str, Any]`: 用户偏好数据

## 工具模块

### 1. 图表生成器 (ChartGenerator)

#### 类: `ChartGenerator`

数据可视化工具，支持多种图表类型的生成。

**初始化**
```python
from tools.chart_generator import ChartGenerator

chart_generator = ChartGenerator()
```

**主要方法**

##### `async create_chart(config: ChartConfig, data: ChartData) -> Dict[str, Any]`
创建图表。

**参数**:
- `config`: 图表配置
- `data`: 图表数据

**返回值**:
- `Dict[str, Any]`: 图表创建结果

## 错误处理

所有API方法都包含适当的错误处理机制：

- **配置错误**: 当配置文件不存在或格式错误时抛出`ConfigError`
- **存储错误**: 当数据库操作失败时抛出`StorageError`
- **网络错误**: 当AI服务调用失败时抛出`NetworkError`
- **验证错误**: 当输入参数无效时抛出`ValidationError`

## 使用示例

### 完整示例：创建一个简单的AI助手

```python
import asyncio
from core.config_manager import ConfigManager
from core.kv_store import KVStore
from core.ai_client import SimpleAIClient
from monitoring.performance_monitor import PerformanceMonitor

async def main():
    # 初始化组件
    config_manager = ConfigManager()
    kv_store = KVStore()
    monitor = PerformanceMonitor()
    
    # 启动监控
    monitor.start_monitoring()
    
    try:
        # 获取系统指标
        metrics = await monitor.get_current_metrics()
        print(f"当前系统状态: CPU {metrics.cpu_usage}%, 内存 {metrics.memory_usage}%")
        
        # 存储一些数据
        await kv_store.set("session:start", {"timestamp": time.time()})
        
        # 获取配置
        config = config_manager.get_default_config()
        print(f"应用名称: {config['application']['name']}")
        
    finally:
        # 清理资源
        monitor.stop_monitoring()
        await kv_store.close()

if __name__ == "__main__":
    asyncio.run(main())
```

## 性能指标

### 基准测试结果

基于第五阶段功能测试的性能基准：

- **配置管理**: 平均响应时间 < 0.01s
- **KV存储**: 单次操作 < 0.02s，批量操作 < 2.0s (100条记录)
- **性能监控**: 指标收集 < 0.1s
- **内存使用**: 基础功能 < 50MB

### 扩展性

- **并发支持**: 支持多线程并发访问
- **数据量**: KV存储支持数万条记录
- **监控频率**: 支持秒级监控间隔

## 更新日志

### v1.0.0-stage5 (2025-07-30)
- 新增高级AI功能模块
- 完善监控系统
- 优化性能和稳定性
- 增加完整的API文档

## 支持与反馈

如有问题或建议，请联系开发团队或提交Issue。
