"""
知识图谱测试模块

测试KnowledgeGraph类的各项功能：
- 知识三元组的添加和管理
- 知识查询和检索
- 关系推理和推断
- 置信度更新机制
- 数据持久化和加载

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import pytest
import asyncio
import logging
from unittest.mock import Mock, AsyncMock
from datetime import datetime

# 导入被测试的模块
from advanced.knowledge_graph import (
    KnowledgeGraph, KnowledgeTriple, KnowledgeResults, InferredRelation,
    RelationType, ConfidenceLevel
)


class TestKnowledgeGraph:
    """知识图谱测试类"""
    
    @pytest.fixture
    def mock_ai_client(self):
        """模拟AI客户端"""
        client = Mock()
        client.chat = AsyncMock()
        return client
    
    @pytest.fixture
    def mock_prompt_manager(self):
        """模拟提示词管理器"""
        manager = Mock()
        return manager
    
    @pytest.fixture
    def mock_kv_store(self):
        """模拟键值存储"""
        store = Mock()
        store.get = AsyncMock()
        store.set = AsyncMock()
        return store
    
    @pytest.fixture
    def knowledge_graph(self, mock_ai_client, mock_prompt_manager, mock_kv_store):
        """创建知识图谱实例"""
        logger = logging.getLogger("test_knowledge_graph")
        return KnowledgeGraph(
            ai_client=mock_ai_client,
            prompt_manager=mock_prompt_manager,
            kv_store=mock_kv_store,
            logger=logger
        )
    
    def test_knowledge_graph_initialization(self, knowledge_graph):
        """测试知识图谱初始化"""
        assert knowledge_graph.ai_client is not None
        assert knowledge_graph.prompt_manager is not None
        assert knowledge_graph.kv_store is not None
        assert knowledge_graph.logger is not None
        assert len(knowledge_graph.triples) == 0
        assert knowledge_graph.stats['total_triples'] == 0
        assert len(knowledge_graph.inference_rules) > 0
    
    def test_knowledge_triple_creation(self):
        """测试知识三元组创建"""
        triple = KnowledgeTriple(
            subject="苏格拉底",
            predicate="是",
            object="哲学家",
            confidence=0.9,
            relation_type=RelationType.IS_A
        )
        
        assert triple.subject == "苏格拉底"
        assert triple.predicate == "是"
        assert triple.object == "哲学家"
        assert triple.confidence == 0.9
        assert triple.relation_type == RelationType.IS_A
        assert triple.triple_id.startswith("triple_")
        assert isinstance(triple.created_at, datetime)
    
    def test_knowledge_triple_serialization(self):
        """测试知识三元组序列化"""
        triple = KnowledgeTriple(
            subject="Python",
            predicate="是",
            object="编程语言",
            confidence=0.95
        )
        
        # 测试转换为字典
        triple_dict = triple.to_dict()
        assert triple_dict['subject'] == "Python"
        assert triple_dict['predicate'] == "是"
        assert triple_dict['object'] == "编程语言"
        assert triple_dict['confidence'] == 0.95
        
        # 测试从字典创建
        restored_triple = KnowledgeTriple.from_dict(triple_dict)
        assert restored_triple.subject == triple.subject
        assert restored_triple.predicate == triple.predicate
        assert restored_triple.object == triple.object
        assert restored_triple.confidence == triple.confidence
    
    @pytest.mark.asyncio
    async def test_add_knowledge_success(self, knowledge_graph):
        """测试成功添加知识"""
        result = await knowledge_graph.add_knowledge(
            subject="北京",
            predicate="是",
            object_="中国的首都",
            confidence=0.95,
            relation_type=RelationType.IS_A
        )
        
        assert result is True
        assert knowledge_graph.stats['total_triples'] == 1
        assert "北京" in knowledge_graph.subject_index
        assert "中国的首都" in knowledge_graph.object_index
        assert "是" in knowledge_graph.predicate_index
    
    @pytest.mark.asyncio
    async def test_add_duplicate_knowledge(self, knowledge_graph):
        """测试添加重复知识"""
        # 第一次添加
        await knowledge_graph.add_knowledge("猫", "是", "动物", confidence=0.8)
        
        # 第二次添加相同的知识，但置信度更高
        await knowledge_graph.add_knowledge("猫", "是", "动物", confidence=0.9)
        
        # 应该只有一个三元组，但置信度更新了
        assert knowledge_graph.stats['total_triples'] == 1
        triple_key = knowledge_graph._get_triple_key("猫", "是", "动物")
        triple = knowledge_graph.triples[triple_key]
        assert triple.confidence == 0.9  # 取最大值
    
    @pytest.mark.asyncio
    async def test_query_knowledge_direct_match(self, knowledge_graph, mock_ai_client):
        """测试直接匹配查询"""
        # 添加一些测试数据
        await knowledge_graph.add_knowledge("狗", "是", "动物", confidence=0.9)
        await knowledge_graph.add_knowledge("狗", "有", "四条腿", confidence=0.8)
        await knowledge_graph.add_knowledge("猫", "是", "动物", confidence=0.85)
        
        # 模拟AI响应
        mock_ai_client.chat.return_value = "狗, 动物"
        
        # 查询
        results = await knowledge_graph.query_knowledge("狗", include_inferred=False)
        
        assert results.query == "狗"
        assert len(results.triples) >= 2  # 至少匹配到两个三元组
        assert results.total_results >= 2
        assert results.execution_time >= 0  # 执行时间可能为0
        
        # 检查置信度分数
        assert 'direct_match_avg' in results.confidence_scores
        assert 'overall_confidence' in results.confidence_scores
    
    @pytest.mark.asyncio
    async def test_query_knowledge_with_inference(self, knowledge_graph, mock_ai_client):
        """测试包含推理的查询"""
        # 添加测试数据
        await knowledge_graph.add_knowledge("A", "关系", "B", confidence=0.9)
        await knowledge_graph.add_knowledge("B", "关系", "C", confidence=0.8)
        await knowledge_graph.add_knowledge("X", "类似", "Y", confidence=0.7)
        
        mock_ai_client.chat.return_value = "A, B"
        
        # 查询并包含推理
        results = await knowledge_graph.query_knowledge("A", include_inferred=True)
        
        assert results.query == "A"
        assert len(results.triples) >= 1
        # 可能有推断关系
        assert results.total_results >= len(results.triples)
    
    @pytest.mark.asyncio
    async def test_update_confidence(self, knowledge_graph):
        """测试更新置信度"""
        # 添加知识
        await knowledge_graph.add_knowledge("地球", "是", "行星", confidence=0.8)
        
        # 更新置信度
        result = await knowledge_graph.update_confidence(
            "地球", "是", "行星", 
            new_confidence=0.95,
            evidence=["天文学证据"]
        )
        
        assert result is True
        
        # 检查置信度是否更新
        triple_key = knowledge_graph._get_triple_key("地球", "是", "行星")
        triple = knowledge_graph.triples[triple_key]
        assert triple.confidence == (0.8 + 0.95) / 2  # 加权平均
        assert "天文学证据" in triple.evidence
    
    @pytest.mark.asyncio
    async def test_infer_relations_transitivity(self, knowledge_graph):
        """测试传递性推理"""
        # 添加传递性关系的数据，使用非对称关系类型
        await knowledge_graph.add_knowledge("A", "包含", "B", confidence=0.9, relation_type=RelationType.HAS_A)
        await knowledge_graph.add_knowledge("B", "包含", "C", confidence=0.8, relation_type=RelationType.HAS_A)

        # 获取所有三元组进行推理
        all_triples = list(knowledge_graph.triples.values())
        inferred = await knowledge_graph._infer_relations(all_triples, 10)

        # 应该推断出 A 包含 C
        assert len(inferred) > 0
        found_transitivity = False
        for relation in inferred:
            if (relation.inferred_triple.subject == "A" and
                relation.inferred_triple.object == "C" and
                relation.inference_rule == "transitivity"):
                found_transitivity = True
                break

        assert found_transitivity
    
    @pytest.mark.asyncio
    async def test_infer_relations_symmetry(self, knowledge_graph):
        """测试对称性推理"""
        # 添加对称关系的数据
        await knowledge_graph.add_knowledge("A", "similar_to", "B", confidence=0.8)
        
        # 进行推理
        inferred = await knowledge_graph.infer_relations("A", max_depth=1)
        
        # 应该推断出 B similar_to A
        found_symmetry = False
        for relation in inferred:
            if (relation.inferred_triple.subject == "B" and 
                relation.inferred_triple.object == "A" and
                relation.inference_rule == "symmetry"):
                found_symmetry = True
                break
        
        assert found_symmetry
    
    def test_get_statistics(self, knowledge_graph):
        """测试获取统计信息"""
        stats = knowledge_graph.get_statistics()
        
        assert 'total_triples' in stats
        assert 'total_entities' in stats
        assert 'total_predicates' in stats
        assert 'total_queries' in stats
        assert 'total_inferences' in stats
        assert 'avg_query_time' in stats
        assert 'cache_hit_rate' in stats
        assert 'inference_cache_size' in stats
        
        # 初始状态的检查
        assert stats['total_triples'] == 0
        assert stats['total_entities'] == 0
        assert stats['total_predicates'] == 0
    
    def test_clear_cache(self, knowledge_graph):
        """测试清空缓存"""
        # 添加一些缓存数据
        knowledge_graph.inference_cache['test_key'] = []
        
        assert len(knowledge_graph.inference_cache) > 0
        
        knowledge_graph.clear_cache()
        
        assert len(knowledge_graph.inference_cache) == 0
    
    @pytest.mark.asyncio
    async def test_storage_operations(self, knowledge_graph, mock_kv_store):
        """测试存储操作"""
        # 添加一些数据
        await knowledge_graph.add_knowledge("测试", "是", "数据", confidence=0.9)
        
        # 测试保存（在add_knowledge中自动调用）
        mock_kv_store.set.assert_called()
        
        # 测试加载
        mock_kv_store.get.return_value = '{"test|is|data": {"subject": "test", "predicate": "is", "object": "data", "confidence": 0.9, "relation_type": "related_to", "source": "user_input", "evidence": [], "created_at": "2025-07-30T12:00:00", "updated_at": "2025-07-30T12:00:00", "triple_id": "triple_123"}}'
        
        result = await knowledge_graph.load_from_storage()
        assert result is True or result is False  # 取决于mock的具体实现
    
    def test_inference_rules_initialization(self, knowledge_graph):
        """测试推理规则初始化"""
        rules = knowledge_graph.inference_rules
        
        assert 'transitivity' in rules
        assert 'symmetry' in rules
        assert 'inverse' in rules
        assert 'hierarchy' in rules
        assert 'causality' in rules
        
        # 检查规则描述
        assert "关系" in rules['transitivity']  # 简化断言
        assert "关系" in rules['symmetry']
