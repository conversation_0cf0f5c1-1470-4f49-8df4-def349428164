"""
UsageTracker 测试模块

测试使用跟踪器的各项功能，包括事件跟踪、会话管理、使用统计等。
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
from collections import deque
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from HyAIAgent.monitoring.usage_tracker import (
    UsageTracker, UsageEvent, UserSession, FeatureUsage
)


class TestUsageEvent:
    """UsageEvent 数据模型测试"""
    
    def test_usage_event_creation(self):
        """测试使用事件创建"""
        timestamp = datetime.now()
        metadata = {'key': 'value', 'count': 10}
        
        event = UsageEvent(
            event_id="event_001",
            user_id="user_123",
            session_id="session_456",
            event_type="action",
            feature_name="test_feature",
            timestamp=timestamp,
            duration=1.5,
            metadata=metadata,
            success=True
        )
        
        assert event.event_id == "event_001"
        assert event.user_id == "user_123"
        assert event.session_id == "session_456"
        assert event.event_type == "action"
        assert event.feature_name == "test_feature"
        assert event.timestamp == timestamp
        assert event.duration == 1.5
        assert event.metadata == metadata
        assert event.success is True
    
    def test_usage_event_to_dict(self):
        """测试使用事件转换为字典"""
        timestamp = datetime.now()
        event = UsageEvent(
            event_id="event_001",
            user_id="user_123",
            session_id="session_456",
            event_type="action",
            feature_name="test_feature",
            timestamp=timestamp
        )
        
        data = event.to_dict()
        assert isinstance(data, dict)
        assert data['event_id'] == "event_001"
        assert data['timestamp'] == timestamp.isoformat()
        assert data['metadata'] == {}  # 默认空字典


class TestUserSession:
    """UserSession 数据模型测试"""
    
    def test_user_session_creation(self):
        """测试用户会话创建"""
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=30)
        
        session = UserSession(
            session_id="session_123",
            user_id="user_456",
            start_time=start_time,
            end_time=end_time,
            total_events=10,
            total_duration=1800.0
        )
        
        assert session.session_id == "session_123"
        assert session.user_id == "user_456"
        assert session.start_time == start_time
        assert session.end_time == end_time
        assert session.total_events == 10
        assert session.total_duration == 1800.0
        assert isinstance(session.features_used, set)
    
    def test_user_session_to_dict(self):
        """测试用户会话转换为字典"""
        start_time = datetime.now()
        session = UserSession(
            session_id="session_123",
            user_id="user_456",
            start_time=start_time
        )
        session.features_used.add("feature1")
        session.features_used.add("feature2")
        
        data = session.to_dict()
        assert isinstance(data, dict)
        assert data['session_id'] == "session_123"
        assert data['start_time'] == start_time.isoformat()
        assert isinstance(data['features_used'], list)
        assert len(data['features_used']) == 2


class TestFeatureUsage:
    """FeatureUsage 数据模型测试"""
    
    def test_feature_usage_creation(self):
        """测试功能使用统计创建"""
        last_used = datetime.now()
        
        usage = FeatureUsage(
            feature_name="test_feature",
            total_uses=100,
            unique_users=25,
            avg_duration=2.5,
            success_rate=95.0,
            peak_usage_time="14:00",
            last_used=last_used
        )
        
        assert usage.feature_name == "test_feature"
        assert usage.total_uses == 100
        assert usage.unique_users == 25
        assert usage.avg_duration == 2.5
        assert usage.success_rate == 95.0
        assert usage.peak_usage_time == "14:00"
        assert usage.last_used == last_used
    
    def test_feature_usage_to_dict(self):
        """测试功能使用统计转换为字典"""
        last_used = datetime.now()
        usage = FeatureUsage(
            feature_name="test_feature",
            total_uses=100,
            unique_users=25,
            avg_duration=2.5,
            success_rate=95.0,
            peak_usage_time="14:00",
            last_used=last_used
        )
        
        data = usage.to_dict()
        assert isinstance(data, dict)
        assert data['feature_name'] == "test_feature"
        assert data['last_used'] == last_used.isoformat()


class TestUsageTracker:
    """UsageTracker 主类测试"""
    
    @pytest.fixture
    def mock_config_manager(self):
        """模拟配置管理器"""
        return Mock()
    
    @pytest.fixture
    def mock_kv_store(self):
        """模拟键值存储"""
        return AsyncMock()
    
    @pytest.fixture
    def usage_tracker(self, mock_config_manager, mock_kv_store):
        """创建使用跟踪器实例"""
        return UsageTracker(
            config_manager=mock_config_manager,
            kv_store=mock_kv_store,
            max_events_history=1000,
            session_timeout=1800
        )
    
    def test_usage_tracker_initialization(self, usage_tracker):
        """测试使用跟踪器初始化"""
        assert usage_tracker.max_events_history == 1000
        assert usage_tracker.session_timeout == 1800
        assert isinstance(usage_tracker.events_history, deque)
        assert len(usage_tracker.events_history) == 0
        assert len(usage_tracker.active_sessions) == 0
        assert len(usage_tracker.feature_stats) == 0
        assert 'total_events' in usage_tracker.stats
        assert 'total_sessions' in usage_tracker.stats
    
    @pytest.mark.asyncio
    async def test_track_event_basic(self, usage_tracker):
        """测试基础事件跟踪"""
        event_id = await usage_tracker.track_event(
            user_id="user_123",
            session_id="session_456",
            event_type="action",
            feature_name="test_feature",
            duration=1.5,
            metadata={'key': 'value'},
            success=True
        )
        
        assert event_id != ""
        assert len(usage_tracker.events_history) == 1
        assert usage_tracker.stats['total_events'] == 1
        
        # 检查事件内容
        event = usage_tracker.events_history[0]
        assert event.user_id == "user_123"
        assert event.session_id == "session_456"
        assert event.feature_name == "test_feature"
        assert event.duration == 1.5
        assert event.success is True
    
    @pytest.mark.asyncio
    async def test_track_event_auto_session_creation(self, usage_tracker):
        """测试事件跟踪时自动创建会话"""
        await usage_tracker.track_event(
            user_id="user_123",
            session_id="new_session",
            event_type="action",
            feature_name="test_feature"
        )
        
        # 检查会话是否自动创建
        assert "new_session" in usage_tracker.active_sessions
        session = usage_tracker.active_sessions["new_session"]
        assert session.user_id == "user_123"
        assert session.total_events == 1
        assert "test_feature" in session.features_used
    
    @pytest.mark.asyncio
    async def test_start_session(self, usage_tracker):
        """测试开始会话"""
        result = await usage_tracker.start_session("user_123", "session_456")
        
        assert result is True
        assert "session_456" in usage_tracker.active_sessions
        assert usage_tracker.stats['total_sessions'] == 1
        assert usage_tracker.stats['active_sessions_count'] == 1
        assert "user_123" in usage_tracker.stats['total_users']
        
        session = usage_tracker.active_sessions["session_456"]
        assert session.user_id == "user_123"
        assert session.session_id == "session_456"
    
    @pytest.mark.asyncio
    async def test_end_session(self, usage_tracker):
        """测试结束会话"""
        # 先开始会话
        await usage_tracker.start_session("user_123", "session_456")
        assert len(usage_tracker.active_sessions) == 1
        
        # 结束会话
        result = await usage_tracker.end_session("session_456")
        
        assert result is True
        assert len(usage_tracker.active_sessions) == 0
        assert usage_tracker.stats['active_sessions_count'] == 0
        
        # 测试结束不存在的会话
        result = await usage_tracker.end_session("nonexistent_session")
        assert result is False
    
    def test_update_feature_stats(self, usage_tracker):
        """测试更新功能统计"""
        timestamp = datetime.now()
        event = UsageEvent(
            event_id="event_001",
            user_id="user_123",
            session_id="session_456",
            event_type="action",
            feature_name="test_feature",
            timestamp=timestamp,
            duration=2.0,
            success=True
        )
        
        usage_tracker._update_feature_stats(event)
        
        stats = usage_tracker.feature_stats["test_feature"]
        assert stats['total_uses'] == 1
        assert "user_123" in stats['unique_users']
        assert 2.0 in stats['durations']
        assert stats['success_count'] == 1
        assert stats['error_count'] == 0
        assert stats['last_used'] == timestamp
        
        # 测试按小时统计
        hour_key = timestamp.strftime('%H')
        assert stats['hourly_usage'][hour_key] == 1
    
    def test_get_usage_stats(self, usage_tracker):
        """测试获取使用统计"""
        # 添加测试数据
        usage_tracker.stats['total_events'] = 100
        usage_tracker.stats['total_sessions'] = 10
        usage_tracker.stats['total_users'].add("user1")
        usage_tracker.stats['total_users'].add("user2")
        
        stats = usage_tracker.get_usage_stats()
        
        assert isinstance(stats, dict)
        assert stats['total_events'] == 100
        assert stats['total_sessions'] == 10
        assert stats['total_unique_users'] == 2
        assert 'uptime_seconds' in stats
        assert 'uptime_formatted' in stats
        assert 'events_per_minute' in stats
        assert 'features_count' in stats
    
    def test_get_feature_usage_single(self, usage_tracker):
        """测试获取单个功能使用统计"""
        # 添加测试数据
        timestamp = datetime.now()
        usage_tracker.feature_stats["test_feature"] = {
            'total_uses': 50,
            'unique_users': {'user1', 'user2', 'user3'},
            'durations': [1.0, 2.0, 1.5],
            'success_count': 45,
            'error_count': 5,
            'hourly_usage': {'14': 20, '15': 30},
            'first_used': timestamp - timedelta(days=1),
            'last_used': timestamp
        }
        
        usage = usage_tracker.get_feature_usage("test_feature")
        
        assert isinstance(usage, dict)
        assert usage['feature_name'] == "test_feature"
        assert usage['total_uses'] == 50
        assert usage['unique_users'] == 3
        assert usage['avg_duration'] == 1.5  # (1.0 + 2.0 + 1.5) / 3
        assert usage['success_rate'] == 90.0  # 45 / 50 * 100
        assert usage['peak_usage_hour'] == "15:00"
    
    def test_get_feature_usage_all(self, usage_tracker):
        """测试获取所有功能使用统计"""
        # 添加测试数据
        timestamp = datetime.now()
        usage_tracker.feature_stats["feature1"] = {
            'total_uses': 10,
            'unique_users': {'user1'},
            'durations': [1.0],
            'success_count': 10,
            'error_count': 0,
            'hourly_usage': {'14': 10},
            'first_used': timestamp,
            'last_used': timestamp
        }
        usage_tracker.feature_stats["feature2"] = {
            'total_uses': 20,
            'unique_users': {'user1', 'user2'},
            'durations': [2.0, 3.0],
            'success_count': 18,
            'error_count': 2,
            'hourly_usage': {'15': 20},
            'first_used': timestamp,
            'last_used': timestamp
        }
        
        usage = usage_tracker.get_feature_usage()
        
        assert isinstance(usage, dict)
        assert len(usage) == 2
        assert 'feature1' in usage
        assert 'feature2' in usage
        assert usage['feature1']['total_uses'] == 10
        assert usage['feature2']['total_uses'] == 20
    
    def test_get_user_activity(self, usage_tracker):
        """测试获取用户活动统计"""
        # 添加测试事件
        timestamp = datetime.now()
        events = [
            UsageEvent("event1", "user_123", "session1", "action", "feature1", timestamp, 1.0, success=True),
            UsageEvent("event2", "user_123", "session1", "action", "feature2", timestamp, 2.0, success=True),
            UsageEvent("event3", "user_123", "session2", "action", "feature1", timestamp, 1.5, success=False),
        ]
        
        for event in events:
            usage_tracker.events_history.append(event)
        
        activity = usage_tracker.get_user_activity("user_123", hours=24)
        
        assert isinstance(activity, dict)
        assert activity['user_id'] == "user_123"
        assert activity['total_events'] == 3
        assert activity['session_count'] == 2
        assert activity['total_duration'] == 4.5  # 1.0 + 2.0 + 1.5
        assert activity['features_used']['feature1'] == 2
        assert activity['features_used']['feature2'] == 1
        assert activity['most_used_feature'] == "feature1"
    
    def test_get_performance_bottlenecks(self, usage_tracker):
        """测试识别性能瓶颈"""
        # 添加性能数据
        timestamp = datetime.now()
        
        # 正常功能
        normal_data = [
            {'timestamp': timestamp, 'duration': 1.0, 'success': True},
            {'timestamp': timestamp, 'duration': 1.1, 'success': True},
            {'timestamp': timestamp, 'duration': 0.9, 'success': True},
        ]
        usage_tracker.performance_data["normal_feature"].extend(normal_data)
        
        # 慢功能
        slow_data = [
            {'timestamp': timestamp, 'duration': 1.0, 'success': True},
            {'timestamp': timestamp, 'duration': 1.2, 'success': True},
            {'timestamp': timestamp, 'duration': 5.0, 'success': True},  # 异常慢
        ]
        usage_tracker.performance_data["slow_feature"].extend(slow_data)
        
        bottlenecks = usage_tracker.get_performance_bottlenecks(threshold_percentile=95.0)
        
        assert isinstance(bottlenecks, list)
        # slow_feature应该被识别为瓶颈
        bottleneck_features = [b['feature_name'] for b in bottlenecks]
        assert "slow_feature" in bottleneck_features
    
    def test_get_usage_patterns(self, usage_tracker):
        """测试分析使用模式"""
        # 添加测试事件
        base_time = datetime.now().replace(hour=14, minute=0, second=0, microsecond=0)
        events = []
        
        for i in range(10):
            event = UsageEvent(
                event_id=f"event_{i}",
                user_id="user_123",
                session_id="session_456",
                event_type="action",
                feature_name="test_feature",
                timestamp=base_time + timedelta(hours=i % 24),
                duration=1.0,
                success=True
            )
            events.append(event)
        
        usage_tracker.events_history.extend(events)
        
        patterns = usage_tracker.get_usage_patterns(hours=168)  # 一周
        
        assert isinstance(patterns, dict)
        assert 'analysis_period_hours' in patterns
        assert 'total_events_analyzed' in patterns
        assert 'hourly_distribution' in patterns
        assert 'daily_distribution' in patterns
        assert 'peak_hours' in patterns
        assert 'peak_days' in patterns
        assert 'feature_usage_patterns' in patterns
    
    @pytest.mark.asyncio
    async def test_cleanup_old_data(self, usage_tracker):
        """测试清理旧数据"""
        # 添加新旧数据
        old_time = datetime.now() - timedelta(days=2)
        new_time = datetime.now()
        
        old_event = UsageEvent("old_event", "user1", "session1", "action", "feature1", old_time)
        new_event = UsageEvent("new_event", "user2", "session2", "action", "feature2", new_time)
        
        usage_tracker.events_history.extend([old_event, new_event])
        
        # 添加旧会话
        old_session = UserSession("old_session", "user1", old_time)
        usage_tracker.active_sessions["old_session"] = old_session
        
        # 清理48小时前的数据
        result = await usage_tracker.cleanup_old_data(older_than_hours=48)
        
        assert isinstance(result, dict)
        assert 'events_cleaned' in result
        assert 'performance_data_cleaned' in result
        assert 'expired_sessions_cleaned' in result
        
        # 检查数据是否正确清理
        assert len(usage_tracker.events_history) == 1
        assert usage_tracker.events_history[0].event_id == "new_event"
    
    @pytest.mark.asyncio
    async def test_cleanup(self, usage_tracker):
        """测试资源清理"""
        # 添加测试数据
        await usage_tracker.start_session("user1", "session1")
        await usage_tracker.track_event("user1", "session1", "action", "feature1")
        
        assert len(usage_tracker.active_sessions) > 0
        assert len(usage_tracker.events_history) > 0
        
        await usage_tracker.cleanup()
        
        assert len(usage_tracker.active_sessions) == 0
        assert len(usage_tracker.events_history) == 0
        assert len(usage_tracker.feature_stats) == 0
        assert len(usage_tracker.performance_data) == 0


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
