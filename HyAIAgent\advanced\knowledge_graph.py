"""
知识图谱框架模块

该模块实现了知识图谱的核心功能，包括：
- 知识三元组的存储和管理
- 知识查询和检索
- 关系推理和推断
- 置信度更新机制
- 知识图谱的可视化支持

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import logging
import time
import json
from datetime import datetime
from typing import Dict, List, Optional, Set, Tuple, Any, Union
from dataclasses import dataclass, field, asdict
from enum import Enum
from collections import defaultdict, deque

from core.ai_client import SimpleAIClient
from core.prompt_manager import PromptManager
from core.kv_store import KVStore


class RelationType(Enum):
    """关系类型枚举"""
    IS_A = "is_a"  # 是一个
    HAS_A = "has_a"  # 拥有
    PART_OF = "part_of"  # 是...的一部分
    RELATED_TO = "related_to"  # 相关于
    CAUSES = "causes"  # 导致
    SIMILAR_TO = "similar_to"  # 类似于
    OPPOSITE_TO = "opposite_to"  # 相反于
    LOCATED_IN = "located_in"  # 位于
    CREATED_BY = "created_by"  # 由...创建
    USED_FOR = "used_for"  # 用于
    CUSTOM = "custom"  # 自定义关系


class ConfidenceLevel(Enum):
    """置信度级别枚举"""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.95


@dataclass
class KnowledgeTriple:
    """知识三元组数据模型"""
    subject: str  # 主体
    predicate: str  # 谓词/关系
    object: str  # 客体
    confidence: float = 0.8  # 置信度
    relation_type: RelationType = RelationType.RELATED_TO  # 关系类型
    source: str = "user_input"  # 知识来源
    evidence: List[str] = field(default_factory=list)  # 支持证据
    created_at: datetime = field(default_factory=datetime.now)  # 创建时间
    updated_at: datetime = field(default_factory=datetime.now)  # 更新时间
    triple_id: str = field(default="")  # 三元组ID
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.triple_id:
            self.triple_id = f"triple_{int(time.time() * 1000)}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['relation_type'] = self.relation_type.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'KnowledgeTriple':
        """从字典创建实例"""
        data = data.copy()
        data['relation_type'] = RelationType(data['relation_type'])
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        return cls(**data)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"({self.subject}) --[{self.predicate}]--> ({self.object}) [confidence: {self.confidence:.2f}]"


@dataclass
class InferredRelation:
    """推断关系数据模型"""
    source_triple: KnowledgeTriple  # 源三元组
    target_triple: KnowledgeTriple  # 目标三元组
    inferred_triple: KnowledgeTriple  # 推断出的三元组
    inference_rule: str  # 推理规则
    confidence: float  # 推理置信度
    reasoning_path: List[str]  # 推理路径
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'source_triple': self.source_triple.to_dict(),
            'target_triple': self.target_triple.to_dict(),
            'inferred_triple': self.inferred_triple.to_dict(),
            'inference_rule': self.inference_rule,
            'confidence': self.confidence,
            'reasoning_path': self.reasoning_path,
            'created_at': self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'InferredRelation':
        """从字典创建实例"""
        return cls(
            source_triple=KnowledgeTriple.from_dict(data['source_triple']),
            target_triple=KnowledgeTriple.from_dict(data['target_triple']),
            inferred_triple=KnowledgeTriple.from_dict(data['inferred_triple']),
            inference_rule=data['inference_rule'],
            confidence=data['confidence'],
            reasoning_path=data['reasoning_path'],
            created_at=datetime.fromisoformat(data['created_at'])
        )


@dataclass
class KnowledgeResults:
    """知识查询结果数据模型"""
    query: str  # 查询语句
    triples: List[KnowledgeTriple]  # 匹配的三元组
    inferred_relations: List[InferredRelation]  # 推断关系
    confidence_scores: Dict[str, float]  # 置信度分数
    execution_time: float  # 执行时间
    total_results: int  # 结果总数
    query_type: str = "general"  # 查询类型
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'query': self.query,
            'triples': [triple.to_dict() for triple in self.triples],
            'inferred_relations': [rel.to_dict() for rel in self.inferred_relations],
            'confidence_scores': self.confidence_scores,
            'execution_time': self.execution_time,
            'total_results': self.total_results,
            'query_type': self.query_type
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'KnowledgeResults':
        """从字典创建实例"""
        return cls(
            query=data['query'],
            triples=[KnowledgeTriple.from_dict(t) for t in data['triples']],
            inferred_relations=[InferredRelation.from_dict(r) for r in data['inferred_relations']],
            confidence_scores=data['confidence_scores'],
            execution_time=data['execution_time'],
            total_results=data['total_results'],
            query_type=data.get('query_type', 'general')
        )


class KnowledgeGraph:
    """知识图谱核心类
    
    提供知识三元组的存储、查询、推理和管理功能
    """
    
    def __init__(self, ai_client: SimpleAIClient, prompt_manager: PromptManager = None,
                 kv_store: KVStore = None, logger: logging.Logger = None):
        """初始化知识图谱
        
        Args:
            ai_client: AI客户端，用于知识推理
            prompt_manager: 提示词管理器
            kv_store: 键值存储，用于持久化
            logger: 日志记录器
        """
        self.ai_client = ai_client
        self.prompt_manager = prompt_manager or PromptManager()
        self.kv_store = kv_store
        self.logger = logger or logging.getLogger(__name__)
        
        # 知识存储
        self.triples: Dict[str, KnowledgeTriple] = {}  # 三元组存储
        self.subject_index: Dict[str, Set[str]] = defaultdict(set)  # 主体索引
        self.object_index: Dict[str, Set[str]] = defaultdict(set)  # 客体索引
        self.predicate_index: Dict[str, Set[str]] = defaultdict(set)  # 谓词索引
        
        # 推理缓存
        self.inference_cache: Dict[str, List[InferredRelation]] = {}
        
        # 统计信息
        self.stats = {
            'total_triples': 0,
            'total_queries': 0,
            'total_inferences': 0,
            'avg_query_time': 0.0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # 推理规则
        self.inference_rules = self._initialize_inference_rules()
        
        self.logger.info("知识图谱初始化完成")

    def _initialize_inference_rules(self) -> Dict[str, str]:
        """初始化推理规则"""
        return {
            'transitivity': "如果A关系B，B关系C，则A关系C",
            'symmetry': "如果A关系B，则B关系A",
            'inverse': "如果A是B的一部分，则B包含A",
            'hierarchy': "如果A是B的一种，B是C的一种，则A是C的一种",
            'causality': "如果A导致B，B导致C，则A间接导致C"
        }

    async def add_knowledge(self, subject: str, predicate: str, object_: str,
                          confidence: float = 0.8, relation_type: RelationType = RelationType.RELATED_TO,
                          source: str = "user_input", evidence: List[str] = None) -> bool:
        """添加知识三元组

        Args:
            subject: 主体
            predicate: 谓词/关系
            object_: 客体
            confidence: 置信度
            relation_type: 关系类型
            source: 知识来源
            evidence: 支持证据

        Returns:
            bool: 是否添加成功
        """
        try:
            # 创建三元组
            triple = KnowledgeTriple(
                subject=subject.strip(),
                predicate=predicate.strip(),
                object=object_.strip(),
                confidence=confidence,
                relation_type=relation_type,
                source=source,
                evidence=evidence or []
            )

            # 检查是否已存在相同的三元组
            existing_key = self._get_triple_key(subject, predicate, object_)
            if existing_key in self.triples:
                # 更新置信度和证据
                existing_triple = self.triples[existing_key]
                existing_triple.confidence = max(existing_triple.confidence, confidence)
                existing_triple.evidence.extend(evidence or [])
                existing_triple.updated_at = datetime.now()
                self.logger.info(f"更新已存在的三元组: {existing_triple}")
            else:
                # 添加新三元组
                triple_key = self._get_triple_key(triple.subject, triple.predicate, triple.object)
                self.triples[triple_key] = triple

                # 更新索引
                self.subject_index[triple.subject].add(triple_key)
                self.object_index[triple.object].add(triple_key)
                self.predicate_index[triple.predicate].add(triple_key)

                self.stats['total_triples'] += 1
                self.logger.info(f"添加新三元组: {triple}")

            # 持久化存储
            if self.kv_store:
                await self._save_to_storage()

            # 清除相关推理缓存
            self._clear_inference_cache(subject, object_)

            return True

        except Exception as e:
            self.logger.error(f"添加知识失败: {str(e)}")
            return False

    def _get_triple_key(self, subject: str, predicate: str, object_: str) -> str:
        """生成三元组的唯一键"""
        return f"{subject}|{predicate}|{object_}"

    def _clear_inference_cache(self, *entities: str) -> None:
        """清除相关实体的推理缓存"""
        keys_to_remove = []
        for cache_key in self.inference_cache:
            if any(entity in cache_key for entity in entities):
                keys_to_remove.append(cache_key)

        for key in keys_to_remove:
            del self.inference_cache[key]

    async def query_knowledge(self, query: str, limit: int = 50,
                            include_inferred: bool = True) -> KnowledgeResults:
        """查询知识图谱

        Args:
            query: 查询语句（可以是实体名、关系或自然语言）
            limit: 结果数量限制
            include_inferred: 是否包含推断关系

        Returns:
            KnowledgeResults: 查询结果
        """
        start_time = time.time()
        self.stats['total_queries'] += 1

        try:
            # 解析查询
            query_entities = await self._parse_query(query)

            # 直接匹配
            matched_triples = self._direct_match(query_entities, limit)

            # 推断关系
            inferred_relations = []
            if include_inferred and matched_triples:
                inferred_relations = await self._infer_relations(matched_triples, limit // 2)

            # 计算置信度分数
            confidence_scores = self._calculate_confidence_scores(matched_triples, inferred_relations)

            execution_time = time.time() - start_time
            self.stats['avg_query_time'] = (self.stats['avg_query_time'] * (self.stats['total_queries'] - 1) + execution_time) / self.stats['total_queries']

            results = KnowledgeResults(
                query=query,
                triples=matched_triples,
                inferred_relations=inferred_relations,
                confidence_scores=confidence_scores,
                execution_time=execution_time,
                total_results=len(matched_triples) + len(inferred_relations)
            )

            self.logger.info(f"查询完成: {query}, 找到 {results.total_results} 个结果")
            return results

        except Exception as e:
            self.logger.error(f"查询知识失败: {str(e)}")
            return KnowledgeResults(
                query=query,
                triples=[],
                inferred_relations=[],
                confidence_scores={},
                execution_time=time.time() - start_time,
                total_results=0
            )

    async def _parse_query(self, query: str) -> List[str]:
        """解析查询语句，提取实体"""
        # 简化实现：分词并过滤
        entities = []
        words = query.lower().split()

        # 检查是否匹配已知实体
        for word in words:
            if word in self.subject_index or word in self.object_index:
                entities.append(word)

        # 如果没有匹配的实体，使用AI进行实体识别
        if not entities:
            try:
                prompt = f"从以下查询中提取关键实体（名词）：{query}\n请只返回实体名称，用逗号分隔："
                response = await self.ai_client.chat(prompt)
                entities = [entity.strip() for entity in response.split(',') if entity.strip()]
            except Exception as e:
                self.logger.warning(f"AI实体识别失败: {str(e)}")
                entities = words  # 回退到简单分词

        return entities[:5]  # 限制实体数量

    def _direct_match(self, entities: List[str], limit: int) -> List[KnowledgeTriple]:
        """直接匹配三元组"""
        matched_triples = []
        matched_keys = set()

        for entity in entities:
            # 作为主体的三元组
            for triple_key in self.subject_index.get(entity, set()):
                if triple_key not in matched_keys and len(matched_triples) < limit:
                    matched_triples.append(self.triples[triple_key])
                    matched_keys.add(triple_key)

            # 作为客体的三元组
            for triple_key in self.object_index.get(entity, set()):
                if triple_key not in matched_keys and len(matched_triples) < limit:
                    matched_triples.append(self.triples[triple_key])
                    matched_keys.add(triple_key)

        # 按置信度排序
        matched_triples.sort(key=lambda x: x.confidence, reverse=True)
        return matched_triples[:limit]

    async def _infer_relations(self, base_triples: List[KnowledgeTriple],
                             limit: int) -> List[InferredRelation]:
        """推断新的关系"""
        inferred_relations = []

        try:
            # 传递性推理
            transitivity_relations = await self._apply_transitivity_rule(base_triples)
            inferred_relations.extend(transitivity_relations[:limit//3])

            # 对称性推理
            symmetry_relations = await self._apply_symmetry_rule(base_triples)
            inferred_relations.extend(symmetry_relations[:limit//3])

            # 层次推理
            hierarchy_relations = await self._apply_hierarchy_rule(base_triples)
            inferred_relations.extend(hierarchy_relations[:limit//3])

            self.stats['total_inferences'] += len(inferred_relations)

        except Exception as e:
            self.logger.error(f"关系推理失败: {str(e)}")

        return inferred_relations[:limit]

    async def _apply_transitivity_rule(self, triples: List[KnowledgeTriple]) -> List[InferredRelation]:
        """应用传递性规则"""
        inferred = []

        for triple1 in triples:
            for triple2 in triples:
                if (triple1.object == triple2.subject and
                    triple1.predicate == triple2.predicate and
                    triple1.subject != triple2.object):

                    # 创建推断的三元组
                    inferred_triple = KnowledgeTriple(
                        subject=triple1.subject,
                        predicate=triple1.predicate,
                        object=triple2.object,
                        confidence=min(triple1.confidence, triple2.confidence) * 0.8,  # 降低置信度
                        relation_type=triple1.relation_type,
                        source="inference_transitivity"
                    )

                    inferred_relation = InferredRelation(
                        source_triple=triple1,
                        target_triple=triple2,
                        inferred_triple=inferred_triple,
                        inference_rule="transitivity",
                        confidence=inferred_triple.confidence,
                        reasoning_path=[
                            f"{triple1.subject} -> {triple1.object}",
                            f"{triple2.subject} -> {triple2.object}",
                            f"因此: {triple1.subject} -> {triple2.object}"
                        ]
                    )

                    inferred.append(inferred_relation)

        return inferred

    async def _apply_symmetry_rule(self, triples: List[KnowledgeTriple]) -> List[InferredRelation]:
        """应用对称性规则"""
        inferred = []
        symmetric_relations = {'similar_to', 'related_to', 'connected_to'}

        for triple in triples:
            if (triple.predicate in symmetric_relations or
                triple.relation_type in [RelationType.SIMILAR_TO, RelationType.RELATED_TO]):

                # 检查反向关系是否已存在
                reverse_key = self._get_triple_key(triple.object, triple.predicate, triple.subject)
                if reverse_key not in self.triples:
                    inferred_triple = KnowledgeTriple(
                        subject=triple.object,
                        predicate=triple.predicate,
                        object=triple.subject,
                        confidence=triple.confidence * 0.9,  # 稍微降低置信度
                        relation_type=triple.relation_type,
                        source="inference_symmetry"
                    )

                    inferred_relation = InferredRelation(
                        source_triple=triple,
                        target_triple=triple,  # 同一个三元组
                        inferred_triple=inferred_triple,
                        inference_rule="symmetry",
                        confidence=inferred_triple.confidence,
                        reasoning_path=[
                            f"已知: {triple.subject} {triple.predicate} {triple.object}",
                            f"对称性规则: 如果A关系B，则B关系A",
                            f"因此: {triple.object} {triple.predicate} {triple.subject}"
                        ]
                    )

                    inferred.append(inferred_relation)

        return inferred

    async def _apply_hierarchy_rule(self, triples: List[KnowledgeTriple]) -> List[InferredRelation]:
        """应用层次推理规则"""
        inferred = []
        hierarchy_relations = {'is_a', 'type_of', 'kind_of'}

        for triple1 in triples:
            if triple1.predicate in hierarchy_relations or triple1.relation_type == RelationType.IS_A:
                for triple2 in triples:
                    if (triple2.predicate in hierarchy_relations and
                        triple1.object == triple2.subject and
                        triple1.subject != triple2.object):

                        inferred_triple = KnowledgeTriple(
                            subject=triple1.subject,
                            predicate=triple1.predicate,
                            object=triple2.object,
                            confidence=min(triple1.confidence, triple2.confidence) * 0.7,
                            relation_type=RelationType.IS_A,
                            source="inference_hierarchy"
                        )

                        inferred_relation = InferredRelation(
                            source_triple=triple1,
                            target_triple=triple2,
                            inferred_triple=inferred_triple,
                            inference_rule="hierarchy",
                            confidence=inferred_triple.confidence,
                            reasoning_path=[
                                f"{triple1.subject} 是 {triple1.object}",
                                f"{triple2.subject} 是 {triple2.object}",
                                f"层次推理: {triple1.subject} 是 {triple2.object}"
                            ]
                        )

                        inferred.append(inferred_relation)

        return inferred

    def _calculate_confidence_scores(self, triples: List[KnowledgeTriple],
                                   inferred_relations: List[InferredRelation]) -> Dict[str, float]:
        """计算置信度分数"""
        scores = {}

        if triples:
            scores['direct_match_avg'] = sum(t.confidence for t in triples) / len(triples)
            scores['direct_match_max'] = max(t.confidence for t in triples)
            scores['direct_match_min'] = min(t.confidence for t in triples)

        if inferred_relations:
            scores['inferred_avg'] = sum(r.confidence for r in inferred_relations) / len(inferred_relations)
            scores['inferred_max'] = max(r.confidence for r in inferred_relations)
            scores['inferred_min'] = min(r.confidence for r in inferred_relations)

        # 整体置信度
        all_confidences = [t.confidence for t in triples] + [r.confidence for r in inferred_relations]
        if all_confidences:
            scores['overall_confidence'] = sum(all_confidences) / len(all_confidences)
        else:
            scores['overall_confidence'] = 0.0

        return scores

    async def update_confidence(self, subject: str, predicate: str, object_: str,
                              new_confidence: float, evidence: List[str] = None) -> bool:
        """更新三元组的置信度

        Args:
            subject: 主体
            predicate: 谓词
            object_: 客体
            new_confidence: 新的置信度
            evidence: 新的证据

        Returns:
            bool: 是否更新成功
        """
        try:
            triple_key = self._get_triple_key(subject, predicate, object_)

            if triple_key in self.triples:
                triple = self.triples[triple_key]
                old_confidence = triple.confidence

                # 使用加权平均更新置信度
                triple.confidence = (old_confidence + new_confidence) / 2
                triple.updated_at = datetime.now()

                # 添加新证据
                if evidence:
                    triple.evidence.extend(evidence)

                # 清除相关缓存
                self._clear_inference_cache(subject, object_)

                # 持久化
                if self.kv_store:
                    await self._save_to_storage()

                self.logger.info(f"更新置信度: {triple_key} {old_confidence:.2f} -> {triple.confidence:.2f}")
                return True
            else:
                self.logger.warning(f"未找到要更新的三元组: {triple_key}")
                return False

        except Exception as e:
            self.logger.error(f"更新置信度失败: {str(e)}")
            return False

    async def infer_relations(self, entity: str, max_depth: int = 2) -> List[InferredRelation]:
        """为指定实体推断关系

        Args:
            entity: 目标实体
            max_depth: 最大推理深度

        Returns:
            List[InferredRelation]: 推断的关系列表
        """
        cache_key = f"infer_{entity}_{max_depth}"

        # 检查缓存
        if cache_key in self.inference_cache:
            self.stats['cache_hits'] += 1
            return self.inference_cache[cache_key]

        self.stats['cache_misses'] += 1

        try:
            # 获取与实体相关的所有三元组
            related_triples = []

            # 作为主体
            for triple_key in self.subject_index.get(entity, set()):
                related_triples.append(self.triples[triple_key])

            # 作为客体
            for triple_key in self.object_index.get(entity, set()):
                related_triples.append(self.triples[triple_key])

            # 多层推理
            all_inferred = []
            current_triples = related_triples

            for depth in range(max_depth):
                if not current_triples:
                    break

                # 在当前层进行推理
                layer_inferred = await self._infer_relations(current_triples, 20)
                all_inferred.extend(layer_inferred)

                # 为下一层准备三元组
                next_triples = []
                for inferred in layer_inferred:
                    next_triples.append(inferred.inferred_triple)

                current_triples = next_triples

            # 缓存结果
            self.inference_cache[cache_key] = all_inferred

            return all_inferred

        except Exception as e:
            self.logger.error(f"推断关系失败: {str(e)}")
            return []

    def get_statistics(self) -> Dict[str, Any]:
        """获取知识图谱统计信息"""
        return {
            'total_triples': self.stats['total_triples'],
            'total_entities': len(set(list(self.subject_index.keys()) + list(self.object_index.keys()))),
            'total_predicates': len(self.predicate_index),
            'total_queries': self.stats['total_queries'],
            'total_inferences': self.stats['total_inferences'],
            'avg_query_time': self.stats['avg_query_time'],
            'cache_hit_rate': self.stats['cache_hits'] / (self.stats['cache_hits'] + self.stats['cache_misses']) if (self.stats['cache_hits'] + self.stats['cache_misses']) > 0 else 0,
            'inference_cache_size': len(self.inference_cache)
        }

    def clear_cache(self) -> None:
        """清空推理缓存"""
        self.inference_cache.clear()
        self.logger.info("推理缓存已清空")

    async def _save_to_storage(self) -> None:
        """保存到持久化存储"""
        if not self.kv_store:
            return

        try:
            # 保存三元组
            triples_data = {key: triple.to_dict() for key, triple in self.triples.items()}
            await self.kv_store.set("knowledge_graph_triples", json.dumps(triples_data))

            # 保存统计信息
            await self.kv_store.set("knowledge_graph_stats", json.dumps(self.stats))

        except Exception as e:
            self.logger.error(f"保存到存储失败: {str(e)}")

    async def load_from_storage(self) -> bool:
        """从持久化存储加载"""
        if not self.kv_store:
            return False

        try:
            # 加载三元组
            triples_json = await self.kv_store.get("knowledge_graph_triples")
            if triples_json:
                triples_data = json.loads(triples_json)
                for key, triple_dict in triples_data.items():
                    triple = KnowledgeTriple.from_dict(triple_dict)
                    self.triples[key] = triple

                    # 重建索引
                    self.subject_index[triple.subject].add(key)
                    self.object_index[triple.object].add(key)
                    self.predicate_index[triple.predicate].add(key)

            # 加载统计信息
            stats_json = await self.kv_store.get("knowledge_graph_stats")
            if stats_json:
                self.stats.update(json.loads(stats_json))

            self.logger.info(f"从存储加载了 {len(self.triples)} 个三元组")
            return True

        except Exception as e:
            self.logger.error(f"从存储加载失败: {str(e)}")
            return False
