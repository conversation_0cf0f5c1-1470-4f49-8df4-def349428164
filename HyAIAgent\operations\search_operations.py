"""
HyAIAgent 第四阶段 - 搜索操作模块
实现Tavily API集成和网络搜索功能
步骤4.4: 基础搜索操作实现
"""

import asyncio
import aiohttp
import json
import hashlib
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging
from pathlib import Path

# 导入现有模块
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager
from operations.base_operation import BaseOperation, OperationType, OperationResult
from core.kv_store import KVStore

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class SearchResult:
    """搜索结果数据模型"""
    title: str
    url: str
    content: str
    score: float
    published_date: Optional[str] = None
    source: str = "tavily"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)


@dataclass
class SearchResponse:
    """搜索响应数据模型"""
    query: str
    results: List[SearchResult]
    total_results: int
    search_time: float
    timestamp: datetime
    search_depth: str = "basic"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class TavilySearchClient:
    """Tavily搜索API客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://api.tavily.com"):
        """
        初始化Tavily搜索客户端
        
        Args:
            api_key: Tavily API密钥
            base_url: API基础URL
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.session: Optional[aiohttp.ClientSession] = None
        self.request_count = 0
        self.last_request_time = 0
        self.rate_limit_delay = 1.0  # 请求间隔限制（秒）
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
            
    async def _ensure_session(self):
        """确保会话存在"""
        if not self.session:
            self.session = aiohttp.ClientSession()
            
    async def _rate_limit(self):
        """实施速率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.rate_limit_delay:
            await asyncio.sleep(self.rate_limit_delay - time_since_last)
        self.last_request_time = time.time()
        
    async def search(self, 
                    query: str, 
                    search_depth: str = "basic",
                    max_results: int = 5,
                    include_domains: Optional[List[str]] = None,
                    exclude_domains: Optional[List[str]] = None,
                    timeout: int = 30) -> SearchResponse:
        """
        执行搜索请求
        
        Args:
            query: 搜索查询
            search_depth: 搜索深度 ("basic" 或 "advanced")
            max_results: 最大结果数量
            include_domains: 包含的域名列表
            exclude_domains: 排除的域名列表
            timeout: 请求超时时间
            
        Returns:
            SearchResponse: 搜索响应对象
            
        Raises:
            Exception: API请求失败时抛出异常
        """
        await self._ensure_session()
        await self._rate_limit()
        
        # 构建请求参数
        params = {
            "api_key": self.api_key,
            "query": query,
            "search_depth": search_depth,
            "max_results": max_results
        }
        
        if include_domains:
            params["include_domains"] = include_domains
        if exclude_domains:
            params["exclude_domains"] = exclude_domains
            
        start_time = time.time()
        
        try:
            # 发送API请求
            async with self.session.post(
                f"{self.base_url}/search",
                json=params,
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                
                self.request_count += 1
                
                if response.status == 200:
                    data = await response.json()
                    search_time = time.time() - start_time
                    
                    # 解析搜索结果
                    results = []
                    for item in data.get("results", []):
                        result = SearchResult(
                            title=item.get("title", ""),
                            url=item.get("url", ""),
                            content=item.get("content", ""),
                            score=item.get("score", 0.0),
                            published_date=item.get("published_date"),
                            source="tavily"
                        )
                        results.append(result)
                    
                    return SearchResponse(
                        query=query,
                        results=results,
                        total_results=len(results),
                        search_time=search_time,
                        timestamp=datetime.now(),
                        search_depth=search_depth
                    )
                else:
                    error_text = await response.text()
                    raise Exception(f"Tavily API请求失败: {response.status} - {error_text}")
                    
        except asyncio.TimeoutError:
            raise Exception(f"Tavily API请求超时 (>{timeout}秒)")
        except Exception as e:
            logger.error(f"Tavily搜索请求失败: {str(e)}")
            raise
            
    async def close(self):
        """关闭客户端会话"""
        if self.session:
            await self.session.close()
            self.session = None


class SearchOperations:
    """网络搜索操作管理器"""
    
    def __init__(self, 
                 config_manager: ConfigManager,
                 security_manager: Optional[SecurityManager] = None,
                 kv_store: Optional[KVStore] = None):
        """
        初始化搜索操作管理器
        
        Args:
            config_manager: 配置管理器
            security_manager: 安全管理器
            kv_store: 键值存储
        """
        self.config_manager = config_manager
        self.security_manager = security_manager
        self.kv_store = kv_store
        
        # 获取搜索配置
        self.search_config = self.config_manager.get("search", {})
        self.tavily_config = self.search_config.get("tavily", {})
        
        # 初始化Tavily客户端
        api_key = self.tavily_config.get("api_key", "")
        if not api_key or api_key == "${TAVILY_API_KEY}":
            raise ValueError("Tavily API密钥未配置，请在配置文件中设置search.tavily.api_key")
            
        self.tavily_client = TavilySearchClient(
            api_key=api_key,
            base_url=self.tavily_config.get("base_url", "https://api.tavily.com")
        )
        
        # 搜索统计
        self.search_stats = {
            "total_searches": 0,
            "successful_searches": 0,
            "failed_searches": 0,
            "cache_hits": 0,
            "total_search_time": 0.0
        }
        
    def _generate_cache_key(self, query: str, **kwargs) -> str:
        """生成缓存键"""
        cache_data = {"query": query, **kwargs}
        cache_str = json.dumps(cache_data, sort_keys=True)
        return f"search_cache_{hashlib.md5(cache_str.encode()).hexdigest()}"
        
    async def _get_cached_result(self, cache_key: str) -> Optional[SearchResponse]:
        """获取缓存的搜索结果"""
        if not self.kv_store:
            return None

        try:
            cached_data = self.kv_store.get(cache_key)
            if cached_data:
                # 检查缓存是否过期
                cache_ttl = self.search_config.get("cache", {}).get("ttl", 3600)
                cached_time = datetime.fromisoformat(cached_data["timestamp"])
                if datetime.now() - cached_time < timedelta(seconds=cache_ttl):
                    # 重构SearchResponse对象
                    results = [SearchResult(**result) for result in cached_data["results"]]
                    response = SearchResponse(
                        query=cached_data["query"],
                        results=results,
                        total_results=cached_data["total_results"],
                        search_time=cached_data["search_time"],
                        timestamp=cached_time,
                        search_depth=cached_data.get("search_depth", "basic")
                    )
                    self.search_stats["cache_hits"] += 1
                    return response
        except Exception as e:
            logger.warning(f"获取搜索缓存失败: {str(e)}")

        return None
        
    async def _cache_result(self, cache_key: str, response: SearchResponse):
        """缓存搜索结果"""
        if not self.kv_store:
            return

        try:
            cache_enabled = self.search_config.get("cache", {}).get("enabled", True)
            if cache_enabled:
                self.kv_store.set(cache_key, response.to_dict())
        except Exception as e:
            logger.warning(f"缓存搜索结果失败: {str(e)}")
            
    async def search_web(self, 
                        query: str, 
                        search_depth: str = "basic",
                        max_results: int = 5,
                        use_cache: bool = True,
                        **kwargs) -> SearchResponse:
        """
        执行网络搜索
        
        Args:
            query: 搜索查询
            search_depth: 搜索深度
            max_results: 最大结果数量
            use_cache: 是否使用缓存
            **kwargs: 其他搜索参数
            
        Returns:
            SearchResponse: 搜索响应
        """
        self.search_stats["total_searches"] += 1
        
        # 生成缓存键
        cache_key = self._generate_cache_key(query, search_depth=search_depth, max_results=max_results, **kwargs)
        
        # 尝试获取缓存结果
        if use_cache:
            cached_result = await self._get_cached_result(cache_key)
            if cached_result:
                logger.info(f"使用缓存搜索结果: {query}")
                return cached_result
        
        try:
            # 执行实际搜索
            logger.info(f"执行网络搜索: {query}")
            response = await self.tavily_client.search(
                query=query,
                search_depth=search_depth,
                max_results=max_results,
                timeout=self.tavily_config.get("timeout", 30),
                **kwargs
            )
            
            # 缓存结果
            if use_cache:
                await self._cache_result(cache_key, response)
            
            # 更新统计
            self.search_stats["successful_searches"] += 1
            self.search_stats["total_search_time"] += response.search_time
            
            logger.info(f"搜索完成: {query}, 结果数量: {response.total_results}, 用时: {response.search_time:.2f}秒")
            return response
            
        except Exception as e:
            self.search_stats["failed_searches"] += 1
            logger.error(f"搜索失败: {query} - {str(e)}")
            raise
            
    async def get_search_stats(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        stats = self.search_stats.copy()
        if stats["successful_searches"] > 0:
            stats["average_search_time"] = stats["total_search_time"] / stats["successful_searches"]
        else:
            stats["average_search_time"] = 0.0
            
        if stats["total_searches"] > 0:
            stats["success_rate"] = stats["successful_searches"] / stats["total_searches"]
            stats["cache_hit_rate"] = stats["cache_hits"] / stats["total_searches"]
        else:
            stats["success_rate"] = 0.0
            stats["cache_hit_rate"] = 0.0
            
        return stats
        
    async def close(self):
        """关闭搜索操作管理器"""
        await self.tavily_client.close()


class BasicSearchOperation(BaseOperation):
    """基础搜索操作类 - 步骤4.4实现"""

    def __init__(self, search_operations: SearchOperations):
        """
        初始化基础搜索操作

        Args:
            search_operations: 搜索操作管理器实例
        """
        super().__init__(
            operation_type=OperationType.NETWORK,
            name="basic_search",
            description="执行基础网络搜索操作"
        )
        self.search_operations = search_operations

    async def execute(self, **kwargs) -> OperationResult:
        """
        执行基础搜索操作

        Args:
            **kwargs: 搜索参数
                - query (str): 搜索查询，必需
                - search_depth (str): 搜索深度，可选，默认"basic"
                - max_results (int): 最大结果数量，可选，默认5
                - use_cache (bool): 是否使用缓存，可选，默认True
                - include_domains (List[str]): 包含域名列表，可选
                - exclude_domains (List[str]): 排除域名列表，可选

        Returns:
            OperationResult: 操作结果，包含搜索响应数据
        """
        try:
            # 验证必需参数
            if "query" not in kwargs or not kwargs["query"]:
                return OperationResult(
                    success=False,
                    error_message="搜索查询参数(query)不能为空",
                    metadata={"operation": "basic_search", "params": kwargs}
                )

            query = kwargs["query"]
            search_depth = kwargs.get("search_depth", "basic")
            max_results = kwargs.get("max_results", 5)
            use_cache = kwargs.get("use_cache", True)
            include_domains = kwargs.get("include_domains")
            exclude_domains = kwargs.get("exclude_domains")

            # 验证参数有效性
            if search_depth not in ["basic", "advanced"]:
                return OperationResult(
                    success=False,
                    error_message=f"无效的搜索深度: {search_depth}，必须是'basic'或'advanced'",
                    metadata={"operation": "basic_search", "params": kwargs}
                )

            if not isinstance(max_results, int) or max_results < 1 or max_results > 20:
                return OperationResult(
                    success=False,
                    error_message=f"无效的最大结果数量: {max_results}，必须是1-20之间的整数",
                    metadata={"operation": "basic_search", "params": kwargs}
                )

            # 记录操作开始
            start_time = time.time()
            logger.info(f"开始执行基础搜索操作: {query}")

            # 执行搜索
            search_kwargs = {}
            if include_domains:
                search_kwargs["include_domains"] = include_domains
            if exclude_domains:
                search_kwargs["exclude_domains"] = exclude_domains

            search_response = await self.search_operations.search_web(
                query=query,
                search_depth=search_depth,
                max_results=max_results,
                use_cache=use_cache,
                **search_kwargs
            )

            # 计算执行时间
            execution_time = time.time() - start_time

            # 构建结果数据
            result_data = {
                "search_response": search_response.to_dict(),
                "query": query,
                "total_results": search_response.total_results,
                "search_time": search_response.search_time,
                "execution_time": execution_time,
                "search_depth": search_depth,
                "cached": hasattr(search_response, '_from_cache') and search_response._from_cache
            }

            # 构建元数据
            metadata = {
                "operation": "basic_search",
                "params": kwargs,
                "execution_time": execution_time,
                "results_count": search_response.total_results,
                "search_depth": search_depth,
                "api_search_time": search_response.search_time
            }

            logger.info(f"基础搜索操作完成: {query}, 结果数量: {search_response.total_results}, 总用时: {execution_time:.2f}秒")

            return OperationResult(
                success=True,
                data=result_data,
                metadata=metadata
            )

        except Exception as e:
            execution_time = time.time() - start_time if 'start_time' in locals() else 0
            error_message = f"基础搜索操作失败: {str(e)}"
            logger.error(error_message)

            return OperationResult(
                success=False,
                error_message=error_message,
                metadata={
                    "operation": "basic_search",
                    "params": kwargs,
                    "execution_time": execution_time,
                    "error_type": type(e).__name__
                }
            )

    async def validate_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证搜索参数

        Args:
            params: 参数字典

        Returns:
            Dict[str, Any]: 验证结果
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }

        # 检查必需参数
        if "query" not in params:
            validation_result["valid"] = False
            validation_result["errors"].append("缺少必需参数: query")
        elif not params["query"] or not isinstance(params["query"], str):
            validation_result["valid"] = False
            validation_result["errors"].append("query参数必须是非空字符串")
        elif len(params["query"].strip()) < 2:
            validation_result["valid"] = False
            validation_result["errors"].append("query参数长度至少为2个字符")

        # 检查可选参数
        if "search_depth" in params:
            if params["search_depth"] not in ["basic", "advanced"]:
                validation_result["valid"] = False
                validation_result["errors"].append("search_depth必须是'basic'或'advanced'")

        if "max_results" in params:
            max_results = params["max_results"]
            if not isinstance(max_results, int) or max_results < 1 or max_results > 20:
                validation_result["valid"] = False
                validation_result["errors"].append("max_results必须是1-20之间的整数")

        if "include_domains" in params:
            include_domains = params["include_domains"]
            if not isinstance(include_domains, list):
                validation_result["valid"] = False
                validation_result["errors"].append("include_domains必须是字符串列表")
            elif not all(isinstance(domain, str) for domain in include_domains):
                validation_result["valid"] = False
                validation_result["errors"].append("include_domains中的所有元素必须是字符串")

        if "exclude_domains" in params:
            exclude_domains = params["exclude_domains"]
            if not isinstance(exclude_domains, list):
                validation_result["valid"] = False
                validation_result["errors"].append("exclude_domains必须是字符串列表")
            elif not all(isinstance(domain, str) for domain in exclude_domains):
                validation_result["valid"] = False
                validation_result["errors"].append("exclude_domains中的所有元素必须是字符串")

        # 添加警告
        if "query" in params and len(params["query"]) > 200:
            validation_result["warnings"].append("查询字符串过长，可能影响搜索效果")

        return validation_result

    def validate_parameters(self, **kwargs) -> bool:
        """
        验证操作参数（BaseOperation抽象方法实现）

        Args:
            **kwargs: 操作参数

        Returns:
            bool: 参数是否有效
        """
        # 检查必需参数
        if "query" not in kwargs or not kwargs["query"]:
            return False

        # 检查搜索深度参数
        if "search_depth" in kwargs:
            if kwargs["search_depth"] not in ["basic", "advanced"]:
                return False

        # 检查最大结果数量参数
        if "max_results" in kwargs:
            max_results = kwargs["max_results"]
            if not isinstance(max_results, int) or max_results < 1 or max_results > 20:
                return False

        return True


class SearchToolkit:
    """搜索工具包 - 提供便捷的搜索功能接口"""

    def __init__(self, config_manager: ConfigManager,
                 security_manager: Optional[SecurityManager] = None,
                 kv_store: Optional[KVStore] = None):
        """
        初始化搜索工具包

        Args:
            config_manager: 配置管理器
            security_manager: 安全管理器
            kv_store: 键值存储
        """
        self.search_operations = SearchOperations(config_manager, security_manager, kv_store)
        self.basic_search_op = BasicSearchOperation(self.search_operations)

    async def quick_search(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """
        快速搜索 - 使用默认参数进行简单搜索

        Args:
            query: 搜索查询
            max_results: 最大结果数量

        Returns:
            Dict[str, Any]: 搜索结果
        """
        result = await self.basic_search_op.execute(
            query=query,
            max_results=max_results,
            search_depth="basic",
            use_cache=True
        )
        return {
            "success": result.success,
            "data": result.data,
            "error": result.error_message if not result.success else None,
            "metadata": result.metadata
        }

    async def advanced_search(self, query: str,
                            max_results: int = 10,
                            include_domains: Optional[List[str]] = None,
                            exclude_domains: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        高级搜索 - 使用高级搜索深度和域名过滤

        Args:
            query: 搜索查询
            max_results: 最大结果数量
            include_domains: 包含的域名列表
            exclude_domains: 排除的域名列表

        Returns:
            Dict[str, Any]: 搜索结果
        """
        kwargs = {
            "query": query,
            "max_results": max_results,
            "search_depth": "advanced",
            "use_cache": True
        }

        if include_domains:
            kwargs["include_domains"] = include_domains
        if exclude_domains:
            kwargs["exclude_domains"] = exclude_domains

        result = await self.basic_search_op.execute(**kwargs)
        return {
            "success": result.success,
            "data": result.data,
            "error": result.error_message if not result.success else None,
            "metadata": result.metadata
        }

    async def search_with_validation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        带参数验证的搜索

        Args:
            params: 搜索参数

        Returns:
            Dict[str, Any]: 搜索结果，包含验证信息
        """
        # 先验证参数
        validation_result = await self.basic_search_op.validate_params(params)

        if not validation_result["valid"]:
            return {
                "success": False,
                "data": None,
                "error": f"参数验证失败: {', '.join(validation_result['errors'])}",
                "validation": validation_result
            }

        # 执行搜索
        result = await self.basic_search_op.execute(**params)
        return {
            "success": result.success,
            "data": result.data,
            "error": result.error_message if not result.success else None,
            "metadata": result.metadata,
            "validation": validation_result
        }

    async def batch_search(self, queries: List[str],
                          max_results_per_query: int = 5,
                          max_concurrent: int = 3) -> Dict[str, Any]:
        """
        批量搜索 - 并发执行多个搜索查询

        Args:
            queries: 搜索查询列表
            max_results_per_query: 每个查询的最大结果数量
            max_concurrent: 最大并发数量

        Returns:
            Dict[str, Any]: 批量搜索结果
        """
        if not queries:
            return {
                "success": False,
                "error": "查询列表不能为空",
                "results": []
            }

        # 限制并发数量
        semaphore = asyncio.Semaphore(max_concurrent)

        async def search_single(query: str) -> Dict[str, Any]:
            async with semaphore:
                try:
                    result = await self.quick_search(query, max_results_per_query)
                    return {
                        "query": query,
                        "success": result["success"],
                        "data": result["data"],
                        "error": result["error"]
                    }
                except Exception as e:
                    return {
                        "query": query,
                        "success": False,
                        "data": None,
                        "error": f"搜索失败: {str(e)}"
                    }

        # 并发执行搜索
        start_time = time.time()
        tasks = [search_single(query) for query in queries]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        execution_time = time.time() - start_time

        # 处理结果
        successful_results = []
        failed_results = []

        for result in results:
            if isinstance(result, Exception):
                failed_results.append({
                    "query": "unknown",
                    "success": False,
                    "error": str(result)
                })
            elif result["success"]:
                successful_results.append(result)
            else:
                failed_results.append(result)

        return {
            "success": len(successful_results) > 0,
            "total_queries": len(queries),
            "successful_count": len(successful_results),
            "failed_count": len(failed_results),
            "execution_time": execution_time,
            "results": successful_results,
            "failures": failed_results
        }

    async def get_search_statistics(self) -> Dict[str, Any]:
        """
        获取搜索统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return await self.search_operations.get_search_stats()

    async def close(self):
        """关闭搜索工具包"""
        await self.search_operations.close()
