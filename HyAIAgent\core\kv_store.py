"""
KV数据库模块
基于TinyDB实现的轻量级键值数据库
"""

import json
import time
from pathlib import Path
from typing import Any, Dict, List, Optional
from tinydb import TinyDB, Query
from loguru import logger


class KVStore:
    """
    轻量级KV数据库
    基于TinyDB实现，支持键值存储、查询和自动清理
    """
    
    def __init__(self, db_path: str = "data/kv_store.json", 
                 auto_cleanup: bool = True, cleanup_interval: int = 3600):
        """
        初始化KV数据库
        
        Args:
            db_path (str): 数据库文件路径
            auto_cleanup (bool): 是否启用自动清理
            cleanup_interval (int): 清理间隔（秒）
        """
        self.db_path = Path(db_path)
        self.auto_cleanup = auto_cleanup
        self.cleanup_interval = cleanup_interval
        self.last_cleanup = time.time()
        
        # 确保目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self.db = TinyDB(self.db_path)
        self.query = Query()
        
        logger.info(f"KV数据库初始化完成: {self.db_path}")
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置键值对
        
        Args:
            key (str): 键名
            value (Any): 值
            ttl (int, optional): 生存时间（秒），None表示永不过期
            
        Returns:
            bool: 设置是否成功
        """
        try:
            current_time = time.time()
            expire_time = current_time + ttl if ttl else None
            
            # 构建数据记录
            record = {
                'key': key,
                'value': value,
                'created_at': current_time,
                'expire_at': expire_time
            }
            
            # 删除已存在的键
            self.db.remove(self.query.key == key)
            
            # 插入新记录
            self.db.insert(record)
            
            # 执行自动清理
            if self.auto_cleanup:
                self._auto_cleanup()
            
            logger.debug(f"设置键值对: {key} = {str(value)[:100]}...")
            return True
            
        except Exception as e:
            logger.error(f"设置键值对失败: {key}, 错误: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取键值
        
        Args:
            key (str): 键名
            default (Any): 默认值
            
        Returns:
            Any: 键对应的值，如果不存在或已过期则返回默认值
        """
        try:
            result = self.db.search(self.query.key == key)
            
            if not result:
                return default
            
            record = result[0]
            current_time = time.time()
            
            # 检查是否过期
            if record.get('expire_at') and current_time > record['expire_at']:
                # 删除过期记录
                self.db.remove(self.query.key == key)
                logger.debug(f"键已过期并删除: {key}")
                return default
            
            logger.debug(f"获取键值: {key} = {str(record['value'])[:100]}...")
            return record['value']
            
        except Exception as e:
            logger.error(f"获取键值失败: {key}, 错误: {e}")
            return default
    
    def delete(self, key: str) -> bool:
        """
        删除键值对
        
        Args:
            key (str): 键名
            
        Returns:
            bool: 删除是否成功
        """
        try:
            removed = self.db.remove(self.query.key == key)
            success = len(removed) > 0
            
            if success:
                logger.debug(f"删除键值对: {key}")
            else:
                logger.debug(f"键不存在: {key}")
            
            return success
            
        except Exception as e:
            logger.error(f"删除键值对失败: {key}, 错误: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """
        检查键是否存在且未过期
        
        Args:
            key (str): 键名
            
        Returns:
            bool: 键是否存在
        """
        value = self.get(key, None)
        return value is not None
    
    def keys(self) -> List[str]:
        """
        获取所有有效键名
        
        Returns:
            List[str]: 键名列表
        """
        try:
            current_time = time.time()
            valid_keys = []
            
            for record in self.db.all():
                # 检查是否过期
                if record.get('expire_at') and current_time > record['expire_at']:
                    continue
                valid_keys.append(record['key'])
            
            return valid_keys
            
        except Exception as e:
            logger.error(f"获取键名列表失败: {e}")
            return []
    
    def clear(self) -> bool:
        """
        清空所有数据
        
        Returns:
            bool: 清空是否成功
        """
        try:
            self.db.truncate()
            logger.info("KV数据库已清空")
            return True
            
        except Exception as e:
            logger.error(f"清空数据库失败: {e}")
            return False
    
    def size(self) -> int:
        """
        获取有效记录数量
        
        Returns:
            int: 记录数量
        """
        return len(self.keys())
    
    def cleanup_expired(self) -> int:
        """
        清理过期记录
        
        Returns:
            int: 清理的记录数量
        """
        try:
            current_time = time.time()
            expired_count = 0
            
            # 查找过期记录
            for record in self.db.all():
                if record.get('expire_at') and current_time > record['expire_at']:
                    self.db.remove(self.query.key == record['key'])
                    expired_count += 1
            
            if expired_count > 0:
                logger.info(f"清理过期记录: {expired_count}条")
            
            self.last_cleanup = current_time
            return expired_count
            
        except Exception as e:
            logger.error(f"清理过期记录失败: {e}")
            return 0
    
    def _auto_cleanup(self) -> None:
        """自动清理过期记录"""
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            self.cleanup_expired()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取数据库统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            current_time = time.time()
            total_records = len(self.db.all())
            valid_records = len(self.keys())
            expired_records = total_records - valid_records
            
            return {
                'total_records': total_records,
                'valid_records': valid_records,
                'expired_records': expired_records,
                'db_path': str(self.db_path),
                'last_cleanup': self.last_cleanup,
                'auto_cleanup': self.auto_cleanup
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def backup(self, backup_path: str) -> bool:
        """
        备份数据库
        
        Args:
            backup_path (str): 备份文件路径
            
        Returns:
            bool: 备份是否成功
        """
        try:
            backup_file = Path(backup_path)
            backup_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 获取所有有效数据
            valid_data = []
            for key in self.keys():
                value = self.get(key)
                if value is not None:
                    valid_data.append({'key': key, 'value': value})
            
            # 写入备份文件
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(valid_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"数据库备份完成: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
    
    def restore(self, backup_path: str, clear_existing: bool = False) -> bool:
        """
        从备份恢复数据库
        
        Args:
            backup_path (str): 备份文件路径
            clear_existing (bool): 是否清空现有数据
            
        Returns:
            bool: 恢复是否成功
        """
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                logger.error(f"备份文件不存在: {backup_path}")
                return False
            
            # 清空现有数据（如果需要）
            if clear_existing:
                self.clear()
            
            # 读取备份数据
            with open(backup_file, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            # 恢复数据
            restored_count = 0
            for item in backup_data:
                if self.set(item['key'], item['value']):
                    restored_count += 1
            
            logger.info(f"数据库恢复完成: 恢复{restored_count}条记录")
            return True
            
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            return False
    
    def close(self) -> None:
        """关闭数据库连接"""
        try:
            self.db.close()
            logger.info("KV数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")
