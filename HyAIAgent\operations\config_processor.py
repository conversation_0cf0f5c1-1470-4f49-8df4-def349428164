"""
配置文件处理器模块

提供全面的配置文件处理功能，包括多格式配置文件的读取、解析、验证、转换和管理。
支持的配置格式：JSON, YAML, INI, TOML
"""

import json
import os
import re
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import asyncio
import logging

# 可选依赖
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

try:
    import toml
    TOML_AVAILABLE = True
except ImportError:
    TOML_AVAILABLE = False

import configparser

from .security_manager import SecurityManager
from .file_operations import FileOperations

logger = logging.getLogger(__name__)


class ConfigProcessor:
    """
    配置文件处理器类
    
    提供全面的配置文件处理功能，包括多格式配置文件的读取、解析、验证、转换和管理。
    支持的配置格式：
    - JSON: 标准JSON格式配置文件
    - YAML: YAML格式配置文件 (需要PyYAML)
    - INI: INI格式配置文件
    - TOML: TOML格式配置文件 (需要toml库)
    
    特性：
    - 异步配置文件处理
    - 自动格式检测
    - 配置文件验证
    - 格式转换
    - 环境变量替换
    - 配置文件备份和恢复
    - 配置合并
    - 安全性验证
    """
    
    def __init__(self, workspace_path: str):
        """
        初始化配置文件处理器

        Args:
            workspace_path (str): 工作空间路径
        """
        self.workspace_path = Path(workspace_path)
        self.security_manager = SecurityManager(workspace_path)
        self.file_operations = FileOperations(workspace_path, self.security_manager)
        
        # 支持的配置格式
        self.supported_formats = {
            '.json': 'json',
            '.yaml': 'yaml',
            '.yml': 'yaml', 
            '.ini': 'ini',
            '.toml': 'toml',
            '.cfg': 'ini',
            '.conf': 'ini'
        }
        
        # 处理统计
        self.processing_stats = {
            "total_processed": 0,
            "successful_processed": 0,
            "failed_processed": 0,
            "formats_processed": {},
            "last_processed": None,
            "validations_performed": 0,
            "conversions_performed": 0,
            "backups_created": 0
        }
        
        # 环境变量替换模式
        self.env_var_pattern = re.compile(r'\$\{([^}]+)\}')
        
        logger.info(f"ConfigProcessor initialized with workspace: {self.workspace_path}")
    
    async def process_config(self, file_path: str, 
                           format_hint: Optional[str] = None,
                           validate_schema: Optional[Dict[str, Any]] = None,
                           replace_env_vars: bool = True) -> Dict[str, Any]:
        """
        处理配置文件
        
        Args:
            file_path (str): 配置文件路径
            format_hint (str, optional): 格式提示
            validate_schema (Dict[str, Any], optional): 验证模式
            replace_env_vars (bool): 是否替换环境变量
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        start_time = datetime.now()
        
        try:
            # 安全验证
            security_result = self.security_manager.validate_operation(file_path, 'read')
            if not security_result.get('allowed', False):
                raise PermissionError(f"File access denied: {file_path}")
            
            # 读取文件内容
            file_result = await self.file_operations.read_file(file_path)
            if not file_result.get('success'):
                raise FileNotFoundError(f"Failed to read config file: {file_path}")
            
            content = file_result['content']
            file_path_obj = Path(file_path)
            
            # 检测配置格式
            config_format = format_hint or self._detect_config_format(file_path_obj)
            
            # 解析配置内容
            parsed_config = await self._parse_config_content(content, config_format)
            
            # 环境变量替换
            if replace_env_vars:
                parsed_config = self._replace_environment_variables(parsed_config)
            
            # 配置验证
            validation_result = None
            if validate_schema:
                validation_result = await self._validate_config(parsed_config, validate_schema)
            
            # 更新统计
            self._update_stats('successful', config_format)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                'status': 'success',
                'file_path': file_path,
                'format': config_format,
                'config': parsed_config,
                'validation': validation_result,
                'metadata': {
                    'file_size': len(content),
                    'processing_time': processing_time,
                    'env_vars_replaced': replace_env_vars,
                    'timestamp': datetime.now().isoformat()
                }
            }
            
            logger.info(f"Config processed successfully: {file_path} ({config_format})")
            return result
            
        except Exception as e:
            self._update_stats('failed')
            logger.error(f"Failed to process config {file_path}: {str(e)}")
            return {
                'status': 'error',
                'file_path': file_path,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def convert_config_format(self, source_path: str, target_path: str,
                                  target_format: str, 
                                  preserve_comments: bool = False) -> Dict[str, Any]:
        """
        转换配置文件格式
        
        Args:
            source_path (str): 源文件路径
            target_path (str): 目标文件路径
            target_format (str): 目标格式 (json, yaml, ini, toml)
            preserve_comments (bool): 是否保留注释
            
        Returns:
            Dict[str, Any]: 转换结果
        """
        try:
            # 读取源配置
            source_result = await self.process_config(source_path, replace_env_vars=False)
            if source_result['status'] != 'success':
                return source_result
            
            source_config = source_result['config']
            source_format = source_result['format']
            
            # 转换配置格式
            converted_content = await self._convert_config_data(
                source_config, target_format, preserve_comments
            )
            
            # 写入目标文件
            write_result = await self.file_operations.write_file(
                target_path, converted_content
            )
            
            if not write_result.get('success'):
                raise Exception(f"Failed to write converted config: {write_result.get('error')}")
            
            self.processing_stats['conversions_performed'] += 1
            
            return {
                'status': 'success',
                'source_path': source_path,
                'target_path': target_path,
                'source_format': source_format,
                'target_format': target_format,
                'converted_size': len(converted_content),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Config conversion failed: {str(e)}")
            return {
                'status': 'error',
                'source_path': source_path,
                'target_path': target_path,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def merge_configs(self, config_paths: List[str], 
                          output_path: Optional[str] = None,
                          merge_strategy: str = 'deep') -> Dict[str, Any]:
        """
        合并多个配置文件
        
        Args:
            config_paths (List[str]): 配置文件路径列表
            output_path (str, optional): 输出文件路径
            merge_strategy (str): 合并策略 ('deep', 'shallow', 'override')
            
        Returns:
            Dict[str, Any]: 合并结果
        """
        try:
            merged_config = {}
            processed_configs = []
            
            # 处理每个配置文件
            for config_path in config_paths:
                result = await self.process_config(config_path)
                if result['status'] == 'success':
                    processed_configs.append({
                        'path': config_path,
                        'format': result['format'],
                        'config': result['config']
                    })
                else:
                    logger.warning(f"Failed to process config for merge: {config_path}")
            
            # 执行合并
            if merge_strategy == 'deep':
                merged_config = self._deep_merge_configs([c['config'] for c in processed_configs])
            elif merge_strategy == 'shallow':
                merged_config = self._shallow_merge_configs([c['config'] for c in processed_configs])
            elif merge_strategy == 'override':
                merged_config = self._override_merge_configs([c['config'] for c in processed_configs])
            
            # 保存合并结果
            if output_path:
                output_format = self._detect_config_format(Path(output_path))
                converted_content = await self._convert_config_data(merged_config, output_format)
                
                write_result = await self.file_operations.write_file(output_path, converted_content)
                if not write_result.get('success'):
                    raise Exception(f"Failed to write merged config: {write_result.get('error')}")
            
            return {
                'status': 'success',
                'merged_config': merged_config,
                'source_configs': processed_configs,
                'output_path': output_path,
                'merge_strategy': merge_strategy,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Config merge failed: {str(e)}")
            return {
                'status': 'error',
                'config_paths': config_paths,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    async def backup_config(self, config_path: str,
                          backup_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        备份配置文件

        Args:
            config_path (str): 配置文件路径
            backup_dir (str, optional): 备份目录

        Returns:
            Dict[str, Any]: 备份结果
        """
        try:
            config_path_obj = Path(config_path)

            if backup_dir is None:
                backup_dir = self.workspace_path / "backups" / "configs"
            else:
                backup_dir = Path(backup_dir)

            # 确保备份目录存在
            backup_dir.mkdir(parents=True, exist_ok=True)

            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{config_path_obj.stem}_{timestamp}{config_path_obj.suffix}"
            backup_path = backup_dir / backup_filename

            # 复制文件
            shutil.copy2(config_path, backup_path)

            self.processing_stats['backups_created'] += 1

            return {
                'status': 'success',
                'original_path': config_path,
                'backup_path': str(backup_path),
                'backup_size': backup_path.stat().st_size,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Config backup failed: {str(e)}")
            return {
                'status': 'error',
                'config_path': config_path,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    async def validate_config_file(self, config_path: str,
                                 schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证配置文件

        Args:
            config_path (str): 配置文件路径
            schema (Dict[str, Any]): 验证模式

        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 处理配置文件
            result = await self.process_config(config_path, validate_schema=schema)

            if result['status'] != 'success':
                return result

            validation_result = result.get('validation', {})

            return {
                'status': 'success',
                'file_path': config_path,
                'validation': validation_result,
                'is_valid': validation_result.get('is_valid', False),
                'errors': validation_result.get('errors', []),
                'warnings': validation_result.get('warnings', []),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Config validation failed: {str(e)}")
            return {
                'status': 'error',
                'file_path': config_path,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            **self.processing_stats,
            'supported_formats': list(self.supported_formats.values()),
            'available_libraries': {
                'yaml': YAML_AVAILABLE,
                'toml': TOML_AVAILABLE
            }
        }

    def reset_stats(self) -> None:
        """重置处理统计"""
        self.processing_stats = {
            "total_processed": 0,
            "successful_processed": 0,
            "failed_processed": 0,
            "formats_processed": {},
            "last_processed": None,
            "validations_performed": 0,
            "conversions_performed": 0,
            "backups_created": 0
        }
        logger.info("ConfigProcessor stats reset")

    def _detect_config_format(self, file_path: Path) -> str:
        """
        检测配置文件格式

        Args:
            file_path (Path): 文件路径

        Returns:
            str: 配置格式
        """
        suffix = file_path.suffix.lower()
        return self.supported_formats.get(suffix, 'json')

    async def _parse_config_content(self, content: str, config_format: str) -> Dict[str, Any]:
        """
        解析配置内容

        Args:
            content (str): 配置内容
            config_format (str): 配置格式

        Returns:
            Dict[str, Any]: 解析结果
        """
        try:
            if config_format == 'json':
                return json.loads(content)

            elif config_format == 'yaml':
                if not YAML_AVAILABLE:
                    raise ImportError("PyYAML is required for YAML support")
                return yaml.safe_load(content)

            elif config_format == 'ini':
                config = configparser.ConfigParser()
                config.read_string(content)

                # 转换为字典格式
                result = {}
                for section_name in config.sections():
                    result[section_name] = dict(config[section_name])

                return result

            elif config_format == 'toml':
                if not TOML_AVAILABLE:
                    raise ImportError("toml library is required for TOML support")
                return toml.loads(content)

            else:
                raise ValueError(f"Unsupported config format: {config_format}")

        except Exception as e:
            raise ValueError(f"Failed to parse {config_format} config: {str(e)}")

    def _replace_environment_variables(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        替换配置中的环境变量

        Args:
            config (Dict[str, Any]): 配置字典

        Returns:
            Dict[str, Any]: 替换后的配置
        """
        def replace_in_value(value):
            if isinstance(value, str):
                def replace_var(match):
                    var_name = match.group(1)
                    return os.getenv(var_name, match.group(0))

                return self.env_var_pattern.sub(replace_var, value)
            elif isinstance(value, dict):
                return {k: replace_in_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [replace_in_value(item) for item in value]
            else:
                return value

        return replace_in_value(config)

    async def _validate_config(self, config: Dict[str, Any],
                             schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证配置

        Args:
            config (Dict[str, Any]): 配置数据
            schema (Dict[str, Any]): 验证模式

        Returns:
            Dict[str, Any]: 验证结果
        """
        errors = []
        warnings = []

        try:
            # 检查必需字段
            required_fields = schema.get('required', [])
            for field in required_fields:
                if not self._check_nested_key(config, field):
                    errors.append(f"Required field missing: {field}")

            # 检查字段类型
            field_types = schema.get('types', {})
            for field, expected_type in field_types.items():
                value = self._get_nested_value(config, field)
                if value is not None and not isinstance(value, expected_type):
                    errors.append(f"Field {field} should be {expected_type.__name__}, got {type(value).__name__}")

            # 检查值范围
            value_ranges = schema.get('ranges', {})
            for field, range_config in value_ranges.items():
                value = self._get_nested_value(config, field)
                if value is not None:
                    if 'min' in range_config and value < range_config['min']:
                        errors.append(f"Field {field} value {value} is below minimum {range_config['min']}")
                    if 'max' in range_config and value > range_config['max']:
                        errors.append(f"Field {field} value {value} is above maximum {range_config['max']}")

            # 检查枚举值
            enum_values = schema.get('enums', {})
            for field, allowed_values in enum_values.items():
                value = self._get_nested_value(config, field)
                if value is not None and value not in allowed_values:
                    errors.append(f"Field {field} value '{value}' not in allowed values: {allowed_values}")

            # 检查自定义验证规则
            custom_rules = schema.get('custom_rules', [])
            for rule in custom_rules:
                rule_result = self._apply_custom_rule(config, rule)
                if not rule_result['valid']:
                    if rule_result['severity'] == 'error':
                        errors.append(rule_result['message'])
                    else:
                        warnings.append(rule_result['message'])

            self.processing_stats['validations_performed'] += 1

            return {
                'is_valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'fields_checked': len(required_fields) + len(field_types) + len(value_ranges) + len(enum_values),
                'custom_rules_applied': len(custom_rules)
            }

        except Exception as e:
            logger.error(f"Config validation error: {str(e)}")
            return {
                'is_valid': False,
                'errors': [f"Validation error: {str(e)}"],
                'warnings': [],
                'fields_checked': 0,
                'custom_rules_applied': 0
            }

    async def _convert_config_data(self, config: Dict[str, Any],
                                 target_format: str,
                                 preserve_comments: bool = False) -> str:
        """
        转换配置数据格式

        Args:
            config (Dict[str, Any]): 配置数据
            target_format (str): 目标格式
            preserve_comments (bool): 是否保留注释

        Returns:
            str: 转换后的内容
        """
        try:
            if target_format == 'json':
                return json.dumps(config, indent=2, ensure_ascii=False)

            elif target_format == 'yaml':
                if not YAML_AVAILABLE:
                    raise ImportError("PyYAML is required for YAML support")
                return yaml.dump(config, default_flow_style=False, allow_unicode=True, indent=2)

            elif target_format == 'ini':
                config_parser = configparser.ConfigParser()

                # 转换字典为INI格式
                for section_name, section_data in config.items():
                    if isinstance(section_data, dict):
                        config_parser.add_section(section_name)
                        for key, value in section_data.items():
                            config_parser.set(section_name, key, str(value))

                # 写入字符串
                from io import StringIO
                output = StringIO()
                config_parser.write(output)
                return output.getvalue()

            elif target_format == 'toml':
                if not TOML_AVAILABLE:
                    raise ImportError("toml library is required for TOML support")
                return toml.dumps(config)

            else:
                raise ValueError(f"Unsupported target format: {target_format}")

        except Exception as e:
            raise ValueError(f"Failed to convert to {target_format}: {str(e)}")

    def _deep_merge_configs(self, configs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """深度合并配置"""
        def deep_merge(dict1, dict2):
            result = dict1.copy()
            for key, value in dict2.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result

        merged = {}
        for config in configs:
            merged = deep_merge(merged, config)
        return merged

    def _shallow_merge_configs(self, configs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """浅合并配置"""
        merged = {}
        for config in configs:
            merged.update(config)
        return merged

    def _override_merge_configs(self, configs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """覆盖合并配置（后面的完全覆盖前面的）"""
        if not configs:
            return {}
        return configs[-1].copy()

    def _check_nested_key(self, config: Dict[str, Any], key_path: str) -> bool:
        """检查嵌套键是否存在"""
        keys = key_path.split('.')
        current = config

        for key in keys:
            if not isinstance(current, dict) or key not in current:
                return False
            current = current[key]

        return True

    def _get_nested_value(self, config: Dict[str, Any], key_path: str) -> Any:
        """获取嵌套值"""
        keys = key_path.split('.')
        current = config

        for key in keys:
            if not isinstance(current, dict) or key not in current:
                return None
            current = current[key]

        return current

    def _apply_custom_rule(self, config: Dict[str, Any], rule: Dict[str, Any]) -> Dict[str, Any]:
        """应用自定义验证规则"""
        try:
            rule_type = rule.get('type', 'unknown')

            if rule_type == 'dependency':
                # 依赖检查：如果字段A存在，则字段B也必须存在
                field_a = rule.get('field_a')
                field_b = rule.get('field_b')

                if self._check_nested_key(config, field_a) and not self._check_nested_key(config, field_b):
                    return {
                        'valid': False,
                        'severity': rule.get('severity', 'error'),
                        'message': f"Field {field_b} is required when {field_a} is present"
                    }

            elif rule_type == 'mutual_exclusion':
                # 互斥检查：字段A和字段B不能同时存在
                field_a = rule.get('field_a')
                field_b = rule.get('field_b')

                if self._check_nested_key(config, field_a) and self._check_nested_key(config, field_b):
                    return {
                        'valid': False,
                        'severity': rule.get('severity', 'error'),
                        'message': f"Fields {field_a} and {field_b} are mutually exclusive"
                    }

            return {'valid': True, 'severity': 'info', 'message': 'Rule passed'}

        except Exception as e:
            return {
                'valid': False,
                'severity': 'error',
                'message': f"Custom rule error: {str(e)}"
            }

    def _update_stats(self, status: str, format_type: Optional[str] = None) -> None:
        """更新处理统计"""
        self.processing_stats['total_processed'] += 1

        if status == 'successful':
            self.processing_stats['successful_processed'] += 1
        elif status == 'failed':
            self.processing_stats['failed_processed'] += 1

        if format_type:
            if format_type not in self.processing_stats['formats_processed']:
                self.processing_stats['formats_processed'][format_type] = 0
            self.processing_stats['formats_processed'][format_type] += 1

        self.processing_stats['last_processed'] = datetime.now().isoformat()
