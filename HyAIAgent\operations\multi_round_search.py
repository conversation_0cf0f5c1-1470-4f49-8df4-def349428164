"""
多轮搜索策略模块

实现智能的多轮搜索策略，支持：
- 渐进式搜索深化
- 查询优化和扩展
- 结果质量评估
- 搜索策略调整
- 上下文保持和传递

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import asyncio
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from operations.search_operations import SearchOperations, SearchResponse, SearchResult
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager
from core.kv_store import KVStore

logger = logging.getLogger(__name__)


class SearchStrategy(Enum):
    """搜索策略枚举"""
    PROGRESSIVE = "progressive"  # 渐进式搜索
    PARALLEL = "parallel"       # 并行搜索
    ADAPTIVE = "adaptive"       # 自适应搜索
    FOCUSED = "focused"         # 聚焦搜索
    EXPLORATORY = "exploratory" # 探索式搜索


class SearchQuality(Enum):
    """搜索质量等级"""
    EXCELLENT = "excellent"     # 优秀 (>= 0.9)
    GOOD = "good"              # 良好 (>= 0.7)
    FAIR = "fair"              # 一般 (>= 0.5)
    POOR = "poor"              # 较差 (>= 0.3)
    VERY_POOR = "very_poor"    # 很差 (< 0.3)


@dataclass
class SearchRound:
    """搜索轮次数据模型"""
    round_id: int
    query: str
    strategy: SearchStrategy
    search_depth: str
    max_results: int
    response: Optional[SearchResponse] = None
    quality_score: float = 0.0
    execution_time: float = 0.0
    timestamp: Optional[datetime] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.metadata is None:
            self.metadata = {}


@dataclass
class MultiRoundSearchResult:
    """多轮搜索结果数据模型"""
    session_id: str
    original_query: str
    total_rounds: int
    search_rounds: List[SearchRound]
    final_results: List[SearchResult]
    overall_quality: float
    total_execution_time: float
    strategy_used: SearchStrategy
    success: bool
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        # 处理datetime对象
        for round_data in data['search_rounds']:
            if round_data['timestamp']:
                round_data['timestamp'] = round_data['timestamp'].isoformat()
            if round_data['response'] and round_data['response']['timestamp']:
                round_data['response']['timestamp'] = round_data['response']['timestamp'].isoformat()
        return data


class QueryOptimizer:
    """查询优化器"""
    
    def __init__(self):
        """初始化查询优化器"""
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'
        }
        
        self.expansion_patterns = {
            '技术': ['技术', '科技', '方法', '方案', '实现'],
            '方法': ['方法', '方式', '技巧', '策略', '手段'],
            '问题': ['问题', '困难', '挑战', '难题', '障碍'],
            '解决': ['解决', '处理', '应对', '克服', '解决方案'],
            '开发': ['开发', '研发', '构建', '创建', '实现'],
            '系统': ['系统', '平台', '框架', '架构', '工具']
        }
    
    def extract_keywords(self, query: str) -> List[str]:
        """提取查询关键词"""
        try:
            # 移除标点符号，保留中文字符
            cleaned_query = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', query)

            # 分词 - 对中文和英文分别处理
            words = []

            # 提取英文单词
            english_words = re.findall(r'[a-zA-Z]+', cleaned_query)
            words.extend(english_words)

            # 提取中文词组（简单按字符分割）
            chinese_text = re.sub(r'[a-zA-Z\s]+', '', cleaned_query)
            if chinese_text:
                # 简单的中文分词：按常见词汇模式分割
                chinese_words = []
                for i in range(len(chinese_text)):
                    for j in range(i+2, min(i+5, len(chinese_text)+1)):  # 2-4字词
                        word = chinese_text[i:j]
                        if len(word) >= 2:
                            chinese_words.append(word)
                words.extend(chinese_words)

            # 过滤停用词和短词
            keywords = [
                word.lower() for word in words
                if len(word) > 1 and word.lower() not in self.stop_words
            ]

            # 去重并限制数量
            unique_keywords = list(set(keywords))
            return unique_keywords[:10]  # 限制最多10个关键词

        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return [query]
    
    def expand_query(self, query: str, expansion_type: str = "synonyms") -> List[str]:
        """扩展查询"""
        try:
            expanded_queries = [query]  # 包含原始查询
            
            if expansion_type == "synonyms":
                # 同义词扩展
                for keyword, synonyms in self.expansion_patterns.items():
                    if keyword in query:
                        for synonym in synonyms:
                            if synonym != keyword:
                                expanded_query = query.replace(keyword, synonym)
                                if expanded_query not in expanded_queries:
                                    expanded_queries.append(expanded_query)
            
            elif expansion_type == "related":
                # 相关词扩展
                keywords = self.extract_keywords(query)
                for keyword in keywords[:3]:  # 限制扩展数量
                    expanded_queries.append(f"{query} {keyword}")
            
            elif expansion_type == "specific":
                # 具体化扩展
                expanded_queries.extend([
                    f"{query} 详细介绍",
                    f"{query} 实现方法",
                    f"{query} 最佳实践"
                ])
            
            return expanded_queries[:5]  # 限制最大扩展数量
            
        except Exception as e:
            logger.error(f"查询扩展失败: {e}")
            return [query]
    
    def optimize_query(self, query: str, previous_results: List[SearchResult]) -> str:
        """基于之前结果优化查询"""
        try:
            if not previous_results:
                return query
            
            # 分析之前结果的关键词
            result_keywords = set()
            for result in previous_results:
                title_keywords = self.extract_keywords(result.title)
                content_keywords = self.extract_keywords(result.content[:200])  # 只取前200字符
                result_keywords.update(title_keywords[:3])  # 限制数量
                result_keywords.update(content_keywords[:3])
            
            # 选择最相关的关键词
            query_keywords = set(self.extract_keywords(query))
            relevant_keywords = result_keywords - query_keywords
            
            if relevant_keywords:
                # 添加最相关的关键词
                additional_keyword = list(relevant_keywords)[0]
                optimized_query = f"{query} {additional_keyword}"
                return optimized_query
            
            return query
            
        except Exception as e:
            logger.error(f"查询优化失败: {e}")
            return query


class ResultQualityEvaluator:
    """结果质量评估器"""
    
    def __init__(self):
        """初始化质量评估器"""
        self.quality_weights = {
            'relevance': 0.4,      # 相关性
            'completeness': 0.3,   # 完整性
            'freshness': 0.2,      # 时效性
            'authority': 0.1       # 权威性
        }
    
    def evaluate_relevance(self, query: str, results: List[SearchResult]) -> float:
        """评估结果相关性"""
        try:
            if not results:
                return 0.0
            
            query_keywords = set(re.findall(r'\w+', query.lower()))
            total_relevance = 0.0
            
            for result in results:
                # 计算标题相关性
                title_keywords = set(re.findall(r'\w+', result.title.lower()))
                title_overlap = len(query_keywords & title_keywords) / max(len(query_keywords), 1)
                
                # 计算内容相关性
                content_keywords = set(re.findall(r'\w+', result.content[:500].lower()))
                content_overlap = len(query_keywords & content_keywords) / max(len(query_keywords), 1)
                
                # 综合相关性
                result_relevance = (title_overlap * 0.7 + content_overlap * 0.3)
                total_relevance += result_relevance
            
            return min(total_relevance / len(results), 1.0)
            
        except Exception as e:
            logger.error(f"相关性评估失败: {e}")
            return 0.0
    
    def evaluate_completeness(self, results: List[SearchResult]) -> float:
        """评估结果完整性"""
        try:
            if not results:
                return 0.0
            
            # 基于结果数量和内容长度评估完整性
            result_count_score = min(len(results) / 10, 1.0)  # 理想结果数为10
            
            content_length_scores = []
            for result in results:
                content_length = len(result.content)
                # 理想内容长度为500-2000字符
                if 500 <= content_length <= 2000:
                    content_length_scores.append(1.0)
                elif content_length < 500:
                    content_length_scores.append(content_length / 500)
                else:
                    content_length_scores.append(max(0.5, 2000 / content_length))
            
            avg_content_score = sum(content_length_scores) / len(content_length_scores) if content_length_scores else 0.0
            
            return (result_count_score * 0.4 + avg_content_score * 0.6)
            
        except Exception as e:
            logger.error(f"完整性评估失败: {e}")
            return 0.0
    
    def evaluate_freshness(self, results: List[SearchResult]) -> float:
        """评估结果时效性"""
        try:
            if not results:
                return 0.0
            
            current_time = datetime.now()
            freshness_scores = []
            
            for result in results:
                if result.published_date:
                    try:
                        # 尝试解析发布日期
                        pub_date = datetime.fromisoformat(result.published_date.replace('Z', '+00:00'))
                        days_old = (current_time - pub_date).days
                        
                        # 计算时效性分数
                        if days_old <= 30:
                            freshness_scores.append(1.0)
                        elif days_old <= 90:
                            freshness_scores.append(0.8)
                        elif days_old <= 365:
                            freshness_scores.append(0.6)
                        else:
                            freshness_scores.append(0.3)
                    except:
                        freshness_scores.append(0.5)  # 无法解析日期时给中等分数
                else:
                    freshness_scores.append(0.5)  # 没有日期信息时给中等分数
            
            return sum(freshness_scores) / len(freshness_scores) if freshness_scores else 0.5
            
        except Exception as e:
            logger.error(f"时效性评估失败: {e}")
            return 0.5
    
    def evaluate_authority(self, results: List[SearchResult]) -> float:
        """评估结果权威性"""
        try:
            if not results:
                return 0.0
            
            authority_scores = []
            
            # 权威域名列表
            authoritative_domains = {
                'edu', 'gov', 'org', 'wikipedia.org', 'github.com',
                'stackoverflow.com', 'medium.com', 'arxiv.org'
            }
            
            for result in results:
                domain = result.url.split('/')[2] if '/' in result.url else result.url
                
                # 检查是否为权威域名
                is_authoritative = any(auth_domain in domain for auth_domain in authoritative_domains)
                
                if is_authoritative:
                    authority_scores.append(1.0)
                elif result.score >= 0.8:  # 高分结果认为有一定权威性
                    authority_scores.append(0.8)
                else:
                    authority_scores.append(0.5)
            
            return sum(authority_scores) / len(authority_scores) if authority_scores else 0.5
            
        except Exception as e:
            logger.error(f"权威性评估失败: {e}")
            return 0.5
    
    def evaluate_overall_quality(self, query: str, results: List[SearchResult]) -> float:
        """评估整体质量"""
        try:
            relevance_score = self.evaluate_relevance(query, results)
            completeness_score = self.evaluate_completeness(results)
            freshness_score = self.evaluate_freshness(results)
            authority_score = self.evaluate_authority(results)
            
            overall_score = (
                relevance_score * self.quality_weights['relevance'] +
                completeness_score * self.quality_weights['completeness'] +
                freshness_score * self.quality_weights['freshness'] +
                authority_score * self.quality_weights['authority']
            )
            
            return min(overall_score, 1.0)
            
        except Exception as e:
            logger.error(f"整体质量评估失败: {e}")
            return 0.0
    
    def get_quality_level(self, score: float) -> SearchQuality:
        """获取质量等级"""
        if score >= 0.9:
            return SearchQuality.EXCELLENT
        elif score >= 0.7:
            return SearchQuality.GOOD
        elif score >= 0.5:
            return SearchQuality.FAIR
        elif score >= 0.3:
            return SearchQuality.POOR
        else:
            return SearchQuality.VERY_POOR


class MultiRoundSearchManager:
    """多轮搜索管理器"""

    def __init__(self,
                 search_operations: SearchOperations,
                 config_manager: ConfigManager,
                 security_manager: Optional[SecurityManager] = None,
                 kv_store: Optional[KVStore] = None):
        """
        初始化多轮搜索管理器

        Args:
            search_operations: 搜索操作管理器
            config_manager: 配置管理器
            security_manager: 安全管理器
            kv_store: 键值存储
        """
        self.search_operations = search_operations
        self.config_manager = config_manager
        self.security_manager = security_manager
        self.kv_store = kv_store

        # 获取多轮搜索配置
        self.multi_search_config = self.config_manager.get("multi_round_search", {})

        # 初始化组件
        self.query_optimizer = QueryOptimizer()
        self.quality_evaluator = ResultQualityEvaluator()

        # 搜索参数配置
        self.max_rounds = self.multi_search_config.get("max_rounds", 5)
        self.quality_threshold = self.multi_search_config.get("quality_threshold", 0.7)
        self.min_results_per_round = self.multi_search_config.get("min_results_per_round", 3)
        self.max_results_per_round = self.multi_search_config.get("max_results_per_round", 10)

        # 搜索统计
        self.search_sessions = {}
        self.session_stats = {
            "total_sessions": 0,
            "successful_sessions": 0,
            "average_rounds": 0.0,
            "average_quality": 0.0,
            "total_execution_time": 0.0
        }

    def _generate_session_id(self) -> str:
        """生成会话ID"""
        import uuid
        return f"multi_search_{uuid.uuid4().hex[:8]}_{int(datetime.now().timestamp())}"

    async def _execute_search_round(self,
                                   round_id: int,
                                   query: str,
                                   strategy: SearchStrategy,
                                   previous_results: List[SearchResult] = None) -> SearchRound:
        """执行单轮搜索"""
        try:
            start_time = datetime.now()

            # 根据策略调整搜索参数
            search_params = self._get_strategy_params(strategy, round_id)

            # 优化查询
            if previous_results and round_id > 1:
                optimized_query = self.query_optimizer.optimize_query(query, previous_results)
            else:
                optimized_query = query

            # 执行搜索
            response = await self.search_operations.search_web(
                query=optimized_query,
                search_depth=search_params["search_depth"],
                max_results=search_params["max_results"],
                use_cache=search_params.get("use_cache", True)
            )

            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()

            # 评估质量
            quality_score = self.quality_evaluator.evaluate_overall_quality(optimized_query, response.results)

            # 创建搜索轮次对象
            search_round = SearchRound(
                round_id=round_id,
                query=optimized_query,
                strategy=strategy,
                search_depth=search_params["search_depth"],
                max_results=search_params["max_results"],
                response=response,
                quality_score=quality_score,
                execution_time=execution_time,
                timestamp=start_time,
                metadata={
                    "original_query": query,
                    "strategy_params": search_params,
                    "quality_level": self.quality_evaluator.get_quality_level(quality_score).value
                }
            )

            logger.info(f"搜索轮次{round_id}完成: 查询='{optimized_query}', 质量={quality_score:.3f}, 结果数={len(response.results)}")
            return search_round

        except Exception as e:
            logger.error(f"搜索轮次{round_id}执行失败: {e}")
            # 返回失败的搜索轮次
            return SearchRound(
                round_id=round_id,
                query=query,
                strategy=strategy,
                search_depth="basic",
                max_results=5,
                response=None,
                quality_score=0.0,
                execution_time=0.0,
                timestamp=datetime.now(),
                metadata={"error": str(e)}
            )

    def _get_strategy_params(self, strategy: SearchStrategy, round_id: int) -> Dict[str, Any]:
        """根据策略获取搜索参数"""
        base_params = {
            "search_depth": "basic",
            "max_results": 5,
            "use_cache": True
        }

        if strategy == SearchStrategy.PROGRESSIVE:
            # 渐进式：逐步增加搜索深度和结果数量
            if round_id == 1:
                base_params.update({"search_depth": "basic", "max_results": 5})
            elif round_id == 2:
                base_params.update({"search_depth": "advanced", "max_results": 8})
            else:
                base_params.update({"search_depth": "advanced", "max_results": 10})

        elif strategy == SearchStrategy.PARALLEL:
            # 并行式：使用不同的搜索深度
            base_params.update({
                "search_depth": "advanced" if round_id % 2 == 0 else "basic",
                "max_results": 8
            })

        elif strategy == SearchStrategy.ADAPTIVE:
            # 自适应：根据轮次动态调整
            base_params.update({
                "search_depth": "advanced" if round_id > 2 else "basic",
                "max_results": min(5 + round_id * 2, 10)
            })

        elif strategy == SearchStrategy.FOCUSED:
            # 聚焦式：使用高质量搜索
            base_params.update({"search_depth": "advanced", "max_results": 6})

        elif strategy == SearchStrategy.EXPLORATORY:
            # 探索式：使用更多结果
            base_params.update({"search_depth": "basic", "max_results": 12})

        return base_params

    def _should_continue_search(self,
                               search_rounds: List[SearchRound],
                               strategy: SearchStrategy) -> bool:
        """判断是否应该继续搜索"""
        try:
            # 检查轮次限制
            if len(search_rounds) >= self.max_rounds:
                return False

            # 检查最后一轮的质量
            if search_rounds:
                last_round = search_rounds[-1]

                # 如果质量已经很好，可以停止
                if last_round.quality_score >= self.quality_threshold:
                    return False

                # 如果连续两轮质量没有提升，停止搜索
                if len(search_rounds) >= 2:
                    prev_round = search_rounds[-2]
                    if (last_round.quality_score <= prev_round.quality_score and
                        last_round.quality_score < 0.5):
                        return False

                # 如果最后一轮没有结果，停止搜索
                if not last_round.response or not last_round.response.results:
                    return False

            return True

        except Exception as e:
            logger.error(f"搜索继续判断失败: {e}")
            return False

    def _merge_results(self, search_rounds: List[SearchRound]) -> List[SearchResult]:
        """合并多轮搜索结果"""
        try:
            all_results = []
            seen_urls = set()

            # 按质量分数排序轮次
            sorted_rounds = sorted(
                [r for r in search_rounds if r.response and r.response.results],
                key=lambda x: x.quality_score,
                reverse=True
            )

            for search_round in sorted_rounds:
                for result in search_round.response.results:
                    # 去重
                    if result.url not in seen_urls:
                        seen_urls.add(result.url)
                        # 调整分数（考虑轮次质量）
                        adjusted_result = SearchResult(
                            title=result.title,
                            url=result.url,
                            content=result.content,
                            score=result.score * search_round.quality_score,
                            published_date=result.published_date,
                            source=result.source
                        )
                        all_results.append(adjusted_result)

            # 按调整后的分数排序
            all_results.sort(key=lambda x: x.score, reverse=True)

            # 限制最终结果数量
            max_final_results = self.multi_search_config.get("max_final_results", 20)
            return all_results[:max_final_results]

        except Exception as e:
            logger.error(f"结果合并失败: {e}")
            return []

    async def execute_multi_round_search(self,
                                       query: str,
                                       strategy: SearchStrategy = SearchStrategy.ADAPTIVE,
                                       max_rounds: Optional[int] = None,
                                       quality_threshold: Optional[float] = None) -> MultiRoundSearchResult:
        """
        执行多轮搜索

        Args:
            query: 搜索查询
            strategy: 搜索策略
            max_rounds: 最大搜索轮次
            quality_threshold: 质量阈值

        Returns:
            MultiRoundSearchResult: 多轮搜索结果
        """
        session_id = self._generate_session_id()
        start_time = datetime.now()

        # 更新统计
        self.session_stats["total_sessions"] += 1

        try:
            logger.info(f"开始多轮搜索: 会话ID={session_id}, 查询='{query}', 策略={strategy.value}")

            # 使用传入的参数或默认配置
            max_rounds = max_rounds or self.max_rounds
            quality_threshold = quality_threshold or self.quality_threshold

            # 安全检查
            if self.security_manager:
                # 检查查询内容是否包含危险字符
                dangerous_chars = ['../', '\\', '<script>', '<iframe>', 'javascript:']
                if any(char in query.lower() for char in dangerous_chars):
                    raise ValueError("查询内容包含不安全字符")

            search_rounds = []
            previous_results = []

            # 执行多轮搜索
            for round_id in range(1, max_rounds + 1):
                logger.info(f"执行搜索轮次 {round_id}/{max_rounds}")

                # 执行当前轮次搜索
                search_round = await self._execute_search_round(
                    round_id=round_id,
                    query=query,
                    strategy=strategy,
                    previous_results=previous_results
                )

                search_rounds.append(search_round)

                # 更新之前的结果
                if search_round.response and search_round.response.results:
                    previous_results.extend(search_round.response.results)

                # 判断是否继续搜索
                if not self._should_continue_search(search_rounds, strategy):
                    logger.info(f"搜索提前结束于轮次 {round_id}")
                    break

                # 轮次间延迟
                if round_id < max_rounds:
                    await asyncio.sleep(0.5)  # 避免请求过于频繁

            # 合并最终结果
            final_results = self._merge_results(search_rounds)

            # 计算整体质量
            overall_quality = 0.0
            if search_rounds:
                quality_scores = [r.quality_score for r in search_rounds if r.quality_score > 0]
                if quality_scores:
                    overall_quality = sum(quality_scores) / len(quality_scores)

            # 计算总执行时间
            total_execution_time = (datetime.now() - start_time).total_seconds()

            # 判断搜索是否成功
            search_success = len(final_results) > 0 or any(r.response and r.response.results for r in search_rounds)

            # 创建结果对象
            result = MultiRoundSearchResult(
                session_id=session_id,
                original_query=query,
                total_rounds=len(search_rounds),
                search_rounds=search_rounds,
                final_results=final_results,
                overall_quality=overall_quality,
                total_execution_time=total_execution_time,
                strategy_used=strategy,
                success=search_success,
                metadata={
                    "max_rounds_configured": max_rounds,
                    "quality_threshold": quality_threshold,
                    "final_result_count": len(final_results),
                    "best_round_quality": max([r.quality_score for r in search_rounds], default=0.0),
                    "quality_level": self.quality_evaluator.get_quality_level(overall_quality).value
                }
            )

            # 缓存搜索会话
            self.search_sessions[session_id] = result

            # 更新统计
            self.session_stats["successful_sessions"] += 1
            self.session_stats["total_execution_time"] += total_execution_time
            self._update_session_stats()

            logger.info(f"多轮搜索完成: 会话ID={session_id}, 轮次={len(search_rounds)}, "
                       f"质量={overall_quality:.3f}, 结果数={len(final_results)}")

            return result

        except Exception as e:
            logger.error(f"多轮搜索失败: 会话ID={session_id}, 错误={str(e)}")

            # 返回失败结果
            total_execution_time = (datetime.now() - start_time).total_seconds()
            return MultiRoundSearchResult(
                session_id=session_id,
                original_query=query,
                total_rounds=0,
                search_rounds=[],
                final_results=[],
                overall_quality=0.0,
                total_execution_time=total_execution_time,
                strategy_used=strategy,
                success=False,
                error_message=str(e),
                metadata={"error_type": type(e).__name__}
            )

    def _update_session_stats(self):
        """更新会话统计"""
        try:
            if self.session_stats["successful_sessions"] > 0:
                # 计算平均轮次
                total_rounds = sum(
                    session.total_rounds
                    for session in self.search_sessions.values()
                    if session.success
                )
                self.session_stats["average_rounds"] = total_rounds / self.session_stats["successful_sessions"]

                # 计算平均质量
                total_quality = sum(
                    session.overall_quality
                    for session in self.search_sessions.values()
                    if session.success
                )
                self.session_stats["average_quality"] = total_quality / self.session_stats["successful_sessions"]

        except Exception as e:
            logger.error(f"统计更新失败: {e}")

    async def get_session_result(self, session_id: str) -> Optional[MultiRoundSearchResult]:
        """获取会话结果"""
        return self.search_sessions.get(session_id)

    async def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计"""
        return self.session_stats.copy()

    async def clear_session_cache(self, max_age_hours: int = 24):
        """清理过期的会话缓存"""
        try:
            current_time = datetime.now()
            expired_sessions = []

            for session_id, result in self.search_sessions.items():
                # 检查最后一轮的时间戳
                if result.search_rounds:
                    last_round_time = result.search_rounds[-1].timestamp
                    if last_round_time and (current_time - last_round_time).total_seconds() > max_age_hours * 3600:
                        expired_sessions.append(session_id)

            # 删除过期会话
            for session_id in expired_sessions:
                del self.search_sessions[session_id]

            logger.info(f"清理了 {len(expired_sessions)} 个过期搜索会话")

        except Exception as e:
            logger.error(f"会话缓存清理失败: {e}")

    async def close(self):
        """关闭多轮搜索管理器"""
        try:
            # 清理所有会话缓存
            self.search_sessions.clear()
            logger.info("多轮搜索管理器已关闭")
        except Exception as e:
            logger.error(f"关闭多轮搜索管理器失败: {e}")
