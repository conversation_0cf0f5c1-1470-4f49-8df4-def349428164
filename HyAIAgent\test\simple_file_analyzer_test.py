"""
简化的文件分析器测试

用于验证FileAnalyzer的基本功能，避免复杂的依赖问题
"""

import asyncio
import tempfile
import os
from pathlib import Path

# 添加项目根目录到Python路径
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from operations.file_analyzer import FileAnalyzer
from operations.security_manager import SecurityManager
from operations.file_operations import FileOperations


async def test_file_analyzer_basic():
    """测试FileAnalyzer的基本功能"""
    print("🚀 开始测试FileAnalyzer基本功能...")
    
    # 创建临时工作目录
    with tempfile.TemporaryDirectory() as temp_dir:
        workspace_path = Path(temp_dir) / "test_workspace"
        workspace_path.mkdir(exist_ok=True)
        
        # 创建测试文件
        test_file = workspace_path / "test.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文本文件。\n包含中文和English内容。\n用于测试文件分析功能。")
        
        print(f"✅ 创建测试文件: {test_file}")
        
        try:
            # 初始化组件
            security_manager = SecurityManager(str(workspace_path))
            file_operations = FileOperations(str(workspace_path), security_manager)
            
            # 创建FileAnalyzer，只传递必要的参数
            file_analyzer = FileAnalyzer(
                workspace_path=str(workspace_path),
                security_manager=security_manager,
                file_operations=file_operations
            )
            
            print("✅ FileAnalyzer初始化成功")
            
            # 测试基础分析
            print("🔍 开始基础文件分析...")
            result = await file_analyzer.analyze_file("test.txt", ["basic"])
            
            if result['success']:
                print("✅ 基础分析成功")
                print(f"   文件路径: {result['file_path']}")
                print(f"   分析类型: {list(result['analyses'].keys())}")
                
                if 'basic' in result['analyses']:
                    basic = result['analyses']['basic']
                    if basic['success']:
                        print(f"   文件名: {basic['file_name']}")
                        print(f"   文件大小: {basic['file_size']} bytes")
                        print(f"   文件扩展名: {basic['file_extension']}")
                        print(f"   是否为文本文件: {basic['is_text_file']}")
                    else:
                        print(f"❌ 基础分析失败: {basic.get('error', '未知错误')}")
            else:
                print(f"❌ 文件分析失败: {result.get('error', '未知错误')}")
                return False
            
            # 测试统计信息
            print("📊 获取分析统计信息...")
            stats = file_analyzer.get_analysis_stats()
            print(f"   总分析次数: {stats['total_analyzed']}")
            print(f"   成功分析次数: {stats['successful_analyses']}")
            print(f"   失败分析次数: {stats['failed_analyses']}")
            print(f"   成功率: {stats['success_rate']:.2%}")
            
            # 测试重置统计
            print("🔄 重置统计信息...")
            file_analyzer.reset_stats()
            stats_after_reset = file_analyzer.get_analysis_stats()
            if stats_after_reset['total_analyzed'] == 0:
                print("✅ 统计信息重置成功")
            else:
                print("❌ 统计信息重置失败")
                return False
            
            print("🎉 所有基础测试通过！")
            return True
            
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False


async def test_file_analyzer_error_handling():
    """测试FileAnalyzer的错误处理"""
    print("\n🚀 开始测试FileAnalyzer错误处理...")
    
    # 创建临时工作目录
    with tempfile.TemporaryDirectory() as temp_dir:
        workspace_path = Path(temp_dir) / "test_workspace"
        workspace_path.mkdir(exist_ok=True)
        
        try:
            # 初始化组件
            security_manager = SecurityManager(str(workspace_path))
            file_operations = FileOperations(str(workspace_path), security_manager)
            file_analyzer = FileAnalyzer(
                workspace_path=str(workspace_path),
                security_manager=security_manager,
                file_operations=file_operations
            )
            
            # 测试不存在的文件
            print("🔍 测试分析不存在的文件...")
            result = await file_analyzer.analyze_file("nonexistent.txt")
            
            if not result['success'] and ("文件不存在" in result['error'] or "Permission denied" in result['error'] or "安全验证失败" in result['error']):
                print("✅ 不存在文件错误处理正确")
            else:
                print(f"❌ 不存在文件错误处理失败: {result}")
                return False
            
            # 测试不安全的路径
            print("🔍 测试分析不安全的路径...")
            result = await file_analyzer.analyze_file("../outside.txt")
            
            if not result['success'] and "安全验证失败" in result['error']:
                print("✅ 不安全路径错误处理正确")
            else:
                print(f"❌ 不安全路径错误处理失败: {result}")
                return False
            
            print("🎉 错误处理测试通过！")
            return True
            
        except Exception as e:
            print(f"❌ 错误处理测试中发生异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return False


async def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 FileAnalyzer 简化测试套件")
    print("=" * 60)
    
    # 运行基础功能测试
    basic_test_passed = await test_file_analyzer_basic()
    
    # 运行错误处理测试
    error_test_passed = await test_file_analyzer_error_handling()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    print(f"基础功能测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"错误处理测试: {'✅ 通过' if error_test_passed else '❌ 失败'}")
    
    if basic_test_passed and error_test_passed:
        print("\n🎉 所有测试通过！FileAnalyzer功能正常。")
        return True
    else:
        print("\n❌ 部分测试失败，需要检查FileAnalyzer实现。")
        return False


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    exit(0 if success else 1)
