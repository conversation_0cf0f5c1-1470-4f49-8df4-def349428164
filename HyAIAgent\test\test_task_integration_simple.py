"""
任务系统集成简化测试模块

测试文件操作功能与任务管理系统的集成。
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from datetime import datetime, timedelta

from operations.task_integration import FileOperationTaskExecutor, TaskSystemIntegrator
from core.task_models import Task, TaskType, TaskPriority, TaskStatus


@pytest.mark.asyncio
async def test_file_operation_task_executor_basic():
    """测试文件操作任务执行器基本功能"""
    # 创建临时工作目录
    temp_dir = tempfile.mkdtemp()
    workspace = Path(temp_dir) / "test_workspace"
    workspace.mkdir(exist_ok=True)
    
    try:
        # 创建测试文件
        test_file = workspace / "test.txt"
        test_file.write_text("Hello World", encoding='utf-8')
        
        # 初始化任务执行器
        executor = FileOperationTaskExecutor(str(workspace))
        
        # 测试文件读取任务
        read_task = Task(
            description="Read test file",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.NORMAL,
            input_data={
                "operation_type": "file_read",
                "file_path": "test.txt"
            }
        )
        
        result = await executor.execute_task(read_task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.success is True
        assert result.result_data["success"] is True
        assert "Hello World" in result.result_data["content"]
        assert result.error_message is None
        
        print("✅ 文件读取任务测试通过")
        
        # 测试文件写入任务
        write_task = Task(
            description="Write new file",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.NORMAL,
            input_data={
                "operation_type": "file_write",
                "file_path": "new_file.txt",
                "content": "New content"
            }
        )
        
        result = await executor.execute_task(write_task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.success is True
        assert result.result_data["success"] is True
        
        # 验证文件确实被创建
        new_file = workspace / "new_file.txt"
        assert new_file.exists()
        assert new_file.read_text(encoding='utf-8') == "New content"
        
        print("✅ 文件写入任务测试通过")
        
        # 测试文件列表任务
        list_task = Task(
            description="List files",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.NORMAL,
            input_data={
                "operation_type": "file_list",
                "directory": "",
                "pattern": "*.txt"
            }
        )
        
        result = await executor.execute_task(list_task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.success is True
        assert result.result_data["success"] is True
        assert len(result.result_data["files"]) >= 2  # test.txt 和 new_file.txt
        
        print("✅ 文件列表任务测试通过")
        
        # 测试无效操作
        invalid_task = Task(
            description="Invalid operation",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.NORMAL,
            input_data={
                "operation_type": "invalid_operation"
            }
        )
        
        result = await executor.execute_task(invalid_task)
        
        # 验证结果
        assert result.status == TaskStatus.FAILED
        assert result.success is False
        assert result.error_message is not None
        assert "Unsupported operation type" in result.error_message
        
        print("✅ 无效操作错误处理测试通过")
        
    finally:
        # 清理
        shutil.rmtree(temp_dir)


@pytest.mark.asyncio
async def test_batch_operations():
    """测试批量操作功能"""
    # 创建临时工作目录
    temp_dir = tempfile.mkdtemp()
    workspace = Path(temp_dir) / "test_workspace"
    workspace.mkdir(exist_ok=True)
    
    try:
        # 创建多个测试文件
        for i in range(3):
            test_file = workspace / f"test{i+1}.txt"
            test_file.write_text(f"Content {i+1}", encoding='utf-8')
        
        # 初始化任务执行器
        executor = FileOperationTaskExecutor(str(workspace))
        
        # 测试批量校验和计算
        checksum_task = Task(
            description="Calculate checksums",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.NORMAL,
            input_data={
                "operation_type": "batch_checksum",
                "file_patterns": ["*.txt"],
                "algorithm": "md5"
            }
        )
        
        result = await executor.execute_task(checksum_task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.success is True
        assert result.result_data["success"] is True
        assert len(result.result_data["checksums"]) == 3

        # 验证校验和格式
        for checksum_info in result.result_data["checksums"]:
            assert len(checksum_info["checksum"]) == 32  # MD5长度
            assert checksum_info["algorithm"] == "md5"
        
        print("✅ 批量校验和计算测试通过")
        
        # 测试批量重命名
        rename_task = Task(
            description="Batch rename files",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.NORMAL,
            input_data={
                "operation_type": "batch_rename",
                "file_patterns": ["test*.txt"],
                "rename_rule": {
                    "pattern": "renamed_{name}",
                    "preserve_extension": True
                }
            }
        )
        
        result = await executor.execute_task(rename_task)
        
        # 验证结果
        assert result.status == TaskStatus.COMPLETED
        assert result.success is True
        assert result.result_data["success"] is True
        assert result.result_data["stats"]["successful_operations"] == 3
        
        # 验证文件确实被重命名
        renamed_files = list(workspace.glob("renamed_*.txt"))
        assert len(renamed_files) == 3
        
        print("✅ 批量重命名测试通过")
        
    finally:
        # 清理
        shutil.rmtree(temp_dir)


def test_task_system_integrator_basic():
    """测试任务系统集成器基本功能"""
    # 创建临时工作目录
    temp_dir = tempfile.mkdtemp()
    workspace = Path(temp_dir) / "test_workspace"
    workspace.mkdir(exist_ok=True)
    
    try:
        # 创建模拟的任务管理器和执行引擎
        from unittest.mock import Mock
        
        mock_ai_client = Mock()
        mock_prompt_manager = Mock()
        mock_kv_store = Mock()
        
        from core.task_manager import TaskManager
        from core.execution_engine import ExecutionEngine
        
        task_manager = TaskManager(mock_ai_client, mock_prompt_manager, mock_kv_store)
        execution_engine = ExecutionEngine(mock_ai_client, mock_prompt_manager)
        
        # 创建集成器
        integrator = TaskSystemIntegrator(task_manager, execution_engine, str(workspace))
        
        # 测试获取支持的操作
        operations = integrator.get_supported_operations()
        
        # 验证操作列表
        assert len(operations) > 0
        
        # 检查特定操作
        operation_types = [op["operation_type"] for op in operations]
        assert "file_read" in operation_types
        assert "file_write" in operation_types
        assert "batch_rename" in operation_types
        assert "document_extract" in operation_types
        
        # 检查操作分类
        categories = set(op["category"] for op in operations)
        assert "基础文件操作" in categories
        assert "批量文件处理" in categories
        assert "文档处理" in categories
        
        print("✅ 任务系统集成器基本功能测试通过")
        
    finally:
        # 清理
        shutil.rmtree(temp_dir)


@pytest.mark.asyncio
async def test_create_file_operation_task():
    """测试创建文件操作任务"""
    # 创建临时工作目录
    temp_dir = tempfile.mkdtemp()
    workspace = Path(temp_dir) / "test_workspace"
    workspace.mkdir(exist_ok=True)
    
    try:
        # 创建模拟的任务管理器和执行引擎
        from unittest.mock import Mock
        
        mock_ai_client = Mock()
        mock_prompt_manager = Mock()
        mock_kv_store = Mock()
        
        from core.task_manager import TaskManager
        from core.execution_engine import ExecutionEngine
        
        task_manager = TaskManager(mock_ai_client, mock_prompt_manager, mock_kv_store)
        execution_engine = ExecutionEngine(mock_ai_client, mock_prompt_manager)
        
        # 创建集成器
        integrator = TaskSystemIntegrator(task_manager, execution_engine, str(workspace))
        
        # 创建文件操作任务
        task = await integrator.create_file_operation_task(
            operation_type="file_read",
            parameters={"file_path": "test.txt"},
            description="Read test file",
            priority="HIGH"
        )
        
        # 验证任务
        assert task.task_type == TaskType.FILE_OPERATION
        assert task.priority == TaskPriority.HIGH
        assert task.input_data["operation_type"] == "file_read"
        assert task.input_data["file_path"] == "test.txt"
        assert "Read test file" in task.description
        
        print("✅ 创建文件操作任务测试通过")
        
    finally:
        # 清理
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    # 运行基本测试
    asyncio.run(test_file_operation_task_executor_basic())
    asyncio.run(test_batch_operations())
    test_task_system_integrator_basic()
    asyncio.run(test_create_file_operation_task())
    
    print("\n🎉 所有任务系统集成测试通过！")
