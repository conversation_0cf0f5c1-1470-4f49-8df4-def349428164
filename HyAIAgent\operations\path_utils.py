"""
路径工具模块
提供跨平台的路径处理和安全检查工具
"""

import os
import re
from pathlib import Path, PurePath
from typing import List, Optional, Union, Tuple
import logging

logger = logging.getLogger(__name__)


class PathUtils:
    """
    路径处理工具类
    
    提供以下功能：
    - 路径规范化和清理
    - 相对路径和绝对路径转换
    - 路径安全检查
    - 跨平台路径处理
    - 路径遍历攻击防护
    """
    
    # 危险路径模式
    DANGEROUS_PATTERNS = [
        r'\.\.[\\/]',  # 路径遍历
        r'[\\/]\.\.[\\/]',  # 中间路径遍历
        r'[\\/]\.\.$',  # 结尾路径遍历
        r'^\.\.[\\/]',  # 开头路径遍历
        r'~[\\/]',  # 用户目录
        r'[\\/]~[\\/]',  # 中间用户目录
    ]
    
    # 系统敏感路径
    SENSITIVE_PATHS = [
        '/etc', '/bin', '/sbin', '/usr/bin', '/usr/sbin',
        'C:\\Windows', 'C:\\Program Files', 'C:\\Program Files (x86)',
        '/System', '/Library', '/Applications'
    ]
    
    @staticmethod
    def normalize_path(path: Union[str, Path]) -> str:
        """
        规范化路径

        Args:
            path (Union[str, Path]): 输入路径

        Returns:
            str: 规范化后的路径
        """
        if not path:
            return ""

        try:
            # 转换为Path对象
            path_obj = Path(path)

            # 如果是相对路径，先转为绝对路径
            if not path_obj.is_absolute():
                path_obj = path_obj.resolve()
            else:
                # 如果已经是绝对路径，直接规范化
                path_obj = path_obj.resolve()

            return str(path_obj)
        except Exception as e:
            logger.warning(f"Failed to normalize path {path}: {e}")
            # 如果失败，尝试返回绝对路径
            try:
                return str(Path(path).absolute())
            except Exception:
                return str(path)
    
    @staticmethod
    def is_safe_path(path: Union[str, Path], base_path: Union[str, Path]) -> bool:
        """
        检查路径是否安全（防止路径遍历攻击）
        
        Args:
            path (Union[str, Path]): 要检查的路径
            base_path (Union[str, Path]): 基础路径（工作目录）
            
        Returns:
            bool: 路径是否安全
        """
        try:
            # 规范化路径
            abs_path = Path(path).resolve()
            abs_base = Path(base_path).resolve()
            
            # 检查是否在基础路径内
            try:
                abs_path.relative_to(abs_base)
                return True
            except ValueError:
                return False
                
        except Exception as e:
            logger.warning(f"Path safety check failed for {path}: {e}")
            return False
    
    @staticmethod
    def contains_dangerous_patterns(path: str) -> bool:
        """
        检查路径是否包含危险模式
        
        Args:
            path (str): 要检查的路径
            
        Returns:
            bool: 是否包含危险模式
        """
        path_str = str(path).replace('\\', '/')
        
        for pattern in PathUtils.DANGEROUS_PATTERNS:
            if re.search(pattern, path_str, re.IGNORECASE):
                return True
        
        return False
    
    @staticmethod
    def is_sensitive_path(path: Union[str, Path]) -> bool:
        """
        检查是否为系统敏感路径
        
        Args:
            path (Union[str, Path]): 要检查的路径
            
        Returns:
            bool: 是否为敏感路径
        """
        path_str = str(path).replace('\\', '/')
        
        for sensitive in PathUtils.SENSITIVE_PATHS:
            sensitive_norm = sensitive.replace('\\', '/')
            if path_str.lower().startswith(sensitive_norm.lower()):
                return True
        
        return False
    
    @staticmethod
    def get_relative_path(path: Union[str, Path], base_path: Union[str, Path]) -> str:
        """
        获取相对路径
        
        Args:
            path (Union[str, Path]): 目标路径
            base_path (Union[str, Path]): 基础路径
            
        Returns:
            str: 相对路径
        """
        try:
            abs_path = Path(path).resolve()
            abs_base = Path(base_path).resolve()
            
            relative = abs_path.relative_to(abs_base)
            return str(relative)
        except ValueError:
            # 如果无法计算相对路径，返回绝对路径
            return str(Path(path).resolve())
        except Exception as e:
            logger.warning(f"Failed to get relative path: {e}")
            return str(path)
    
    @staticmethod
    def join_paths(*paths: Union[str, Path]) -> str:
        """
        安全地连接路径
        
        Args:
            *paths: 要连接的路径组件
            
        Returns:
            str: 连接后的路径
        """
        if not paths:
            return ""
        
        try:
            result = Path(paths[0])
            for path in paths[1:]:
                if path:
                    result = result / path
            return str(result)
        except Exception as e:
            logger.warning(f"Failed to join paths {paths}: {e}")
            return str(paths[0]) if paths else ""
    
    @staticmethod
    def split_path(path: Union[str, Path]) -> Tuple[str, str]:
        """
        分割路径为目录和文件名
        
        Args:
            path (Union[str, Path]): 要分割的路径
            
        Returns:
            Tuple[str, str]: (目录, 文件名)
        """
        try:
            path_obj = Path(path)
            return str(path_obj.parent), path_obj.name
        except Exception as e:
            logger.warning(f"Failed to split path {path}: {e}")
            return str(path), ""
    
    @staticmethod
    def get_path_depth(path: Union[str, Path]) -> int:
        """
        获取路径深度
        
        Args:
            path (Union[str, Path]): 路径
            
        Returns:
            int: 路径深度
        """
        try:
            path_obj = Path(path)
            return len(path_obj.parts)
        except Exception:
            return 0
    
    @staticmethod
    def is_absolute_path(path: Union[str, Path]) -> bool:
        """
        检查是否为绝对路径
        
        Args:
            path (Union[str, Path]): 路径
            
        Returns:
            bool: 是否为绝对路径
        """
        try:
            return Path(path).is_absolute()
        except Exception:
            return False
    
    @staticmethod
    def ensure_directory_exists(path: Union[str, Path]) -> bool:
        """
        确保目录存在
        
        Args:
            path (Union[str, Path]): 目录路径
            
        Returns:
            bool: 是否成功创建或已存在
        """
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Failed to create directory {path}: {e}")
            return False
    
    @staticmethod
    def get_common_path(paths: List[Union[str, Path]]) -> str:
        """
        获取多个路径的公共父路径
        
        Args:
            paths (List[Union[str, Path]]): 路径列表
            
        Returns:
            str: 公共父路径
        """
        if not paths:
            return ""
        
        try:
            path_objects = [Path(p).resolve() for p in paths]
            
            if len(path_objects) == 1:
                return str(path_objects[0].parent)
            
            # 找到公共路径
            common_parts = []
            min_parts = min(len(p.parts) for p in path_objects)
            
            for i in range(min_parts):
                part = path_objects[0].parts[i]
                if all(p.parts[i] == part for p in path_objects):
                    common_parts.append(part)
                else:
                    break
            
            if common_parts:
                return str(Path(*common_parts))
            else:
                return ""
                
        except Exception as e:
            logger.warning(f"Failed to get common path: {e}")
            return ""
    
    @staticmethod
    def validate_filename(filename: str) -> bool:
        """
        验证文件名是否合法
        
        Args:
            filename (str): 文件名
            
        Returns:
            bool: 文件名是否合法
        """
        if not filename or filename.strip() == "":
            return False
        
        # Windows禁用字符
        invalid_chars = r'<>:"/\\|?*'
        if any(char in filename for char in invalid_chars):
            return False
        
        # Windows保留名称
        reserved_names = [
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        ]
        
        name_without_ext = filename.split('.')[0].upper()
        if name_without_ext in reserved_names:
            return False
        
        # 检查长度
        if len(filename) > 255:
            return False
        
        return True
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        清理文件名，移除非法字符
        
        Args:
            filename (str): 原始文件名
            
        Returns:
            str: 清理后的文件名
        """
        if not filename:
            return "unnamed"
        
        # 移除非法字符
        invalid_chars = r'<>:"/\\|?*'
        sanitized = filename
        for char in invalid_chars:
            sanitized = sanitized.replace(char, '_')
        
        # 移除控制字符
        sanitized = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', sanitized)
        
        # 限制长度
        if len(sanitized) > 255:
            name, ext = os.path.splitext(sanitized)
            max_name_len = 255 - len(ext)
            sanitized = name[:max_name_len] + ext
        
        # 确保不为空
        if not sanitized.strip():
            sanitized = "unnamed"
        
        return sanitized.strip()
