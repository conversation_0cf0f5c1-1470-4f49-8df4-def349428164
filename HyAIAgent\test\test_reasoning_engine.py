"""
ReasoningEngine 测试模块

测试多步推理引擎的各项功能，包括：
- 多步推理
- 逻辑演绎
- 因果分析
- 假设验证
"""

import asyncio
import pytest
import sys
import os
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from advanced.reasoning_engine import (
    ReasoningEngine, ReasoningStep, ReasoningChain, ReasoningResult,
    ReasoningType, ConfidenceLevel
)
from core.ai_client import SimpleAIClient
from core.prompt_manager import PromptManager
from core.kv_store import KVStore


class TestReasoningEngine:
    """ReasoningEngine 测试类"""
    
    @pytest.fixture
    def mock_ai_client(self):
        """模拟AI客户端"""
        client = Mock(spec=SimpleAIClient)
        client.chat = AsyncMock()
        return client
    
    @pytest.fixture
    def mock_prompt_manager(self):
        """模拟提示词管理器"""
        return Mock(spec=PromptManager)
    
    @pytest.fixture
    def mock_kv_store(self):
        """模拟KV存储"""
        store = Mock(spec=KVStore)
        store.set = AsyncMock()
        store.get = AsyncMock()
        return store
    
    @pytest.fixture
    def reasoning_engine(self, mock_ai_client, mock_prompt_manager, mock_kv_store):
        """创建推理引擎实例"""
        return ReasoningEngine(mock_ai_client, mock_prompt_manager, mock_kv_store)
    
    def test_reasoning_engine_initialization(self, reasoning_engine):
        """测试推理引擎初始化"""
        assert reasoning_engine is not None
        assert reasoning_engine.reasoning_stats['total_reasoning_chains'] == 0
        assert reasoning_engine.reasoning_stats['successful_chains'] == 0
        assert reasoning_engine.reasoning_stats['failed_chains'] == 0
        assert len(reasoning_engine.reasoning_rules) == 5  # 5种推理类型
    
    def test_reasoning_step_creation(self):
        """测试推理步骤创建"""
        step = ReasoningStep(
            step_id="test_step_1",
            premise="所有人都会死",
            rule="演绎推理规则",
            conclusion="苏格拉底会死",
            reasoning_type=ReasoningType.DEDUCTIVE,
            evidence=["苏格拉底是人"]
        )
        
        assert step.step_id == "test_step_1"
        assert step.premise == "所有人都会死"
        assert step.reasoning_type == ReasoningType.DEDUCTIVE
        assert len(step.evidence) == 1
        assert step.timestamp is not None
    
    def test_reasoning_chain_creation(self):
        """测试推理链创建"""
        step1 = ReasoningStep(
            step_id="step_1",
            premise="所有人都会死",
            rule="演绎推理",
            conclusion="苏格拉底会死",
            reasoning_type=ReasoningType.DEDUCTIVE
        )
        
        chain = ReasoningChain(
            chain_id="test_chain_1",
            problem="证明苏格拉底会死",
            steps=[step1],
            final_conclusion="苏格拉底会死"
        )
        
        assert chain.chain_id == "test_chain_1"
        assert len(chain.steps) == 1
        assert chain.final_conclusion == "苏格拉底会死"
        assert chain.created_at is not None
    
    @pytest.mark.asyncio
    async def test_multi_step_reasoning_success(self, reasoning_engine, mock_ai_client):
        """测试多步推理成功场景"""
        # 模拟AI响应
        mock_ai_client.chat.side_effect = [
            """前提: 所有人都会死
规则: 演绎推理规则
结论: 苏格拉底会死
证据: 苏格拉底是人""",
            """前提: 苏格拉底会死
规则: 逻辑推理
结论: 因此，苏格拉底是有限的存在
证据: 死亡意味着有限性"""
        ]
        
        result = await reasoning_engine.multi_step_reasoning(
            problem="证明苏格拉底的有限性",
            max_steps=2
        )
        
        assert result.success is True
        assert result.reasoning_chain is not None
        assert len(result.reasoning_chain.steps) >= 1
        assert result.execution_time > 0
        assert result.steps_count >= 1
    
    @pytest.mark.asyncio
    async def test_logical_deduction(self, reasoning_engine, mock_ai_client):
        """测试逻辑演绎推理"""
        mock_ai_client.chat.return_value = """
步骤1: 所有人都会死 + 演绎推理规则 -> 苏格拉底会死
步骤2: 苏格拉底会死 + 逻辑推理 -> 苏格拉底是有限的
最终结论: 苏格拉底是有限的存在
"""
        
        premises = ["所有人都会死", "苏格拉底是人"]
        result = await reasoning_engine.logical_deduction(premises)
        
        assert result.success is True
        assert result.reasoning_chain is not None
        assert "苏格拉底" in result.reasoning_chain.final_conclusion
    
    @pytest.mark.asyncio
    async def test_causal_analysis(self, reasoning_engine, mock_ai_client):
        """测试因果关系分析"""
        mock_ai_client.chat.return_value = """
1. 直接因果关系：下雨导致地面湿润
2. 间接因果关系：下雨通过积水导致交通拥堵
3. 因果强度评估：强
4. 置信度评估：0.9
"""
        
        events = [
            {"description": "开始下雨", "timestamp": "2024-01-01 10:00"},
            {"description": "地面变湿", "timestamp": "2024-01-01 10:05"},
            {"description": "交通拥堵", "timestamp": "2024-01-01 10:15"}
        ]
        
        result = await reasoning_engine.causal_analysis(events)
        
        assert result.success is True
        assert result.reasoning_chain is not None
        assert len(result.reasoning_chain.steps) >= 1
    
    @pytest.mark.asyncio
    async def test_hypothesis_testing(self, reasoning_engine, mock_ai_client):
        """测试假设验证"""
        mock_ai_client.chat.return_value = """
支持证据：实验组比对照组表现更好
支持证据：统计显著性p<0.05
反对证据：样本量较小
替代解释：可能存在其他混淆变量
验证结论：接受假设
置信度：0.75
"""
        
        hypothesis = "新药物能够有效治疗疾病"
        evidence = [
            "实验组治愈率80%",
            "对照组治愈率60%",
            "样本量100人",
            "双盲实验设计"
        ]
        
        result = await reasoning_engine.hypothesis_testing(hypothesis, evidence)
        
        assert result.success is True
        assert result.reasoning_chain is not None
        assert "假设" in result.reasoning_chain.final_conclusion
    
    def test_reasoning_type_selection(self, reasoning_engine):
        """测试推理类型选择"""
        # 测试因果推理
        context1 = "下雨导致地面湿润"
        type1 = asyncio.run(reasoning_engine._select_reasoning_type(
            context1, [ReasoningType.CAUSAL, ReasoningType.DEDUCTIVE]
        ))
        assert type1 == ReasoningType.CAUSAL
        
        # 测试演绎推理
        context2 = "所有人都会死，苏格拉底是人"
        type2 = asyncio.run(reasoning_engine._select_reasoning_type(
            context2, [ReasoningType.DEDUCTIVE, ReasoningType.INDUCTIVE]
        ))
        assert type2 == ReasoningType.DEDUCTIVE
    
    def test_confidence_calculation(self, reasoning_engine):
        """测试置信度计算"""
        steps = [
            ReasoningStep("1", "前提1", "规则1", "结论1", ReasoningType.DEDUCTIVE, confidence=0.9),
            ReasoningStep("2", "前提2", "规则2", "结论2", ReasoningType.DEDUCTIVE, confidence=0.8),
            ReasoningStep("3", "前提3", "规则3", "结论3", ReasoningType.DEDUCTIVE, confidence=0.7)
        ]

        chain_confidence = reasoning_engine._calculate_chain_confidence(steps)
        assert 0.0 <= chain_confidence <= 1.0
        # 修正断言：使用加权平均数后，整体置信度应该在合理范围内
        average_confidence = sum(step.confidence for step in steps) / len(steps)
        assert chain_confidence <= average_confidence  # 应该小于等于平均值（因为有惩罚因子）
    
    def test_confidence_distribution(self, reasoning_engine):
        """测试置信度分布"""
        steps = [
            ReasoningStep("1", "前提1", "规则1", "结论1", ReasoningType.DEDUCTIVE, confidence=0.9),
            ReasoningStep("2", "前提2", "规则2", "结论2", ReasoningType.DEDUCTIVE, confidence=0.8),
            ReasoningStep("3", "前提3", "规则3", "结论3", ReasoningType.DEDUCTIVE, confidence=0.6)
        ]
        
        distribution = reasoning_engine._get_confidence_distribution(steps)
        assert isinstance(distribution, dict)
        assert all(0.0 <= value <= 1.0 for value in distribution.values())
    
    def test_stats_management(self, reasoning_engine):
        """测试统计信息管理"""
        # 获取初始统计信息
        initial_stats = reasoning_engine.get_reasoning_stats()
        assert initial_stats['total_reasoning_chains'] == 0
        
        # 模拟推理链
        chain = ReasoningChain(
            chain_id="test_chain",
            problem="测试问题",
            steps=[
                ReasoningStep("1", "前提", "规则", "结论", ReasoningType.DEDUCTIVE, confidence=0.8)
            ],
            final_conclusion="测试结论",
            overall_confidence=0.8
        )
        
        # 更新统计信息
        reasoning_engine._update_reasoning_stats(chain, 1.5, True)
        
        updated_stats = reasoning_engine.get_reasoning_stats()
        assert updated_stats['total_reasoning_chains'] == 1
        assert updated_stats['successful_chains'] == 1
        assert updated_stats['failed_chains'] == 0
        assert updated_stats['total_execution_time'] == 1.5
        
        # 重置统计信息
        reasoning_engine.reset_stats()
        reset_stats = reasoning_engine.get_reasoning_stats()
        assert reset_stats['total_reasoning_chains'] == 0
    
    @pytest.mark.asyncio
    async def test_reasoning_step_validation(self, reasoning_engine):
        """测试推理步骤验证"""
        # 有效步骤
        valid_step = ReasoningStep(
            step_id="valid",
            premise="有效前提",
            rule="有效规则",
            conclusion="有效结论",
            reasoning_type=ReasoningType.DEDUCTIVE
        )
        assert await reasoning_engine._validate_reasoning_step(valid_step) is True
        
        # 无效步骤（缺少前提）
        invalid_step = ReasoningStep(
            step_id="invalid",
            premise="",
            rule="规则",
            conclusion="结论",
            reasoning_type=ReasoningType.DEDUCTIVE
        )
        assert await reasoning_engine._validate_reasoning_step(invalid_step) is False
    
    @pytest.mark.asyncio
    async def test_error_handling(self, reasoning_engine, mock_ai_client):
        """测试错误处理"""
        # 模拟AI客户端异常
        mock_ai_client.chat.side_effect = Exception("AI服务不可用")

        result = await reasoning_engine.multi_step_reasoning("测试问题")

        assert result.success is False
        assert result.error is not None
        assert "AI服务不可用" in result.error
        assert result.execution_time >= 0  # 修复：执行时间可能为0


def run_reasoning_engine_tests():
    """运行推理引擎测试"""
    print("🧠 开始测试 ReasoningEngine...")
    
    # 运行测试
    pytest_args = [
        __file__,
        "-v",
        "--tb=short",
        "-x"  # 遇到第一个失败就停止
    ]
    
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("✅ ReasoningEngine 所有测试通过！")
        return True
    else:
        print("❌ ReasoningEngine 测试失败！")
        return False


if __name__ == "__main__":
    # 直接运行测试
    success = run_reasoning_engine_tests()
    if not success:
        sys.exit(1)
