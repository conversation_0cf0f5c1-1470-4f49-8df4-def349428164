"""
基础操作模块

本模块定义了所有操作的基类和通用接口。
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)


class OperationType(Enum):
    """操作类型枚举"""
    SYSTEM = "system"
    FILE = "file"
    NETWORK = "network"
    DATABASE = "database"
    API = "api"
    COMPUTATION = "computation"
    COMMUNICATION = "communication"


class OperationStatus(Enum):
    """操作状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class OperationResult:
    """操作结果类"""
    
    def __init__(self, success: bool = False, data: Any = None, 
                 error_message: str = "", metadata: Optional[Dict[str, Any]] = None):
        self.success = success
        self.data = data
        self.error_message = error_message
        self.metadata = metadata or {}
        self.timestamp = datetime.now()
        self.execution_time: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'success': self.success,
            'data': self.data,
            'error_message': self.error_message,
            'metadata': self.metadata,
            'timestamp': self.timestamp.isoformat(),
            'execution_time': self.execution_time
        }


class BaseOperation(ABC):
    """操作基类
    
    定义所有操作的通用接口和基础功能。
    所有具体操作都应该继承此类。
    """
    
    def __init__(self, operation_type: OperationType, name: str, description: str = ""):
        """初始化操作
        
        Args:
            operation_type: 操作类型
            name: 操作名称
            description: 操作描述
        """
        self.operation_type = operation_type
        self.name = name
        self.description = description
        self.status = OperationStatus.PENDING
        self.created_at = datetime.now()
        self.started_at: Optional[datetime] = None
        self.completed_at: Optional[datetime] = None
        self.result: Optional[OperationResult] = None
        
        # 操作配置
        self.timeout = 300  # 默认5分钟超时
        self.retry_count = 0
        self.max_retries = 3
        self.retry_delay = 1.0  # 重试延迟（秒）
        
        logger.debug(f"创建操作: {self.name} ({self.operation_type.value})")
    
    @abstractmethod
    async def execute(self, **kwargs) -> OperationResult:
        """执行操作
        
        Args:
            **kwargs: 操作参数
            
        Returns:
            OperationResult: 操作结果
        """
        pass
    
    @abstractmethod
    def validate_parameters(self, **kwargs) -> bool:
        """验证操作参数
        
        Args:
            **kwargs: 操作参数
            
        Returns:
            bool: 参数是否有效
        """
        pass
    
    async def run(self, **kwargs) -> OperationResult:
        """运行操作（包含重试逻辑）
        
        Args:
            **kwargs: 操作参数
            
        Returns:
            OperationResult: 操作结果
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"开始执行操作: {self.name}")
            
            # 验证参数
            if not self.validate_parameters(**kwargs):
                return OperationResult(
                    success=False,
                    error_message="参数验证失败",
                    metadata={'operation': self.name}
                )
            
            # 更新状态
            self.status = OperationStatus.RUNNING
            self.started_at = datetime.now()
            
            # 执行操作（带重试）
            result = await self._execute_with_retry(**kwargs)
            
            # 更新状态
            if result.success:
                self.status = OperationStatus.COMPLETED
            else:
                self.status = OperationStatus.FAILED
            
            self.completed_at = datetime.now()
            self.result = result
            
            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()
            result.execution_time = execution_time
            
            logger.info(f"操作完成: {self.name} - 成功: {result.success}")
            return result
            
        except Exception as e:
            logger.error(f"操作执行异常: {self.name} - {str(e)}")
            
            self.status = OperationStatus.FAILED
            self.completed_at = datetime.now()
            
            result = OperationResult(
                success=False,
                error_message=str(e),
                metadata={'operation': self.name, 'exception_type': type(e).__name__}
            )
            
            execution_time = (datetime.now() - start_time).total_seconds()
            result.execution_time = execution_time
            
            self.result = result
            return result
    
    async def _execute_with_retry(self, **kwargs) -> OperationResult:
        """带重试的执行逻辑"""
        last_error = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    logger.info(f"重试操作: {self.name} (第{attempt}次重试)")
                    await asyncio.sleep(self.retry_delay * attempt)  # 指数退避
                
                # 执行操作
                result = await asyncio.wait_for(
                    self.execute(**kwargs),
                    timeout=self.timeout
                )
                
                if result.success:
                    if attempt > 0:
                        result.metadata['retry_count'] = attempt
                    return result
                else:
                    last_error = result.error_message
                    self.retry_count = attempt
                    
            except asyncio.TimeoutError:
                last_error = f"操作超时 ({self.timeout}秒)"
                self.retry_count = attempt
                logger.warning(f"操作超时: {self.name}")
                
            except Exception as e:
                last_error = str(e)
                self.retry_count = attempt
                logger.warning(f"操作执行失败: {self.name} - {str(e)}")
        
        # 所有重试都失败
        return OperationResult(
            success=False,
            error_message=f"操作失败，已重试{self.max_retries}次。最后错误: {last_error}",
            metadata={
                'operation': self.name,
                'retry_count': self.retry_count,
                'max_retries': self.max_retries
            }
        )
    
    def cancel(self) -> bool:
        """取消操作
        
        Returns:
            bool: 是否成功取消
        """
        if self.status in [OperationStatus.PENDING, OperationStatus.RUNNING]:
            self.status = OperationStatus.CANCELLED
            self.completed_at = datetime.now()
            logger.info(f"操作已取消: {self.name}")
            return True
        return False
    
    def get_status_info(self) -> Dict[str, Any]:
        """获取操作状态信息
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        info = {
            'name': self.name,
            'type': self.operation_type.value,
            'status': self.status.value,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'retry_count': self.retry_count,
            'max_retries': self.max_retries
        }
        
        if self.started_at:
            info['started_at'] = self.started_at.isoformat()
        
        if self.completed_at:
            info['completed_at'] = self.completed_at.isoformat()
            info['duration'] = (self.completed_at - (self.started_at or self.created_at)).total_seconds()
        
        if self.result:
            info['result'] = self.result.to_dict()
        
        return info
    
    def configure(self, timeout: Optional[int] = None, max_retries: Optional[int] = None,
                 retry_delay: Optional[float] = None) -> None:
        """配置操作参数
        
        Args:
            timeout: 超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
        """
        if timeout is not None:
            self.timeout = timeout
        if max_retries is not None:
            self.max_retries = max_retries
        if retry_delay is not None:
            self.retry_delay = retry_delay
        
        logger.debug(f"操作配置更新: {self.name} - 超时:{self.timeout}s, 重试:{self.max_retries}次")


class OperationRegistry:
    """操作注册表
    
    管理所有可用的操作类型和实例。
    """
    
    def __init__(self):
        self.operations: Dict[str, type] = {}
        self.running_operations: Dict[str, BaseOperation] = {}
        self.operation_history: List[Dict[str, Any]] = []
    
    def register_operation(self, operation_class: type, name: str = None) -> bool:
        """注册操作类
        
        Args:
            operation_class: 操作类
            name: 操作名称（可选，默认使用类名）
            
        Returns:
            bool: 是否注册成功
        """
        try:
            operation_name = name or operation_class.__name__
            
            if not issubclass(operation_class, BaseOperation):
                logger.error(f"操作类必须继承BaseOperation: {operation_name}")
                return False
            
            self.operations[operation_name] = operation_class
            logger.info(f"操作已注册: {operation_name}")
            return True
            
        except Exception as e:
            logger.error(f"注册操作失败: {str(e)}")
            return False
    
    def create_operation(self, operation_name: str, **kwargs) -> Optional[BaseOperation]:
        """创建操作实例
        
        Args:
            operation_name: 操作名称
            **kwargs: 操作初始化参数
            
        Returns:
            Optional[BaseOperation]: 操作实例
        """
        try:
            if operation_name not in self.operations:
                logger.error(f"未找到操作: {operation_name}")
                return None
            
            operation_class = self.operations[operation_name]
            operation = operation_class(**kwargs)
            
            logger.debug(f"创建操作实例: {operation_name}")
            return operation
            
        except Exception as e:
            logger.error(f"创建操作实例失败: {operation_name} - {str(e)}")
            return None
    
    def get_available_operations(self) -> List[str]:
        """获取可用操作列表
        
        Returns:
            List[str]: 操作名称列表
        """
        return list(self.operations.keys())
    
    def add_running_operation(self, operation_id: str, operation: BaseOperation) -> None:
        """添加运行中的操作
        
        Args:
            operation_id: 操作ID
            operation: 操作实例
        """
        self.running_operations[operation_id] = operation
    
    def remove_running_operation(self, operation_id: str) -> None:
        """移除运行中的操作
        
        Args:
            operation_id: 操作ID
        """
        if operation_id in self.running_operations:
            operation = self.running_operations.pop(operation_id)
            
            # 添加到历史记录
            self.operation_history.append({
                'operation_id': operation_id,
                'operation_info': operation.get_status_info(),
                'completed_at': datetime.now().isoformat()
            })
            
            # 保持历史记录在合理范围内
            if len(self.operation_history) > 100:
                self.operation_history = self.operation_history[-50:]
    
    def get_running_operations(self) -> Dict[str, Dict[str, Any]]:
        """获取运行中的操作状态
        
        Returns:
            Dict[str, Dict[str, Any]]: 运行中的操作信息
        """
        return {
            op_id: operation.get_status_info()
            for op_id, operation in self.running_operations.items()
        }
    
    def get_operation_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取操作历史
        
        Args:
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 操作历史记录
        """
        return self.operation_history[-limit:]
