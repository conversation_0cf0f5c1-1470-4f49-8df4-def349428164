"""
高级UI组件模块

提供智能输入组件、动态布局组件、交互式图表组件和多媒体展示组件等高级UI功能。
支持响应式设计、主题系统和无障碍访问。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import sys
import json
import asyncio
from typing import Dict, List, Any, Optional, Callable, Union
from pathlib import Path
from datetime import datetime
import logging

# PyQt6 imports
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QStackedLayout,
    QLabel, QLineEdit, QTextEdit, QPushButton, QComboBox, QSpinBox,
    QSlider, QProgressBar, QTabWidget, QSplitter, QFrame, QScrollArea,
    QGroupBox, QCheckBox, QRadioButton, QButtonGroup, QListWidget,
    QTreeWidget, QTableWidget, QGraphicsView, QGraphicsScene,
    QSizePolicy, QApplication, QMainWindow, QDialog, QMessageBox
)
from PyQt6.QtCore import (
    Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve,
    QRect, QSize, QPoint, QObject, QEvent, QMimeData
)
from PyQt6.QtGui import (
    QFont, QColor, QPalette, QPixmap, QIcon, QPainter, QPen, QBrush,
    QLinearGradient, QRadialGradient, QDragEnterEvent, QDropEvent,
    QKeySequence, QAction, QShortcut
)

# 导入项目模块
from core.config_manager import ConfigManager
from core.kv_store import KVStore

# 设置日志
logger = logging.getLogger(__name__)


class SmartInputWidget(QWidget):
    """智能输入组件
    
    支持语音输入、手写识别、自动补全、输入验证等功能
    """
    
    # 信号定义
    textChanged = pyqtSignal(str)
    inputValidated = pyqtSignal(bool)
    voiceInputRequested = pyqtSignal()
    handwritingRequested = pyqtSignal()
    
    def __init__(self, parent=None, input_type: str = "text", 
                 validation_rules: Optional[Dict[str, Any]] = None):
        """初始化智能输入组件
        
        Args:
            parent: 父组件
            input_type: 输入类型 (text, number, email, url, password等)
            validation_rules: 验证规则字典
        """
        super().__init__(parent)
        self.input_type = input_type
        self.validation_rules = validation_rules or {}
        self.suggestions = []
        self.is_valid = True
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 主输入框
        self.input_field = QLineEdit()
        self.input_field.setPlaceholderText(f"请输入{self.input_type}...")
        layout.addWidget(self.input_field)
        
        # 语音输入按钮
        self.voice_button = QPushButton("🎤")
        self.voice_button.setToolTip("语音输入")
        self.voice_button.setFixedSize(30, 30)
        layout.addWidget(self.voice_button)
        
        # 手写输入按钮
        self.handwriting_button = QPushButton("✏️")
        self.handwriting_button.setToolTip("手写输入")
        self.handwriting_button.setFixedSize(30, 30)
        layout.addWidget(self.handwriting_button)
        
        # 验证状态指示器
        self.status_label = QLabel("✓")
        self.status_label.setFixedSize(20, 20)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 设置样式
        self.apply_theme()
        
    def setup_connections(self):
        """设置信号连接"""
        self.input_field.textChanged.connect(self.on_text_changed)
        self.voice_button.clicked.connect(self.voiceInputRequested.emit)
        self.handwriting_button.clicked.connect(self.handwritingRequested.emit)
        
    def on_text_changed(self, text: str):
        """处理文本变化"""
        self.validate_input(text)
        self.textChanged.emit(text)
        
    def validate_input(self, text: str) -> bool:
        """验证输入内容"""
        # 根据输入类型进行验证
        self.is_valid = self._perform_validation(text)

        # 更新状态指示器
        if self.is_valid:
            self.status_label.setText("✓")
            self.status_label.setStyleSheet("color: green;")
        else:
            self.status_label.setText("✗")
            self.status_label.setStyleSheet("color: red;")

        self.inputValidated.emit(self.is_valid)
        return self.is_valid
        
    def _perform_validation(self, text: str) -> bool:
        """执行具体的验证逻辑"""
        # 空文本的处理
        if not text.strip():
            return self.input_type == "text"  # 只有text类型允许空值

        # 根据输入类型和验证规则进行验证
        if self.input_type == "email":
            import re
            pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            return re.match(pattern, text) is not None
        elif self.input_type == "number":
            try:
                float(text)
                return True
            except ValueError:
                return False
        elif self.input_type == "url":
            import re
            pattern = r'^https?://[^\s/$.?#].[^\s]*$'
            return re.match(pattern, text) is not None
        # 默认text类型总是有效
        return True
        
    def set_suggestions(self, suggestions: List[str]):
        """设置自动补全建议"""
        self.suggestions = suggestions
        # 这里可以实现自动补全功能
        
    def apply_theme(self, theme: str = "default"):
        """应用主题样式"""
        if theme == "dark":
            self.setStyleSheet("""
                QLineEdit {
                    background-color: #2b2b2b;
                    color: white;
                    border: 1px solid #555;
                    border-radius: 4px;
                    padding: 5px;
                }
                QPushButton {
                    background-color: #404040;
                    color: white;
                    border: none;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #505050;
                }
            """)
        else:
            self.setStyleSheet("""
                QLineEdit {
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    padding: 5px;
                }
                QPushButton {
                    background-color: #f0f0f0;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
            """)


class DynamicLayoutWidget(QWidget):
    """动态布局组件
    
    根据任务类型和内容自动调整布局结构
    """
    
    def __init__(self, parent=None):
        """初始化动态布局组件"""
        super().__init__(parent)
        self.current_layout_type = "vertical"
        self.widgets = []
        self.layout_configs = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 使用堆叠布局来支持动态切换
        self.stacked_layout = QStackedLayout(self)
        
        # 创建不同类型的布局容器
        self.vertical_container = QWidget()
        self.vertical_layout = QVBoxLayout(self.vertical_container)
        
        self.horizontal_container = QWidget()
        self.horizontal_layout = QHBoxLayout(self.horizontal_container)
        
        self.grid_container = QWidget()
        self.grid_layout = QGridLayout(self.grid_container)
        
        # 添加到堆叠布局
        self.stacked_layout.addWidget(self.vertical_container)
        self.stacked_layout.addWidget(self.horizontal_container)
        self.stacked_layout.addWidget(self.grid_container)
        
    def add_widget(self, widget: QWidget, layout_hint: Optional[Dict[str, Any]] = None):
        """添加组件到动态布局"""
        self.widgets.append({
            'widget': widget,
            'hint': layout_hint or {}
        })
        self._refresh_layout()
        
    def remove_widget(self, widget: QWidget):
        """从动态布局中移除组件"""
        self.widgets = [w for w in self.widgets if w['widget'] != widget]
        widget.setParent(None)
        self._refresh_layout()
        
    def set_layout_type(self, layout_type: str):
        """设置布局类型"""
        if layout_type in ["vertical", "horizontal", "grid"]:
            self.current_layout_type = layout_type
            self._refresh_layout()
            
    def _refresh_layout(self):
        """刷新布局"""
        # 清除当前布局中的所有组件
        self._clear_all_layouts()
        
        # 根据当前布局类型重新添加组件
        if self.current_layout_type == "vertical":
            self._arrange_vertical()
            self.stacked_layout.setCurrentWidget(self.vertical_container)
        elif self.current_layout_type == "horizontal":
            self._arrange_horizontal()
            self.stacked_layout.setCurrentWidget(self.horizontal_container)
        elif self.current_layout_type == "grid":
            self._arrange_grid()
            self.stacked_layout.setCurrentWidget(self.grid_container)
            
    def _clear_all_layouts(self):
        """清除所有布局中的组件"""
        for layout in [self.vertical_layout, self.horizontal_layout, self.grid_layout]:
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().setParent(None)
                    
    def _arrange_vertical(self):
        """垂直排列组件"""
        for widget_info in self.widgets:
            self.vertical_layout.addWidget(widget_info['widget'])
            
    def _arrange_horizontal(self):
        """水平排列组件"""
        for widget_info in self.widgets:
            self.horizontal_layout.addWidget(widget_info['widget'])
            
    def _arrange_grid(self):
        """网格排列组件"""
        import math
        cols = math.ceil(math.sqrt(len(self.widgets)))
        for i, widget_info in enumerate(self.widgets):
            row = i // cols
            col = i % cols
            self.grid_layout.addWidget(widget_info['widget'], row, col)
            
    def auto_adjust_layout(self, task_type: str, content_count: int):
        """根据任务类型和内容数量自动调整布局"""
        if task_type == "comparison" and content_count == 2:
            self.set_layout_type("horizontal")
        elif task_type == "dashboard" or content_count > 4:
            self.set_layout_type("grid")
        else:
            self.set_layout_type("vertical")


class InteractiveChartWidget(QWidget):
    """交互式图表组件
    
    支持实时数据展示、缩放、筛选等交互功能
    """
    
    # 信号定义
    dataPointClicked = pyqtSignal(dict)
    chartUpdated = pyqtSignal()
    
    def __init__(self, parent=None, chart_type: str = "line"):
        """初始化交互式图表组件"""
        super().__init__(parent)
        self.chart_type = chart_type
        self.data_series = []
        self.chart_config = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 图表视图
        self.chart_view = QGraphicsView()
        self.chart_scene = QGraphicsScene()
        self.chart_view.setScene(self.chart_scene)
        layout.addWidget(self.chart_view)
        
        # 控制面板
        control_layout = QHBoxLayout()
        
        # 图表类型选择
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["line", "bar", "pie", "scatter", "area"])
        self.chart_type_combo.setCurrentText(self.chart_type)
        control_layout.addWidget(QLabel("图表类型:"))
        control_layout.addWidget(self.chart_type_combo)
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        control_layout.addWidget(self.refresh_button)
        
        # 导出按钮
        self.export_button = QPushButton("导出")
        control_layout.addWidget(self.export_button)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 连接信号
        self.chart_type_combo.currentTextChanged.connect(self.on_chart_type_changed)
        self.refresh_button.clicked.connect(self.refresh_chart)
        self.export_button.clicked.connect(self.export_chart)
        
    def set_data(self, data: List[Dict[str, Any]]):
        """设置图表数据"""
        self.data_series = data
        self.refresh_chart()
        
    def add_data_point(self, data_point: Dict[str, Any]):
        """添加数据点"""
        self.data_series.append(data_point)
        self.refresh_chart()
        
    def refresh_chart(self):
        """刷新图表"""
        self.chart_scene.clear()
        
        if not self.data_series:
            return
            
        # 根据图表类型绘制
        if self.chart_type == "line":
            self._draw_line_chart()
        elif self.chart_type == "bar":
            self._draw_bar_chart()
        elif self.chart_type == "pie":
            self._draw_pie_chart()
        # 可以添加更多图表类型
        
        self.chartUpdated.emit()
        
    def _draw_line_chart(self):
        """绘制折线图"""
        if len(self.data_series) < 2:
            return
            
        # 简单的折线图绘制示例
        pen = QPen(QColor(0, 100, 200), 2)
        
        for i in range(len(self.data_series) - 1):
            x1 = i * 50
            y1 = 200 - (self.data_series[i].get('value', 0) * 2)
            x2 = (i + 1) * 50
            y2 = 200 - (self.data_series[i + 1].get('value', 0) * 2)
            
            self.chart_scene.addLine(x1, y1, x2, y2, pen)
            
    def _draw_bar_chart(self):
        """绘制柱状图"""
        brush = QBrush(QColor(100, 150, 200))
        
        for i, data_point in enumerate(self.data_series):
            x = i * 60
            height = data_point.get('value', 0) * 2
            y = 200 - height
            
            rect = self.chart_scene.addRect(x, y, 40, height, QPen(), brush)
            
    def _draw_pie_chart(self):
        """绘制饼图"""
        # 饼图绘制逻辑
        total = sum(data.get('value', 0) for data in self.data_series)
        if total == 0:
            return
            
        start_angle = 0
        colors = [QColor(255, 100, 100), QColor(100, 255, 100), QColor(100, 100, 255)]
        
        for i, data_point in enumerate(self.data_series):
            value = data_point.get('value', 0)
            span_angle = int((value / total) * 360 * 16)  # Qt uses 1/16th degrees
            
            color = colors[i % len(colors)]
            brush = QBrush(color)
            
            self.chart_scene.addEllipse(50, 50, 100, 100, QPen(), brush)
            start_angle += span_angle
            
    def on_chart_type_changed(self, chart_type: str):
        """处理图表类型变化"""
        self.chart_type = chart_type
        self.refresh_chart()
        
    def export_chart(self):
        """导出图表"""
        # 实现图表导出功能
        pixmap = QPixmap(self.chart_view.size())
        self.chart_view.render(pixmap)
        
        filename = f"chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        pixmap.save(filename)
        
        QMessageBox.information(self, "导出成功", f"图表已导出为: {filename}")


class MultimediaWidget(QWidget):
    """多媒体展示组件

    支持图片、视频、音频等多媒体内容的展示和播放
    """

    # 信号定义
    mediaLoaded = pyqtSignal(str)
    mediaError = pyqtSignal(str)
    playbackStateChanged = pyqtSignal(str)

    def __init__(self, parent=None):
        """初始化多媒体展示组件"""
        super().__init__(parent)
        self.current_media = None
        self.media_type = None
        self.supported_formats = {
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'],
            'video': ['.mp4', '.avi', '.mov', '.wmv', '.flv'],
            'audio': ['.mp3', '.wav', '.ogg', '.m4a', '.flac']
        }

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 媒体显示区域
        self.media_container = QStackedLayout()

        # 图片显示
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet("border: 1px solid #ccc;")
        self.image_label.setMinimumSize(400, 300)
        self.image_label.setScaledContents(True)

        # 滚动区域用于大图片
        self.image_scroll = QScrollArea()
        self.image_scroll.setWidget(self.image_label)
        self.image_scroll.setWidgetResizable(True)

        # 视频播放器占位符
        self.video_widget = QLabel("视频播放器")
        self.video_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.video_widget.setStyleSheet("background-color: black; color: white;")
        self.video_widget.setMinimumSize(400, 300)

        # 音频播放器占位符
        self.audio_widget = QLabel("音频播放器")
        self.audio_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.audio_widget.setStyleSheet("background-color: #f0f0f0;")
        self.audio_widget.setMinimumSize(400, 100)

        # 添加到容器
        container_widget = QWidget()
        container_widget.setLayout(self.media_container)
        self.media_container.addWidget(self.image_scroll)
        self.media_container.addWidget(self.video_widget)
        self.media_container.addWidget(self.audio_widget)

        layout.addWidget(container_widget)

        # 控制面板
        control_layout = QHBoxLayout()

        # 文件选择按钮
        self.load_button = QPushButton("加载媒体文件")
        control_layout.addWidget(self.load_button)

        # 播放控制按钮
        self.play_button = QPushButton("播放")
        self.pause_button = QPushButton("暂停")
        self.stop_button = QPushButton("停止")

        control_layout.addWidget(self.play_button)
        control_layout.addWidget(self.pause_button)
        control_layout.addWidget(self.stop_button)

        # 音量控制
        control_layout.addWidget(QLabel("音量:"))
        self.volume_slider = QSlider(Qt.Orientation.Horizontal)
        self.volume_slider.setRange(0, 100)
        self.volume_slider.setValue(50)
        control_layout.addWidget(self.volume_slider)

        control_layout.addStretch()
        layout.addLayout(control_layout)

        # 信息显示
        self.info_label = QLabel("未加载媒体文件")
        layout.addWidget(self.info_label)

        # 连接信号
        self.load_button.clicked.connect(self.load_media_file)
        self.play_button.clicked.connect(self.play_media)
        self.pause_button.clicked.connect(self.pause_media)
        self.stop_button.clicked.connect(self.stop_media)

    def load_media_file(self):
        """加载媒体文件"""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择媒体文件", "",
            "所有支持的文件 (*.jpg *.jpeg *.png *.gif *.bmp *.mp4 *.avi *.mp3 *.wav);;图片文件 (*.jpg *.jpeg *.png *.gif *.bmp);;视频文件 (*.mp4 *.avi);;音频文件 (*.mp3 *.wav)"
        )

        if file_path:
            self.load_media(file_path)

    def load_media(self, file_path: str):
        """加载指定的媒体文件"""
        try:
            file_ext = Path(file_path).suffix.lower()

            # 确定媒体类型
            for media_type, extensions in self.supported_formats.items():
                if file_ext in extensions:
                    self.media_type = media_type
                    break
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")

            self.current_media = file_path

            # 根据媒体类型加载
            if self.media_type == 'image':
                self._load_image(file_path)
            elif self.media_type == 'video':
                self._load_video(file_path)
            elif self.media_type == 'audio':
                self._load_audio(file_path)

            self.info_label.setText(f"已加载: {Path(file_path).name} ({self.media_type})")
            self.mediaLoaded.emit(file_path)

        except Exception as e:
            error_msg = f"加载媒体文件失败: {str(e)}"
            self.info_label.setText(error_msg)
            self.mediaError.emit(error_msg)

    def _load_image(self, file_path: str):
        """加载图片"""
        pixmap = QPixmap(file_path)
        if not pixmap.isNull():
            # 如果图片很大，进行缩放
            if pixmap.width() > 800 or pixmap.height() > 600:
                pixmap = pixmap.scaled(800, 600, Qt.AspectRatioMode.KeepAspectRatio,
                                     Qt.TransformationMode.SmoothTransformation)

            self.image_label.setPixmap(pixmap)
            self.media_container.setCurrentWidget(self.image_scroll)
        else:
            raise ValueError("无法加载图片文件")

    def _load_video(self, file_path: str):
        """加载视频"""
        # 这里应该集成真正的视频播放器，如QMediaPlayer
        self.video_widget.setText(f"视频文件: {Path(file_path).name}\n(需要集成视频播放器)")
        self.media_container.setCurrentWidget(self.video_widget)

    def _load_audio(self, file_path: str):
        """加载音频"""
        # 这里应该集成真正的音频播放器
        self.audio_widget.setText(f"音频文件: {Path(file_path).name}\n(需要集成音频播放器)")
        self.media_container.setCurrentWidget(self.audio_widget)

    def play_media(self):
        """播放媒体"""
        if self.current_media and self.media_type in ['video', 'audio']:
            # 实现播放逻辑
            self.playbackStateChanged.emit("playing")

    def pause_media(self):
        """暂停播放"""
        if self.current_media and self.media_type in ['video', 'audio']:
            # 实现暂停逻辑
            self.playbackStateChanged.emit("paused")

    def stop_media(self):
        """停止播放"""
        if self.current_media and self.media_type in ['video', 'audio']:
            # 实现停止逻辑
            self.playbackStateChanged.emit("stopped")


class ThemeManager:
    """主题管理器

    管理应用程序的主题和配色方案
    """

    def __init__(self):
        """初始化主题管理器"""
        self.current_theme = "default"
        self.themes = {
            "default": self._get_default_theme(),
            "dark": self._get_dark_theme(),
            "blue": self._get_blue_theme(),
            "green": self._get_green_theme()
        }

    def _get_default_theme(self) -> Dict[str, str]:
        """获取默认主题"""
        return {
            "background": "#ffffff",
            "foreground": "#000000",
            "primary": "#0078d4",
            "secondary": "#6c757d",
            "success": "#28a745",
            "warning": "#ffc107",
            "danger": "#dc3545",
            "border": "#dee2e6",
            "hover": "#f8f9fa"
        }

    def _get_dark_theme(self) -> Dict[str, str]:
        """获取深色主题"""
        return {
            "background": "#2b2b2b",
            "foreground": "#ffffff",
            "primary": "#0d7377",
            "secondary": "#495057",
            "success": "#20c997",
            "warning": "#fd7e14",
            "danger": "#e74c3c",
            "border": "#495057",
            "hover": "#404040"
        }

    def _get_blue_theme(self) -> Dict[str, str]:
        """获取蓝色主题"""
        return {
            "background": "#f0f8ff",
            "foreground": "#1e3a8a",
            "primary": "#3b82f6",
            "secondary": "#64748b",
            "success": "#10b981",
            "warning": "#f59e0b",
            "danger": "#ef4444",
            "border": "#bfdbfe",
            "hover": "#dbeafe"
        }

    def _get_green_theme(self) -> Dict[str, str]:
        """获取绿色主题"""
        return {
            "background": "#f0fff4",
            "foreground": "#064e3b",
            "primary": "#059669",
            "secondary": "#6b7280",
            "success": "#10b981",
            "warning": "#d97706",
            "danger": "#dc2626",
            "border": "#a7f3d0",
            "hover": "#d1fae5"
        }

    def get_theme(self, theme_name: str) -> Dict[str, str]:
        """获取指定主题"""
        return self.themes.get(theme_name, self.themes["default"])

    def apply_theme_to_widget(self, widget: QWidget, theme_name: str):
        """将主题应用到组件"""
        theme = self.get_theme(theme_name)

        style_sheet = f"""
        QWidget {{
            background-color: {theme['background']};
            color: {theme['foreground']};
        }}
        QPushButton {{
            background-color: {theme['primary']};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
        }}
        QPushButton:hover {{
            background-color: {theme['hover']};
        }}
        QLineEdit, QTextEdit {{
            background-color: {theme['background']};
            color: {theme['foreground']};
            border: 1px solid {theme['border']};
            border-radius: 4px;
            padding: 4px;
        }}
        QLabel {{
            color: {theme['foreground']};
        }}
        QComboBox {{
            background-color: {theme['background']};
            color: {theme['foreground']};
            border: 1px solid {theme['border']};
            border-radius: 4px;
            padding: 4px;
        }}
        """

        widget.setStyleSheet(style_sheet)

    def get_available_themes(self) -> List[str]:
        """获取可用主题列表"""
        return list(self.themes.keys())


class AccessibilityHelper:
    """无障碍访问助手

    提供键盘导航、屏幕阅读器支持等无障碍功能
    """

    @staticmethod
    def setup_keyboard_navigation(widget: QWidget):
        """设置键盘导航"""
        # 设置Tab顺序
        widget.setFocusPolicy(Qt.FocusPolicy.TabFocus)

        # 添加键盘快捷键
        if isinstance(widget, QPushButton):
            # 为按钮添加快捷键支持
            widget.setShortcut(QKeySequence("Return"))

    @staticmethod
    def setup_screen_reader_support(widget: QWidget, description: str):
        """设置屏幕阅读器支持"""
        widget.setAccessibleName(description)
        widget.setAccessibleDescription(description)

    @staticmethod
    def setup_high_contrast_support(widget: QWidget):
        """设置高对比度支持"""
        # 检查系统是否启用了高对比度模式
        app = QApplication.instance()
        if app:
            palette = app.palette()
            # 根据系统调色板调整组件样式
            widget.setPalette(palette)

    @staticmethod
    def add_focus_indicators(widget: QWidget):
        """添加焦点指示器"""
        original_style = widget.styleSheet()
        focus_style = """
        QWidget:focus {
            border: 2px solid #0078d4;
            outline: none;
        }
        """
        widget.setStyleSheet(original_style + focus_style)


class AdvancedWidgets:
    """高级UI组件管理器

    统一管理所有高级UI组件，提供组件创建、主题管理、响应式设计等功能
    """

    def __init__(self, config_manager: Optional[ConfigManager] = None,
                 kv_store: Optional[KVStore] = None):
        """初始化高级UI组件管理器

        Args:
            config_manager: 配置管理器
            kv_store: 键值存储
        """
        self.config_manager = config_manager
        self.kv_store = kv_store
        self.theme_manager = ThemeManager()
        self.current_theme = "default"
        self.responsive_breakpoints = {
            'mobile': 480,
            'tablet': 768,
            'desktop': 1024,
            'large': 1200
        }
        self.widgets_registry = {}

        # 设置日志
        self.logger = logging.getLogger(__name__)

    def create_smart_input(self, input_type: str = "text",
                          validation_rules: Optional[Dict[str, Any]] = None,
                          parent: Optional[QWidget] = None) -> SmartInputWidget:
        """创建智能输入组件

        Args:
            input_type: 输入类型
            validation_rules: 验证规则
            parent: 父组件

        Returns:
            SmartInputWidget: 智能输入组件实例
        """
        widget = SmartInputWidget(parent, input_type, validation_rules)
        self._apply_current_theme(widget)
        self._setup_accessibility(widget, f"智能输入框 - {input_type}")

        # 注册组件
        widget_id = f"smart_input_{len(self.widgets_registry)}"
        self.widgets_registry[widget_id] = widget

        return widget

    def create_dynamic_layout(self, parent: Optional[QWidget] = None) -> DynamicLayoutWidget:
        """创建动态布局组件

        Args:
            parent: 父组件

        Returns:
            DynamicLayoutWidget: 动态布局组件实例
        """
        widget = DynamicLayoutWidget(parent)
        self._apply_current_theme(widget)
        self._setup_accessibility(widget, "动态布局容器")

        # 注册组件
        widget_id = f"dynamic_layout_{len(self.widgets_registry)}"
        self.widgets_registry[widget_id] = widget

        return widget

    def create_interactive_chart(self, chart_type: str = "line",
                               parent: Optional[QWidget] = None) -> InteractiveChartWidget:
        """创建交互式图表组件

        Args:
            chart_type: 图表类型
            parent: 父组件

        Returns:
            InteractiveChartWidget: 交互式图表组件实例
        """
        widget = InteractiveChartWidget(parent, chart_type)
        self._apply_current_theme(widget)
        self._setup_accessibility(widget, f"交互式图表 - {chart_type}")

        # 注册组件
        widget_id = f"interactive_chart_{len(self.widgets_registry)}"
        self.widgets_registry[widget_id] = widget

        return widget

    def create_multimedia_widget(self, parent: Optional[QWidget] = None) -> MultimediaWidget:
        """创建多媒体展示组件

        Args:
            parent: 父组件

        Returns:
            MultimediaWidget: 多媒体展示组件实例
        """
        widget = MultimediaWidget(parent)
        self._apply_current_theme(widget)
        self._setup_accessibility(widget, "多媒体展示器")

        # 注册组件
        widget_id = f"multimedia_{len(self.widgets_registry)}"
        self.widgets_registry[widget_id] = widget

        return widget

    def set_theme(self, theme_name: str):
        """设置全局主题

        Args:
            theme_name: 主题名称
        """
        if theme_name in self.theme_manager.get_available_themes():
            self.current_theme = theme_name

            # 应用主题到所有已注册的组件
            for widget in self.widgets_registry.values():
                self._apply_current_theme(widget)

            self.logger.info(f"主题已切换到: {theme_name}")
        else:
            self.logger.warning(f"未知的主题: {theme_name}")

    def get_available_themes(self) -> List[str]:
        """获取可用主题列表

        Returns:
            List[str]: 主题名称列表
        """
        return self.theme_manager.get_available_themes()

    def setup_responsive_design(self, widget: QWidget, breakpoints: Optional[Dict[str, int]] = None):
        """设置响应式设计

        Args:
            widget: 目标组件
            breakpoints: 断点配置
        """
        if breakpoints:
            self.responsive_breakpoints.update(breakpoints)

        # 监听窗口大小变化
        if hasattr(widget, 'resizeEvent'):
            original_resize = widget.resizeEvent

            def responsive_resize(event):
                self._handle_responsive_resize(widget, event.size())
                if original_resize:
                    original_resize(event)

            widget.resizeEvent = responsive_resize

    def _apply_current_theme(self, widget: QWidget):
        """应用当前主题到组件"""
        self.theme_manager.apply_theme_to_widget(widget, self.current_theme)

    def _setup_accessibility(self, widget: QWidget, description: str):
        """设置无障碍访问"""
        AccessibilityHelper.setup_keyboard_navigation(widget)
        AccessibilityHelper.setup_screen_reader_support(widget, description)
        AccessibilityHelper.setup_high_contrast_support(widget)
        AccessibilityHelper.add_focus_indicators(widget)

    def _handle_responsive_resize(self, widget: QWidget, size: QSize):
        """处理响应式调整"""
        width = size.width()

        # 根据宽度调整布局
        if width <= self.responsive_breakpoints['mobile']:
            # 移动端布局
            if isinstance(widget, DynamicLayoutWidget):
                widget.set_layout_type("vertical")
        elif width <= self.responsive_breakpoints['tablet']:
            # 平板布局
            if isinstance(widget, DynamicLayoutWidget):
                widget.set_layout_type("vertical")
        else:
            # 桌面布局
            if isinstance(widget, DynamicLayoutWidget):
                widget.set_layout_type("grid")

    def get_widget_stats(self) -> Dict[str, Any]:
        """获取组件统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = {
            "total_widgets": len(self.widgets_registry),
            "current_theme": self.current_theme,
            "available_themes": len(self.theme_manager.get_available_themes()),
            "widget_types": {}
        }

        # 统计各类型组件数量
        for widget in self.widgets_registry.values():
            widget_type = type(widget).__name__
            stats["widget_types"][widget_type] = stats["widget_types"].get(widget_type, 0) + 1

        return stats

    def cleanup(self):
        """清理资源"""
        self.widgets_registry.clear()
        self.logger.info("高级UI组件管理器已清理")
