"""
HyAIAgent 第四阶段 - 混合任务执行器测试模块
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, List, Any

# 导入被测试的模块
from operations.hybrid_task_executor import (
    HybridTaskExecutor, HybridTask, TaskStep, StepResult, ExecutionReport,
    HybridTaskType, TaskStepType, ExecutionStatus
)


class TestHybridTaskExecutor:
    """混合任务执行器测试类"""
    
    @pytest.fixture
    def mock_dependencies(self):
        """创建模拟依赖"""
        search_operations = Mock()
        search_operations.search = AsyncMock(return_value=[
            {"title": "Test Result 1", "url": "http://example1.com", "content": "Content 1"},
            {"title": "Test Result 2", "url": "http://example2.com", "content": "Content 2"}
        ])
        
        file_operations = Mock()
        file_operations.write_file = AsyncMock(return_value=True)
        
        content_processor = Mock()
        content_processor.process_content = AsyncMock(return_value={
            "processed_content": "Processed content",
            "summary": "Content summary"
        })
        content_processor.generate_summary = AsyncMock(return_value="Generated summary")
        
        data_integrator = Mock()
        data_integrator.register_data_source = AsyncMock(return_value="source_001")
        data_integrator.integrate_data = AsyncMock(return_value="integration_001")
        data_integrator.get_integrated_data = AsyncMock(return_value=Mock(
            to_dict=Mock(return_value={"integration_id": "integration_001", "content": "integrated"})
        ))
        
        task_manager = Mock()
        execution_engine = Mock()
        
        return {
            "search_operations": search_operations,
            "file_operations": file_operations,
            "content_processor": content_processor,
            "data_integrator": data_integrator,
            "task_manager": task_manager,
            "execution_engine": execution_engine
        }
    
    @pytest.fixture
    def executor(self, mock_dependencies):
        """创建混合任务执行器实例"""
        return HybridTaskExecutor(**mock_dependencies)
    
    @pytest.mark.asyncio
    async def test_create_search_and_save_task(self, executor):
        """测试创建搜索并保存任务"""
        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="测试搜索保存任务",
            description="搜索内容并保存到文件",
            parameters={
                "query": "Python编程",
                "max_results": 5,
                "output_file": "results.json"
            }
        )
        
        assert task_id is not None
        assert task_id in executor.active_tasks
        
        task = executor.active_tasks[task_id]
        assert task.task_type == HybridTaskType.SEARCH_AND_SAVE
        assert task.title == "测试搜索保存任务"
        assert len(task.steps) == 3  # 搜索、处理、保存
        
        # 验证步骤类型
        step_types = [step.step_type for step in task.steps]
        assert TaskStepType.SEARCH in step_types
        assert TaskStepType.DATA_PROCESSING in step_types
        assert TaskStepType.FILE_OPERATION in step_types
    
    @pytest.mark.asyncio
    async def test_create_research_and_analyze_task(self, executor):
        """测试创建调研分析任务"""
        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.RESEARCH_AND_ANALYZE,
            title="测试调研分析任务",
            description="多主题调研并生成分析报告",
            parameters={
                "topics": ["人工智能", "机器学习", "深度学习"],
                "max_results_per_topic": 10,
                "report_format": "markdown"
            }
        )
        
        assert task_id is not None
        task = executor.active_tasks[task_id]
        assert task.task_type == HybridTaskType.RESEARCH_AND_ANALYZE
        assert len(task.steps) == 3  # 搜索、分析、报告
    
    @pytest.mark.asyncio
    async def test_create_collect_and_integrate_task(self, executor):
        """测试创建收集整合任务"""
        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.COLLECT_AND_INTEGRATE,
            title="测试收集整合任务",
            description="收集多源数据并整合",
            parameters={
                "query": "collect and integrate test",  # 添加查询参数
                "sources": ["source1", "source2", "source3"],
                "integration_type": "merge",
                "quality_threshold": 0.8
            }
        )
        
        assert task_id is not None
        task = executor.active_tasks[task_id]
        assert task.task_type == HybridTaskType.COLLECT_AND_INTEGRATE
        assert len(task.steps) == 3  # 收集、整合、验证
    
    @pytest.mark.asyncio
    async def test_execute_search_and_save_task(self, executor):
        """测试执行搜索并保存任务"""
        # 创建任务
        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="执行测试任务",
            description="测试任务执行",
            parameters={
                "query": "测试查询",
                "max_results": 3,
                "output_file": "test_output.json"
            }
        )
        
        # 执行任务
        report = await executor.execute_hybrid_task(task_id)
        
        # 验证报告
        assert isinstance(report, ExecutionReport)
        assert report.task_id == task_id
        assert report.total_steps == 3
        assert report.success_rate >= 0  # 可能有步骤失败
        assert len(report.step_results) == 3
        
        # 验证任务已移动到已完成
        assert task_id not in executor.active_tasks
        assert task_id in executor.completed_tasks
        assert task_id in executor.execution_reports
    
    @pytest.mark.asyncio
    async def test_execute_research_and_analyze_task(self, executor):
        """测试执行调研分析任务"""
        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.RESEARCH_AND_ANALYZE,
            title="调研分析测试",
            description="测试调研分析功能",
            parameters={
                "topics": ["AI", "ML"],
                "max_results_per_topic": 5,
                "include_trends": True,
                "include_correlations": True
            }
        )
        
        report = await executor.execute_hybrid_task(task_id)
        
        assert report.task_id == task_id
        assert report.total_steps == 3
        assert len(report.step_results) == 3
    
    @pytest.mark.asyncio
    async def test_get_task_status(self, executor):
        """测试获取任务状态"""
        # 测试活跃任务状态
        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="状态测试任务",
            description="测试任务状态获取",
            parameters={"query": "test"}
        )
        
        status = await executor.get_task_status(task_id)
        assert status["task_id"] == task_id
        assert status["status"] == "active"
        assert status["task_type"] == "search_and_save"
        
        # 执行任务后测试已完成状态
        await executor.execute_hybrid_task(task_id)
        
        status = await executor.get_task_status(task_id)
        assert status["status"] == "completed"
        assert "success_rate" in status
        assert "completed_at" in status
        
        # 测试不存在的任务
        status = await executor.get_task_status("non_existent")
        assert status["status"] == "not_found"
    
    @pytest.mark.asyncio
    async def test_cancel_task(self, executor):
        """测试取消任务"""
        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="取消测试任务",
            description="测试任务取消功能",
            parameters={"query": "test"}
        )
        
        # 取消任务
        result = await executor.cancel_task(task_id)
        assert result is True
        
        # 验证任务已从活跃列表移除
        assert task_id not in executor.active_tasks
        
        # 验证生成了取消报告
        assert task_id in executor.execution_reports
        report = executor.execution_reports[task_id]
        assert report.success_rate == 0
        assert "取消" in report.recommendations[0]
        
        # 测试取消不存在的任务
        result = await executor.cancel_task("non_existent")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_list_tasks(self, executor):
        """测试列出任务"""
        # 创建多个任务
        task_id1 = await executor.create_hybrid_task(
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="任务1",
            description="测试任务1",
            parameters={"query": "test1"}
        )
        
        task_id2 = await executor.create_hybrid_task(
            task_type=HybridTaskType.RESEARCH_AND_ANALYZE,
            title="任务2",
            description="测试任务2",
            parameters={"topics": ["test2"]}
        )
        
        # 执行一个任务
        await executor.execute_hybrid_task(task_id1)
        
        # 测试列出所有任务
        all_tasks = await executor.list_tasks("all")
        assert len(all_tasks) == 2
        
        # 测试列出活跃任务
        active_tasks = await executor.list_tasks("active")
        assert len(active_tasks) == 1
        assert active_tasks[0]["task_id"] == task_id2
        
        # 测试列出已完成任务
        completed_tasks = await executor.list_tasks("completed")
        assert len(completed_tasks) == 1
        assert completed_tasks[0]["task_id"] == task_id1
    
    @pytest.mark.asyncio
    async def test_get_execution_statistics(self, executor):
        """测试获取执行统计"""
        # 初始统计
        stats = await executor.get_execution_statistics()
        assert stats["total_tasks"] == 0
        assert stats["active_tasks"] == 0
        
        # 创建并执行任务
        task_id1 = await executor.create_hybrid_task(
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="统计测试任务1",
            description="测试统计功能",
            parameters={"query": "test1"}
        )
        
        task_id2 = await executor.create_hybrid_task(
            task_type=HybridTaskType.RESEARCH_AND_ANALYZE,
            title="统计测试任务2",
            description="测试统计功能",
            parameters={"topics": ["test2"]}
        )
        
        await executor.execute_hybrid_task(task_id1)
        await executor.execute_hybrid_task(task_id2)
        
        # 获取统计
        stats = await executor.get_execution_statistics()
        assert stats["total_tasks"] == 2
        assert stats["active_tasks"] == 0
        assert "average_success_rate" in stats
        assert "task_type_distribution" in stats
        assert "average_execution_time" in stats
        
        # 验证任务类型分布
        distribution = stats["task_type_distribution"]
        assert "search_and_save" in distribution
        assert "research_and_analyze" in distribution
    
    @pytest.mark.asyncio
    async def test_step_execution_with_dependencies(self, executor):
        """测试带依赖关系的步骤执行"""
        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="依赖测试任务",
            description="测试步骤依赖执行",
            parameters={"query": "dependency test"}
        )
        
        task = executor.active_tasks[task_id]
        
        # 验证依赖关系
        search_step = next(s for s in task.steps if s.step_type == TaskStepType.SEARCH)
        process_step = next(s for s in task.steps if s.step_type == TaskStepType.DATA_PROCESSING)
        save_step = next(s for s in task.steps if s.step_type == TaskStepType.FILE_OPERATION)
        
        assert len(search_step.dependencies) == 0  # 搜索步骤无依赖
        assert search_step.step_id in process_step.dependencies  # 处理步骤依赖搜索
        assert process_step.step_id in save_step.dependencies  # 保存步骤依赖处理
        
        # 执行任务并验证步骤按正确顺序执行
        report = await executor.execute_hybrid_task(task_id)
        
        # 验证所有步骤都被执行
        assert len(report.step_results) == 3
        
        # 验证步骤执行顺序（通过时间戳）
        step_results_by_id = {r.step_id: r for r in report.step_results}
        
        search_result = step_results_by_id[search_step.step_id]
        process_result = step_results_by_id[process_step.step_id]
        save_result = step_results_by_id[save_step.step_id]
        
        # 搜索应该在处理之前完成
        assert search_result.timestamp <= process_result.timestamp
        # 处理应该在保存之前完成
        assert process_result.timestamp <= save_result.timestamp

    @pytest.mark.asyncio
    async def test_step_retry_mechanism(self, executor):
        """测试步骤重试机制"""
        # 模拟搜索操作失败
        executor.search_operations.search.side_effect = [
            Exception("Network error"),  # 第一次失败
            Exception("Timeout error"),  # 第二次失败
            [{"title": "Success", "content": "Success content"}]  # 第三次成功
        ]

        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="重试测试任务",
            description="测试步骤重试功能",
            parameters={"query": "retry test"}
        )

        # 修改步骤的最大重试次数
        task = executor.active_tasks[task_id]
        for step in task.steps:
            if step.step_type == TaskStepType.SEARCH:
                step.max_retries = 3

        report = await executor.execute_hybrid_task(task_id)

        # 验证搜索步骤最终成功（经过重试）
        search_results = [r for r in report.step_results if r.step_id.startswith("search")]
        assert len(search_results) == 1
        search_result = search_results[0]
        assert search_result.status == ExecutionStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_step_failure_handling(self, executor):
        """测试步骤失败处理"""
        # 模拟搜索操作持续失败
        executor.search_operations.search.side_effect = Exception("Persistent error")

        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="失败处理测试",
            description="测试步骤失败处理",
            parameters={"query": "failure test"}
        )

        # 设置较少的重试次数
        task = executor.active_tasks[task_id]
        for step in task.steps:
            step.max_retries = 1

        report = await executor.execute_hybrid_task(task_id)

        # 验证搜索步骤失败
        search_results = [r for r in report.step_results if r.step_id.startswith("search")]
        assert len(search_results) == 1
        search_result = search_results[0]
        assert search_result.status == ExecutionStatus.FAILED
        assert "Persistent error" in search_result.error_message

        # 验证依赖步骤被跳过或失败
        assert report.failed_steps >= 1
        assert report.success_rate < 1.0

    @pytest.mark.asyncio
    async def test_data_integration_step(self, executor):
        """测试数据整合步骤"""
        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.COLLECT_AND_INTEGRATE,
            title="整合测试任务",
            description="测试数据整合功能",
            parameters={
                "query": "integration test data",  # 添加查询参数
                "sources": ["source1", "source2"],
                "integration_type": "merge"
            }
        )

        report = await executor.execute_hybrid_task(task_id)

        # 验证数据整合器被调用
        assert executor.data_integrator.register_data_source.called
        assert executor.data_integrator.integrate_data.called
        assert executor.data_integrator.get_integrated_data.called

        # 验证整合步骤成功
        integration_results = [r for r in report.step_results if r.step_id.startswith("integrate")]
        if integration_results:
            integration_result = integration_results[0]
            assert integration_result.status == ExecutionStatus.COMPLETED
            assert "integration_id" in integration_result.result_data

    @pytest.mark.asyncio
    async def test_validation_step(self, executor):
        """测试验证步骤"""
        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.VALIDATE_AND_UPDATE,
            title="验证测试任务",
            description="测试数据验证功能",
            parameters={
                "data_sources": ["source1"],
                "validation_rules": [
                    {"type": "required_field", "field": "search_results"},
                    {"type": "min_items", "field": "search_results", "min_count": 1}
                ]
            }
        )

        report = await executor.execute_hybrid_task(task_id)

        # 验证验证步骤执行
        validation_results = [r for r in report.step_results if r.step_id.startswith("validate")]
        if validation_results:
            validation_result = validation_results[0]
            assert "validation_result" in validation_result.result_data
            validation_data = validation_result.result_data["validation_result"]
            assert "is_valid" in validation_data
            assert "validation_errors" in validation_data
            assert "quality_score" in validation_data

    @pytest.mark.asyncio
    async def test_monitoring_task_execution(self, executor):
        """测试监控任务执行"""
        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.MONITOR_AND_REPORT,
            title="监控测试任务",
            description="测试监控功能",
            parameters={
                "targets": ["target1", "target2"],
                "interval": 60,
                "report_path": "monitor_report.json"
            }
        )

        report = await executor.execute_hybrid_task(task_id)

        # 验证监控任务包含正确的步骤
        assert report.total_steps == 3  # 监控、分析、报告

        # 验证报告生成
        report_results = [r for r in report.step_results if r.step_id.startswith("monitor_report")]
        if report_results:
            report_result = report_results[0]
            assert "report_path" in report_result.result_data
            assert report_result.result_data["report_path"] == "monitor_report.json"

    @pytest.mark.asyncio
    async def test_concurrent_task_execution(self, executor):
        """测试并发任务执行"""
        # 创建多个任务
        task_ids = []
        for i in range(3):
            task_id = await executor.create_hybrid_task(
                task_type=HybridTaskType.SEARCH_AND_SAVE,
                title=f"并发测试任务{i+1}",
                description=f"测试并发执行{i+1}",
                parameters={"query": f"concurrent test {i+1}"}
            )
            task_ids.append(task_id)

        # 并发执行任务
        execution_tasks = [
            executor.execute_hybrid_task(task_id)
            for task_id in task_ids
        ]

        reports = await asyncio.gather(*execution_tasks)

        # 验证所有任务都执行完成
        assert len(reports) == 3
        for i, report in enumerate(reports):
            assert report.task_id == task_ids[i]
            assert report.total_steps > 0

    @pytest.mark.asyncio
    async def test_task_timeout_handling(self, executor):
        """测试任务超时处理"""
        # 模拟长时间运行的操作
        async def slow_search(*args, **kwargs):
            await asyncio.sleep(2)  # 模拟慢速搜索
            return [{"title": "Slow result", "content": "Slow content"}]

        executor.search_operations.search = slow_search

        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="超时测试任务",
            description="测试任务超时处理",
            parameters={
                "query": "timeout test",
                "timeout": 1  # 设置1秒超时
            }
        )

        # 修改任务超时设置
        task = executor.active_tasks[task_id]
        task.timeout = 1

        # 执行任务（可能会超时，但不应该崩溃）
        try:
            report = await executor.execute_hybrid_task(task_id)
            # 如果没有超时，验证报告正常
            assert isinstance(report, ExecutionReport)
        except asyncio.TimeoutError:
            # 如果超时，这是预期的行为
            pass

    @pytest.mark.asyncio
    async def test_error_recovery_and_recommendations(self, executor):
        """测试错误恢复和建议生成"""
        # 模拟部分步骤失败
        executor.search_operations.search.side_effect = [
            [{"title": "Success", "content": "Success"}],  # 搜索成功
        ]
        executor.content_processor.process_content.side_effect = Exception("Processing failed")  # 处理失败

        task_id = await executor.create_hybrid_task(
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="错误恢复测试",
            description="测试错误恢复和建议",
            parameters={"query": "error recovery test"}
        )

        report = await executor.execute_hybrid_task(task_id)

        # 验证有失败步骤
        assert report.failed_steps > 0
        assert report.success_rate < 1.0

        # 验证生成了建议
        assert len(report.recommendations) > 0
        assert any("失败" in rec for rec in report.recommendations)

    def test_task_step_to_dict(self):
        """测试TaskStep转字典功能"""
        step = TaskStep(
            step_id="test_001",
            step_type=TaskStepType.SEARCH,
            description="测试步骤",
            parameters={"query": "test"},
            dependencies=["dep_001"],
            estimated_duration=60
        )

        step_dict = step.to_dict()
        assert step_dict["step_id"] == "test_001"
        assert step_dict["step_type"] == "search"
        assert step_dict["description"] == "测试步骤"
        assert step_dict["parameters"] == {"query": "test"}
        assert step_dict["dependencies"] == ["dep_001"]
        assert step_dict["estimated_duration"] == 60

    def test_step_result_to_dict(self):
        """测试StepResult转字典功能"""
        result = StepResult(
            step_id="test_001",
            status=ExecutionStatus.COMPLETED,
            result_data={"result": "success"},
            error_message=None,
            execution_time=1.5,
            timestamp=datetime.now()
        )

        result_dict = result.to_dict()
        assert result_dict["step_id"] == "test_001"
        assert result_dict["status"] == "completed"
        assert result_dict["result_data"] == {"result": "success"}
        assert result_dict["error_message"] is None
        assert result_dict["execution_time"] == 1.5
        assert "timestamp" in result_dict

    def test_hybrid_task_to_dict(self):
        """测试HybridTask转字典功能"""
        step = TaskStep(
            step_id="step_001",
            step_type=TaskStepType.SEARCH,
            description="测试步骤",
            parameters={},
            dependencies=[],
            estimated_duration=30
        )

        task = HybridTask(
            task_id="task_001",
            task_type=HybridTaskType.SEARCH_AND_SAVE,
            title="测试任务",
            description="测试任务描述",
            steps=[step],
            context={"test": "context"}
        )

        task_dict = task.to_dict()
        assert task_dict["task_id"] == "task_001"
        assert task_dict["task_type"] == "search_and_save"
        assert task_dict["title"] == "测试任务"
        assert task_dict["description"] == "测试任务描述"
        assert len(task_dict["steps"]) == 1
        assert task_dict["context"] == {"test": "context"}
        assert "created_at" in task_dict


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
