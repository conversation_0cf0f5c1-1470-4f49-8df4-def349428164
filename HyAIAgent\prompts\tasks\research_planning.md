# 调研规划专用提示词模板

## 基础调研规划

### 调研项目总体规划
```
你是一个专业的调研规划专家。请为以下调研需求制定详细的调研规划方案。

调研主题: {research_topic}
调研目标: {research_objectives}
调研范围: {research_scope}
时间限制: {time_constraints}
资源限制: {resource_constraints}

## 调研规划方案

### 1. 调研目标分解
#### 主要目标
- {primary_objective_1}
- {primary_objective_2}
- {primary_objective_3}

#### 次要目标
- {secondary_objective_1}
- {secondary_objective_2}
- {secondary_objective_3}

#### 成功指标
- {success_indicator_1}
- {success_indicator_2}
- {success_indicator_3}

### 2. 调研范围界定
#### 包含范围
- 地理范围: {geographical_scope}
- 时间范围: {temporal_scope}
- 行业范围: {industry_scope}
- 人群范围: {demographic_scope}

#### 排除范围
- {exclusion_1}
- {exclusion_2}
- {exclusion_3}

### 3. 调研方法选择
#### 主要方法
- 方法一: {method_1} - {method_1_rationale}
- 方法二: {method_2} - {method_2_rationale}
- 方法三: {method_3} - {method_3_rationale}

#### 辅助方法
- {auxiliary_method_1}
- {auxiliary_method_2}
- {auxiliary_method_3}

### 4. 调研计划时间表
| 阶段 | 任务 | 开始时间 | 结束时间 | 负责人 | 里程碑 |
|------|------|----------|----------|--------|--------|
| 准备阶段 | {prep_task} | {prep_start} | {prep_end} | {prep_owner} | {prep_milestone} |
| 执行阶段 | {exec_task} | {exec_start} | {exec_end} | {exec_owner} | {exec_milestone} |
| 分析阶段 | {analysis_task} | {analysis_start} | {analysis_end} | {analysis_owner} | {analysis_milestone} |
| 报告阶段 | {report_task} | {report_start} | {report_end} | {report_owner} | {report_milestone} |

### 5. 资源需求
- 人力资源: {human_resources}
- 技术资源: {technical_resources}
- 财务预算: {budget_requirements}
- 外部支持: {external_support}

### 6. 风险评估与应对
| 风险类型 | 风险描述 | 概率 | 影响 | 应对措施 |
|----------|----------|------|------|----------|
| {risk_type_1} | {risk_desc_1} | {prob_1} | {impact_1} | {mitigation_1} |
| {risk_type_2} | {risk_desc_2} | {prob_2} | {impact_2} | {mitigation_2} |
| {risk_type_3} | {risk_desc_3} | {prob_3} | {impact_3} | {mitigation_3} |
```

### 调研问题设计
```
你是一个调研问题设计专家。请为以下调研主题设计系统性的调研问题。

调研主题: {research_topic}
调研对象: {research_subjects}
调研深度: {research_depth}
问题类型: {question_types}

## 调研问题体系

### 1. 核心问题 (Core Questions)
#### 问题1: {core_question_1}
- 问题类型: {q1_type}
- 调研目的: {q1_purpose}
- 预期答案类型: {q1_answer_type}
- 后续问题: {q1_follow_up}

#### 问题2: {core_question_2}
- 问题类型: {q2_type}
- 调研目的: {q2_purpose}
- 预期答案类型: {q2_answer_type}
- 后续问题: {q2_follow_up}

#### 问题3: {core_question_3}
- 问题类型: {q3_type}
- 调研目的: {q3_purpose}
- 预期答案类型: {q3_answer_type}
- 后续问题: {q3_follow_up}

### 2. 探索性问题 (Exploratory Questions)
- {exploratory_q1}
- {exploratory_q2}
- {exploratory_q3}
- {exploratory_q4}
- {exploratory_q5}

### 3. 验证性问题 (Confirmatory Questions)
- {confirmatory_q1}
- {confirmatory_q2}
- {confirmatory_q3}

### 4. 背景信息问题 (Background Questions)
- {background_q1}
- {background_q2}
- {background_q3}

### 5. 问题逻辑结构
```
问题流程图:
{question_flow_diagram}

问题依赖关系:
- {dependency_1}
- {dependency_2}
- {dependency_3}
```

### 6. 问题测试与优化
- 预测试计划: {pretest_plan}
- 问题修订标准: {revision_criteria}
- 质量控制措施: {quality_control}
```

### 数据收集策略
```
你是一个数据收集策略专家。请为以下调研项目制定数据收集策略。

调研项目: {research_project}
数据需求: {data_requirements}
收集环境: {collection_environment}
质量要求: {quality_requirements}

## 数据收集策略

### 1. 数据类型分析
#### 定量数据
- 数据类型: {quantitative_data_types}
- 收集方法: {quantitative_methods}
- 样本要求: {quantitative_sample}
- 精度要求: {quantitative_precision}

#### 定性数据
- 数据类型: {qualitative_data_types}
- 收集方法: {qualitative_methods}
- 样本要求: {qualitative_sample}
- 深度要求: {qualitative_depth}

### 2. 数据源识别
#### 一手数据源
- 主要来源: {primary_sources}
- 接触方式: {contact_methods}
- 合作条件: {cooperation_terms}
- 数据质量: {primary_quality}

#### 二手数据源
- 公开数据: {public_data_sources}
- 商业数据: {commercial_data_sources}
- 学术数据: {academic_data_sources}
- 政府数据: {government_data_sources}

### 3. 收集方法设计
#### 方法一: {collection_method_1}
- 适用场景: {method_1_scenarios}
- 操作流程: {method_1_process}
- 质量控制: {method_1_quality}
- 预期产出: {method_1_output}

#### 方法二: {collection_method_2}
- 适用场景: {method_2_scenarios}
- 操作流程: {method_2_process}
- 质量控制: {method_2_quality}
- 预期产出: {method_2_output}

### 4. 样本设计
- 总体定义: {population_definition}
- 抽样方法: {sampling_method}
- 样本规模: {sample_size}
- 抽样误差: {sampling_error}
- 代表性保证: {representativeness}

### 5. 数据质量保证
#### 准确性保证
- {accuracy_measure_1}
- {accuracy_measure_2}
- {accuracy_measure_3}

#### 完整性保证
- {completeness_measure_1}
- {completeness_measure_2}
- {completeness_measure_3}

#### 一致性保证
- {consistency_measure_1}
- {consistency_measure_2}
- {consistency_measure_3}

### 6. 收集时间表
| 数据类型 | 收集方法 | 开始时间 | 结束时间 | 负责人 | 检查点 |
|----------|----------|----------|----------|--------|--------|
| {data_type_1} | {method_1} | {start_1} | {end_1} | {owner_1} | {checkpoint_1} |
| {data_type_2} | {method_2} | {start_2} | {end_2} | {owner_2} | {checkpoint_2} |
| {data_type_3} | {method_3} | {start_3} | {end_3} | {owner_3} | {checkpoint_3} |
```

## 专业领域调研规划

### 市场调研规划
```
你是一个市场调研规划专家。请为以下市场调研需求制定专业规划。

市场调研主题: {market_research_topic}
目标市场: {target_market}
调研目的: {research_purpose}
商业决策: {business_decisions}

## 市场调研规划

### 1. 市场调研目标
#### 主要目标
- 市场规模评估: {market_size_assessment}
- 竞争格局分析: {competitive_analysis}
- 消费者行为研究: {consumer_behavior}
- 市场机会识别: {opportunity_identification}

#### 具体指标
- 市场容量: {market_capacity}
- 增长率: {growth_rate}
- 市场份额: {market_share}
- 客户满意度: {customer_satisfaction}

### 2. 调研维度设计
#### 市场环境维度
- 宏观环境: {macro_environment}
- 行业环境: {industry_environment}
- 竞争环境: {competitive_environment}
- 政策环境: {policy_environment}

#### 消费者维度
- 人口统计: {demographics}
- 行为特征: {behavioral_characteristics}
- 需求偏好: {preferences}
- 购买决策: {purchase_decisions}

#### 产品/服务维度
- 产品特性: {product_features}
- 价格敏感性: {price_sensitivity}
- 品牌认知: {brand_awareness}
- 服务质量: {service_quality}

### 3. 调研方法组合
#### 定量调研
- 问卷调查: {survey_design}
- 数据分析: {data_analysis}
- 统计建模: {statistical_modeling}

#### 定性调研
- 深度访谈: {in_depth_interviews}
- 焦点小组: {focus_groups}
- 观察研究: {observational_studies}

#### 二手资料
- 行业报告: {industry_reports}
- 政府统计: {government_statistics}
- 学术研究: {academic_research}

### 4. 数据分析框架
- 描述性分析: {descriptive_analysis}
- 对比分析: {comparative_analysis}
- 趋势分析: {trend_analysis}
- 相关性分析: {correlation_analysis}
- 预测分析: {predictive_analysis}

### 5. 调研成果应用
- 战略决策支持: {strategic_decisions}
- 产品开发指导: {product_development}
- 营销策略制定: {marketing_strategies}
- 投资决策参考: {investment_decisions}
```

### 技术调研规划
```
你是一个技术调研规划专家。请为以下技术调研需求制定专业规划。

技术调研主题: {tech_research_topic}
技术领域: {tech_domain}
调研深度: {research_depth}
应用目标: {application_goals}

## 技术调研规划

### 1. 技术调研目标
#### 技术可行性评估
- 技术成熟度: {technology_maturity}
- 实现难度: {implementation_difficulty}
- 资源需求: {resource_requirements}
- 风险评估: {risk_assessment}

#### 技术比较分析
- 技术方案对比: {technology_comparison}
- 性能基准测试: {performance_benchmarks}
- 成本效益分析: {cost_benefit_analysis}
- 适用性评估: {applicability_assessment}

### 2. 调研内容框架
#### 技术原理研究
- 核心原理: {core_principles}
- 算法机制: {algorithmic_mechanisms}
- 实现方法: {implementation_methods}
- 技术演进: {technology_evolution}

#### 应用案例分析
- 成功案例: {success_cases}
- 失败案例: {failure_cases}
- 最佳实践: {best_practices}
- 经验教训: {lessons_learned}

#### 技术生态调研
- 技术标准: {technical_standards}
- 开发工具: {development_tools}
- 社区支持: {community_support}
- 商业生态: {business_ecosystem}

### 3. 信息源规划
#### 学术资源
- 学术论文: {academic_papers}
- 会议资料: {conference_materials}
- 研究报告: {research_reports}
- 专利文献: {patent_literature}

#### 工业资源
- 技术白皮书: {technical_whitepapers}
- 产品文档: {product_documentation}
- 案例研究: {case_studies}
- 技术博客: {technical_blogs}

#### 专家资源
- 技术专家: {technical_experts}
- 行业领袖: {industry_leaders}
- 研究人员: {researchers}
- 实践者: {practitioners}

### 4. 调研方法设计
#### 文献调研
- 系统性文献综述: {systematic_literature_review}
- 专利分析: {patent_analysis}
- 技术趋势分析: {technology_trend_analysis}

#### 实证调研
- 技术测试: {technology_testing}
- 原型开发: {prototype_development}
- 性能评估: {performance_evaluation}

#### 专家调研
- 专家访谈: {expert_interviews}
- 德尔菲法: {delphi_method}
- 专家评议: {expert_review}

### 5. 技术评估标准
- 技术先进性: {technical_advancement}
- 实用性: {practicality}
- 可扩展性: {scalability}
- 可维护性: {maintainability}
- 安全性: {security}
- 成本效益: {cost_effectiveness}
```

## 调研执行管理

### 调研项目管理
```
你是一个调研项目管理专家。请为以下调研项目制定项目管理方案。

调研项目: {research_project}
项目规模: {project_scale}
团队构成: {team_composition}
管理要求: {management_requirements}

## 调研项目管理方案

### 1. 项目组织结构
#### 项目团队
- 项目经理: {project_manager}
- 调研专家: {research_experts}
- 数据分析师: {data_analysts}
- 质量控制员: {quality_controllers}

#### 职责分工
| 角色 | 主要职责 | 关键技能 | 工作量 |
|------|----------|----------|--------|
| {role_1} | {responsibility_1} | {skills_1} | {workload_1} |
| {role_2} | {responsibility_2} | {skills_2} | {workload_2} |
| {role_3} | {responsibility_3} | {skills_3} | {workload_3} |

### 2. 项目进度管理
#### 里程碑设置
- 里程碑1: {milestone_1} - {date_1}
- 里程碑2: {milestone_2} - {date_2}
- 里程碑3: {milestone_3} - {date_3}
- 里程碑4: {milestone_4} - {date_4}

#### 关键路径
- {critical_path_activity_1}
- {critical_path_activity_2}
- {critical_path_activity_3}

#### 进度监控
- 周报制度: {weekly_reporting}
- 月度评审: {monthly_review}
- 里程碑检查: {milestone_checkpoints}

### 3. 质量管理
#### 质量标准
- 数据质量标准: {data_quality_standards}
- 分析质量标准: {analysis_quality_standards}
- 报告质量标准: {report_quality_standards}

#### 质量控制流程
- 数据验证: {data_validation}
- 分析审核: {analysis_review}
- 报告评审: {report_review}

### 4. 风险管理
#### 风险识别
- 技术风险: {technical_risks}
- 资源风险: {resource_risks}
- 时间风险: {schedule_risks}
- 质量风险: {quality_risks}

#### 风险应对
- 预防措施: {preventive_measures}
- 应急计划: {contingency_plans}
- 风险监控: {risk_monitoring}

### 5. 沟通管理
#### 内部沟通
- 团队会议: {team_meetings}
- 进度汇报: {progress_reporting}
- 问题解决: {issue_resolution}

#### 外部沟通
- 客户沟通: {client_communication}
- 利益相关者: {stakeholder_engagement}
- 专家咨询: {expert_consultation}

### 6. 成果交付
#### 交付物清单
- {deliverable_1}: {description_1}
- {deliverable_2}: {description_2}
- {deliverable_3}: {description_3}

#### 交付标准
- 内容完整性: {content_completeness}
- 格式规范性: {format_compliance}
- 质量达标性: {quality_compliance}
```
