"""
工作流引擎测试模块

测试工作流引擎的各项功能，包括工作流创建、任务管理、执行控制等。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

from tools.workflow_engine import (
    WorkflowEngine, Workflow, Task, TaskResult,
    TaskStatus, WorkflowStatus, TaskType
)


class TestWorkflowEngine:
    """工作流引擎测试类"""
    
    @pytest.fixture
    def engine(self):
        """创建工作流引擎实例"""
        return WorkflowEngine()
    
    @pytest.fixture
    def sample_task(self):
        """创建示例任务"""
        return Task(
            task_id="task_001",
            name="测试任务",
            task_type=TaskType.FUNCTION,
            config={
                "function_name": "test_function",
                "parameters": {"param1": "value1"},
                "output_variable": "result"
            }
        )
    
    @pytest.fixture
    def sample_workflow(self, sample_task):
        """创建示例工作流"""
        workflow = Workflow(
            workflow_id="workflow_001",
            name="测试工作流",
            description="用于测试的工作流",
            start_task_id="task_001"
        )
        workflow.tasks[sample_task.task_id] = sample_task
        return workflow
    
    @pytest.mark.asyncio
    async def test_init(self, engine):
        """测试初始化"""
        assert engine is not None
        assert engine.workflows == {}
        assert engine.executions == {}
        assert len(engine.task_handlers) > 0
        assert TaskType.FUNCTION in engine.task_handlers
    
    @pytest.mark.asyncio
    async def test_create_workflow(self, engine, sample_workflow):
        """测试创建工作流"""
        result = await engine.create_workflow(sample_workflow)
        assert result is True
        assert sample_workflow.workflow_id in engine.workflows
        
        retrieved_workflow = await engine.get_workflow(sample_workflow.workflow_id)
        assert retrieved_workflow is not None
        assert retrieved_workflow.name == sample_workflow.name
    
    @pytest.mark.asyncio
    async def test_list_workflows(self, engine, sample_workflow):
        """测试列出工作流"""
        await engine.create_workflow(sample_workflow)
        
        workflows = await engine.list_workflows()
        assert len(workflows) == 1
        assert workflows[0].workflow_id == sample_workflow.workflow_id
    
    @pytest.mark.asyncio
    async def test_add_task(self, engine, sample_workflow):
        """测试添加任务"""
        await engine.create_workflow(sample_workflow)
        
        new_task = Task(
            task_id="task_002",
            name="新任务",
            task_type=TaskType.DELAY,
            config={"delay_seconds": 1}
        )
        
        result = await engine.add_task(sample_workflow.workflow_id, new_task)
        assert result is True
        
        workflow = await engine.get_workflow(sample_workflow.workflow_id)
        assert new_task.task_id in workflow.tasks
    
    @pytest.mark.asyncio
    async def test_remove_task(self, engine, sample_workflow):
        """测试移除任务"""
        await engine.create_workflow(sample_workflow)
        
        result = await engine.remove_task(sample_workflow.workflow_id, "task_001")
        assert result is True
        
        workflow = await engine.get_workflow(sample_workflow.workflow_id)
        assert "task_001" not in workflow.tasks
    
    @pytest.mark.asyncio
    async def test_remove_task_with_dependencies(self, engine, sample_workflow):
        """测试移除有依赖的任务"""
        await engine.create_workflow(sample_workflow)
        
        # 添加依赖任务
        dependent_task = Task(
            task_id="task_002",
            name="依赖任务",
            task_type=TaskType.FUNCTION,
            config={"function_name": "dependent_function"},
            dependencies=["task_001"]
        )
        await engine.add_task(sample_workflow.workflow_id, dependent_task)
        
        # 尝试移除被依赖的任务
        result = await engine.remove_task(sample_workflow.workflow_id, "task_001")
        assert result is False  # 应该失败
    
    @pytest.mark.asyncio
    async def test_execute_workflow(self, engine, sample_workflow):
        """测试执行工作流"""
        await engine.create_workflow(sample_workflow)
        
        execution_id = await engine.execute_workflow(sample_workflow.workflow_id)
        assert execution_id is not None
        assert execution_id in engine.executions
        
        # 等待一小段时间让异步执行开始
        await asyncio.sleep(0.1)
        
        execution = engine.executions[execution_id]
        assert execution.workflow_id == sample_workflow.workflow_id
        assert execution.status in [WorkflowStatus.RUNNING, WorkflowStatus.COMPLETED]
    
    @pytest.mark.asyncio
    async def test_find_start_tasks(self, engine):
        """测试找到起始任务"""
        workflow = Workflow(
            workflow_id="test_workflow",
            name="测试工作流",
            description="测试"
        )
        
        # 添加没有依赖的任务
        task1 = Task(
            task_id="task_001",
            name="起始任务1",
            task_type=TaskType.FUNCTION,
            config={}
        )
        
        task2 = Task(
            task_id="task_002",
            name="起始任务2",
            task_type=TaskType.FUNCTION,
            config={}
        )
        
        # 添加有依赖的任务
        task3 = Task(
            task_id="task_003",
            name="依赖任务",
            task_type=TaskType.FUNCTION,
            config={},
            dependencies=["task_001"]
        )
        
        workflow.tasks = {
            "task_001": task1,
            "task_002": task2,
            "task_003": task3
        }
        
        start_tasks = engine._find_start_tasks(workflow)
        assert len(start_tasks) == 2
        assert "task_001" in start_tasks
        assert "task_002" in start_tasks
        assert "task_003" not in start_tasks
    
    @pytest.mark.asyncio
    async def test_check_dependencies(self, engine):
        """测试检查任务依赖"""
        from tools.workflow_engine import WorkflowExecution
        
        execution = WorkflowExecution(
            execution_id="exec_001",
            workflow_id="workflow_001",
            status=WorkflowStatus.RUNNING
        )
        execution.completed_tasks.add("task_001")
        execution.task_results["task_001"] = TaskResult(
            task_id="task_001",
            status=TaskStatus.COMPLETED
        )
        
        # 测试依赖已完成的任务
        task_with_dep = Task(
            task_id="task_002",
            name="依赖任务",
            task_type=TaskType.FUNCTION,
            config={},
            dependencies=["task_001"]
        )
        
        result = engine._check_dependencies(execution, task_with_dep)
        assert result is True
        
        # 测试依赖未完成的任务
        task_with_missing_dep = Task(
            task_id="task_003",
            name="缺失依赖任务",
            task_type=TaskType.FUNCTION,
            config={},
            dependencies=["task_999"]
        )
        
        result = engine._check_dependencies(execution, task_with_missing_dep)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_check_conditions(self, engine):
        """测试检查任务条件"""
        from tools.workflow_engine import WorkflowExecution
        
        execution = WorkflowExecution(
            execution_id="exec_001",
            workflow_id="workflow_001",
            status=WorkflowStatus.RUNNING
        )
        execution.execution_context["test_var"] = "test_value"
        
        # 测试变量条件
        task_with_condition = Task(
            task_id="task_001",
            name="条件任务",
            task_type=TaskType.FUNCTION,
            config={},
            conditions=[{
                "type": "variable_equals",
                "variable": "test_var",
                "value": "test_value"
            }]
        )
        
        result = await engine._check_conditions(execution, task_with_condition)
        assert result is True
        
        # 测试条件不满足
        task_with_false_condition = Task(
            task_id="task_002",
            name="假条件任务",
            task_type=TaskType.FUNCTION,
            config={},
            conditions=[{
                "type": "variable_equals",
                "variable": "test_var",
                "value": "wrong_value"
            }]
        )
        
        result = await engine._check_conditions(execution, task_with_false_condition)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_handle_function_task(self, engine):
        """测试处理函数任务"""
        task = Task(
            task_id="func_task",
            name="函数任务",
            task_type=TaskType.FUNCTION,
            config={
                "function_name": "test_function",
                "parameters": {"param1": "value1"},
                "output_variable": "result"
            }
        )
        
        context = {}
        result = await engine._handle_function_task(task, context)
        
        assert result["function_name"] == "test_function"
        assert result["parameters"]["param1"] == "value1"
        assert "result" in context
    
    @pytest.mark.asyncio
    async def test_handle_http_request_task(self, engine):
        """测试处理HTTP请求任务"""
        task = Task(
            task_id="http_task",
            name="HTTP任务",
            task_type=TaskType.HTTP_REQUEST,
            config={
                "url": "https://api.example.com/data",
                "method": "GET",
                "output_variable": "api_result"
            }
        )
        
        context = {}
        result = await engine._handle_http_request_task(task, context)
        
        assert result["url"] == "https://api.example.com/data"
        assert result["method"] == "GET"
        assert result["status_code"] == 200
        assert "api_result" in context
    
    @pytest.mark.asyncio
    async def test_handle_delay_task(self, engine):
        """测试处理延迟任务"""
        task = Task(
            task_id="delay_task",
            name="延迟任务",
            task_type=TaskType.DELAY,
            config={"delay_seconds": 0.1}  # 使用很短的延迟进行测试
        )
        
        start_time = datetime.now()
        result = await engine._handle_delay_task(task, {})
        end_time = datetime.now()
        
        assert result["delay_seconds"] == 0.1
        assert (end_time - start_time).total_seconds() >= 0.1
    
    @pytest.mark.asyncio
    async def test_resolve_parameters(self, engine):
        """测试解析参数"""
        context = {"var1": "value1", "var2": "value2"}
        parameters = {
            "static_param": "static_value",
            "dynamic_param": "${var1}",
            "nested": {
                "nested_param": "${var2}"
            }
        }
        
        resolved = engine._resolve_parameters(parameters, context)
        
        assert resolved["static_param"] == "static_value"
        assert resolved["dynamic_param"] == "value1"
        assert resolved["nested"]["nested_param"] == "value2"
    
    @pytest.mark.asyncio
    async def test_resolve_string(self, engine):
        """测试解析字符串"""
        context = {"name": "Alice", "age": "25"}
        
        text = "Hello ${name}, you are ${age} years old"
        resolved = engine._resolve_string(text, context)
        
        assert resolved == "Hello Alice, you are 25 years old"
    
    @pytest.mark.asyncio
    async def test_evaluate_condition(self, engine):
        """测试评估条件"""
        context = {"num1": 10, "num2": 5, "text": "hello world"}
        
        # 测试等于条件
        equals_condition = {
            "type": "equals",
            "left": "$num1",
            "right": 10
        }
        result = engine._evaluate_condition(equals_condition, context)
        assert result is True
        
        # 测试大于条件
        greater_condition = {
            "type": "greater_than",
            "left": "$num1",
            "right": "$num2"
        }
        result = engine._evaluate_condition(greater_condition, context)
        assert result is True
        
        # 测试包含条件
        contains_condition = {
            "type": "contains",
            "container": "$text",
            "item": "world"
        }
        result = engine._evaluate_condition(contains_condition, context)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_get_execution_status(self, engine, sample_workflow):
        """测试获取执行状态"""
        await engine.create_workflow(sample_workflow)
        execution_id = await engine.execute_workflow(sample_workflow.workflow_id)
        
        # 等待一小段时间
        await asyncio.sleep(0.1)
        
        status = await engine.get_execution_status(execution_id)
        assert status is not None
        assert status["execution_id"] == execution_id
        assert status["workflow_id"] == sample_workflow.workflow_id
        assert "status" in status
    
    @pytest.mark.asyncio
    async def test_cancel_execution(self, engine):
        """测试取消执行"""
        # 创建一个包含延迟任务的工作流，确保有时间取消
        workflow = Workflow(
            workflow_id="cancel_test_workflow",
            name="取消测试工作流",
            description="用于测试取消功能的工作流",
            start_task_id="delay_task"
        )

        delay_task = Task(
            task_id="delay_task",
            name="延迟任务",
            task_type=TaskType.DELAY,
            config={"delay_seconds": 2}  # 2秒延迟，足够时间取消
        )
        workflow.tasks["delay_task"] = delay_task

        await engine.create_workflow(workflow)
        execution_id = await engine.execute_workflow(workflow.workflow_id)

        # 等待一小段时间让执行开始
        await asyncio.sleep(0.1)

        result = await engine.cancel_execution(execution_id)
        assert result is True

        execution = engine.executions[execution_id]
        assert execution.status == WorkflowStatus.CANCELLED
    
    @pytest.mark.asyncio
    async def test_validate_workflow(self, engine):
        """测试验证工作流"""
        # 测试有效工作流
        valid_workflow = Workflow(
            workflow_id="valid_workflow",
            name="有效工作流",
            description="测试"
        )
        valid_workflow.tasks["task_001"] = Task(
            task_id="task_001",
            name="任务1",
            task_type=TaskType.FUNCTION,
            config={}
        )
        
        result = await engine.validate_workflow(valid_workflow)
        assert result["valid"] is True
        
        # 测试空工作流
        empty_workflow = Workflow(
            workflow_id="empty_workflow",
            name="空工作流",
            description="测试"
        )
        
        result = await engine.validate_workflow(empty_workflow)
        assert result["valid"] is False
        assert len(result["errors"]) > 0
    
    @pytest.mark.asyncio
    async def test_has_circular_dependency(self, engine):
        """测试检查循环依赖"""
        workflow = Workflow(
            workflow_id="circular_workflow",
            name="循环依赖工作流",
            description="测试"
        )
        
        # 创建循环依赖：task1 -> task2 -> task3 -> task1
        workflow.tasks["task1"] = Task(
            task_id="task1",
            name="任务1",
            task_type=TaskType.FUNCTION,
            config={},
            dependencies=["task3"]
        )
        workflow.tasks["task2"] = Task(
            task_id="task2",
            name="任务2",
            task_type=TaskType.FUNCTION,
            config={},
            dependencies=["task1"]
        )
        workflow.tasks["task3"] = Task(
            task_id="task3",
            name="任务3",
            task_type=TaskType.FUNCTION,
            config={},
            dependencies=["task2"]
        )
        
        result = engine._has_circular_dependency(workflow)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_get_workflow_statistics(self, engine, sample_workflow):
        """测试获取工作流统计"""
        await engine.create_workflow(sample_workflow)
        
        stats = await engine.get_workflow_statistics(sample_workflow.workflow_id)
        assert stats["workflow_id"] == sample_workflow.workflow_id
        assert stats["workflow_name"] == sample_workflow.name
        assert stats["total_tasks"] == 1
        assert stats["total_executions"] == 0
