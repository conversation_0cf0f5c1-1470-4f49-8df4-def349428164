@echo off
title PyQt6 Hello World 应用启动器

echo.
echo ================================================
echo PyQt6 Hello World 应用启动器
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo [成功] 检测到Python环境
python --version

echo.
echo [信息] 正在启动应用...
echo.

REM 运行Python启动器
python run.py

echo.
echo [信息] 应用程序已结束
pause
