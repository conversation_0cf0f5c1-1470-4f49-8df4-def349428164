"""
搜索策略优化器模块

提供搜索策略的动态优化功能，包括参数调优、策略选择、性能监控等。
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import statistics
from collections import defaultdict, deque

# 配置日志
logger = logging.getLogger(__name__)


class OptimizationStrategy(Enum):
    """优化策略枚举"""
    PERFORMANCE_FIRST = "performance_first"
    QUALITY_FIRST = "quality_first"
    BALANCED = "balanced"
    COST_EFFECTIVE = "cost_effective"


class SearchMetric(Enum):
    """搜索指标枚举"""
    RESPONSE_TIME = "response_time"
    RESULT_QUALITY = "result_quality"
    CACHE_HIT_RATE = "cache_hit_rate"
    SUCCESS_RATE = "success_rate"
    COST_PER_QUERY = "cost_per_query"


@dataclass
class SearchParameters:
    """搜索参数配置"""
    max_results: int = 5
    search_depth: str = "basic"
    timeout: int = 30
    include_images: bool = False
    include_raw_content: bool = False
    time_range: Optional[str] = None
    domains_include: List[str] = None
    domains_exclude: List[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchParameters':
        """从字典创建"""
        return cls(**data)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    response_time: float
    result_count: int
    quality_score: float
    cache_hit: bool
    success: bool
    cost: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


@dataclass
class OptimizationResult:
    """优化结果"""
    optimized_parameters: SearchParameters
    expected_improvement: Dict[str, float]
    confidence_score: float
    optimization_reason: str
    baseline_metrics: Dict[str, float]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'optimized_parameters': self.optimized_parameters.to_dict(),
            'expected_improvement': self.expected_improvement,
            'confidence_score': self.confidence_score,
            'optimization_reason': self.optimization_reason,
            'baseline_metrics': self.baseline_metrics
        }


class SearchStrategyOptimizer:
    """搜索策略优化器"""
    
    def __init__(self, config_manager=None):
        """初始化搜索策略优化器"""
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 性能历史记录
        self.performance_history = deque(maxlen=1000)
        self.parameter_performance = defaultdict(list)
        
        # 优化配置
        self.optimization_strategy = OptimizationStrategy.BALANCED
        self.min_samples_for_optimization = 10
        self.optimization_threshold = 0.1  # 10%改进阈值
        
        # 参数范围
        self.parameter_ranges = {
            'max_results': (1, 20),
            'timeout': (10, 120),
            'search_depth': ['basic', 'advanced']
        }
        
        self.logger.info("SearchStrategyOptimizer initialized")
    
    async def optimize_parameters(self, current_params: SearchParameters, 
                                query_context: Dict[str, Any] = None) -> OptimizationResult:
        """优化搜索参数"""
        try:
            # 分析历史性能
            baseline_metrics = self._calculate_baseline_metrics(current_params)
            
            # 根据上下文和历史数据生成优化建议
            optimized_params = await self._generate_optimized_parameters(
                current_params, query_context, baseline_metrics
            )
            
            # 预测改进效果
            expected_improvement = self._predict_improvement(
                current_params, optimized_params, baseline_metrics
            )
            
            # 计算置信度
            confidence = self._calculate_optimization_confidence(
                current_params, optimized_params
            )
            
            # 生成优化原因
            reason = self._generate_optimization_reason(
                current_params, optimized_params, expected_improvement
            )
            
            result = OptimizationResult(
                optimized_parameters=optimized_params,
                expected_improvement=expected_improvement,
                confidence_score=confidence,
                optimization_reason=reason,
                baseline_metrics=baseline_metrics
            )
            
            self.logger.info(f"参数优化完成，置信度: {confidence:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"参数优化失败: {str(e)}")
            raise
    
    async def record_performance(self, params: SearchParameters, metrics: PerformanceMetrics):
        """记录搜索性能"""
        try:
            # 添加到历史记录
            self.performance_history.append((params, metrics))
            
            # 按参数分组记录
            param_key = self._get_parameter_key(params)
            self.parameter_performance[param_key].append(metrics)
            
            # 限制每个参数组合的记录数量
            if len(self.parameter_performance[param_key]) > 100:
                self.parameter_performance[param_key] = \
                    self.parameter_performance[param_key][-100:]
            
            self.logger.debug(f"性能记录已更新: {param_key}")
            
        except Exception as e:
            self.logger.error(f"性能记录失败: {str(e)}")
    
    async def get_best_parameters(self, query_type: str = None) -> SearchParameters:
        """获取最佳参数配置"""
        try:
            if len(self.performance_history) < self.min_samples_for_optimization:
                # 数据不足，返回默认参数
                return SearchParameters()
            
            # 分析所有参数组合的性能
            best_params = None
            best_score = -1
            
            for param_key, metrics_list in self.parameter_performance.items():
                if len(metrics_list) < 3:  # 至少需要3个样本
                    continue
                
                # 计算综合评分
                score = self._calculate_composite_score(metrics_list)
                
                if score > best_score:
                    best_score = score
                    best_params = self._parse_parameter_key(param_key)
            
            if best_params is None:
                return SearchParameters()
            
            self.logger.info(f"最佳参数配置: 评分 {best_score:.2f}")
            return best_params
            
        except Exception as e:
            self.logger.error(f"获取最佳参数失败: {str(e)}")
            return SearchParameters()
    
    async def analyze_performance_trends(self) -> Dict[str, Any]:
        """分析性能趋势"""
        try:
            if len(self.performance_history) < 5:
                return {"status": "insufficient_data", "message": "数据不足，无法分析趋势"}
            
            # 按时间排序
            sorted_history = sorted(self.performance_history, 
                                  key=lambda x: x[1].timestamp)
            
            # 分析各项指标的趋势
            trends = {}
            
            # 响应时间趋势
            response_times = [m.response_time for _, m in sorted_history[-20:]]
            trends['response_time'] = self._analyze_metric_trend(response_times)
            
            # 质量评分趋势
            quality_scores = [m.quality_score for _, m in sorted_history[-20:]]
            trends['quality_score'] = self._analyze_metric_trend(quality_scores)
            
            # 成功率趋势
            success_rates = [1.0 if m.success else 0.0 for _, m in sorted_history[-20:]]
            trends['success_rate'] = self._analyze_metric_trend(success_rates)
            
            # 缓存命中率趋势
            cache_hits = [1.0 if m.cache_hit else 0.0 for _, m in sorted_history[-20:]]
            trends['cache_hit_rate'] = self._analyze_metric_trend(cache_hits)
            
            # 生成趋势摘要
            summary = self._generate_trend_summary(trends)
            
            result = {
                "status": "success",
                "trends": trends,
                "summary": summary,
                "sample_count": len(sorted_history),
                "analysis_timestamp": datetime.now().isoformat()
            }
            
            self.logger.info("性能趋势分析完成")
            return result
            
        except Exception as e:
            self.logger.error(f"性能趋势分析失败: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def set_optimization_strategy(self, strategy: OptimizationStrategy):
        """设置优化策略"""
        self.optimization_strategy = strategy
        self.logger.info(f"优化策略已设置为: {strategy.value}")
    
    def get_performance_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        try:
            if not self.performance_history:
                return {"status": "no_data", "message": "暂无性能数据"}
            
            # 提取所有指标
            response_times = [m.response_time for _, m in self.performance_history]
            quality_scores = [m.quality_score for _, m in self.performance_history]
            success_count = sum(1 for _, m in self.performance_history if m.success)
            cache_hit_count = sum(1 for _, m in self.performance_history if m.cache_hit)
            
            total_count = len(self.performance_history)
            
            stats = {
                "total_queries": total_count,
                "success_rate": success_count / total_count,
                "cache_hit_rate": cache_hit_count / total_count,
                "response_time": {
                    "mean": statistics.mean(response_times),
                    "median": statistics.median(response_times),
                    "min": min(response_times),
                    "max": max(response_times),
                    "std_dev": statistics.stdev(response_times) if len(response_times) > 1 else 0
                },
                "quality_score": {
                    "mean": statistics.mean(quality_scores),
                    "median": statistics.median(quality_scores),
                    "min": min(quality_scores),
                    "max": max(quality_scores),
                    "std_dev": statistics.stdev(quality_scores) if len(quality_scores) > 1 else 0
                },
                "parameter_combinations": len(self.parameter_performance),
                "optimization_strategy": self.optimization_strategy.value
            }
            
            return {"status": "success", "statistics": stats}
            
        except Exception as e:
            self.logger.error(f"获取性能统计失败: {str(e)}")
            return {"status": "error", "message": str(e)}

    # 辅助方法
    def _calculate_baseline_metrics(self, params: SearchParameters) -> Dict[str, float]:
        """计算基线指标"""
        param_key = self._get_parameter_key(params)
        metrics_list = self.parameter_performance.get(param_key, [])

        if not metrics_list:
            # 返回默认基线
            return {
                'response_time': 3.0,
                'quality_score': 0.7,
                'success_rate': 0.9,
                'cache_hit_rate': 0.3
            }

        return {
            'response_time': statistics.mean([m.response_time for m in metrics_list]),
            'quality_score': statistics.mean([m.quality_score for m in metrics_list]),
            'success_rate': sum(1 for m in metrics_list if m.success) / len(metrics_list),
            'cache_hit_rate': sum(1 for m in metrics_list if m.cache_hit) / len(metrics_list)
        }

    async def _generate_optimized_parameters(self, current_params: SearchParameters,
                                           query_context: Dict[str, Any],
                                           baseline_metrics: Dict[str, float]) -> SearchParameters:
        """生成优化参数"""
        optimized = SearchParameters(
            max_results=current_params.max_results,
            search_depth=current_params.search_depth,
            timeout=current_params.timeout,
            include_images=current_params.include_images,
            include_raw_content=current_params.include_raw_content,
            time_range=current_params.time_range,
            domains_include=current_params.domains_include,
            domains_exclude=current_params.domains_exclude
        )

        # 根据优化策略调整参数
        if self.optimization_strategy == OptimizationStrategy.PERFORMANCE_FIRST:
            # 优先考虑性能
            if baseline_metrics['response_time'] > 2.0:
                optimized.max_results = min(3, current_params.max_results)
                optimized.search_depth = "basic"
                optimized.timeout = min(20, current_params.timeout)

        elif self.optimization_strategy == OptimizationStrategy.QUALITY_FIRST:
            # 优先考虑质量
            if baseline_metrics['quality_score'] < 0.8:
                optimized.max_results = min(10, current_params.max_results + 2)
                optimized.search_depth = "advanced"
                optimized.include_raw_content = True

        elif self.optimization_strategy == OptimizationStrategy.BALANCED:
            # 平衡优化
            if baseline_metrics['response_time'] > 3.0 and baseline_metrics['quality_score'] > 0.7:
                optimized.max_results = max(3, current_params.max_results - 1)
            elif baseline_metrics['quality_score'] < 0.6 and baseline_metrics['response_time'] < 2.0:
                optimized.max_results = min(8, current_params.max_results + 1)
                optimized.search_depth = "advanced"

        # 根据查询上下文调整
        if query_context:
            if query_context.get('urgent', False):
                optimized.timeout = min(15, optimized.timeout)
                optimized.max_results = min(3, optimized.max_results)

            if query_context.get('comprehensive', False):
                optimized.max_results = min(15, optimized.max_results + 3)
                optimized.search_depth = "advanced"

        return optimized

    def _predict_improvement(self, current_params: SearchParameters,
                           optimized_params: SearchParameters,
                           baseline_metrics: Dict[str, float]) -> Dict[str, float]:
        """预测改进效果"""
        improvements = {}

        # 预测响应时间改进
        if optimized_params.max_results < current_params.max_results:
            time_reduction = (current_params.max_results - optimized_params.max_results) * 0.2
            improvements['response_time'] = -time_reduction  # 负值表示减少
        elif optimized_params.max_results > current_params.max_results:
            time_increase = (optimized_params.max_results - current_params.max_results) * 0.2
            improvements['response_time'] = time_increase
        else:
            improvements['response_time'] = 0.0

        # 预测质量改进
        if optimized_params.search_depth == "advanced" and current_params.search_depth == "basic":
            improvements['quality_score'] = 0.1
        elif optimized_params.search_depth == "basic" and current_params.search_depth == "advanced":
            improvements['quality_score'] = -0.05
        else:
            improvements['quality_score'] = 0.0

        # 预测成功率改进
        if optimized_params.timeout > current_params.timeout:
            improvements['success_rate'] = 0.02
        elif optimized_params.timeout < current_params.timeout:
            improvements['success_rate'] = -0.01
        else:
            improvements['success_rate'] = 0.0

        return improvements

    def _calculate_optimization_confidence(self, current_params: SearchParameters,
                                         optimized_params: SearchParameters) -> float:
        """计算优化置信度"""
        # 基于历史数据量计算置信度
        param_key = self._get_parameter_key(optimized_params)
        sample_count = len(self.parameter_performance.get(param_key, []))

        # 数据量因子
        data_factor = min(1.0, sample_count / 20)  # 20个样本认为是充足的

        # 参数变化因子
        changes = 0
        if current_params.max_results != optimized_params.max_results:
            changes += 1
        if current_params.search_depth != optimized_params.search_depth:
            changes += 1
        if current_params.timeout != optimized_params.timeout:
            changes += 1

        change_factor = max(0.3, 1.0 - changes * 0.2)  # 变化越多置信度越低

        # 综合置信度
        confidence = (data_factor + change_factor) / 2
        return max(0.1, min(1.0, confidence))

    def _generate_optimization_reason(self, current_params: SearchParameters,
                                    optimized_params: SearchParameters,
                                    expected_improvement: Dict[str, float]) -> str:
        """生成优化原因"""
        reasons = []

        if optimized_params.max_results != current_params.max_results:
            if optimized_params.max_results < current_params.max_results:
                reasons.append(f"减少结果数量从{current_params.max_results}到{optimized_params.max_results}以提高响应速度")
            else:
                reasons.append(f"增加结果数量从{current_params.max_results}到{optimized_params.max_results}以提高结果质量")

        if optimized_params.search_depth != current_params.search_depth:
            if optimized_params.search_depth == "advanced":
                reasons.append("使用高级搜索深度以获得更好的结果质量")
            else:
                reasons.append("使用基础搜索深度以提高响应速度")

        if optimized_params.timeout != current_params.timeout:
            if optimized_params.timeout < current_params.timeout:
                reasons.append(f"减少超时时间到{optimized_params.timeout}秒以提高响应性")
            else:
                reasons.append(f"增加超时时间到{optimized_params.timeout}秒以提高成功率")

        if not reasons:
            reasons.append("当前参数已经是最优配置")

        return "；".join(reasons)

    def _get_parameter_key(self, params: SearchParameters) -> str:
        """获取参数键"""
        return f"{params.max_results}_{params.search_depth}_{params.timeout}"

    def _parse_parameter_key(self, param_key: str) -> SearchParameters:
        """解析参数键"""
        parts = param_key.split('_')
        return SearchParameters(
            max_results=int(parts[0]),
            search_depth=parts[1],
            timeout=int(parts[2])
        )

    def _calculate_composite_score(self, metrics_list: List[PerformanceMetrics]) -> float:
        """计算综合评分"""
        if not metrics_list:
            return 0.0

        # 计算各项指标的平均值
        avg_response_time = statistics.mean([m.response_time for m in metrics_list])
        avg_quality = statistics.mean([m.quality_score for m in metrics_list])
        success_rate = sum(1 for m in metrics_list if m.success) / len(metrics_list)

        # 根据优化策略计算权重
        if self.optimization_strategy == OptimizationStrategy.PERFORMANCE_FIRST:
            # 响应时间权重更高（越小越好，所以用倒数）
            time_score = 1.0 / max(0.1, avg_response_time)
            score = time_score * 0.6 + avg_quality * 0.3 + success_rate * 0.1
        elif self.optimization_strategy == OptimizationStrategy.QUALITY_FIRST:
            # 质量权重更高
            time_score = 1.0 / max(0.1, avg_response_time)
            score = avg_quality * 0.6 + time_score * 0.2 + success_rate * 0.2
        else:  # BALANCED
            # 平衡权重
            time_score = 1.0 / max(0.1, avg_response_time)
            score = avg_quality * 0.4 + time_score * 0.4 + success_rate * 0.2

        return score

    def _analyze_metric_trend(self, values: List[float]) -> Dict[str, Any]:
        """分析指标趋势"""
        if len(values) < 3:
            return {"trend": "insufficient_data", "direction": "unknown"}

        # 计算线性回归斜率
        n = len(values)
        x = list(range(n))
        x_mean = sum(x) / n
        y_mean = sum(values) / n

        numerator = sum((x[i] - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))

        if denominator == 0:
            slope = 0
        else:
            slope = numerator / denominator

        # 判断趋势方向
        if abs(slope) < 0.01:
            direction = "stable"
        elif slope > 0:
            direction = "increasing"
        else:
            direction = "decreasing"

        # 计算趋势强度
        if len(values) > 1:
            std_dev = statistics.stdev(values)
            mean_val = statistics.mean(values)
            cv = std_dev / abs(mean_val) if mean_val != 0 else 0
            strength = max(0, 1 - cv)  # 变异系数越小，趋势越强
        else:
            strength = 0

        return {
            "trend": direction,
            "slope": slope,
            "strength": strength,
            "recent_value": values[-1],
            "average": statistics.mean(values)
        }

    def _generate_trend_summary(self, trends: Dict[str, Dict[str, Any]]) -> str:
        """生成趋势摘要"""
        summaries = []

        # 响应时间趋势
        rt_trend = trends.get('response_time', {})
        if rt_trend.get('trend') == 'decreasing':
            summaries.append("响应时间呈下降趋势，性能在改善")
        elif rt_trend.get('trend') == 'increasing':
            summaries.append("响应时间呈上升趋势，需要关注性能")

        # 质量评分趋势
        qs_trend = trends.get('quality_score', {})
        if qs_trend.get('trend') == 'increasing':
            summaries.append("结果质量呈上升趋势，搜索效果在改善")
        elif qs_trend.get('trend') == 'decreasing':
            summaries.append("结果质量呈下降趋势，需要优化搜索策略")

        # 成功率趋势
        sr_trend = trends.get('success_rate', {})
        if sr_trend.get('trend') == 'decreasing':
            summaries.append("成功率呈下降趋势，需要检查系统稳定性")

        if not summaries:
            summaries.append("各项指标保持相对稳定")

        return "；".join(summaries)
