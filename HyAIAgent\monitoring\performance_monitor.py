"""
性能监控器

提供系统指标监控、响应时间分析、活跃任务统计和异常检测功能。
"""

import asyncio
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
import json
import logging

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """系统指标数据模型"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, int]
    process_count: int
    thread_count: int
    response_time: float
    active_tasks: int
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class PerformanceAlert:
    """性能告警数据模型"""
    alert_id: str
    alert_type: str
    severity: str  # low, medium, high, critical
    message: str
    metric_name: str
    current_value: float
    threshold_value: float
    timestamp: datetime
    resolved: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class PerformanceMonitor:
    """
    性能监控器
    
    提供系统指标监控、响应时间分析、活跃任务统计和异常检测功能。
    支持实时监控、历史数据存储、告警机制和性能分析。
    """
    
    def __init__(self, 
                 config_manager=None,
                 kv_store=None,
                 monitoring_interval: int = 30,
                 history_size: int = 1000,
                 enable_alerts: bool = True):
        """
        初始化性能监控器
        
        Args:
            config_manager: 配置管理器
            kv_store: 键值存储
            monitoring_interval: 监控间隔（秒）
            history_size: 历史数据保存数量
            enable_alerts: 是否启用告警
        """
        self.config_manager = config_manager
        self.kv_store = kv_store
        self.monitoring_interval = monitoring_interval
        self.history_size = history_size
        self.enable_alerts = enable_alerts
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        # 数据存储
        self.metrics_history: deque = deque(maxlen=history_size)
        self.response_times: deque = deque(maxlen=1000)
        self.active_tasks_count = 0
        self.alerts: List[PerformanceAlert] = []
        
        # 统计信息
        self.stats = {
            'total_samples': 0,
            'avg_cpu_usage': 0.0,
            'avg_memory_usage': 0.0,
            'avg_response_time': 0.0,
            'peak_cpu_usage': 0.0,
            'peak_memory_usage': 0.0,
            'alert_count': 0,
            'uptime_start': datetime.now()
        }
        
        # 告警阈值配置
        self.alert_thresholds = {
            'cpu_usage': {'high': 80.0, 'critical': 95.0},
            'memory_usage': {'high': 80.0, 'critical': 95.0},
            'disk_usage': {'high': 85.0, 'critical': 95.0},
            'response_time': {'high': 5.0, 'critical': 10.0},
            'active_tasks': {'high': 50, 'critical': 100}
        }
        
        # 回调函数
        self.alert_callbacks: List[Callable] = []
        
        logger.info("性能监控器初始化完成")
    
    async def start_monitoring(self) -> bool:
        """
        启动性能监控
        
        Returns:
            bool: 启动是否成功
        """
        try:
            if self.is_monitoring:
                logger.warning("性能监控已在运行中")
                return True
            
            self.is_monitoring = True
            self.monitor_task = asyncio.create_task(self._monitoring_loop())
            
            logger.info(f"性能监控已启动，监控间隔: {self.monitoring_interval}秒")
            return True
            
        except Exception as e:
            logger.error(f"启动性能监控失败: {e}")
            self.is_monitoring = False
            return False
    
    async def stop_monitoring(self) -> bool:
        """
        停止性能监控
        
        Returns:
            bool: 停止是否成功
        """
        try:
            if not self.is_monitoring:
                logger.warning("性能监控未在运行")
                return True
            
            self.is_monitoring = False
            
            if self.monitor_task and not self.monitor_task.done():
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("性能监控已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止性能监控失败: {e}")
            return False

    async def _monitoring_loop(self) -> None:
        """监控循环"""
        while self.is_monitoring:
            try:
                # 收集系统指标
                metrics = await self._collect_system_metrics()

                # 存储指标数据
                self.metrics_history.append(metrics)

                # 更新统计信息
                self._update_stats(metrics)

                # 检查告警条件
                if self.enable_alerts:
                    await self._check_alerts(metrics)

                # 持久化数据（如果配置了存储）
                if self.kv_store:
                    await self._persist_metrics(metrics)

                # 等待下次监控
                await asyncio.sleep(self.monitoring_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(self.monitoring_interval)

    async def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_usage = psutil.cpu_percent(interval=1)

            # 内存使用率
            memory = psutil.virtual_memory()
            memory_usage = memory.percent

            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_usage = (disk.used / disk.total) * 100

            # 网络I/O
            network = psutil.net_io_counters()
            network_io = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }

            # 进程和线程数
            process_count = len(psutil.pids())
            thread_count = threading.active_count()

            # 平均响应时间
            avg_response_time = self._calculate_avg_response_time()

            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                disk_usage=disk_usage,
                network_io=network_io,
                process_count=process_count,
                thread_count=thread_count,
                response_time=avg_response_time,
                active_tasks=self.active_tasks_count
            )

        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            # 返回默认指标
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_usage=0.0,
                memory_usage=0.0,
                disk_usage=0.0,
                network_io={},
                process_count=0,
                thread_count=0,
                response_time=0.0,
                active_tasks=0
            )

    def _calculate_avg_response_time(self) -> float:
        """计算平均响应时间"""
        if not self.response_times:
            return 0.0
        return sum(self.response_times) / len(self.response_times)

    def _update_stats(self, metrics: SystemMetrics) -> None:
        """更新统计信息"""
        self.stats['total_samples'] += 1

        # 更新平均值
        total = self.stats['total_samples']
        self.stats['avg_cpu_usage'] = (
            (self.stats['avg_cpu_usage'] * (total - 1) + metrics.cpu_usage) / total
        )
        self.stats['avg_memory_usage'] = (
            (self.stats['avg_memory_usage'] * (total - 1) + metrics.memory_usage) / total
        )
        self.stats['avg_response_time'] = (
            (self.stats['avg_response_time'] * (total - 1) + metrics.response_time) / total
        )

        # 更新峰值
        self.stats['peak_cpu_usage'] = max(self.stats['peak_cpu_usage'], metrics.cpu_usage)
        self.stats['peak_memory_usage'] = max(self.stats['peak_memory_usage'], metrics.memory_usage)

    async def _check_alerts(self, metrics: SystemMetrics) -> None:
        """检查告警条件"""
        alerts_to_create = []

        # 检查CPU使用率
        if metrics.cpu_usage >= self.alert_thresholds['cpu_usage']['critical']:
            alerts_to_create.append(self._create_alert(
                'cpu_usage', 'critical',
                f'CPU使用率达到临界值: {metrics.cpu_usage:.1f}%',
                metrics.cpu_usage, self.alert_thresholds['cpu_usage']['critical']
            ))
        elif metrics.cpu_usage >= self.alert_thresholds['cpu_usage']['high']:
            alerts_to_create.append(self._create_alert(
                'cpu_usage', 'high',
                f'CPU使用率过高: {metrics.cpu_usage:.1f}%',
                metrics.cpu_usage, self.alert_thresholds['cpu_usage']['high']
            ))

        # 检查内存使用率
        if metrics.memory_usage >= self.alert_thresholds['memory_usage']['critical']:
            alerts_to_create.append(self._create_alert(
                'memory_usage', 'critical',
                f'内存使用率达到临界值: {metrics.memory_usage:.1f}%',
                metrics.memory_usage, self.alert_thresholds['memory_usage']['critical']
            ))
        elif metrics.memory_usage >= self.alert_thresholds['memory_usage']['high']:
            alerts_to_create.append(self._create_alert(
                'memory_usage', 'high',
                f'内存使用率过高: {metrics.memory_usage:.1f}%',
                metrics.memory_usage, self.alert_thresholds['memory_usage']['high']
            ))

        # 检查响应时间
        if metrics.response_time >= self.alert_thresholds['response_time']['critical']:
            alerts_to_create.append(self._create_alert(
                'response_time', 'critical',
                f'响应时间达到临界值: {metrics.response_time:.2f}秒',
                metrics.response_time, self.alert_thresholds['response_time']['critical']
            ))
        elif metrics.response_time >= self.alert_thresholds['response_time']['high']:
            alerts_to_create.append(self._create_alert(
                'response_time', 'high',
                f'响应时间过长: {metrics.response_time:.2f}秒',
                metrics.response_time, self.alert_thresholds['response_time']['high']
            ))

        # 处理新告警
        for alert in alerts_to_create:
            self.alerts.append(alert)
            self.stats['alert_count'] += 1

            # 调用告警回调
            for callback in self.alert_callbacks:
                try:
                    await callback(alert)
                except Exception as e:
                    logger.error(f"告警回调执行失败: {e}")

    def _create_alert(self, metric_name: str, severity: str, message: str,
                     current_value: float, threshold_value: float) -> PerformanceAlert:
        """创建告警"""
        alert_id = f"{metric_name}_{severity}_{int(time.time())}"
        return PerformanceAlert(
            alert_id=alert_id,
            alert_type=metric_name,
            severity=severity,
            message=message,
            metric_name=metric_name,
            current_value=current_value,
            threshold_value=threshold_value,
            timestamp=datetime.now()
        )

    async def _persist_metrics(self, metrics: SystemMetrics) -> None:
        """持久化指标数据"""
        try:
            if self.kv_store:
                key = f"performance_metrics_{metrics.timestamp.strftime('%Y%m%d_%H%M%S')}"
                await self.kv_store.set(key, metrics.to_dict(), ttl=86400 * 7)  # 保存7天
        except Exception as e:
            logger.error(f"持久化指标数据失败: {e}")

    async def get_current_metrics(self) -> Optional[SystemMetrics]:
        """
        获取当前系统指标

        Returns:
            Optional[SystemMetrics]: 当前系统指标，如果获取失败返回None
        """
        try:
            return await self._collect_system_metrics()
        except Exception as e:
            logger.error(f"获取当前指标失败: {e}")
            return None

    def get_metrics_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取指标历史数据

        Args:
            limit: 限制返回的数据条数

        Returns:
            List[Dict[str, Any]]: 历史指标数据列表
        """
        history = list(self.metrics_history)
        if limit:
            history = history[-limit:]
        return [metrics.to_dict() for metrics in history]

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            Dict[str, Any]: 性能统计信息
        """
        uptime = datetime.now() - self.stats['uptime_start']

        return {
            **self.stats,
            'uptime_seconds': uptime.total_seconds(),
            'uptime_formatted': str(uptime),
            'monitoring_status': self.is_monitoring,
            'metrics_count': len(self.metrics_history),
            'active_alerts': len([a for a in self.alerts if not a.resolved])
        }

    def get_alerts(self, severity: Optional[str] = None,
                  resolved: Optional[bool] = None) -> List[Dict[str, Any]]:
        """
        获取告警列表

        Args:
            severity: 告警级别过滤
            resolved: 是否已解决过滤

        Returns:
            List[Dict[str, Any]]: 告警列表
        """
        alerts = self.alerts

        if severity:
            alerts = [a for a in alerts if a.severity == severity]

        if resolved is not None:
            alerts = [a for a in alerts if a.resolved == resolved]

        return [alert.to_dict() for alert in alerts]

    async def resolve_alert(self, alert_id: str) -> bool:
        """
        解决告警

        Args:
            alert_id: 告警ID

        Returns:
            bool: 是否成功解决
        """
        try:
            for alert in self.alerts:
                if alert.alert_id == alert_id:
                    alert.resolved = True
                    logger.info(f"告警已解决: {alert_id}")
                    return True

            logger.warning(f"未找到告警: {alert_id}")
            return False

        except Exception as e:
            logger.error(f"解决告警失败: {e}")
            return False

    def add_alert_callback(self, callback: Callable) -> None:
        """
        添加告警回调函数

        Args:
            callback: 告警回调函数
        """
        self.alert_callbacks.append(callback)

    def remove_alert_callback(self, callback: Callable) -> bool:
        """
        移除告警回调函数

        Args:
            callback: 要移除的回调函数

        Returns:
            bool: 是否成功移除
        """
        try:
            self.alert_callbacks.remove(callback)
            return True
        except ValueError:
            return False

    def record_response_time(self, response_time: float) -> None:
        """
        记录响应时间

        Args:
            response_time: 响应时间（秒）
        """
        self.response_times.append(response_time)

    def increment_active_tasks(self) -> None:
        """增加活跃任务计数"""
        self.active_tasks_count += 1

    def decrement_active_tasks(self) -> None:
        """减少活跃任务计数"""
        if self.active_tasks_count > 0:
            self.active_tasks_count -= 1

    def set_alert_threshold(self, metric_name: str, level: str, value: float) -> bool:
        """
        设置告警阈值

        Args:
            metric_name: 指标名称
            level: 告警级别 (high/critical)
            value: 阈值

        Returns:
            bool: 设置是否成功
        """
        try:
            if metric_name in self.alert_thresholds and level in ['high', 'critical']:
                self.alert_thresholds[metric_name][level] = value
                logger.info(f"告警阈值已更新: {metric_name}.{level} = {value}")
                return True
            return False
        except Exception as e:
            logger.error(f"设置告警阈值失败: {e}")
            return False

    async def cleanup(self) -> None:
        """清理资源"""
        try:
            await self.stop_monitoring()
            self.metrics_history.clear()
            self.response_times.clear()
            self.alerts.clear()
            self.alert_callbacks.clear()
            logger.info("性能监控器资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")
