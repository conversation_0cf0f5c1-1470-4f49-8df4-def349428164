#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序主类模块
负责应用程序的初始化、配置和生命周期管理
"""

import sys
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTranslator, QLocale
from PyQt6.QtGui import QIcon

# 导入自定义模块
from ui.main_window import MainWindowUI
from logic.app_controller import AppController


class HelloWorldApplication:
    """
    应用程序主类
    负责整个应用的初始化、配置和管理
    """
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.controller = None
        self.translator = None
        
    def initialize(self):
        """初始化应用程序"""
        # 创建QApplication实例
        self.app = QApplication(sys.argv)
        
        # 设置应用程序信息
        self.setup_application_info()
        
        # 设置应用程序图标（如果有的话）
        self.setup_application_icon()
        
        # 设置国际化（可选）
        self.setup_internationalization()
        
        # 创建主窗口
        self.main_window = MainWindowUI()
        
        # 创建控制器
        self.controller = AppController(self.main_window)
        
        # 应用全局样式（可选）
        self.apply_global_styles()
        
        return True
    
    def setup_application_info(self):
        """设置应用程序基本信息"""
        self.app.setApplicationName("Python PyQt6 Hello World")
        self.app.setApplicationVersion("2.0.0")
        self.app.setOrganizationName("Python学习示例")
        self.app.setOrganizationDomain("example.com")
        
    def setup_application_icon(self):
        """设置应用程序图标"""
        # 如果有图标文件，可以在这里设置
        # icon_path = "resources/icons/app_icon.png"
        # if os.path.exists(icon_path):
        #     self.app.setWindowIcon(QIcon(icon_path))
        pass
    
    def setup_internationalization(self):
        """设置国际化支持"""
        # 创建翻译器
        self.translator = QTranslator()
        
        # 获取系统语言
        locale = QLocale.system().name()
        
        # 加载翻译文件（如果存在）
        # translation_file = f"translations/app_{locale}.qm"
        # if os.path.exists(translation_file):
        #     self.translator.load(translation_file)
        #     self.app.installTranslator(self.translator)
        
    def apply_global_styles(self):
        """应用全局样式"""
        # 可以在这里设置全局样式表
        global_style = """
            QApplication {
                font-family: "Microsoft YaHei", "Arial", sans-serif;
                font-size: 9pt;
            }
        """
        self.app.setStyleSheet(global_style)
    
    def show_main_window(self):
        """显示主窗口"""
        if self.main_window:
            self.main_window.show()
            return True
        return False
    
    def run(self):
        """运行应用程序"""
        if not self.app:
            return -1
        
        # 启动事件循环
        return self.app.exec()
    
    def quit(self):
        """退出应用程序"""
        if self.app:
            self.app.quit()
    
    def get_controller(self):
        """获取控制器实例"""
        return self.controller
    
    def get_main_window(self):
        """获取主窗口实例"""
        return self.main_window
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """
        全局异常处理器
        
        Args:
            exc_type: 异常类型
            exc_value: 异常值
            exc_traceback: 异常追踪
        """
        import traceback
        from PyQt6.QtWidgets import QMessageBox
        
        # 格式化异常信息
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # 显示错误对话框
        if self.main_window:
            QMessageBox.critical(
                self.main_window,
                "应用程序错误",
                f"发生未处理的异常:\n\n{error_msg}"
            )
        
        # 记录到日志（如果有日志系统）
        print(f"未处理的异常: {error_msg}")
    
    def setup_exception_handling(self):
        """设置全局异常处理"""
        sys.excepthook = self.handle_exception


def create_application():
    """
    工厂函数：创建应用程序实例
    
    Returns:
        HelloWorldApplication: 应用程序实例
    """
    app = HelloWorldApplication()
    
    # 初始化应用程序
    if app.initialize():
        # 设置异常处理
        app.setup_exception_handling()
        return app
    
    return None


# 便捷函数
def run_application():
    """
    便捷函数：创建并运行应用程序
    
    Returns:
        int: 应用程序退出代码
    """
    app = create_application()
    if app:
        app.show_main_window()
        return app.run()
    return -1
