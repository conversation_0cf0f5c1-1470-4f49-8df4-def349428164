"""
文本分析器模块

提供全面的文本分析功能，包括：
- 基础文本统计分析
- 关键词提取和词频分析
- 文本结构分析
- 语言检测和编码分析
- 文本质量评估
- 内容分类和标签提取
"""

import re
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Set
from pathlib import Path
from collections import Counter, defaultdict
import json
import hashlib
from datetime import datetime

from .security_manager import SecurityManager
from .file_operations import FileOperations


class TextAnalyzer:
    """
    文本分析器类
    
    提供全面的文本分析功能，支持多种分析维度和指标
    """
    
    def __init__(self, 
                 workspace_path: str = "./workspace",
                 security_manager: Optional[SecurityManager] = None,
                 file_operations: Optional[FileOperations] = None):
        """
        初始化文本分析器
        
        Args:
            workspace_path (str): 工作空间路径
            security_manager (SecurityManager, optional): 安全管理器实例
            file_operations (FileOperations, optional): 文件操作实例
        """
        self.workspace_path = Path(workspace_path)
        self.security_manager = security_manager or SecurityManager(workspace_path)
        self.file_operations = file_operations or FileOperations(workspace_path, self.security_manager)
        
        # 分析统计信息
        self.analysis_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'total_text_length': 0,
            'average_analysis_time': 0.0
        }
        
        # 常用停用词（中英文）
        self.stop_words = {
            'en': {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'},
            'zh': {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '里', '就是', '还', '把', '比', '让', '时', '过', '出', '小', '么', '起', '你们', '到了', '大', '来', '可以', '如果', '就会', '多', '然后', '现在', '所以', '因为', '但是', '虽然', '已经', '还是', '只是', '不过', '而且', '或者', '不仅', '不但', '无论', '不管', '即使', '尽管', '除了', '另外', '此外', '总之', '因此', '所以', '然而', '但是', '可是', '不过', '而且', '并且', '同时', '另一方面', '相反', '相比之下', '与此同时', '首先', '其次', '最后', '总的来说', '综上所述'}
        }
        
        # 文本类型模式
        self.text_patterns = {
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'url': re.compile(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'),
            'phone': re.compile(r'(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'),
            'chinese_phone': re.compile(r'1[3-9]\d{9}'),
            'ip_address': re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'),
            'date': re.compile(r'\b\d{4}[-/]\d{1,2}[-/]\d{1,2}\b|\b\d{1,2}[-/]\d{1,2}[-/]\d{4}\b'),
            'time': re.compile(r'\b\d{1,2}:\d{2}(:\d{2})?\b'),
            'number': re.compile(r'\b\d+\.?\d*\b'),
            'chinese_char': re.compile(r'[\u4e00-\u9fff]'),
            'english_word': re.compile(r'\b[A-Za-z]+\b'),
            'punctuation': re.compile(r'[^\w\s]'),
            'whitespace': re.compile(r'\s+')
        }

    async def analyze_text(self, text: str, analysis_options: Optional[Dict[str, bool]] = None) -> Dict[str, Any]:
        """
        全面分析文本内容
        
        Args:
            text (str): 要分析的文本内容
            analysis_options (Dict[str, bool], optional): 分析选项配置
            
        Returns:
            Dict[str, Any]: 分析结果字典
        """
        start_time = datetime.now()
        
        try:
            # 检查文本是否为空或None
            if text is None:
                text = ""

            # 默认分析选项
            options = analysis_options or {
                'basic_stats': True,
                'word_frequency': True,
                'pattern_extraction': True,
                'language_detection': True,
                'structure_analysis': True,
                'quality_assessment': True,
                'content_classification': True
            }
            
            analysis_result = {
                'status': 'success',
                'text_hash': hashlib.md5(text.encode()).hexdigest(),
                'analysis_timestamp': start_time.isoformat(),
                'text_length': len(text),
                'analysis_options': options,
                'results': {}
            }
            
            # 基础统计分析
            if options.get('basic_stats', True):
                analysis_result['results']['basic_stats'] = await self._analyze_basic_stats(text)
            
            # 词频分析
            if options.get('word_frequency', True):
                analysis_result['results']['word_frequency'] = await self._analyze_word_frequency(text)
            
            # 模式提取
            if options.get('pattern_extraction', True):
                analysis_result['results']['pattern_extraction'] = await self._extract_patterns(text)
            
            # 语言检测
            if options.get('language_detection', True):
                analysis_result['results']['language_detection'] = await self._detect_language(text)
            
            # 结构分析
            if options.get('structure_analysis', True):
                analysis_result['results']['structure_analysis'] = await self._analyze_structure(text)
            
            # 质量评估
            if options.get('quality_assessment', True):
                analysis_result['results']['quality_assessment'] = await self._assess_quality(text)
            
            # 内容分类
            if options.get('content_classification', True):
                analysis_result['results']['content_classification'] = await self._classify_content(text)
            
            # 计算分析时间
            end_time = datetime.now()
            analysis_time = (end_time - start_time).total_seconds()
            analysis_result['analysis_time'] = analysis_time
            
            # 更新统计信息
            self.analysis_stats['total_analyses'] += 1
            self.analysis_stats['successful_analyses'] += 1
            self.analysis_stats['total_text_length'] += len(text)
            self.analysis_stats['average_analysis_time'] = (
                (self.analysis_stats['average_analysis_time'] * (self.analysis_stats['total_analyses'] - 1) + analysis_time) /
                self.analysis_stats['total_analyses']
            )
            
            return analysis_result
            
        except Exception as e:
            self.analysis_stats['total_analyses'] += 1
            self.analysis_stats['failed_analyses'] += 1
            
            return {
                'status': 'error',
                'error': str(e),
                'text_hash': hashlib.md5(text.encode()).hexdigest() if text else None,
                'analysis_timestamp': start_time.isoformat()
            }

    async def analyze_file(self, file_path: str, analysis_options: Optional[Dict[str, bool]] = None) -> Dict[str, Any]:
        """
        分析文件中的文本内容
        
        Args:
            file_path (str): 文件路径
            analysis_options (Dict[str, bool], optional): 分析选项配置
            
        Returns:
            Dict[str, Any]: 分析结果字典
        """
        try:
            # 读取文件内容
            file_result = await self.file_operations.read_file(file_path)
            
            if file_result.get('status') == 'error':
                return {
                    'status': 'error',
                    'error': f"Failed to read file: {file_result.get('message', 'Unknown error')}",
                    'file_path': file_path
                }
            
            text_content = file_result.get('content', '')
            
            # 分析文本内容
            analysis_result = await self.analyze_text(text_content, analysis_options)
            
            # 添加文件信息
            if analysis_result.get('status') != 'error':
                analysis_result['file_path'] = file_path
                analysis_result['file_info'] = {
                    'size': len(text_content),
                    'encoding': file_result.get('encoding', 'unknown')
                }

            return analysis_result
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'file_path': file_path
            }

    async def batch_analyze_files(self, file_patterns: List[str], 
                                analysis_options: Optional[Dict[str, bool]] = None,
                                max_concurrent: int = 5) -> Dict[str, Any]:
        """
        批量分析多个文件
        
        Args:
            file_patterns (List[str]): 文件模式列表
            analysis_options (Dict[str, bool], optional): 分析选项配置
            max_concurrent (int): 最大并发数
            
        Returns:
            Dict[str, Any]: 批量分析结果
        """
        try:
            # 获取匹配的文件列表
            all_files = []
            for pattern in file_patterns:
                files_result = await self.file_operations.list_files("", pattern, recursive=True)
                if files_result.get('success') == True:
                    all_files.extend([f['path'] for f in files_result.get('files', [])])
            
            if not all_files:
                return {
                    'status': 'success',
                    'message': 'No files found matching the patterns',
                    'patterns': file_patterns,
                    'results': []
                }
            
            # 创建信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def analyze_single_file(file_path: str) -> Dict[str, Any]:
                async with semaphore:
                    return await self.analyze_file(file_path, analysis_options)
            
            # 并发分析所有文件
            tasks = [analyze_single_file(file_path) for file_path in all_files]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            successful_results = []
            failed_results = []
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    failed_results.append({
                        'file_path': all_files[i],
                        'error': str(result)
                    })
                elif result.get('status') == 'error':
                    failed_results.append(result)
                else:
                    successful_results.append(result)
            
            return {
                'status': 'success',
                'total_files': len(all_files),
                'successful_analyses': len(successful_results),
                'failed_analyses': len(failed_results),
                'patterns': file_patterns,
                'results': successful_results,
                'failures': failed_results,
                'summary': await self._generate_batch_summary(successful_results)
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'patterns': file_patterns
            }

    async def _analyze_basic_stats(self, text: str) -> Dict[str, Any]:
        """分析基础文本统计信息"""
        lines = text.split('\n')
        words = text.split()
        sentences = re.split(r'[.!?]+', text)
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]

        sentence_count = len([s for s in sentences if s.strip()])

        return {
            'character_count': len(text),
            'character_count_no_spaces': len(text.replace(' ', '')),
            'word_count': len(words),
            'line_count': len(lines),
            'sentence_count': sentence_count,
            'paragraph_count': len(paragraphs),
            'average_word_length': sum(len(word) for word in words) / len(words) if words else 0,
            'average_sentence_length': len(words) / sentence_count if sentence_count > 0 else 0,
            'average_paragraph_length': len(words) / len(paragraphs) if paragraphs else 0,
            'blank_lines': len([line for line in lines if not line.strip()]),
            'longest_line': max(len(line) for line in lines) if lines else 0,
            'shortest_line': min(len(line) for line in lines if line.strip()) if [line for line in lines if line.strip()] else 0
        }

    async def _analyze_word_frequency(self, text: str) -> Dict[str, Any]:
        """分析词频统计"""
        # 提取单词（支持中英文）
        english_words = self.text_patterns['english_word'].findall(text.lower())
        chinese_chars = self.text_patterns['chinese_char'].findall(text)

        # 过滤停用词
        english_words_filtered = [word for word in english_words if word not in self.stop_words['en']]
        chinese_chars_filtered = [char for char in chinese_chars if char not in self.stop_words['zh']]

        # 统计词频
        english_freq = Counter(english_words_filtered)
        chinese_freq = Counter(chinese_chars_filtered)
        all_words_freq = Counter(english_words_filtered + chinese_chars_filtered)

        return {
            'total_unique_words': len(all_words_freq),
            'english_word_count': len(english_words),
            'chinese_char_count': len(chinese_chars),
            'top_english_words': english_freq.most_common(20),
            'top_chinese_chars': chinese_freq.most_common(20),
            'top_words_overall': all_words_freq.most_common(30),
            'word_diversity': len(all_words_freq) / len(english_words + chinese_chars) if (english_words + chinese_chars) else 0,
            'hapax_legomena': len([word for word, count in all_words_freq.items() if count == 1])
        }

    async def _extract_patterns(self, text: str) -> Dict[str, Any]:
        """提取文本中的各种模式"""
        patterns_found = {}

        for pattern_name, pattern_regex in self.text_patterns.items():
            matches = pattern_regex.findall(text)
            patterns_found[pattern_name] = {
                'count': len(matches),
                'examples': matches[:10] if matches else [],
                'unique_count': len(set(matches)) if matches else 0
            }

        return patterns_found

    async def _detect_language(self, text: str) -> Dict[str, Any]:
        """检测文本语言"""
        chinese_chars = len(self.text_patterns['chinese_char'].findall(text))
        english_words = len(self.text_patterns['english_word'].findall(text))
        total_chars = len(text.replace(' ', ''))

        chinese_ratio = chinese_chars / total_chars if total_chars > 0 else 0
        english_ratio = english_words / len(text.split()) if text.split() else 0

        # 简单的语言检测逻辑
        if chinese_ratio > 0.3:
            primary_language = 'chinese'
            confidence = min(chinese_ratio * 2, 1.0)
        elif english_ratio > 0.5:
            primary_language = 'english'
            confidence = min(english_ratio * 1.5, 1.0)
        else:
            primary_language = 'mixed_or_other'
            confidence = 0.5

        return {
            'primary_language': primary_language,
            'confidence': confidence,
            'chinese_char_ratio': chinese_ratio,
            'english_word_ratio': english_ratio,
            'is_multilingual': chinese_ratio > 0.1 and english_ratio > 0.1
        }

    async def _analyze_structure(self, text: str) -> Dict[str, Any]:
        """分析文本结构"""
        lines = text.split('\n')

        # 分析标题结构（Markdown风格）
        headers = []
        for i, line in enumerate(lines):
            line = line.strip()
            if line.startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                headers.append({
                    'level': level,
                    'text': line.lstrip('#').strip(),
                    'line_number': i + 1
                })

        # 分析列表结构
        list_items = []
        for i, line in enumerate(lines):
            line = line.strip()
            if line.startswith(('-', '*', '+')):
                list_items.append({
                    'type': 'unordered',
                    'text': line[1:].strip(),
                    'line_number': i + 1
                })
            elif re.match(r'^\d+\.', line):
                list_items.append({
                    'type': 'ordered',
                    'text': re.sub(r'^\d+\.', '', line).strip(),
                    'line_number': i + 1
                })

        # 分析代码块
        code_blocks = []
        in_code_block = False
        current_block = []
        for i, line in enumerate(lines):
            if line.strip().startswith('```'):
                if in_code_block:
                    code_blocks.append({
                        'content': '\n'.join(current_block),
                        'start_line': i - len(current_block),
                        'end_line': i + 1,
                        'language': current_block[0] if current_block and current_block[0].strip() else 'unknown'
                    })
                    current_block = []
                    in_code_block = False
                else:
                    in_code_block = True
                    current_block = [line.strip()[3:]]
            elif in_code_block:
                current_block.append(line)

        return {
            'header_count': len(headers),
            'headers': headers,
            'max_header_level': max([h['level'] for h in headers]) if headers else 0,
            'list_item_count': len(list_items),
            'list_items': list_items[:20],  # 限制返回数量
            'code_block_count': len(code_blocks),
            'code_blocks': code_blocks,
            'has_structured_content': len(headers) > 0 or len(list_items) > 0 or len(code_blocks) > 0
        }

    async def _assess_quality(self, text: str) -> Dict[str, Any]:
        """评估文本质量"""
        words = text.split()
        sentences = re.split(r'[.!?]+', text)
        sentences = [s.strip() for s in sentences if s.strip()]

        # 计算可读性指标
        avg_sentence_length = len(words) / len(sentences) if sentences else 0
        avg_word_length = sum(len(word) for word in words) / len(words) if words else 0

        # 简单的可读性评分（基于句子长度和单词长度）
        readability_score = max(0, min(100, 100 - (avg_sentence_length - 15) * 2 - (avg_word_length - 5) * 5))

        # 检查常见问题
        issues = []
        if avg_sentence_length > 25:
            issues.append("句子过长，可能影响可读性")
        if avg_word_length > 8:
            issues.append("单词过长，可能影响理解")

        # 检查重复内容
        word_freq = Counter(words)
        repeated_words = [word for word, count in word_freq.items() if count > len(words) * 0.05]
        if repeated_words:
            issues.append(f"存在高频重复词汇: {', '.join(repeated_words[:5])}")

        # 检查标点符号使用
        punctuation_count = len(self.text_patterns['punctuation'].findall(text))
        punctuation_ratio = punctuation_count / len(text) if text else 0

        if punctuation_ratio < 0.02:
            issues.append("标点符号使用过少")
        elif punctuation_ratio > 0.15:
            issues.append("标点符号使用过多")

        return {
            'readability_score': readability_score,
            'average_sentence_length': avg_sentence_length,
            'average_word_length': avg_word_length,
            'punctuation_ratio': punctuation_ratio,
            'quality_issues': issues,
            'quality_grade': self._get_quality_grade(readability_score),
            'word_diversity_ratio': len(set(words)) / len(words) if words else 0
        }

    async def _classify_content(self, text: str) -> Dict[str, Any]:
        """内容分类和标签提取"""
        text_lower = text.lower()

        # 定义内容类型关键词
        content_keywords = {
            'technical': ['api', 'function', 'class', 'method', 'algorithm', 'database', 'server', 'client', 'code', 'programming', 'software', 'system', 'technical', 'implementation', 'configuration'],
            'documentation': ['readme', 'guide', 'tutorial', 'manual', 'documentation', 'instruction', 'how to', 'step by step', 'example', 'usage'],
            'business': ['business', 'market', 'customer', 'revenue', 'profit', 'strategy', 'management', 'company', 'organization', 'project'],
            'academic': ['research', 'study', 'analysis', 'theory', 'hypothesis', 'experiment', 'conclusion', 'abstract', 'methodology', 'literature'],
            'news': ['news', 'report', 'article', 'journalist', 'breaking', 'update', 'announcement', 'press release'],
            'personal': ['diary', 'journal', 'personal', 'thoughts', 'feelings', 'experience', 'memory', 'reflection']
        }

        # 计算每个类型的匹配分数
        category_scores = {}
        for category, keywords in content_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            category_scores[category] = score / len(keywords)  # 归一化分数

        # 确定主要类型
        primary_category = max(category_scores, key=category_scores.get) if category_scores else 'unknown'
        confidence = category_scores.get(primary_category, 0)

        # 提取可能的标签
        tags = []
        for category, score in category_scores.items():
            if score > 0.1:  # 阈值
                tags.append(category)

        # 检测特殊格式
        format_indicators = {
            'json': text.strip().startswith('{') and text.strip().endswith('}'),
            'xml': '<' in text and '>' in text,
            'csv': ',' in text and '\n' in text,
            'markdown': '#' in text or '*' in text or '`' in text,
            'code': any(keyword in text_lower for keyword in ['def ', 'function', 'class ', 'import ', 'return ', 'if ', 'for ', 'while ']),
            'log': any(keyword in text_lower for keyword in ['error', 'warning', 'info', 'debug', 'timestamp', 'log'])
        }

        detected_formats = [fmt for fmt, detected in format_indicators.items() if detected]

        return {
            'primary_category': primary_category,
            'confidence': confidence,
            'category_scores': category_scores,
            'tags': tags,
            'detected_formats': detected_formats,
            'is_structured_data': any(fmt in detected_formats for fmt in ['json', 'xml', 'csv']),
            'is_code': 'code' in detected_formats,
            'is_documentation': 'markdown' in detected_formats or 'documentation' in tags
        }

    async def _generate_batch_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成批量分析的汇总信息"""
        if not results:
            return {}

        total_chars = sum(r.get('text_length', 0) for r in results)
        total_words = sum(r.get('results', {}).get('basic_stats', {}).get('word_count', 0) for r in results)

        # 统计语言分布
        languages = [r.get('results', {}).get('language_detection', {}).get('primary_language', 'unknown') for r in results]
        language_dist = Counter(languages)

        # 统计内容类型分布
        categories = [r.get('results', {}).get('content_classification', {}).get('primary_category', 'unknown') for r in results]
        category_dist = Counter(categories)

        # 计算平均质量分数
        quality_scores = [r.get('results', {}).get('quality_assessment', {}).get('readability_score', 0) for r in results]
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0

        return {
            'total_files': len(results),
            'total_characters': total_chars,
            'total_words': total_words,
            'average_file_size': total_chars / len(results) if results else 0,
            'language_distribution': dict(language_dist),
            'category_distribution': dict(category_dist),
            'average_quality_score': avg_quality,
            'quality_grade_distribution': Counter([self._get_quality_grade(score) for score in quality_scores])
        }

    def _get_quality_grade(self, score: float) -> str:
        """根据分数获取质量等级"""
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'

    def get_analysis_stats(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        return self.analysis_stats.copy()

    def reset_stats(self) -> None:
        """重置分析统计"""
        self.analysis_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'total_text_length': 0,
            'average_analysis_time': 0.0
        }
