"""
HyAIAgent功能验收测试脚本
验证第一阶段所有功能是否符合预期
"""

import sys
import tempfile
import time
from pathlib import Path
from unittest.mock import Mock, patch
from PyQt6.QtWidgets import QApplication
from PyQt6.QtTest import QTest
from PyQt6.QtCore import Qt

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入所有模块
from main import main as app_main, initialize_application
from core.config_manager import ConfigManager
from core.ai_client import SimpleAIClient
from core.kv_store import KVStore
from core.prompt_manager import PromptManager
from ui.chat_window import ChatWindow


def test_acceptance_core_modules():
    """验收测试：核心模块功能"""
    print("🧪 开始核心模块功能验收测试...")
    
    try:
        # 测试1: ConfigManager完整功能
        print("📋 测试1: ConfigManager配置管理功能")
        config_manager = ConfigManager()
        
        # 验证配置加载
        assert config_manager.config is not None
        assert isinstance(config_manager.config, dict)
        
        # 验证AI配置获取
        ai_config = config_manager.get_ai_config()
        assert "api_key" in ai_config
        assert "model" in ai_config
        
        # 验证配置设置和获取
        config_manager.set("test.key", "test_value")
        assert config_manager.get("test.key") == "test_value"
        
        print("✅ ConfigManager功能验收通过")
        
        # 测试2: KVStore数据存储功能
        print("📋 测试2: KVStore数据存储功能")
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            temp_db_path = f.name
        
        try:
            kv_store = KVStore(temp_db_path)
            
            # 验证基本存储功能
            kv_store.set("user_data", {"name": "test", "age": 25})
            user_data = kv_store.get("user_data")
            assert user_data["name"] == "test"
            assert user_data["age"] == 25
            
            # 验证TTL功能
            kv_store.set("temp_data", "temporary", ttl=1)
            assert kv_store.exists("temp_data") == True
            time.sleep(1.1)
            kv_store.cleanup_expired()
            assert kv_store.exists("temp_data") == False
            
            # 验证批量操作
            kv_store.set("key1", "value1")
            kv_store.set("key2", "value2")
            keys = kv_store.keys()
            assert "key1" in keys
            assert "key2" in keys
            
            kv_store.close()
            
        finally:
            Path(temp_db_path).unlink(missing_ok=True)
        
        print("✅ KVStore功能验收通过")
        
        # 测试3: PromptManager提示词管理功能
        print("📋 测试3: PromptManager提示词管理功能")
        with tempfile.TemporaryDirectory() as temp_dir:
            prompt_manager = PromptManager(temp_dir)
            
            # 验证提示词添加
            prompt_manager.add_prompt("test_prompt", "Hello {{name}}", "templates")
            
            # 验证模板渲染
            result = prompt_manager.get_template_prompt("test_prompt", {"name": "World"})
            assert result == "Hello World"
            
            # 验证提示词列表
            prompts = prompt_manager.list_prompts()
            assert "templates.test_prompt" in prompts
            
            # 验证提示词更新
            prompt_manager.update_prompt("test_prompt", "Hi {{name}}!", "templates")
            result = prompt_manager.get_template_prompt("test_prompt", {"name": "User"})
            assert result == "Hi User!"
        
        print("✅ PromptManager功能验收通过")
        
        # 测试4: AIClient模拟功能
        print("📋 测试4: AIClient AI客户端功能")
        with patch('openai.OpenAI') as mock_openai:
            mock_client = Mock()
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "AI回复测试"
            mock_client.chat.completions.create.return_value = mock_response
            mock_openai.return_value = mock_client
            
            ai_client = SimpleAIClient("test_key")
            
            # 验证基本对话功能
            response = ai_client.chat("你好")
            assert response == "AI回复测试"
            
            # 验证历史记录功能
            history = ai_client.get_history()
            assert len(history) == 2  # 用户消息 + AI回复
            assert history[0]["role"] == "user"
            assert history[1]["role"] == "assistant"
            
            # 验证历史清空功能
            ai_client.clear_history()
            history = ai_client.get_history()
            assert len(history) == 0
        
        print("✅ AIClient功能验收通过")
        
        print("🎉 核心模块功能验收测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 核心模块功能验收测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_acceptance_ui_functionality():
    """验收测试：用户界面功能"""
    print("🧪 开始用户界面功能验收测试...")
    
    try:
        # 创建QApplication实例
        if not QApplication.instance():
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        
        # 测试1: 主窗口完整功能
        print("📋 测试1: 主窗口界面功能")
        with patch('core.config_manager.ConfigManager') as mock_config:
            mock_config_instance = Mock()
            mock_config_instance.get_ai_config.return_value = {
                "api_key": "test_key",
                "base_url": "https://api.openai.com/v1",
                "model": "gpt-3.5-turbo",
                "max_tokens": 1000,
                "temperature": 0.7
            }
            mock_config_instance.get.return_value = {}
            mock_config_instance.set.return_value = True
            mock_config.return_value = mock_config_instance
            
            window = ChatWindow()
            
            # 验证窗口基本属性
            assert window.windowTitle() == "HyAIAgent - AI聊天助手"
            assert window.isVisible() == False  # 未显示
            
            # 验证界面组件存在
            assert window.chat_display is not None
            assert window.input_field is not None
            assert window.send_button is not None
            assert window.status_label is not None
            
            # 验证菜单栏
            menubar = window.menuBar()
            assert menubar is not None
            menus = [action.text() for action in menubar.actions()]
            assert "文件" in menus
            assert "设置" in menus
            assert "帮助" in menus
            
            print("✅ 主窗口界面功能验收通过")
        
        # 测试2: 用户交互功能
        print("📋 测试2: 用户交互功能")
        
        # 验证输入功能
        window.input_field.setText("测试消息")
        assert window.input_field.text() == "测试消息"
        
        # 验证消息显示功能
        window.append_message("用户", "测试消息")
        chat_content = window.chat_display.toPlainText()
        assert "用户:" in chat_content
        assert "测试消息" in chat_content
        
        # 验证新建对话功能
        window.new_chat()
        assert window.chat_display.toPlainText() == ""
        
        print("✅ 用户交互功能验收通过")
        
        # 测试3: 设置对话框功能
        print("📋 测试3: 设置对话框功能")
        from ui.chat_window import SettingsDialog
        
        settings_dialog = SettingsDialog(window.config_manager, window)
        
        # 验证设置组件存在
        assert settings_dialog.model_combo is not None
        assert settings_dialog.max_tokens_spin is not None
        assert settings_dialog.temperature_spin is not None
        assert settings_dialog.font_size_spin is not None
        assert settings_dialog.auto_save_check is not None
        
        # 验证设置保存功能
        settings_dialog.model_combo.setCurrentText("gpt-4")
        settings_dialog.max_tokens_spin.setValue(2000)
        
        with patch.object(settings_dialog.config_manager, 'set') as mock_set:
            result = settings_dialog.save_settings()
            assert result == True
            assert mock_set.call_count >= 5
        
        print("✅ 设置对话框功能验收通过")
        
        # 清理
        window.close()
        
        print("🎉 用户界面功能验收测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 用户界面功能验收测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_acceptance_integration():
    """验收测试：系统集成功能"""
    print("🧪 开始系统集成功能验收测试...")
    
    try:
        # 测试1: 应用程序完整启动流程
        print("📋 测试1: 应用程序启动流程")
        
        # 验证初始化功能
        success, message = initialize_application()
        assert success == True, f"应用程序初始化失败: {message}"
        
        print("✅ 应用程序启动流程验收通过")
        
        # 测试2: 端到端对话流程
        print("📋 测试2: 端到端对话流程")
        
        if not QApplication.instance():
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        
        with patch('core.config_manager.ConfigManager') as mock_config:
            mock_config_instance = Mock()
            mock_config_instance.get_ai_config.return_value = {
                "api_key": "test_key",
                "base_url": "https://api.openai.com/v1",
                "model": "gpt-3.5-turbo",
                "max_tokens": 1000,
                "temperature": 0.7
            }
            mock_config_instance.get.return_value = {}
            mock_config_instance.set.return_value = True
            mock_config.return_value = mock_config_instance
            
            window = ChatWindow()
            
            # 模拟完整对话流程
            with patch.object(window.ai_client, 'chat') as mock_chat:
                mock_chat.return_value = "这是AI的测试回复"
                
                # 1. 用户输入消息
                window.input_field.setText("你好，AI")
                
                # 2. 发送消息（模拟）
                message = window.input_field.text()
                window.append_message("用户", message)
                window.input_field.clear()
                
                # 3. AI回复（模拟）
                ai_response = mock_chat.return_value
                window.append_message("AI助手", ai_response)
                
                # 4. 验证对话内容
                chat_content = window.chat_display.toPlainText()
                assert "用户:" in chat_content
                assert "你好，AI" in chat_content
                assert "AI助手:" in chat_content
                assert "这是AI的测试回复" in chat_content
                
                # 5. 保存对话
                window.save_chat()
                
                # 6. 验证对话已保存到数据库
                saved_keys = window.kv_store.keys()
                chat_keys = [k for k in saved_keys if k.startswith("chat_")]
                assert len(chat_keys) > 0
            
            window.close()
        
        print("✅ 端到端对话流程验收通过")
        
        print("🎉 系统集成功能验收测试全部通过！")
        return True
        
    except Exception as e:
        print(f"❌ 系统集成功能验收测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主验收测试函数"""
    print("🚀 开始HyAIAgent第一阶段功能验收测试")
    print("=" * 60)
    
    # 执行所有验收测试
    tests = [
        ("核心模块功能", test_acceptance_core_modules),
        ("用户界面功能", test_acceptance_ui_functionality),
        ("系统集成功能", test_acceptance_integration)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 执行验收测试: {test_name}")
        print("-" * 40)
        
        if test_func():
            passed_tests += 1
            print(f"✅ {test_name} 验收测试通过")
        else:
            print(f"❌ {test_name} 验收测试失败")
    
    print("\n" + "=" * 60)
    print(f"📊 验收测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有功能验收测试通过，第一阶段开发完成！")
        print("\n📋 第一阶段功能清单:")
        print("✅ AI对话功能 - 支持OpenAI GPT模型对话")
        print("✅ 图形界面 - 基于PyQt6的现代化界面")
        print("✅ 配置管理 - 灵活的配置系统")
        print("✅ 数据存储 - 轻量级KV数据库")
        print("✅ 提示词管理 - 支持Jinja2模板")
        print("✅ 日志系统 - 完善的日志记录")
        print("✅ 对话历史 - 自动保存和管理")
        print("✅ 设置界面 - 用户友好的配置界面")
        return True
    else:
        print("❌ 部分验收测试失败，需要修复问题")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
