"""
安全管理器模块
负责文件操作的安全检查、路径验证、权限控制和操作审计
"""

import os
import json
import hashlib
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
import mimetypes

logger = logging.getLogger(__name__)


class SecurityManager:
    """
    文件操作安全管理器
    
    提供以下安全功能：
    - 路径安全验证，防止路径遍历攻击
    - 文件类型白名单检查
    - 文件权限验证
    - 操作审计日志记录
    - 文件大小限制检查
    """
    
    def __init__(self, workspace_path: str = "./workspace", 
                 config_path: str = "config/file_security.json"):
        """
        初始化安全管理器
        
        Args:
            workspace_path (str): 工作目录路径
            config_path (str): 安全配置文件路径
        """
        self.workspace_path = Path(workspace_path).resolve()
        self.config_path = Path(config_path)
        self.config = self._load_security_config()
        self.audit_log = []
        
        # 确保工作目录存在
        self.workspace_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"SecurityManager initialized with workspace: {self.workspace_path}")
    
    def _load_security_config(self) -> Dict[str, Any]:
        """
        加载安全配置文件
        
        Returns:
            Dict[str, Any]: 安全配置字典
        """
        default_config = {
            "allowed_extensions": [
                ".txt", ".md", ".json", ".yaml", ".yml", ".csv", 
                ".log", ".py", ".js", ".html", ".css", ".xml",
                ".ini", ".cfg", ".conf"
            ],
            "forbidden_extensions": [
                ".exe", ".dll", ".bat", ".sh", ".ps1", ".cmd",
                ".scr", ".com", ".pif", ".vbs", ".jar"
            ],
            "max_file_size": 10485760,  # 10MB
            "max_path_length": 260,
            "allow_hidden_files": False,
            "audit_enabled": True
        }
        
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    default_config.update(config)
                    logger.info(f"Security config loaded from {self.config_path}")
            except Exception as e:
                logger.warning(f"Failed to load security config: {e}, using defaults")
        else:
            # 创建默认配置文件
            self._save_default_config(default_config)
        
        return default_config
    
    def _save_default_config(self, config: Dict[str, Any]) -> None:
        """
        保存默认配置文件
        
        Args:
            config (Dict[str, Any]): 配置字典
        """
        try:
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            logger.info(f"Default security config saved to {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to save default config: {e}")
    
    def validate_path(self, file_path: str) -> bool:
        """
        验证文件路径安全性

        Args:
            file_path (str): 要验证的文件路径

        Returns:
            bool: 路径是否安全
        """
        try:
            # 检查输入参数
            if file_path is None or not isinstance(file_path, (str, Path)):
                self._audit_operation("path_validation", str(file_path), "failed", "Invalid path type")
                return False

            if not file_path or str(file_path).strip() == "":
                self._audit_operation("path_validation", file_path, "failed", "Empty path")
                return False

            # 转换为绝对路径（基于工作空间）
            if Path(file_path).is_absolute():
                abs_path = Path(file_path).resolve()
            else:
                abs_path = (self.workspace_path / file_path).resolve()

            # 检查路径长度
            if len(str(abs_path)) > self.config.get("max_path_length", 260):
                self._audit_operation("path_validation", file_path, "failed", "Path too long")
                return False

            # 检查是否在工作目录内
            try:
                abs_path.relative_to(self.workspace_path.resolve())
            except ValueError:
                self._audit_operation("path_validation", file_path, "failed", "Path outside workspace")
                return False
            
            # 检查隐藏文件
            if not self.config.get("allow_hidden_files", False):
                if any(part.startswith('.') for part in abs_path.parts[len(self.workspace_path.parts):]):
                    self._audit_operation("path_validation", file_path, "failed", "Hidden file not allowed")
                    return False
            
            self._audit_operation("path_validation", file_path, "success", "Path validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Path validation error: {e}")
            self._audit_operation("path_validation", file_path, "error", str(e))
            return False
    
    def check_file_type(self, file_path: str) -> bool:
        """
        检查文件类型是否允许

        Args:
            file_path (str): 文件路径

        Returns:
            bool: 文件类型是否允许
        """
        try:
            path = Path(file_path)

            # 如果是目录路径（没有扩展名或以/结尾），直接允许
            if not path.suffix or file_path.endswith('/') or file_path.endswith('\\'):
                self._audit_operation("file_type_check", file_path, "success", "Directory path allowed")
                return True

            # 如果路径存在且是目录，允许
            if path.exists() and path.is_dir():
                self._audit_operation("file_type_check", file_path, "success", "Existing directory allowed")
                return True

            extension = path.suffix.lower()

            # 检查禁止的扩展名
            forbidden_extensions = self.config.get("forbidden_extensions", [])
            if extension in forbidden_extensions:
                self._audit_operation("file_type_check", file_path, "failed", f"Forbidden extension: {extension}")
                return False

            # 检查允许的扩展名
            allowed_extensions = self.config.get("allowed_extensions", [])
            if allowed_extensions and extension not in allowed_extensions:
                self._audit_operation("file_type_check", file_path, "failed", f"Extension not in whitelist: {extension}")
                return False

            # 使用MIME类型进行额外验证
            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type and mime_type.startswith('application/x-executable'):
                self._audit_operation("file_type_check", file_path, "failed", f"Executable MIME type: {mime_type}")
                return False

            self._audit_operation("file_type_check", file_path, "success", "File type check passed")
            return True

        except Exception as e:
            logger.error(f"File type check error: {e}")
            self._audit_operation("file_type_check", file_path, "error", str(e))
            return False
    
    def check_file_permissions(self, file_path: str, operation: str = "read") -> bool:
        """
        检查文件权限
        
        Args:
            file_path (str): 文件路径
            operation (str): 操作类型 ("read", "write", "delete")
            
        Returns:
            bool: 是否有权限执行操作
        """
        try:
            # 转换为基于工作空间的绝对路径
            if Path(file_path).is_absolute():
                path = Path(file_path)
            else:
                path = self.workspace_path / file_path
            
            if operation == "read":
                # 检查文件是否存在且可读
                if path.exists() and os.access(path, os.R_OK):
                    self._audit_operation("permission_check", file_path, "success", f"Read permission granted")
                    return True
                else:
                    self._audit_operation("permission_check", file_path, "failed", f"No read permission")
                    return False
                    
            elif operation == "write":
                # 检查文件是否可写，或目录是否可写（用于创建新文件）
                if path.exists():
                    has_permission = os.access(path, os.W_OK)
                else:
                    # 对于不存在的路径，找到最近的存在的父目录进行权限检查
                    current_path = path.parent
                    while not current_path.exists() and current_path != current_path.parent:
                        current_path = current_path.parent

                    if current_path.exists():
                        has_permission = os.access(current_path, os.W_OK)
                    else:
                        has_permission = False

                if has_permission:
                    self._audit_operation("permission_check", file_path, "success", f"Write permission granted")
                    return True
                else:
                    self._audit_operation("permission_check", file_path, "failed", f"No write permission")
                    return False
                    
            elif operation == "delete":
                # 检查文件是否可删除
                if path.exists() and os.access(path.parent, os.W_OK):
                    self._audit_operation("permission_check", file_path, "success", f"Delete permission granted")
                    return True
                else:
                    self._audit_operation("permission_check", file_path, "failed", f"No delete permission")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"Permission check error: {e}")
            self._audit_operation("permission_check", file_path, "error", str(e))
            return False
    
    def check_file_size(self, file_path: str) -> bool:
        """
        检查文件大小是否超过限制
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            bool: 文件大小是否在允许范围内
        """
        try:
            path = Path(file_path)
            
            if not path.exists():
                return True  # 新文件，允许创建
            
            file_size = path.stat().st_size
            max_size = self.config.get("max_file_size", 10485760)  # 默认10MB
            
            if file_size > max_size:
                self._audit_operation("size_check", file_path, "failed", 
                                    f"File size {file_size} exceeds limit {max_size}")
                return False
            
            self._audit_operation("size_check", file_path, "success", f"File size {file_size} within limit")
            return True
            
        except Exception as e:
            logger.error(f"File size check error: {e}")
            self._audit_operation("size_check", file_path, "error", str(e))
            return False
    
    def validate_operation(self, file_path: str, operation: str = "read") -> Dict[str, Any]:
        """
        综合验证文件操作的安全性
        
        Args:
            file_path (str): 文件路径
            operation (str): 操作类型
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            "valid": False,
            "file_path": file_path,
            "operation": operation,
            "checks": {},
            "errors": []
        }
        
        try:
            # 路径安全检查
            result["checks"]["path_safe"] = self.validate_path(file_path)
            if not result["checks"]["path_safe"]:
                result["errors"].append("Path validation failed")
            
            # 文件类型检查
            result["checks"]["type_allowed"] = self.check_file_type(file_path)
            if not result["checks"]["type_allowed"]:
                result["errors"].append("File type not allowed")
            
            # 权限检查
            result["checks"]["permission_granted"] = self.check_file_permissions(file_path, operation)
            if not result["checks"]["permission_granted"]:
                result["errors"].append("Permission denied")
            
            # 文件大小检查
            result["checks"]["size_valid"] = self.check_file_size(file_path)
            if not result["checks"]["size_valid"]:
                result["errors"].append("File size exceeds limit")
            
            # 综合判断
            result["valid"] = all(result["checks"].values())
            
            # 记录审计日志
            status = "success" if result["valid"] else "failed"
            errors_str = "; ".join(result["errors"]) if result["errors"] else "All checks passed"
            self._audit_operation("validate_operation", file_path, status, errors_str)
            
        except Exception as e:
            logger.error(f"Operation validation error: {e}")
            result["errors"].append(f"Validation error: {str(e)}")
            self._audit_operation("validate_operation", file_path, "error", str(e))
        
        return result
    
    def _audit_operation(self, operation: str, file_path: str, status: str, details: str = "") -> None:
        """
        记录操作审计日志
        
        Args:
            operation (str): 操作类型
            file_path (str): 文件路径
            status (str): 操作状态 (success/failed/error)
            details (str): 详细信息
        """
        if not self.config.get("audit_enabled", True):
            return
        
        audit_record = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "file_path": file_path,
            "status": status,
            "details": details,
            "workspace": str(self.workspace_path)
        }
        
        self.audit_log.append(audit_record)
        
        # 记录到日志文件
        logger.info(f"AUDIT: {operation} on {file_path} - {status}: {details}")
    
    def get_audit_log(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取审计日志
        
        Args:
            limit (Optional[int]): 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 审计日志记录列表
        """
        if limit:
            return self.audit_log[-limit:]
        return self.audit_log.copy()
    
    def clear_audit_log(self) -> None:
        """清空审计日志"""
        self.audit_log.clear()
        logger.info("Audit log cleared")
    
    def get_security_stats(self) -> Dict[str, Any]:
        """
        获取安全统计信息
        
        Returns:
            Dict[str, Any]: 安全统计信息
        """
        total_operations = len(self.audit_log)
        if total_operations == 0:
            return {
                "total_operations": 0,
                "success_rate": 0.0,
                "failed_operations": 0,
                "error_operations": 0
            }
        
        success_count = sum(1 for record in self.audit_log if record["status"] == "success")
        failed_count = sum(1 for record in self.audit_log if record["status"] == "failed")
        error_count = sum(1 for record in self.audit_log if record["status"] == "error")
        
        return {
            "total_operations": total_operations,
            "success_rate": success_count / total_operations,
            "success_operations": success_count,
            "failed_operations": failed_count,
            "error_operations": error_count,
            "workspace_path": str(self.workspace_path),
            "config_loaded": bool(self.config)
        }
    
    def update_config(self, new_config: Dict[str, Any]) -> bool:
        """
        更新安全配置
        
        Args:
            new_config (Dict[str, Any]): 新的配置
            
        Returns:
            bool: 更新是否成功
        """
        try:
            self.config.update(new_config)
            self._save_default_config(self.config)
            self._audit_operation("config_update", "security_config", "success", "Configuration updated")
            logger.info("Security configuration updated")
            return True
        except Exception as e:
            logger.error(f"Failed to update security config: {e}")
            self._audit_operation("config_update", "security_config", "error", str(e))
            return False
