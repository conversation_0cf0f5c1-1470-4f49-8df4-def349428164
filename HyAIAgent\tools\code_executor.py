"""
代码执行器模块

提供安全的代码执行沙箱环境，支持多语言代码执行、结果捕获和安全控制。
"""

import os
import sys
import ast
import time
import uuid
import json
import tempfile
import subprocess
import threading
from datetime import datetime
from dataclasses import dataclass, asdict
from typing import Dict, List, Any, Optional, Callable, Union
from contextlib import contextmanager
import logging

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class ExecutionResult:
    """代码执行结果数据模型"""
    execution_id: str
    language: str
    code: str
    output: str
    error: str
    execution_time: float
    timestamp: datetime
    success: bool
    resource_usage: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result

@dataclass
class SecurityPolicy:
    """安全策略配置"""
    max_execution_time: int = 30  # 最大执行时间（秒）
    max_memory_mb: int = 128      # 最大内存使用（MB）
    allow_file_access: bool = False  # 是否允许文件访问
    allow_network_access: bool = False  # 是否允许网络访问
    allowed_modules: List[str] = None  # 允许的模块列表
    blocked_functions: List[str] = None  # 禁用的函数列表
    
    def __post_init__(self):
        if self.allowed_modules is None:
            self.allowed_modules = [
                'math', 'random', 'datetime', 'json', 'collections',
                'itertools', 'functools', 'operator', 're', 'string'
            ]
        if self.blocked_functions is None:
            self.blocked_functions = [
                'open', 'file', 'input', 'raw_input', 'exec', 'eval',
                'compile', '__import__', 'reload', 'exit', 'quit'
            ]

@dataclass
class CodeAnalysisResult:
    """代码分析结果"""
    is_safe: bool
    issues: List[str]
    warnings: List[str]
    complexity_score: int
    line_count: int
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)

class CodeExecutor:
    """
    代码执行器
    
    提供安全的代码执行沙箱环境，支持多语言代码执行、结果捕获和安全控制。
    支持Python、JavaScript等语言的安全执行，包含完整的安全机制和资源监控。
    """
    
    def __init__(self, 
                 config_manager=None,
                 kv_store=None,
                 security_policy: SecurityPolicy = None):
        """
        初始化代码执行器
        
        Args:
            config_manager: 配置管理器
            kv_store: 键值存储
            security_policy: 安全策略配置
        """
        self.config_manager = config_manager
        self.kv_store = kv_store
        self.security_policy = security_policy or SecurityPolicy()
        
        # 执行历史
        self.execution_history: List[ExecutionResult] = []
        self.max_history_size = 1000
        
        # 支持的语言
        self.supported_languages = {
            'python': self._execute_python,
            'javascript': self._execute_javascript,
            'bash': self._execute_bash
        }
        
        # 执行统计
        self.stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'blocked_executions': 0,
            'total_execution_time': 0.0,
            'average_execution_time': 0.0
        }
        
        # 回调函数
        self.execution_callbacks: List[Callable] = []
        
        logger.info("代码执行器初始化完成")
    
    async def execute_code(self, 
                          code: str, 
                          language: str = 'python',
                          context: Dict[str, Any] = None) -> ExecutionResult:
        """
        执行代码
        
        Args:
            code: 要执行的代码
            language: 编程语言
            context: 执行上下文
            
        Returns:
            ExecutionResult: 执行结果
        """
        execution_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # 代码安全分析
            analysis_result = self._analyze_code_security(code, language)
            if not analysis_result.is_safe:
                self.stats['blocked_executions'] += 1
                return ExecutionResult(
                    execution_id=execution_id,
                    language=language,
                    code=code,
                    output="",
                    error=f"代码安全检查失败: {', '.join(analysis_result.issues)}",
                    execution_time=0.0,
                    timestamp=datetime.now(),
                    success=False,
                    resource_usage={}
                )
            
            # 执行代码
            if language not in self.supported_languages:
                raise ValueError(f"不支持的编程语言: {language}")
            
            executor = self.supported_languages[language]
            output, error, resource_usage = await executor(code, context or {})
            
            execution_time = time.time() - start_time
            success = error == ""
            
            # 创建执行结果
            result = ExecutionResult(
                execution_id=execution_id,
                language=language,
                code=code,
                output=output,
                error=error,
                execution_time=execution_time,
                timestamp=datetime.now(),
                success=success,
                resource_usage=resource_usage
            )
            
            # 更新统计
            self.stats['total_executions'] += 1
            if success:
                self.stats['successful_executions'] += 1
            else:
                self.stats['failed_executions'] += 1
            
            self.stats['total_execution_time'] += execution_time
            self.stats['average_execution_time'] = (
                self.stats['total_execution_time'] / self.stats['total_executions']
            )
            
            # 保存到历史记录
            self.execution_history.append(result)
            if len(self.execution_history) > self.max_history_size:
                self.execution_history.pop(0)
            
            # 调用回调函数
            for callback in self.execution_callbacks:
                try:
                    await callback(result)
                except Exception as e:
                    logger.error(f"执行回调函数失败: {e}")
            
            logger.info(f"代码执行完成: {execution_id}, 成功: {success}, 耗时: {execution_time:.2f}秒")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.stats['total_executions'] += 1
            self.stats['failed_executions'] += 1
            
            error_result = ExecutionResult(
                execution_id=execution_id,
                language=language,
                code=code,
                output="",
                error=str(e),
                execution_time=execution_time,
                timestamp=datetime.now(),
                success=False,
                resource_usage={}
            )
            
            self.execution_history.append(error_result)
            logger.error(f"代码执行失败: {execution_id}, 错误: {e}")
            return error_result
    
    def _analyze_code_security(self, code: str, language: str) -> CodeAnalysisResult:
        """分析代码安全性"""
        issues = []
        warnings = []
        
        if language == 'python':
            return self._analyze_python_security(code)
        elif language == 'javascript':
            return self._analyze_javascript_security(code)
        else:
            # 基本安全检查
            dangerous_patterns = [
                'import os', 'import sys', 'import subprocess',
                'exec(', 'eval(', 'open(', '__import__',
                'file(', 'input(', 'raw_input('
            ]
            
            for pattern in dangerous_patterns:
                if pattern in code:
                    issues.append(f"检测到危险操作: {pattern}")
        
        is_safe = len(issues) == 0
        complexity_score = len(code.split('\n'))
        line_count = len([line for line in code.split('\n') if line.strip()])
        
        return CodeAnalysisResult(
            is_safe=is_safe,
            issues=issues,
            warnings=warnings,
            complexity_score=complexity_score,
            line_count=line_count
        )

    def _analyze_python_security(self, code: str) -> CodeAnalysisResult:
        """分析Python代码安全性"""
        issues = []
        warnings = []

        try:
            # 解析AST
            tree = ast.parse(code)

            # 检查危险的AST节点
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name not in self.security_policy.allowed_modules:
                            issues.append(f"禁止导入模块: {alias.name}")

                elif isinstance(node, ast.ImportFrom):
                    if node.module and node.module not in self.security_policy.allowed_modules:
                        issues.append(f"禁止从模块导入: {node.module}")

                elif isinstance(node, ast.Call):
                    if isinstance(node.func, ast.Name):
                        if node.func.id in self.security_policy.blocked_functions:
                            issues.append(f"禁止调用函数: {node.func.id}")

                elif isinstance(node, ast.Attribute):
                    if node.attr in ['__globals__', '__locals__', '__dict__']:
                        issues.append(f"禁止访问属性: {node.attr}")

        except SyntaxError as e:
            issues.append(f"语法错误: {e}")

        # 检查字符串模式
        dangerous_strings = [
            'subprocess', 'os.system', 'eval', 'exec',
            '__import__', 'compile', 'globals', 'locals'
        ]

        for pattern in dangerous_strings:
            if pattern in code:
                warnings.append(f"检测到潜在危险操作: {pattern}")

        is_safe = len(issues) == 0
        complexity_score = len([node for node in ast.walk(ast.parse(code)) if isinstance(node, ast.stmt)])
        line_count = len([line for line in code.split('\n') if line.strip()])

        return CodeAnalysisResult(
            is_safe=is_safe,
            issues=issues,
            warnings=warnings,
            complexity_score=complexity_score,
            line_count=line_count
        )

    def _analyze_javascript_security(self, code: str) -> CodeAnalysisResult:
        """分析JavaScript代码安全性"""
        issues = []
        warnings = []

        # JavaScript危险模式检查
        dangerous_patterns = [
            'eval(', 'Function(', 'setTimeout(', 'setInterval(',
            'document.', 'window.', 'global.', 'process.',
            'require(', 'import(', 'fetch(', 'XMLHttpRequest'
        ]

        for pattern in dangerous_patterns:
            if pattern in code:
                issues.append(f"检测到危险操作: {pattern}")

        # 检查网络相关操作
        network_patterns = ['http', 'https', 'fetch', 'ajax', 'xhr']
        for pattern in network_patterns:
            if pattern.lower() in code.lower():
                if not self.security_policy.allow_network_access:
                    issues.append(f"禁止网络访问: {pattern}")

        is_safe = len(issues) == 0
        complexity_score = code.count('{') + code.count('function') + code.count('if')
        line_count = len([line for line in code.split('\n') if line.strip()])

        return CodeAnalysisResult(
            is_safe=is_safe,
            issues=issues,
            warnings=warnings,
            complexity_score=complexity_score,
            line_count=line_count
        )

    async def _execute_python(self, code: str, context: Dict[str, Any]) -> tuple:
        """执行Python代码"""
        import io
        import sys
        from contextlib import redirect_stdout, redirect_stderr

        # 创建安全的执行环境
        safe_globals = {
            '__builtins__': {
                'print': print,
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'bool': bool,
                'list': list,
                'dict': dict,
                'tuple': tuple,
                'set': set,
                'range': range,
                'enumerate': enumerate,
                'zip': zip,
                'map': map,
                'filter': filter,
                'sum': sum,
                'min': min,
                'max': max,
                'abs': abs,
                'round': round,
                'sorted': sorted,
                'reversed': reversed
            }
        }

        # 添加允许的模块
        for module_name in self.security_policy.allowed_modules:
            try:
                safe_globals[module_name] = __import__(module_name)
            except ImportError:
                pass

        # 添加上下文变量
        safe_globals.update(context)

        # 捕获输出
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()

        try:
            # 用于捕获线程中的异常
            thread_exception = None

            def execute_with_exception_capture():
                nonlocal thread_exception
                try:
                    exec(code, safe_globals, {})
                except Exception as e:
                    thread_exception = e

            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                # 使用超时执行
                exec_thread = threading.Thread(target=execute_with_exception_capture)
                exec_thread.daemon = True
                exec_thread.start()
                exec_thread.join(timeout=self.security_policy.max_execution_time)

                if exec_thread.is_alive():
                    return "", "执行超时", {"timeout": True}

                # 检查线程中是否有异常
                if thread_exception:
                    return "", str(thread_exception), {"error_type": type(thread_exception).__name__}

            output = stdout_capture.getvalue()
            error = stderr_capture.getvalue()

            return output, error, {"memory_usage": 0, "cpu_time": 0}

        except Exception as e:
            return "", str(e), {"error_type": type(e).__name__}

    async def _execute_javascript(self, code: str, context: Dict[str, Any]) -> tuple:
        """执行JavaScript代码"""
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                # 添加上下文变量
                context_js = "const context = " + json.dumps(context) + ";\n"
                f.write(context_js + code)
                temp_file = f.name

            # 使用Node.js执行
            process = subprocess.Popen(
                ['node', temp_file],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=self.security_policy.max_execution_time
            )

            stdout, stderr = process.communicate()

            # 清理临时文件
            os.unlink(temp_file)

            return stdout, stderr, {"exit_code": process.returncode}

        except subprocess.TimeoutExpired:
            return "", "执行超时", {"timeout": True}
        except FileNotFoundError:
            return "", "Node.js未安装或不在PATH中", {"missing_runtime": True}
        except Exception as e:
            return "", str(e), {"error_type": type(e).__name__}

    async def _execute_bash(self, code: str, context: Dict[str, Any]) -> tuple:
        """执行Bash代码"""
        try:
            # 安全检查
            dangerous_commands = ['rm', 'del', 'format', 'mkfs', 'dd', 'sudo', 'su']
            for cmd in dangerous_commands:
                if cmd in code:
                    return "", f"禁止执行危险命令: {cmd}", {"blocked": True}

            # 执行命令
            process = subprocess.Popen(
                code,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=self.security_policy.max_execution_time
            )

            stdout, stderr = process.communicate()

            return stdout, stderr, {"exit_code": process.returncode}

        except subprocess.TimeoutExpired:
            return "", "执行超时", {"timeout": True}
        except Exception as e:
            return "", str(e), {"error_type": type(e).__name__}

    def get_execution_history(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取执行历史

        Args:
            limit: 限制返回数量

        Returns:
            List[Dict]: 执行历史列表
        """
        history = self.execution_history[-limit:] if limit else self.execution_history
        return [result.to_dict() for result in history]

    def get_execution_stats(self) -> Dict[str, Any]:
        """
        获取执行统计信息

        Returns:
            Dict: 统计信息
        """
        return {
            **self.stats,
            'success_rate': (
                self.stats['successful_executions'] / max(self.stats['total_executions'], 1) * 100
            ),
            'failure_rate': (
                self.stats['failed_executions'] / max(self.stats['total_executions'], 1) * 100
            ),
            'block_rate': (
                self.stats['blocked_executions'] / max(self.stats['total_executions'], 1) * 100
            ),
            'supported_languages': list(self.supported_languages.keys()),
            'security_policy': asdict(self.security_policy)
        }

    def update_security_policy(self, policy: SecurityPolicy) -> bool:
        """
        更新安全策略

        Args:
            policy: 新的安全策略

        Returns:
            bool: 更新是否成功
        """
        try:
            self.security_policy = policy
            logger.info("安全策略已更新")
            return True
        except Exception as e:
            logger.error(f"更新安全策略失败: {e}")
            return False

    def add_execution_callback(self, callback: Callable) -> bool:
        """
        添加执行回调函数

        Args:
            callback: 回调函数

        Returns:
            bool: 添加是否成功
        """
        try:
            if callback not in self.execution_callbacks:
                self.execution_callbacks.append(callback)
                logger.info("执行回调函数已添加")
                return True
            return False
        except Exception as e:
            logger.error(f"添加执行回调函数失败: {e}")
            return False

    def remove_execution_callback(self, callback: Callable) -> bool:
        """
        移除执行回调函数

        Args:
            callback: 回调函数

        Returns:
            bool: 移除是否成功
        """
        try:
            if callback in self.execution_callbacks:
                self.execution_callbacks.remove(callback)
                logger.info("执行回调函数已移除")
                return True
            return False
        except Exception as e:
            logger.error(f"移除执行回调函数失败: {e}")
            return False

    def clear_execution_history(self) -> bool:
        """
        清空执行历史

        Returns:
            bool: 清空是否成功
        """
        try:
            self.execution_history.clear()
            logger.info("执行历史已清空")
            return True
        except Exception as e:
            logger.error(f"清空执行历史失败: {e}")
            return False

    def get_language_support(self) -> Dict[str, Dict[str, Any]]:
        """
        获取语言支持信息

        Returns:
            Dict: 语言支持信息
        """
        return {
            'python': {
                'supported': True,
                'version': sys.version,
                'features': ['ast_analysis', 'safe_execution', 'timeout_control'],
                'security_level': 'high'
            },
            'javascript': {
                'supported': True,
                'runtime': 'node.js',
                'features': ['syntax_check', 'timeout_control'],
                'security_level': 'medium'
            },
            'bash': {
                'supported': True,
                'features': ['command_filtering', 'timeout_control'],
                'security_level': 'low'
            }
        }

    async def validate_code(self, code: str, language: str) -> CodeAnalysisResult:
        """
        验证代码而不执行

        Args:
            code: 代码内容
            language: 编程语言

        Returns:
            CodeAnalysisResult: 分析结果
        """
        return self._analyze_code_security(code, language)

    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.execution_history.clear()
            self.execution_callbacks.clear()
            logger.info("代码执行器资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {e}")

    def __del__(self):
        """析构函数"""
        try:
            # 清理临时文件等资源
            pass
        except:
            pass
