"""
搜索策略优化器测试模块

测试SearchStrategyOptimizer类的各项功能，包括参数优化、性能记录、趋势分析等。
"""

import asyncio
import pytest
import sys
import os
from datetime import datetime, timedelta
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from operations.search_strategy_optimizer import (
    SearchStrategyOptimizer, SearchParameters, PerformanceMetrics,
    OptimizationStrategy, OptimizationResult
)


class TestSearchStrategyOptimizer:
    """搜索策略优化器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.optimizer = SearchStrategyOptimizer()
        
        # 创建测试参数
        self.default_params = SearchParameters(
            max_results=5,
            search_depth="basic",
            timeout=30,
            include_images=False,
            include_raw_content=False
        )
        
        # 创建测试性能指标
        base_time = datetime.now()
        self.test_metrics = [
            PerformanceMetrics(
                response_time=2.5,
                result_count=5,
                quality_score=0.8,
                cache_hit=False,
                success=True,
                cost=0.1,
                timestamp=base_time
            ),
            PerformanceMetrics(
                response_time=3.2,
                result_count=5,
                quality_score=0.75,
                cache_hit=True,
                success=True,
                cost=0.05,
                timestamp=base_time + timedelta(minutes=1)
            ),
            PerformanceMetrics(
                response_time=1.8,
                result_count=3,
                quality_score=0.7,
                cache_hit=False,
                success=True,
                cost=0.08,
                timestamp=base_time + timedelta(minutes=2)
            )
        ]
    
    @pytest.mark.asyncio
    async def test_optimize_parameters_basic(self):
        """测试基础参数优化"""
        # 添加一些历史数据
        for i, metrics in enumerate(self.test_metrics):
            params = SearchParameters(max_results=5, search_depth="basic", timeout=30)
            await self.optimizer.record_performance(params, metrics)
        
        # 执行优化
        result = await self.optimizer.optimize_parameters(self.default_params)
        
        assert isinstance(result, OptimizationResult)
        assert isinstance(result.optimized_parameters, SearchParameters)
        assert 0.0 <= result.confidence_score <= 1.0
        assert len(result.optimization_reason) > 0
        assert isinstance(result.expected_improvement, dict)
        assert isinstance(result.baseline_metrics, dict)
        
        print(f"✅ 基础参数优化测试通过: 置信度 {result.confidence_score:.2f}")
    
    @pytest.mark.asyncio
    async def test_optimize_parameters_with_context(self):
        """测试带上下文的参数优化"""
        # 紧急查询上下文
        urgent_context = {"urgent": True, "max_time": 10}
        
        result = await self.optimizer.optimize_parameters(
            self.default_params, 
            query_context=urgent_context
        )
        
        assert isinstance(result, OptimizationResult)
        # 紧急查询应该减少超时时间和结果数量
        assert result.optimized_parameters.timeout <= self.default_params.timeout
        assert result.optimized_parameters.max_results <= self.default_params.max_results
        
        print("✅ 带上下文参数优化测试通过")
    
    @pytest.mark.asyncio
    async def test_record_performance(self):
        """测试性能记录"""
        initial_count = len(self.optimizer.performance_history)
        
        await self.optimizer.record_performance(self.default_params, self.test_metrics[0])
        
        assert len(self.optimizer.performance_history) == initial_count + 1
        
        # 检查参数性能记录
        param_key = self.optimizer._get_parameter_key(self.default_params)
        assert param_key in self.optimizer.parameter_performance
        assert len(self.optimizer.parameter_performance[param_key]) == 1
        
        print("✅ 性能记录测试通过")
    
    @pytest.mark.asyncio
    async def test_get_best_parameters_insufficient_data(self):
        """测试数据不足时获取最佳参数"""
        # 清空历史数据
        self.optimizer.performance_history.clear()
        self.optimizer.parameter_performance.clear()
        
        best_params = await self.optimizer.get_best_parameters()
        
        # 应该返回默认参数
        assert isinstance(best_params, SearchParameters)
        assert best_params.max_results == 5  # 默认值
        assert best_params.search_depth == "basic"  # 默认值
        
        print("✅ 数据不足时获取最佳参数测试通过")
    
    @pytest.mark.asyncio
    async def test_get_best_parameters_with_data(self):
        """测试有数据时获取最佳参数"""
        # 添加多组参数的性能数据
        params1 = SearchParameters(max_results=3, search_depth="basic", timeout=20)
        params2 = SearchParameters(max_results=5, search_depth="advanced", timeout=30)
        
        # 为params1添加更好的性能数据
        good_metrics = PerformanceMetrics(
            response_time=1.5, result_count=3, quality_score=0.9,
            cache_hit=True, success=True, cost=0.05, timestamp=datetime.now()
        )
        
        for _ in range(5):  # 添加足够的样本
            await self.optimizer.record_performance(params1, good_metrics)
        
        # 为params2添加较差的性能数据
        bad_metrics = PerformanceMetrics(
            response_time=4.0, result_count=5, quality_score=0.6,
            cache_hit=False, success=True, cost=0.15, timestamp=datetime.now()
        )
        
        for _ in range(5):
            await self.optimizer.record_performance(params2, bad_metrics)
        
        best_params = await self.optimizer.get_best_parameters()
        
        assert isinstance(best_params, SearchParameters)
        # 应该选择性能更好的params1
        assert best_params.max_results == 3
        assert best_params.search_depth == "basic"
        assert best_params.timeout == 20
        
        print("✅ 有数据时获取最佳参数测试通过")
    
    @pytest.mark.asyncio
    async def test_analyze_performance_trends_insufficient_data(self):
        """测试数据不足时的性能趋势分析"""
        # 清空历史数据
        self.optimizer.performance_history.clear()
        
        result = await self.optimizer.analyze_performance_trends()
        
        assert result["status"] == "insufficient_data"
        assert "数据不足" in result["message"]
        
        print("✅ 数据不足时性能趋势分析测试通过")
    
    @pytest.mark.asyncio
    async def test_analyze_performance_trends_with_data(self):
        """测试有数据时的性能趋势分析"""
        # 添加趋势数据
        base_time = datetime.now()
        for i in range(10):
            metrics = PerformanceMetrics(
                response_time=2.0 + i * 0.1,  # 递增趋势
                result_count=5,
                quality_score=0.8 - i * 0.02,  # 递减趋势
                cache_hit=i % 2 == 0,
                success=True,
                cost=0.1,
                timestamp=base_time + timedelta(minutes=i)
            )
            await self.optimizer.record_performance(self.default_params, metrics)
        
        result = await self.optimizer.analyze_performance_trends()
        
        assert result["status"] == "success"
        assert "trends" in result
        assert "summary" in result
        assert "sample_count" in result
        
        # 检查趋势分析结果
        trends = result["trends"]
        assert "response_time" in trends
        assert "quality_score" in trends
        assert "success_rate" in trends
        assert "cache_hit_rate" in trends
        
        print("✅ 有数据时性能趋势分析测试通过")
    
    def test_set_optimization_strategy(self):
        """测试设置优化策略"""
        # 测试性能优先策略
        self.optimizer.set_optimization_strategy(OptimizationStrategy.PERFORMANCE_FIRST)
        assert self.optimizer.optimization_strategy == OptimizationStrategy.PERFORMANCE_FIRST
        
        # 测试质量优先策略
        self.optimizer.set_optimization_strategy(OptimizationStrategy.QUALITY_FIRST)
        assert self.optimizer.optimization_strategy == OptimizationStrategy.QUALITY_FIRST
        
        # 测试平衡策略
        self.optimizer.set_optimization_strategy(OptimizationStrategy.BALANCED)
        assert self.optimizer.optimization_strategy == OptimizationStrategy.BALANCED
        
        print("✅ 设置优化策略测试通过")
    
    def test_get_performance_statistics_no_data(self):
        """测试无数据时获取性能统计"""
        # 清空历史数据
        self.optimizer.performance_history.clear()
        
        stats = self.optimizer.get_performance_statistics()
        
        assert stats["status"] == "no_data"
        assert "暂无性能数据" in stats["message"]
        
        print("✅ 无数据时性能统计测试通过")
    
    @pytest.mark.asyncio
    async def test_get_performance_statistics_with_data(self):
        """测试有数据时获取性能统计"""
        # 添加测试数据
        for metrics in self.test_metrics:
            await self.optimizer.record_performance(self.default_params, metrics)
        
        stats = self.optimizer.get_performance_statistics()
        
        assert stats["status"] == "success"
        assert "statistics" in stats
        
        statistics = stats["statistics"]
        assert "total_queries" in statistics
        assert "success_rate" in statistics
        assert "cache_hit_rate" in statistics
        assert "response_time" in statistics
        assert "quality_score" in statistics
        
        # 检查统计值的合理性
        assert statistics["total_queries"] == len(self.test_metrics)
        assert 0.0 <= statistics["success_rate"] <= 1.0
        assert 0.0 <= statistics["cache_hit_rate"] <= 1.0
        
        print("✅ 有数据时性能统计测试通过")
    
    def test_search_parameters_serialization(self):
        """测试搜索参数序列化"""
        params = SearchParameters(
            max_results=10,
            search_depth="advanced",
            timeout=60,
            include_images=True,
            domains_include=["example.com"]
        )
        
        # 测试转换为字典
        params_dict = params.to_dict()
        assert isinstance(params_dict, dict)
        assert params_dict["max_results"] == 10
        assert params_dict["search_depth"] == "advanced"
        
        # 测试从字典创建
        restored_params = SearchParameters.from_dict(params_dict)
        assert restored_params.max_results == params.max_results
        assert restored_params.search_depth == params.search_depth
        assert restored_params.timeout == params.timeout
        
        print("✅ 搜索参数序列化测试通过")
    
    def test_performance_metrics_serialization(self):
        """测试性能指标序列化"""
        metrics = self.test_metrics[0]
        
        metrics_dict = metrics.to_dict()
        assert isinstance(metrics_dict, dict)
        assert "response_time" in metrics_dict
        assert "quality_score" in metrics_dict
        assert "timestamp" in metrics_dict
        assert isinstance(metrics_dict["timestamp"], str)  # 应该是ISO格式字符串
        
        print("✅ 性能指标序列化测试通过")
    
    def test_parameter_key_operations(self):
        """测试参数键操作"""
        params = SearchParameters(max_results=8, search_depth="advanced", timeout=45)
        
        # 测试生成参数键
        param_key = self.optimizer._get_parameter_key(params)
        assert isinstance(param_key, str)
        assert "8" in param_key
        assert "advanced" in param_key
        assert "45" in param_key
        
        # 测试解析参数键
        parsed_params = self.optimizer._parse_parameter_key(param_key)
        assert parsed_params.max_results == params.max_results
        assert parsed_params.search_depth == params.search_depth
        assert parsed_params.timeout == params.timeout
        
        print("✅ 参数键操作测试通过")


async def run_all_tests():
    """运行所有测试"""
    test_instance = TestSearchStrategyOptimizer()
    test_methods = [method for method in dir(test_instance) if method.startswith('test_')]
    
    print("🚀 开始搜索策略优化器测试...")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for method_name in test_methods:
        try:
            test_instance.setup_method()
            method = getattr(test_instance, method_name)
            
            if asyncio.iscoroutinefunction(method):
                await method()
            else:
                method()
            
            passed += 1
        except Exception as e:
            print(f"❌ {method_name} 失败: {str(e)}")
            import traceback
            traceback.print_exc()
            failed += 1
    
    print("=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有搜索策略优化器测试通过！")
    else:
        print(f"⚠️  有 {failed} 个测试失败，请检查代码")
    
    return failed == 0


if __name__ == "__main__":
    asyncio.run(run_all_tests())
