"""
信息分析器模块

提供信息分析和整合功能，包括趋势分析、关联发现、洞察生成等。
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
import re
import statistics
from collections import Counter, defaultdict

# 配置日志
logger = logging.getLogger(__name__)


class AnalysisType(Enum):
    """分析类型枚举"""
    TREND = "trend"
    CORRELATION = "correlation"
    SENTIMENT = "sentiment"
    KEYWORD = "keyword"
    TEMPORAL = "temporal"
    COMPARATIVE = "comparative"


class TrendDirection(Enum):
    """趋势方向枚举"""
    INCREASING = "increasing"
    DECREASING = "decreasing"
    STABLE = "stable"
    VOLATILE = "volatile"


@dataclass
class DataPoint:
    """数据点结构"""
    timestamp: datetime
    value: float
    source: str
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'value': self.value,
            'source': self.source,
            'metadata': self.metadata or {}
        }


@dataclass
class TrendAnalysis:
    """趋势分析结果"""
    direction: TrendDirection
    strength: float  # 0-1之间，表示趋势强度
    confidence: float  # 0-1之间，表示置信度
    data_points: List[DataPoint]
    time_range: Tuple[datetime, datetime]
    key_insights: List[str]
    statistical_summary: Dict[str, float]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'direction': self.direction.value,
            'strength': self.strength,
            'confidence': self.confidence,
            'data_points': [dp.to_dict() for dp in self.data_points],
            'time_range': [self.time_range[0].isoformat(), self.time_range[1].isoformat()],
            'key_insights': self.key_insights,
            'statistical_summary': self.statistical_summary
        }


@dataclass
class CorrelationReport:
    """关联分析报告"""
    correlation_coefficient: float
    p_value: float
    significance_level: str
    dataset_pairs: List[Tuple[str, str]]
    correlation_insights: List[str]
    scatter_data: List[Tuple[float, float]]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'correlation_coefficient': self.correlation_coefficient,
            'p_value': self.p_value,
            'significance_level': self.significance_level,
            'dataset_pairs': self.dataset_pairs,
            'correlation_insights': self.correlation_insights,
            'scatter_data': self.scatter_data
        }


@dataclass
class Dataset:
    """数据集结构"""
    name: str
    data_points: List[DataPoint]
    metadata: Dict[str, Any] = None
    
    def get_values(self) -> List[float]:
        """获取数值列表"""
        return [dp.value for dp in self.data_points]
    
    def get_timestamps(self) -> List[datetime]:
        """获取时间戳列表"""
        return [dp.timestamp for dp in self.data_points]


@dataclass
class Insights:
    """洞察报告"""
    summary: str
    key_findings: List[str]
    recommendations: List[str]
    confidence_score: float
    supporting_evidence: List[str]
    analysis_metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class AnalysisResults:
    """分析结果集合"""
    trend_analyses: List[TrendAnalysis] = None
    correlation_reports: List[CorrelationReport] = None
    sentiment_analysis: Dict[str, Any] = None
    keyword_analysis: Dict[str, Any] = None
    temporal_patterns: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {}
        if self.trend_analyses:
            result['trend_analyses'] = [ta.to_dict() for ta in self.trend_analyses]
        if self.correlation_reports:
            result['correlation_reports'] = [cr.to_dict() for cr in self.correlation_reports]
        if self.sentiment_analysis:
            result['sentiment_analysis'] = self.sentiment_analysis
        if self.keyword_analysis:
            result['keyword_analysis'] = self.keyword_analysis
        if self.temporal_patterns:
            result['temporal_patterns'] = self.temporal_patterns
        return result


class InformationAnalyzer:
    """信息分析器，提供信息分析和整合功能"""
    
    def __init__(self, config_manager=None):
        """初始化信息分析器"""
        self.config_manager = config_manager
        self.analysis_cache = {}
        self.logger = logging.getLogger(__name__)
        
        # 分析配置
        self.trend_threshold = 0.01  # 趋势检测阈值（降低以提高敏感度）
        self.correlation_threshold = 0.5  # 相关性阈值
        self.confidence_threshold = 0.7  # 置信度阈值
        
        self.logger.info("InformationAnalyzer initialized")
    
    async def analyze_trends(self, data_points: List[DataPoint]) -> TrendAnalysis:
        """分析信息趋势"""
        try:
            if not data_points:
                raise ValueError("数据点列表不能为空")
            
            # 按时间排序
            sorted_points = sorted(data_points, key=lambda x: x.timestamp)
            values = [dp.value for dp in sorted_points]
            
            # 计算趋势方向
            direction = self._calculate_trend_direction(values)
            
            # 计算趋势强度
            strength = self._calculate_trend_strength(values)
            
            # 计算置信度
            confidence = self._calculate_confidence(values)
            
            # 生成洞察
            insights = self._generate_trend_insights(direction, strength, values)
            
            # 统计摘要
            stats = self._calculate_statistical_summary(values)
            
            # 时间范围
            time_range = (sorted_points[0].timestamp, sorted_points[-1].timestamp)
            
            result = TrendAnalysis(
                direction=direction,
                strength=strength,
                confidence=confidence,
                data_points=sorted_points,
                time_range=time_range,
                key_insights=insights,
                statistical_summary=stats
            )
            
            self.logger.info(f"趋势分析完成: {direction.value}, 强度: {strength:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"趋势分析失败: {str(e)}")
            raise
    
    async def find_correlations(self, datasets: List[Dataset]) -> CorrelationReport:
        """发现数据关联"""
        try:
            if len(datasets) < 2:
                raise ValueError("至少需要两个数据集进行关联分析")
            
            # 选择前两个数据集进行分析
            dataset1, dataset2 = datasets[0], datasets[1]
            values1 = dataset1.get_values()
            values2 = dataset2.get_values()
            
            # 确保数据长度一致
            min_length = min(len(values1), len(values2))
            values1 = values1[:min_length]
            values2 = values2[:min_length]
            
            # 计算相关系数
            correlation_coeff = self._calculate_correlation(values1, values2)
            
            # 计算p值（简化版本）
            p_value = self._calculate_p_value(correlation_coeff, min_length)
            
            # 确定显著性水平
            significance = self._determine_significance(p_value)
            
            # 生成关联洞察
            insights = self._generate_correlation_insights(correlation_coeff, significance)
            
            # 生成散点图数据
            scatter_data = list(zip(values1, values2))
            
            result = CorrelationReport(
                correlation_coefficient=correlation_coeff,
                p_value=p_value,
                significance_level=significance,
                dataset_pairs=[(dataset1.name, dataset2.name)],
                correlation_insights=insights,
                scatter_data=scatter_data
            )
            
            self.logger.info(f"关联分析完成: 相关系数 {correlation_coeff:.3f}")
            return result
            
        except Exception as e:
            self.logger.error(f"关联分析失败: {str(e)}")
            raise
    
    async def generate_insights(self, analysis_results: AnalysisResults) -> Insights:
        """生成洞察报告"""
        try:
            key_findings = []
            recommendations = []
            supporting_evidence = []
            
            # 分析趋势结果
            if analysis_results.trend_analyses:
                for trend in analysis_results.trend_analyses:
                    key_findings.extend(trend.key_insights)
                    supporting_evidence.append(f"趋势分析: {trend.direction.value}, 强度: {trend.strength:.2f}")
            
            # 分析关联结果
            if analysis_results.correlation_reports:
                for corr in analysis_results.correlation_reports:
                    key_findings.extend(corr.correlation_insights)
                    supporting_evidence.append(f"关联分析: 相关系数 {corr.correlation_coefficient:.3f}")
            
            # 生成综合摘要
            summary = self._generate_comprehensive_summary(analysis_results)
            
            # 生成建议
            recommendations = self._generate_recommendations(analysis_results)
            
            # 计算综合置信度
            confidence_score = self._calculate_overall_confidence(analysis_results)
            
            # 分析元数据
            metadata = {
                'analysis_timestamp': datetime.now().isoformat(),
                'analysis_types': self._get_analysis_types(analysis_results),
                'data_quality_score': self._assess_data_quality(analysis_results)
            }
            
            result = Insights(
                summary=summary,
                key_findings=key_findings,
                recommendations=recommendations,
                confidence_score=confidence_score,
                supporting_evidence=supporting_evidence,
                analysis_metadata=metadata
            )
            
            self.logger.info("洞察报告生成完成")
            return result
            
        except Exception as e:
            self.logger.error(f"洞察生成失败: {str(e)}")
            raise

    # 辅助方法
    def _calculate_trend_direction(self, values: List[float]) -> TrendDirection:
        """计算趋势方向"""
        if len(values) < 2:
            return TrendDirection.STABLE

        # 计算线性回归斜率
        n = len(values)
        x = list(range(n))

        # 计算斜率
        x_mean = sum(x) / n
        y_mean = sum(values) / n

        numerator = sum((x[i] - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))

        if denominator == 0:
            return TrendDirection.STABLE

        slope = numerator / denominator

        # 计算变异系数判断波动性
        std_dev = statistics.stdev(values) if len(values) > 1 else 0
        cv = std_dev / abs(y_mean) if y_mean != 0 else 0

        # 判断趋势
        if cv > 0.3:  # 高变异系数表示波动
            return TrendDirection.VOLATILE
        elif abs(slope) < self.trend_threshold:
            return TrendDirection.STABLE
        elif slope > 0:
            return TrendDirection.INCREASING
        else:
            return TrendDirection.DECREASING

    def _calculate_trend_strength(self, values: List[float]) -> float:
        """计算趋势强度"""
        if len(values) < 2:
            return 0.0

        # 使用R²值作为趋势强度指标
        n = len(values)
        x = list(range(n))

        # 计算线性回归
        x_mean = sum(x) / n
        y_mean = sum(values) / n

        numerator = sum((x[i] - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))

        if denominator == 0:
            return 0.0

        slope = numerator / denominator
        intercept = y_mean - slope * x_mean

        # 计算R²
        y_pred = [slope * x[i] + intercept for i in range(n)]
        ss_res = sum((values[i] - y_pred[i]) ** 2 for i in range(n))
        ss_tot = sum((values[i] - y_mean) ** 2 for i in range(n))

        if ss_tot == 0:
            return 0.0

        r_squared = 1 - (ss_res / ss_tot)
        return max(0.0, min(1.0, r_squared))

    def _calculate_confidence(self, values: List[float]) -> float:
        """计算置信度"""
        if len(values) < 3:
            return 0.5

        # 基于数据点数量和一致性计算置信度
        n = len(values)

        # 数据量因子
        quantity_factor = min(1.0, n / 10)  # 10个点以上认为数据充足

        # 一致性因子（基于标准差）
        std_dev = statistics.stdev(values)
        mean_val = statistics.mean(values)
        cv = std_dev / abs(mean_val) if mean_val != 0 else 1.0
        consistency_factor = max(0.0, 1.0 - cv)

        # 综合置信度
        confidence = (quantity_factor + consistency_factor) / 2
        return max(0.1, min(1.0, confidence))

    def _generate_trend_insights(self, direction: TrendDirection, strength: float, values: List[float]) -> List[str]:
        """生成趋势洞察"""
        insights = []

        # 基本趋势描述
        if direction == TrendDirection.INCREASING:
            insights.append(f"数据呈现上升趋势，趋势强度为 {strength:.2f}")
        elif direction == TrendDirection.DECREASING:
            insights.append(f"数据呈现下降趋势，趋势强度为 {strength:.2f}")
        elif direction == TrendDirection.STABLE:
            insights.append(f"数据保持相对稳定，变化幅度较小")
        else:  # VOLATILE
            insights.append(f"数据波动较大，存在明显的不稳定性")

        # 强度评估
        if strength > 0.8:
            insights.append("趋势非常明显，具有很强的预测价值")
        elif strength > 0.6:
            insights.append("趋势较为明显，具有一定的预测价值")
        elif strength > 0.4:
            insights.append("趋势不够明显，需要更多数据验证")
        else:
            insights.append("趋势不明显，可能存在随机波动")

        # 数值范围分析
        if values:
            min_val, max_val = min(values), max(values)
            range_val = max_val - min_val
            insights.append(f"数值范围: {min_val:.2f} - {max_val:.2f}，变化幅度: {range_val:.2f}")

        return insights

    def _calculate_statistical_summary(self, values: List[float]) -> Dict[str, float]:
        """计算统计摘要"""
        if not values:
            return {}

        return {
            'mean': statistics.mean(values),
            'median': statistics.median(values),
            'std_dev': statistics.stdev(values) if len(values) > 1 else 0.0,
            'min': min(values),
            'max': max(values),
            'range': max(values) - min(values),
            'count': len(values)
        }

    def _calculate_correlation(self, values1: List[float], values2: List[float]) -> float:
        """计算皮尔逊相关系数"""
        if len(values1) != len(values2) or len(values1) < 2:
            return 0.0

        n = len(values1)
        mean1 = sum(values1) / n
        mean2 = sum(values2) / n

        numerator = sum((values1[i] - mean1) * (values2[i] - mean2) for i in range(n))

        sum_sq1 = sum((values1[i] - mean1) ** 2 for i in range(n))
        sum_sq2 = sum((values2[i] - mean2) ** 2 for i in range(n))

        # 防止除零错误
        if sum_sq1 == 0 or sum_sq2 == 0:
            return 0.0

        denominator = (sum_sq1 * sum_sq2) ** 0.5

        if denominator == 0:
            return 0.0

        correlation = numerator / denominator
        return max(-1.0, min(1.0, correlation))

    def _calculate_p_value(self, correlation: float, n: int) -> float:
        """计算p值（简化版本）"""
        if n < 3:
            return 1.0

        # 防止除零错误
        if abs(correlation) >= 1.0:
            return 0.001  # 完全相关的情况

        # 使用t统计量的简化计算
        denominator = 1 - correlation ** 2
        if denominator <= 0:
            return 0.001

        t_stat = abs(correlation) * ((n - 2) / denominator) ** 0.5

        # 简化的p值估算
        if t_stat > 2.576:  # 99%置信水平
            return 0.01
        elif t_stat > 1.96:  # 95%置信水平
            return 0.05
        elif t_stat > 1.645:  # 90%置信水平
            return 0.10
        else:
            return 0.20

    def _determine_significance(self, p_value: float) -> str:
        """确定显著性水平"""
        if p_value <= 0.01:
            return "highly_significant"
        elif p_value <= 0.05:
            return "significant"
        elif p_value <= 0.10:
            return "marginally_significant"
        else:
            return "not_significant"

    def _generate_correlation_insights(self, correlation: float, significance: str) -> List[str]:
        """生成关联洞察"""
        insights = []

        # 相关性强度描述
        abs_corr = abs(correlation)
        if abs_corr > 0.8:
            strength = "非常强"
        elif abs_corr > 0.6:
            strength = "强"
        elif abs_corr > 0.4:
            strength = "中等"
        elif abs_corr > 0.2:
            strength = "弱"
        else:
            strength = "很弱"

        # 相关性方向
        direction = "正" if correlation > 0 else "负"

        insights.append(f"两个数据集之间存在{strength}的{direction}相关性 (r={correlation:.3f})")

        # 显著性描述
        if significance == "highly_significant":
            insights.append("相关性在统计学上高度显著，可信度很高")
        elif significance == "significant":
            insights.append("相关性在统计学上显著，具有一定可信度")
        elif significance == "marginally_significant":
            insights.append("相关性在统计学上边缘显著，需要谨慎解释")
        else:
            insights.append("相关性在统计学上不显著，可能是偶然现象")

        # 实际意义解释
        if abs_corr > 0.5:
            insights.append("这种相关性具有实际意义，可用于预测和决策")
        else:
            insights.append("相关性较弱，实际应用价值有限")

        return insights

    def _generate_comprehensive_summary(self, analysis_results: AnalysisResults) -> str:
        """生成综合摘要"""
        summary_parts = []

        # 趋势分析摘要
        if analysis_results.trend_analyses:
            trend_count = len(analysis_results.trend_analyses)
            summary_parts.append(f"完成了{trend_count}项趋势分析")

            # 主要趋势方向统计
            directions = [ta.direction for ta in analysis_results.trend_analyses]
            direction_counts = Counter(directions)
            most_common = direction_counts.most_common(1)[0]
            summary_parts.append(f"主要趋势方向为{most_common[0].value}")

        # 关联分析摘要
        if analysis_results.correlation_reports:
            corr_count = len(analysis_results.correlation_reports)
            summary_parts.append(f"完成了{corr_count}项关联分析")

            # 显著相关性统计
            significant_count = sum(1 for cr in analysis_results.correlation_reports
                                  if cr.significance_level in ["significant", "highly_significant"])
            if significant_count > 0:
                summary_parts.append(f"发现{significant_count}个显著相关性")

        # 其他分析摘要
        if analysis_results.sentiment_analysis:
            summary_parts.append("包含情感分析结果")

        if analysis_results.keyword_analysis:
            summary_parts.append("包含关键词分析结果")

        if analysis_results.temporal_patterns:
            summary_parts.append("包含时间模式分析结果")

        if not summary_parts:
            return "未发现明显的分析结果"

        return "分析结果显示：" + "，".join(summary_parts) + "。"

    def _generate_recommendations(self, analysis_results: AnalysisResults) -> List[str]:
        """生成建议"""
        recommendations = []

        # 基于趋势分析的建议
        if analysis_results.trend_analyses:
            for trend in analysis_results.trend_analyses:
                if trend.direction == TrendDirection.INCREASING and trend.strength > 0.7:
                    recommendations.append("上升趋势明显，建议继续关注并可能加大投入")
                elif trend.direction == TrendDirection.DECREASING and trend.strength > 0.7:
                    recommendations.append("下降趋势明显，建议及时采取干预措施")
                elif trend.direction == TrendDirection.VOLATILE:
                    recommendations.append("数据波动较大，建议增加监控频率并制定应急预案")

        # 基于关联分析的建议
        if analysis_results.correlation_reports:
            for corr in analysis_results.correlation_reports:
                if abs(corr.correlation_coefficient) > 0.7:
                    recommendations.append("发现强相关性，可考虑利用这种关系进行预测或优化")
                elif corr.significance_level == "not_significant":
                    recommendations.append("相关性不显著，建议收集更多数据或寻找其他影响因素")

        # 通用建议
        if not recommendations:
            recommendations.append("建议继续收集数据以获得更准确的分析结果")
            recommendations.append("定期重新评估分析结果以跟踪变化趋势")

        return recommendations

    def _calculate_overall_confidence(self, analysis_results: AnalysisResults) -> float:
        """计算综合置信度"""
        confidence_scores = []

        # 趋势分析置信度
        if analysis_results.trend_analyses:
            trend_confidences = [ta.confidence for ta in analysis_results.trend_analyses]
            confidence_scores.extend(trend_confidences)

        # 关联分析置信度（基于显著性）
        if analysis_results.correlation_reports:
            for cr in analysis_results.correlation_reports:
                if cr.significance_level == "highly_significant":
                    confidence_scores.append(0.95)
                elif cr.significance_level == "significant":
                    confidence_scores.append(0.85)
                elif cr.significance_level == "marginally_significant":
                    confidence_scores.append(0.70)
                else:
                    confidence_scores.append(0.50)

        if not confidence_scores:
            return 0.5

        return statistics.mean(confidence_scores)

    def _get_analysis_types(self, analysis_results: AnalysisResults) -> List[str]:
        """获取分析类型列表"""
        types = []

        if analysis_results.trend_analyses:
            types.append("trend")
        if analysis_results.correlation_reports:
            types.append("correlation")
        if analysis_results.sentiment_analysis:
            types.append("sentiment")
        if analysis_results.keyword_analysis:
            types.append("keyword")
        if analysis_results.temporal_patterns:
            types.append("temporal")

        return types

    def _assess_data_quality(self, analysis_results: AnalysisResults) -> float:
        """评估数据质量"""
        quality_scores = []

        # 基于趋势分析的数据质量
        if analysis_results.trend_analyses:
            for trend in analysis_results.trend_analyses:
                data_count = len(trend.data_points)
                # 数据点数量质量评分
                count_score = min(1.0, data_count / 20)  # 20个点认为是高质量

                # 时间跨度质量评分
                time_span = (trend.time_range[1] - trend.time_range[0]).days
                span_score = min(1.0, time_span / 30)  # 30天认为是合理跨度

                quality_scores.append((count_score + span_score) / 2)

        # 基于关联分析的数据质量
        if analysis_results.correlation_reports:
            for cr in analysis_results.correlation_reports:
                data_count = len(cr.scatter_data)
                count_score = min(1.0, data_count / 15)  # 15个点认为是基本要求
                quality_scores.append(count_score)

        if not quality_scores:
            return 0.5

        return statistics.mean(quality_scores)
