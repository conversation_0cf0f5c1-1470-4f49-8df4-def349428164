"""
配置文件处理器测试模块

测试ConfigProcessor类的所有功能，包括配置文件读取、解析、验证、转换和管理。
"""

import pytest
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import AsyncMock, patch, MagicMock
import asyncio

# 导入被测试的类
from operations.config_processor import ConfigProcessor


class TestConfigProcessor:
    """ConfigProcessor测试类"""
    
    @pytest.fixture
    def temp_workspace(self):
        """创建临时工作空间"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def config_processor(self, temp_workspace):
        """创建ConfigProcessor实例"""
        processor = ConfigProcessor(temp_workspace)
        # Mock安全管理器以允许测试文件访问
        processor.security_manager.validate_operation = MagicMock(return_value={'allowed': True, 'reason': 'test'})
        return processor

    @pytest.fixture
    def sample_configs(self, temp_workspace):
        """创建示例配置文件"""
        configs = {}
        
        # JSON配置
        json_config = {
            "app": {
                "name": "TestApp",
                "version": "1.0.0",
                "debug": True
            },
            "database": {
                "host": "${DB_HOST}",
                "port": 5432,
                "name": "testdb"
            },
            "features": ["auth", "logging", "cache"]
        }
        
        json_path = Path(temp_workspace) / "config.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_config, f, indent=2)
        configs['json'] = str(json_path)
        
        # YAML配置（如果支持）
        yaml_content = """
app:
  name: TestApp
  version: 1.0.0
  debug: true
database:
  host: ${DB_HOST}
  port: 5432
  name: testdb
features:
  - auth
  - logging
  - cache
"""
        yaml_path = Path(temp_workspace) / "config.yaml"
        with open(yaml_path, 'w', encoding='utf-8') as f:
            f.write(yaml_content.strip())
        configs['yaml'] = str(yaml_path)
        
        # INI配置
        ini_content = """
[app]
name = TestApp
version = 1.0.0
debug = true

[database]
host = ${DB_HOST}
port = 5432
name = testdb
"""
        ini_path = Path(temp_workspace) / "config.ini"
        with open(ini_path, 'w', encoding='utf-8') as f:
            f.write(ini_content.strip())
        configs['ini'] = str(ini_path)
        
        # 无效JSON配置
        invalid_json_path = Path(temp_workspace) / "invalid.json"
        with open(invalid_json_path, 'w', encoding='utf-8') as f:
            f.write('{"invalid": json content}')
        configs['invalid_json'] = str(invalid_json_path)
        
        return configs
    
    @pytest.fixture
    def validation_schema(self):
        """创建验证模式"""
        return {
            "required": ["app.name", "app.version", "database.host"],
            "types": {
                "app.name": str,
                "app.version": str,
                "app.debug": bool,
                "database.port": int
            },
            "ranges": {
                "database.port": {"min": 1, "max": 65535}
            },
            "enums": {
                "app.name": ["TestApp", "ProdApp", "DevApp"]
            },
            "custom_rules": [
                {
                    "type": "dependency",
                    "field_a": "database.host",
                    "field_b": "database.port",
                    "severity": "error"
                }
            ]
        }
    
    @pytest.mark.asyncio
    async def test_basic_config_processing(self, config_processor, sample_configs):
        """测试基础配置处理"""
        # 测试JSON配置处理
        result = await config_processor.process_config(sample_configs['json'])

        assert result['status'] == 'success'
        assert result['format'] == 'json'
        assert 'config' in result
        assert result['config']['app']['name'] == 'TestApp'
        assert 'metadata' in result
        assert result['metadata']['file_size'] > 0
    
    @pytest.mark.asyncio
    async def test_yaml_config_processing(self, config_processor, sample_configs):
        """测试YAML配置处理"""
        result = await config_processor.process_config(sample_configs['yaml'])

        if result['status'] == 'error' and 'PyYAML' in result.get('error', ''):
            # YAML库不可用时跳过测试
            pytest.skip("PyYAML not available")
        else:
            assert result['status'] == 'success'
            assert result['format'] == 'yaml'
            assert result['config']['app']['name'] == 'TestApp'
            assert isinstance(result['config']['features'], list)
    
    @pytest.mark.asyncio
    async def test_ini_config_processing(self, config_processor, sample_configs):
        """测试INI配置处理"""
        result = await config_processor.process_config(sample_configs['ini'])
        
        assert result['status'] == 'success'
        assert result['format'] == 'ini'
        assert result['config']['app']['name'] == 'TestApp'
        assert result['config']['database']['port'] == '5432'  # INI值都是字符串
    
    @pytest.mark.asyncio
    async def test_environment_variable_replacement(self, config_processor, sample_configs):
        """测试环境变量替换"""
        import os
        
        # 设置环境变量
        os.environ['DB_HOST'] = 'localhost'
        
        try:
            result = await config_processor.process_config(
                sample_configs['json'], 
                replace_env_vars=True
            )
            
            assert result['status'] == 'success'
            assert result['config']['database']['host'] == 'localhost'
        finally:
            # 清理环境变量
            if 'DB_HOST' in os.environ:
                del os.environ['DB_HOST']
    
    @pytest.mark.asyncio
    async def test_config_validation(self, config_processor, sample_configs, validation_schema):
        """测试配置验证"""
        result = await config_processor.process_config(
            sample_configs['json'],
            validate_schema=validation_schema
        )
        
        assert result['status'] == 'success'
        assert 'validation' in result
        validation = result['validation']
        assert 'is_valid' in validation
        assert 'errors' in validation
        assert 'warnings' in validation
    
    @pytest.mark.asyncio
    async def test_config_format_conversion(self, config_processor, sample_configs, temp_workspace):
        """测试配置格式转换"""
        target_path = Path(temp_workspace) / "converted.yaml"
        
        result = await config_processor.convert_config_format(
            sample_configs['json'],
            str(target_path),
            'yaml'
        )
        
        if result['status'] == 'success':
            assert result['source_format'] == 'json'
            assert result['target_format'] == 'yaml'
            assert target_path.exists()
        else:
            # YAML库不可用时应该返回错误
            assert 'PyYAML' in result['error']
    
    @pytest.mark.asyncio
    async def test_config_merge(self, config_processor, sample_configs, temp_workspace):
        """测试配置合并"""
        # 创建第二个配置文件
        config2 = {
            "app": {
                "name": "TestApp",
                "version": "2.0.0",
                "new_feature": True
            },
            "cache": {
                "enabled": True,
                "ttl": 3600
            }
        }
        
        config2_path = Path(temp_workspace) / "config2.json"
        with open(config2_path, 'w', encoding='utf-8') as f:
            json.dump(config2, f, indent=2)
        
        # 测试深度合并
        result = await config_processor.merge_configs(
            [sample_configs['json'], str(config2_path)],
            merge_strategy='deep'
        )
        
        assert result['status'] == 'success'
        merged = result['merged_config']
        assert merged['app']['name'] == 'TestApp'
        assert merged['app']['version'] == '2.0.0'  # 被覆盖
        assert merged['app']['debug'] == True  # 保留
        assert merged['app']['new_feature'] == True  # 新增
        assert 'cache' in merged  # 新增部分
    
    @pytest.mark.asyncio
    async def test_config_backup(self, config_processor, sample_configs, temp_workspace):
        """测试配置备份"""
        result = await config_processor.backup_config(sample_configs['json'])
        
        assert result['status'] == 'success'
        assert 'backup_path' in result
        assert Path(result['backup_path']).exists()
        assert result['backup_size'] > 0
    
    @pytest.mark.asyncio
    async def test_config_validation_standalone(self, config_processor, sample_configs, validation_schema):
        """测试独立配置验证"""
        result = await config_processor.validate_config_file(
            sample_configs['json'],
            validation_schema
        )
        
        assert result['status'] == 'success'
        assert 'is_valid' in result
        assert 'errors' in result
        assert 'warnings' in result
    
    @pytest.mark.asyncio
    async def test_error_handling(self, config_processor, sample_configs):
        """测试错误处理"""
        # 测试无效JSON
        result = await config_processor.process_config(sample_configs['invalid_json'])
        assert result['status'] == 'error'
        assert 'error' in result
        
        # 测试不存在的文件
        result = await config_processor.process_config("nonexistent.json")
        assert result['status'] == 'error'
    
    @pytest.mark.asyncio
    async def test_statistics_tracking(self, config_processor, sample_configs):
        """测试统计信息跟踪"""
        # 重置统计
        config_processor.reset_stats()
        initial_stats = config_processor.get_processing_stats()
        assert initial_stats['total_processed'] == 0
        
        # 处理一些配置
        await config_processor.process_config(sample_configs['json'])
        await config_processor.process_config(sample_configs['ini'])
        
        # 检查统计更新
        stats = config_processor.get_processing_stats()
        assert stats['total_processed'] == 2
        assert stats['successful_processed'] == 2
        assert 'json' in stats['formats_processed']
        assert 'ini' in stats['formats_processed']
    
    @pytest.mark.asyncio
    async def test_format_detection(self, config_processor, temp_workspace):
        """测试格式检测"""
        # 测试各种扩展名
        test_files = {
            'config.json': 'json',
            'config.yaml': 'yaml',
            'config.yml': 'yaml',
            'config.ini': 'ini',
            'config.cfg': 'ini',
            'config.conf': 'ini',
            'config.toml': 'toml'
        }
        
        for filename, expected_format in test_files.items():
            file_path = Path(temp_workspace) / filename
            # 创建空文件用于格式检测
            file_path.touch()
            
            detected_format = config_processor._detect_config_format(file_path)
            assert detected_format == expected_format
    
    def test_nested_key_operations(self, config_processor):
        """测试嵌套键操作"""
        test_config = {
            "level1": {
                "level2": {
                    "level3": "value"
                }
            }
        }
        
        # 测试嵌套键检查
        assert config_processor._check_nested_key(test_config, "level1.level2.level3")
        assert not config_processor._check_nested_key(test_config, "level1.level2.nonexistent")
        
        # 测试嵌套值获取
        value = config_processor._get_nested_value(test_config, "level1.level2.level3")
        assert value == "value"
        
        none_value = config_processor._get_nested_value(test_config, "level1.nonexistent")
        assert none_value is None
