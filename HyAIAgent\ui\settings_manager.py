"""
个性化设置系统模块

提供用户偏好学习和记录、个性化推荐系统、自定义快捷键配置、工作环境个性化等功能。
支持多设备间的配置同步和智能推荐算法。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import sys
import json
import asyncio
import hashlib
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from pathlib import Path
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import pickle

# PyQt6 imports
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QLabel, QLineEdit, QTextEdit, QPushButton, QComboBox, QSpinBox,
    QSlider, QProgressBar, QTabWidget, QGroupBox, QCheckBox, QRadioButton,
    QButtonGroup, QListWidget, QTreeWidget, QTableWidget, QScrollArea,
    QSizePolicy, QDialog, QMessageBox, QColorDialog, QFontDialog,
    QKeySequenceEdit, QFileDialog, QDialogButtonBox
)
from PyQt6.QtCore import (
    Qt, QTimer, QThread, pyqtSignal, QSettings, QStandardPaths,
    QObject, QEvent, QSize, QPoint
)
from PyQt6.QtGui import (
    QFont, QColor, QPalette, QIcon, QKeySequence, QAction, QShortcut
)

# 导入项目模块
from core.config_manager import ConfigManager
from core.kv_store import KVStore

# 设置日志
logger = logging.getLogger(__name__)


@dataclass
class UserPreference:
    """用户偏好数据模型"""
    key: str
    value: Any
    category: str
    priority: float = 1.0
    created_at: datetime = None
    updated_at: datetime = None
    usage_count: int = 0
    last_used: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if self.last_used is None:
            self.last_used = datetime.now()


@dataclass
class RecommendationItem:
    """推荐项数据模型"""
    item_id: str
    item_type: str
    title: str
    description: str
    score: float
    category: str
    metadata: Dict[str, Any] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.metadata is None:
            self.metadata = {}


@dataclass
class WorkspaceProfile:
    """工作环境配置文件"""
    profile_id: str
    name: str
    description: str
    ui_theme: str
    layout_config: Dict[str, Any]
    shortcuts: Dict[str, str]
    preferences: Dict[str, Any]
    created_at: datetime = None
    updated_at: datetime = None
    is_active: bool = False
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


class SettingsManager(QObject):
    """个性化设置管理器
    
    提供用户偏好学习、个性化推荐、快捷键配置、工作环境管理等功能
    """
    
    # 信号定义
    preferencesChanged = pyqtSignal(str, dict)  # 偏好变更信号
    recommendationUpdated = pyqtSignal(list)    # 推荐更新信号
    profileChanged = pyqtSignal(str)            # 配置文件变更信号
    syncCompleted = pyqtSignal(bool)            # 同步完成信号
    
    def __init__(self, config_manager: ConfigManager, 
                 kv_store: Optional[KVStore] = None):
        """初始化设置管理器
        
        Args:
            config_manager: 配置管理器实例
            kv_store: 键值存储实例
        """
        super().__init__()
        self.config_manager = config_manager
        self.kv_store = kv_store
        
        # 设置存储路径
        self.settings_dir = Path(QStandardPaths.writableLocation(
            QStandardPaths.StandardLocation.AppDataLocation)) / "HyAIAgent" / "settings"
        self.settings_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化QSettings
        self.qt_settings = QSettings("HyAIAgent", "PersonalSettings")
        
        # 数据存储
        self.preferences: Dict[str, UserPreference] = {}
        self.recommendations: List[RecommendationItem] = []
        self.workspace_profiles: Dict[str, WorkspaceProfile] = {}
        self.current_profile_id: Optional[str] = None
        self.usage_history: Dict[str, List[datetime]] = defaultdict(list)
        self.behavior_patterns: Dict[str, Any] = {}
        
        # 推荐算法配置
        self.recommendation_config = {
            'max_recommendations': 10,
            'min_score_threshold': 0.3,
            'decay_factor': 0.95,  # 时间衰减因子
            'category_weights': {
                'ui': 1.0,
                'shortcuts': 0.8,
                'workflow': 1.2,
                'tools': 0.9
            }
        }
        
        # 统计信息
        self.stats = {
            'preferences_count': 0,
            'recommendations_generated': 0,
            'profiles_created': 0,
            'sync_operations': 0,
            'learning_iterations': 0
        }
        
        # 初始化
        self.load_settings()
        self.setup_auto_save()
        
        logger.info("SettingsManager initialized successfully")

    def load_settings(self):
        """加载设置数据"""
        try:
            # 加载用户偏好
            preferences_file = self.settings_dir / "preferences.json"
            if preferences_file.exists():
                with open(preferences_file, 'r', encoding='utf-8') as f:
                    prefs_data = json.load(f)
                    for key, data in prefs_data.items():
                        # 转换datetime字符串
                        if 'created_at' in data:
                            data['created_at'] = datetime.fromisoformat(data['created_at'])
                        if 'updated_at' in data:
                            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
                        if 'last_used' in data:
                            data['last_used'] = datetime.fromisoformat(data['last_used'])
                        self.preferences[key] = UserPreference(**data)

            # 加载工作环境配置
            profiles_file = self.settings_dir / "profiles.json"
            if profiles_file.exists():
                with open(profiles_file, 'r', encoding='utf-8') as f:
                    profiles_data = json.load(f)
                    for profile_id, data in profiles_data.items():
                        if 'created_at' in data:
                            data['created_at'] = datetime.fromisoformat(data['created_at'])
                        if 'updated_at' in data:
                            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
                        self.workspace_profiles[profile_id] = WorkspaceProfile(**data)

            # 加载使用历史
            history_file = self.settings_dir / "usage_history.pkl"
            if history_file.exists():
                with open(history_file, 'rb') as f:
                    self.usage_history = pickle.load(f)

            # 加载行为模式
            patterns_file = self.settings_dir / "behavior_patterns.json"
            if patterns_file.exists():
                with open(patterns_file, 'r', encoding='utf-8') as f:
                    self.behavior_patterns = json.load(f)

            # 获取当前活跃配置文件
            self.current_profile_id = self.qt_settings.value("current_profile", None)

            # 更新统计信息
            self.stats['preferences_count'] = len(self.preferences)
            self.stats['profiles_created'] = len(self.workspace_profiles)

            logger.info(f"Settings loaded: {len(self.preferences)} preferences, {len(self.workspace_profiles)} profiles")

        except Exception as e:
            logger.error(f"Error loading settings: {e}")

    def save_settings(self):
        """保存设置数据"""
        try:
            # 保存用户偏好
            preferences_data = {}
            for key, pref in self.preferences.items():
                data = asdict(pref)
                # 转换datetime为字符串
                data['created_at'] = data['created_at'].isoformat()
                data['updated_at'] = data['updated_at'].isoformat()
                data['last_used'] = data['last_used'].isoformat()
                preferences_data[key] = data

            preferences_file = self.settings_dir / "preferences.json"
            with open(preferences_file, 'w', encoding='utf-8') as f:
                json.dump(preferences_data, f, indent=2, ensure_ascii=False)

            # 保存工作环境配置
            profiles_data = {}
            for profile_id, profile in self.workspace_profiles.items():
                data = asdict(profile)
                data['created_at'] = data['created_at'].isoformat()
                data['updated_at'] = data['updated_at'].isoformat()
                profiles_data[profile_id] = data

            profiles_file = self.settings_dir / "profiles.json"
            with open(profiles_file, 'w', encoding='utf-8') as f:
                json.dump(profiles_data, f, indent=2, ensure_ascii=False)

            # 保存使用历史
            history_file = self.settings_dir / "usage_history.pkl"
            with open(history_file, 'wb') as f:
                pickle.dump(dict(self.usage_history), f)

            # 保存行为模式
            patterns_file = self.settings_dir / "behavior_patterns.json"
            with open(patterns_file, 'w', encoding='utf-8') as f:
                json.dump(self.behavior_patterns, f, indent=2, ensure_ascii=False)

            # 保存当前配置文件
            if self.current_profile_id:
                self.qt_settings.setValue("current_profile", self.current_profile_id)

            logger.info("Settings saved successfully")

        except Exception as e:
            logger.error(f"Error saving settings: {e}")

    def setup_auto_save(self):
        """设置自动保存"""
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.save_settings)
        self.auto_save_timer.start(300000)  # 5分钟自动保存

    async def learn_user_preference(self, key: str, value: Any, category: str = "general",
                                  priority: float = 1.0) -> bool:
        """学习用户偏好

        Args:
            key: 偏好键
            value: 偏好值
            category: 偏好类别
            priority: 优先级

        Returns:
            bool: 学习是否成功
        """
        try:
            now = datetime.now()

            if key in self.preferences:
                # 更新现有偏好
                pref = self.preferences[key]
                pref.value = value
                pref.updated_at = now
                pref.last_used = now
                pref.usage_count += 1
                pref.priority = max(pref.priority, priority)
            else:
                # 创建新偏好
                pref = UserPreference(
                    key=key,
                    value=value,
                    category=category,
                    priority=priority,
                    created_at=now,
                    updated_at=now,
                    last_used=now,
                    usage_count=1
                )
                self.preferences[key] = pref
                self.stats['preferences_count'] += 1

            # 记录使用历史
            self.usage_history[key].append(now)

            # 更新行为模式
            await self._update_behavior_patterns(key, value, category)

            # 发送信号
            self.preferencesChanged.emit(category, {key: value})

            logger.debug(f"Learned preference: {key} = {value} (category: {category})")
            return True

        except Exception as e:
            logger.error(f"Error learning preference: {e}")
            return False

    async def generate_recommendations(self, context: Optional[Dict[str, Any]] = None) -> List[RecommendationItem]:
        """生成个性化推荐

        Args:
            context: 上下文信息

        Returns:
            List[RecommendationItem]: 推荐项列表
        """
        try:
            recommendations = []
            now = datetime.now()

            # 基于使用频率的推荐
            usage_scores = self._calculate_usage_scores()

            # 基于时间模式的推荐
            time_scores = self._calculate_time_pattern_scores()

            # 基于类别偏好的推荐
            category_scores = self._calculate_category_scores()

            # 生成推荐项
            for pref_key, pref in self.preferences.items():
                if pref.usage_count < 2:  # 跳过使用次数太少的偏好
                    continue

                # 计算综合评分
                usage_score = usage_scores.get(pref_key, 0)
                time_score = time_scores.get(pref_key, 0)
                category_score = category_scores.get(pref.category, 0)

                final_score = (
                    usage_score * 0.4 +
                    time_score * 0.3 +
                    category_score * 0.2 +
                    pref.priority * 0.1
                )

                if final_score >= self.recommendation_config['min_score_threshold']:
                    rec = RecommendationItem(
                        item_id=f"pref_{pref_key}",
                        item_type="preference",
                        title=f"使用 {pref_key}",
                        description=f"基于您的使用习惯，推荐使用此设置",
                        score=final_score,
                        category=pref.category,
                        metadata={
                            'preference_key': pref_key,
                            'preference_value': pref.value,
                            'usage_count': pref.usage_count,
                            'last_used': pref.last_used.isoformat()
                        }
                    )
                    recommendations.append(rec)

            # 排序并限制数量
            recommendations.sort(key=lambda x: x.score, reverse=True)
            recommendations = recommendations[:self.recommendation_config['max_recommendations']]

            self.recommendations = recommendations
            self.stats['recommendations_generated'] += len(recommendations)

            # 发送信号
            self.recommendationUpdated.emit(recommendations)

            logger.info(f"Generated {len(recommendations)} recommendations")
            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return []

    def create_workspace_profile(self, name: str, description: str = "",
                               ui_theme: str = "default") -> Optional[str]:
        """创建工作环境配置文件

        Args:
            name: 配置文件名称
            description: 描述
            ui_theme: UI主题

        Returns:
            Optional[str]: 配置文件ID，失败返回None
        """
        try:
            profile_id = hashlib.md5(f"{name}_{datetime.now().isoformat()}".encode()).hexdigest()[:8]

            profile = WorkspaceProfile(
                profile_id=profile_id,
                name=name,
                description=description,
                ui_theme=ui_theme,
                layout_config={},
                shortcuts={},
                preferences={}
            )

            self.workspace_profiles[profile_id] = profile
            self.stats['profiles_created'] += 1

            logger.info(f"Created workspace profile: {name} (ID: {profile_id})")
            return profile_id

        except Exception as e:
            logger.error(f"Error creating workspace profile: {e}")
            return None

    def activate_workspace_profile(self, profile_id: str) -> bool:
        """激活工作环境配置文件

        Args:
            profile_id: 配置文件ID

        Returns:
            bool: 激活是否成功
        """
        try:
            if profile_id not in self.workspace_profiles:
                logger.error(f"Profile not found: {profile_id}")
                return False

            # 取消当前活跃配置文件
            if self.current_profile_id:
                if self.current_profile_id in self.workspace_profiles:
                    self.workspace_profiles[self.current_profile_id].is_active = False

            # 激活新配置文件
            profile = self.workspace_profiles[profile_id]
            profile.is_active = True
            profile.updated_at = datetime.now()
            self.current_profile_id = profile_id

            # 应用配置文件设置
            self._apply_profile_settings(profile)

            # 发送信号
            self.profileChanged.emit(profile_id)

            logger.info(f"Activated workspace profile: {profile.name} (ID: {profile_id})")
            return True

        except Exception as e:
            logger.error(f"Error activating workspace profile: {e}")
            return False

    def update_profile_shortcuts(self, profile_id: str, shortcuts: Dict[str, str]) -> bool:
        """更新配置文件的快捷键设置

        Args:
            profile_id: 配置文件ID
            shortcuts: 快捷键映射

        Returns:
            bool: 更新是否成功
        """
        try:
            if profile_id not in self.workspace_profiles:
                return False

            profile = self.workspace_profiles[profile_id]
            profile.shortcuts.update(shortcuts)
            profile.updated_at = datetime.now()

            # 如果是当前活跃配置文件，立即应用
            if profile_id == self.current_profile_id:
                self._apply_shortcuts(shortcuts)

            logger.info(f"Updated shortcuts for profile: {profile.name}")
            return True

        except Exception as e:
            logger.error(f"Error updating profile shortcuts: {e}")
            return False

    async def sync_settings(self, sync_target: str = "cloud") -> bool:
        """同步设置到指定目标

        Args:
            sync_target: 同步目标 (cloud, local_backup, device)

        Returns:
            bool: 同步是否成功
        """
        try:
            if sync_target == "local_backup":
                return await self._sync_to_local_backup()
            elif sync_target == "cloud":
                return await self._sync_to_cloud()
            elif sync_target == "device":
                return await self._sync_to_device()
            else:
                logger.error(f"Unknown sync target: {sync_target}")
                return False

        except Exception as e:
            logger.error(f"Error syncing settings: {e}")
            return False

    def get_preference(self, key: str, default: Any = None) -> Any:
        """获取用户偏好值

        Args:
            key: 偏好键
            default: 默认值

        Returns:
            Any: 偏好值
        """
        if key in self.preferences:
            pref = self.preferences[key]
            pref.last_used = datetime.now()
            pref.usage_count += 1
            return pref.value
        return default

    def get_recommendations(self, category: Optional[str] = None,
                          limit: Optional[int] = None) -> List[RecommendationItem]:
        """获取推荐项

        Args:
            category: 类别过滤
            limit: 数量限制

        Returns:
            List[RecommendationItem]: 推荐项列表
        """
        recommendations = self.recommendations

        if category:
            recommendations = [r for r in recommendations if r.category == category]

        if limit:
            recommendations = recommendations[:limit]

        return recommendations

    def get_workspace_profiles(self) -> Dict[str, WorkspaceProfile]:
        """获取所有工作环境配置文件"""
        return self.workspace_profiles.copy()

    def get_current_profile(self) -> Optional[WorkspaceProfile]:
        """获取当前活跃的配置文件"""
        if self.current_profile_id and self.current_profile_id in self.workspace_profiles:
            return self.workspace_profiles[self.current_profile_id]
        return None

    def get_settings_stats(self) -> Dict[str, Any]:
        """获取设置统计信息"""
        return {
            **self.stats,
            'total_usage_records': sum(len(history) for history in self.usage_history.values()),
            'active_profile': self.current_profile_id,
            'behavior_patterns_count': len(self.behavior_patterns)
        }

    # 私有辅助方法
    async def _update_behavior_patterns(self, key: str, value: Any, category: str):
        """更新行为模式"""
        try:
            if category not in self.behavior_patterns:
                self.behavior_patterns[category] = {
                    'frequent_keys': Counter(),
                    'value_patterns': defaultdict(Counter),
                    'time_patterns': defaultdict(list)
                }

            patterns = self.behavior_patterns[category]
            patterns['frequent_keys'][key] += 1
            patterns['value_patterns'][key][str(value)] += 1

            # 记录时间模式
            now = datetime.now()
            hour = now.hour
            patterns['time_patterns'][key].append(hour)

            # 保持时间模式数据在合理范围内
            if len(patterns['time_patterns'][key]) > 100:
                patterns['time_patterns'][key] = patterns['time_patterns'][key][-50:]

        except Exception as e:
            logger.error(f"Error updating behavior patterns: {e}")

    def _calculate_usage_scores(self) -> Dict[str, float]:
        """计算使用频率评分"""
        scores = {}
        max_usage = max((pref.usage_count for pref in self.preferences.values()), default=1)

        for key, pref in self.preferences.items():
            # 基础使用频率评分
            usage_score = pref.usage_count / max_usage

            # 时间衰减
            days_since_last_use = (datetime.now() - pref.last_used).days
            decay_factor = self.recommendation_config['decay_factor'] ** days_since_last_use

            scores[key] = usage_score * decay_factor

        return scores

    def _calculate_time_pattern_scores(self) -> Dict[str, float]:
        """计算时间模式评分"""
        scores = {}
        current_hour = datetime.now().hour

        for key, pref in self.preferences.items():
            if key in self.behavior_patterns.get(pref.category, {}).get('time_patterns', {}):
                time_history = self.behavior_patterns[pref.category]['time_patterns'][key]
                if time_history:
                    # 计算当前时间与历史使用时间的匹配度
                    hour_counts = Counter(time_history)
                    total_uses = sum(hour_counts.values())
                    current_hour_usage = hour_counts.get(current_hour, 0)
                    scores[key] = current_hour_usage / total_uses if total_uses > 0 else 0
                else:
                    scores[key] = 0
            else:
                scores[key] = 0

        return scores

    def _calculate_category_scores(self) -> Dict[str, float]:
        """计算类别评分"""
        category_usage = Counter()
        for pref in self.preferences.values():
            category_usage[pref.category] += pref.usage_count

        max_usage = max(category_usage.values()) if category_usage else 1

        scores = {}
        for category, usage in category_usage.items():
            base_score = usage / max_usage
            weight = self.recommendation_config['category_weights'].get(category, 1.0)
            scores[category] = base_score * weight

        return scores

    def _apply_profile_settings(self, profile: WorkspaceProfile):
        """应用配置文件设置"""
        try:
            # 应用UI主题
            if hasattr(self, 'parent') and self.parent():
                # 这里可以调用主窗口的主题切换方法
                pass

            # 应用快捷键
            self._apply_shortcuts(profile.shortcuts)

            # 应用布局配置
            self._apply_layout_config(profile.layout_config)

            # 应用偏好设置
            for key, value in profile.preferences.items():
                if key in self.preferences:
                    self.preferences[key].value = value
                    self.preferences[key].updated_at = datetime.now()

            logger.info(f"Applied settings for profile: {profile.name}")

        except Exception as e:
            logger.error(f"Error applying profile settings: {e}")

    def _apply_shortcuts(self, shortcuts: Dict[str, str]):
        """应用快捷键设置"""
        try:
            # 这里可以设置全局快捷键
            # 具体实现取决于主应用程序的架构
            logger.info(f"Applied {len(shortcuts)} shortcuts")

        except Exception as e:
            logger.error(f"Error applying shortcuts: {e}")

    def _apply_layout_config(self, layout_config: Dict[str, Any]):
        """应用布局配置"""
        try:
            # 这里可以调整窗口布局
            # 具体实现取决于主应用程序的架构
            logger.info("Applied layout configuration")

        except Exception as e:
            logger.error(f"Error applying layout config: {e}")

    async def _sync_to_local_backup(self) -> bool:
        """同步到本地备份"""
        try:
            backup_dir = self.settings_dir / "backups"
            backup_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_dir / f"settings_backup_{timestamp}.json"

            backup_data = {
                'preferences': {k: asdict(v) for k, v in self.preferences.items()},
                'profiles': {k: asdict(v) for k, v in self.workspace_profiles.items()},
                'behavior_patterns': self.behavior_patterns,
                'current_profile': self.current_profile_id,
                'backup_time': datetime.now().isoformat()
            }

            # 转换datetime对象为字符串
            for pref_data in backup_data['preferences'].values():
                for key in ['created_at', 'updated_at', 'last_used']:
                    if key in pref_data and isinstance(pref_data[key], datetime):
                        pref_data[key] = pref_data[key].isoformat()

            for profile_data in backup_data['profiles'].values():
                for key in ['created_at', 'updated_at']:
                    if key in profile_data and isinstance(profile_data[key], datetime):
                        profile_data[key] = profile_data[key].isoformat()

            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)

            self.stats['sync_operations'] += 1
            self.syncCompleted.emit(True)

            logger.info(f"Settings backed up to: {backup_file}")
            return True

        except Exception as e:
            logger.error(f"Error creating local backup: {e}")
            self.syncCompleted.emit(False)
            return False

    async def _sync_to_cloud(self) -> bool:
        """同步到云端"""
        try:
            # 这里可以实现云端同步逻辑
            # 例如上传到云存储服务
            logger.info("Cloud sync not implemented yet")
            return False

        except Exception as e:
            logger.error(f"Error syncing to cloud: {e}")
            return False

    async def _sync_to_device(self) -> bool:
        """同步到其他设备"""
        try:
            # 这里可以实现设备间同步逻辑
            # 例如通过网络或USB传输
            logger.info("Device sync not implemented yet")
            return False

        except Exception as e:
            logger.error(f"Error syncing to device: {e}")
            return False


class SettingsDialog(QDialog):
    """设置对话框

    提供图形化的设置管理界面
    """

    def __init__(self, settings_manager: SettingsManager, parent=None):
        """初始化设置对话框

        Args:
            settings_manager: 设置管理器实例
            parent: 父窗口
        """
        super().__init__(parent)
        self.settings_manager = settings_manager
        self.setWindowTitle("个性化设置")
        self.setModal(True)
        self.resize(800, 600)

        self.setup_ui()
        self.load_current_settings()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 偏好设置标签页
        self.setup_preferences_tab()

        # 工作环境标签页
        self.setup_workspace_tab()

        # 快捷键标签页
        self.setup_shortcuts_tab()

        # 推荐设置标签页
        self.setup_recommendations_tab()

        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok |
            QDialogButtonBox.StandardButton.Cancel |
            QDialogButtonBox.StandardButton.Apply
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.StandardButton.Apply).clicked.connect(self.apply_settings)
        layout.addWidget(button_box)

    def setup_preferences_tab(self):
        """设置偏好设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 偏好列表
        self.preferences_list = QListWidget()
        layout.addWidget(QLabel("用户偏好设置:"))
        layout.addWidget(self.preferences_list)

        self.tab_widget.addTab(widget, "偏好设置")

    def setup_workspace_tab(self):
        """设置工作环境标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 配置文件列表
        self.profiles_list = QListWidget()
        layout.addWidget(QLabel("工作环境配置:"))
        layout.addWidget(self.profiles_list)

        # 配置文件操作按钮
        button_layout = QHBoxLayout()
        self.create_profile_btn = QPushButton("创建配置")
        self.activate_profile_btn = QPushButton("激活配置")
        self.delete_profile_btn = QPushButton("删除配置")

        button_layout.addWidget(self.create_profile_btn)
        button_layout.addWidget(self.activate_profile_btn)
        button_layout.addWidget(self.delete_profile_btn)
        layout.addLayout(button_layout)

        self.tab_widget.addTab(widget, "工作环境")

    def setup_shortcuts_tab(self):
        """设置快捷键标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 快捷键表格
        self.shortcuts_table = QTableWidget()
        self.shortcuts_table.setColumnCount(2)
        self.shortcuts_table.setHorizontalHeaderLabels(["功能", "快捷键"])
        layout.addWidget(QLabel("快捷键设置:"))
        layout.addWidget(self.shortcuts_table)

        self.tab_widget.addTab(widget, "快捷键")

    def setup_recommendations_tab(self):
        """设置推荐设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 推荐列表
        self.recommendations_list = QListWidget()
        layout.addWidget(QLabel("个性化推荐:"))
        layout.addWidget(self.recommendations_list)

        # 刷新推荐按钮
        self.refresh_recommendations_btn = QPushButton("刷新推荐")
        layout.addWidget(self.refresh_recommendations_btn)

        self.tab_widget.addTab(widget, "推荐设置")

    def load_current_settings(self):
        """加载当前设置"""
        # 加载偏好设置
        for key, pref in self.settings_manager.preferences.items():
            item_text = f"{key}: {pref.value} (类别: {pref.category})"
            self.preferences_list.addItem(item_text)

        # 加载工作环境配置
        for profile_id, profile in self.settings_manager.workspace_profiles.items():
            item_text = f"{profile.name} ({'活跃' if profile.is_active else '非活跃'})"
            self.profiles_list.addItem(item_text)

        # 加载推荐
        recommendations = self.settings_manager.get_recommendations()
        for rec in recommendations:
            item_text = f"{rec.title} (评分: {rec.score:.2f})"
            self.recommendations_list.addItem(item_text)

    def apply_settings(self):
        """应用设置"""
        # 这里可以实现设置应用逻辑
        self.settings_manager.save_settings()
        logger.info("Settings applied")
