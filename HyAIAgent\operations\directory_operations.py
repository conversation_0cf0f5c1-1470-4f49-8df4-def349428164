"""
目录操作模块
提供安全的异步目录操作功能
"""

import os
import shutil
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import logging

from .security_manager import SecurityManager
from .path_utils import PathUtils
from .file_utils import FileUtils

logger = logging.getLogger(__name__)


class DirectoryOperations:
    """
    目录操作类
    
    提供以下功能：
    - 目录创建、删除、复制、移动
    - 目录遍历和文件统计
    - 安全检查和权限验证
    - 操作审计和日志记录
    """
    
    def __init__(self, workspace_path: str = "./workspace",
                 security_manager: Optional[SecurityManager] = None):
        """
        初始化目录操作管理器

        Args:
            workspace_path (str): 工作目录路径
            security_manager (Optional[SecurityManager]): 安全管理器实例
        """
        self.workspace_path = Path(workspace_path).resolve()
        self.security_manager = security_manager or SecurityManager(workspace_path=str(self.workspace_path))
        
        # 操作统计
        self.operation_stats = {
            "successful_operations": 0,
            "failed_operations": 0,
            "directories_created": 0,
            "directories_deleted": 0,
            "directories_copied": 0,
            "directories_moved": 0
        }
        
        # 确保工作目录存在
        self.workspace_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"DirectoryOperations initialized with workspace: {self.workspace_path}")
    
    async def create_directory(self, directory_path: str, parents: bool = True) -> Dict[str, Any]:
        """
        异步创建目录
        
        Args:
            directory_path (str): 目录路径（相对于工作目录）
            parents (bool): 是否创建父目录
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            # 构建绝对路径
            abs_path = self.workspace_path / directory_path
            
            # 安全验证
            validation_result = self.security_manager.validate_operation(str(abs_path), "write")
            if not validation_result["valid"]:
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Security validation failed",
                    "details": validation_result["errors"],
                    "directory_path": directory_path
                }
            
            # 检查目录是否已存在
            if abs_path.exists():
                if abs_path.is_dir():
                    return {
                        "success": True,
                        "message": "Directory already exists",
                        "directory_path": directory_path,
                        "absolute_path": str(abs_path)
                    }
                else:
                    self.operation_stats["failed_operations"] += 1
                    return {
                        "success": False,
                        "error": "Path exists but is not a directory",
                        "directory_path": directory_path
                    }
            
            # 创建目录
            abs_path.mkdir(parents=parents, exist_ok=True)
            
            self.operation_stats["successful_operations"] += 1
            self.operation_stats["directories_created"] += 1
            
            logger.info(f"Successfully created directory: {directory_path}")
            
            return {
                "success": True,
                "message": "Directory created successfully",
                "directory_path": directory_path,
                "absolute_path": str(abs_path),
                "parents_created": parents
            }
            
        except PermissionError:
            self.operation_stats["failed_operations"] += 1
            error_msg = f"Permission denied: {directory_path}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": "Permission denied",
                "directory_path": directory_path
            }
        except Exception as e:
            self.operation_stats["failed_operations"] += 1
            error_msg = f"Failed to create directory {directory_path}: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": str(e),
                "directory_path": directory_path
            }
    
    async def delete_directory(self, directory_path: str, recursive: bool = False) -> Dict[str, Any]:
        """
        异步删除目录
        
        Args:
            directory_path (str): 目录路径（相对于工作目录）
            recursive (bool): 是否递归删除（删除非空目录）
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            # 构建绝对路径
            abs_path = self.workspace_path / directory_path
            
            # 先检查目录是否存在
            if not abs_path.exists():
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Directory does not exist",
                    "directory_path": directory_path
                }
            
            if not abs_path.is_dir():
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Path is not a directory",
                    "directory_path": directory_path
                }
            
            # 安全验证
            validation_result = self.security_manager.validate_operation(str(abs_path), "delete")
            if not validation_result["valid"]:
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Security validation failed",
                    "details": validation_result["errors"],
                    "directory_path": directory_path
                }
            
            # 检查目录是否为空
            if not recursive and any(abs_path.iterdir()):
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Directory is not empty (use recursive=True to force delete)",
                    "directory_path": directory_path
                }
            
            # 统计删除前的信息
            file_count = 0
            dir_count = 0
            total_size = 0
            
            if recursive:
                for item in abs_path.rglob('*'):
                    if item.is_file():
                        file_count += 1
                        try:
                            total_size += item.stat().st_size
                        except (OSError, IOError):
                            pass
                    elif item.is_dir():
                        dir_count += 1
            
            # 删除目录
            if recursive:
                shutil.rmtree(abs_path)
            else:
                abs_path.rmdir()
            
            self.operation_stats["successful_operations"] += 1
            self.operation_stats["directories_deleted"] += 1
            
            logger.info(f"Successfully deleted directory: {directory_path}")
            
            result = {
                "success": True,
                "message": "Directory deleted successfully",
                "directory_path": directory_path,
                "recursive": recursive
            }
            
            if recursive:
                result.update({
                    "files_deleted": file_count,
                    "subdirectories_deleted": dir_count,
                    "total_size_deleted": total_size,
                    "size_formatted": FileUtils.format_file_size(total_size)
                })
            
            return result
            
        except PermissionError:
            self.operation_stats["failed_operations"] += 1
            error_msg = f"Permission denied: {directory_path}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": "Permission denied",
                "directory_path": directory_path
            }
        except Exception as e:
            self.operation_stats["failed_operations"] += 1
            error_msg = f"Failed to delete directory {directory_path}: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": str(e),
                "directory_path": directory_path
            }
    
    async def copy_directory(self, source_path: str, destination_path: str, 
                           overwrite: bool = False) -> Dict[str, Any]:
        """
        异步复制目录
        
        Args:
            source_path (str): 源目录路径（相对于工作目录）
            destination_path (str): 目标目录路径（相对于工作目录）
            overwrite (bool): 是否覆盖已存在的目标目录
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            # 构建绝对路径
            abs_source = self.workspace_path / source_path
            abs_dest = self.workspace_path / destination_path
            
            # 检查源目录是否存在
            if not abs_source.exists():
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Source directory does not exist",
                    "source_path": source_path
                }
            
            if not abs_source.is_dir():
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Source path is not a directory",
                    "source_path": source_path
                }
            
            # 安全验证
            source_validation = self.security_manager.validate_operation(str(abs_source), "read")
            dest_validation = self.security_manager.validate_operation(str(abs_dest), "write")
            
            if not source_validation["valid"]:
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Source security validation failed",
                    "details": source_validation["errors"],
                    "source_path": source_path
                }
            
            if not dest_validation["valid"]:
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Destination security validation failed",
                    "details": dest_validation["errors"],
                    "destination_path": destination_path
                }
            
            # 检查目标目录是否已存在
            if abs_dest.exists():
                if not overwrite:
                    self.operation_stats["failed_operations"] += 1
                    return {
                        "success": False,
                        "error": "Destination directory already exists (use overwrite=True to force)",
                        "destination_path": destination_path
                    }
                else:
                    # 删除已存在的目标目录
                    if abs_dest.is_dir():
                        shutil.rmtree(abs_dest)
                    else:
                        abs_dest.unlink()
            
            # 统计源目录信息
            file_count = 0
            total_size = 0
            
            for item in abs_source.rglob('*'):
                if item.is_file():
                    file_count += 1
                    try:
                        total_size += item.stat().st_size
                    except (OSError, IOError):
                        pass
            
            # 复制目录
            shutil.copytree(abs_source, abs_dest)
            
            self.operation_stats["successful_operations"] += 1
            self.operation_stats["directories_copied"] += 1
            
            logger.info(f"Successfully copied directory from {source_path} to {destination_path}")
            
            return {
                "success": True,
                "message": "Directory copied successfully",
                "source_path": source_path,
                "destination_path": destination_path,
                "files_copied": file_count,
                "total_size": total_size,
                "size_formatted": FileUtils.format_file_size(total_size),
                "overwrite": overwrite
            }
            
        except PermissionError:
            self.operation_stats["failed_operations"] += 1
            error_msg = f"Permission denied during copy operation"
            logger.error(error_msg)
            return {
                "success": False,
                "error": "Permission denied",
                "source_path": source_path,
                "destination_path": destination_path
            }
        except Exception as e:
            self.operation_stats["failed_operations"] += 1
            error_msg = f"Failed to copy directory from {source_path} to {destination_path}: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": str(e),
                "source_path": source_path,
                "destination_path": destination_path
            }

    async def move_directory(self, source_path: str, destination_path: str,
                           overwrite: bool = False) -> Dict[str, Any]:
        """
        异步移动目录

        Args:
            source_path (str): 源目录路径（相对于工作目录）
            destination_path (str): 目标目录路径（相对于工作目录）
            overwrite (bool): 是否覆盖已存在的目标目录

        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            # 构建绝对路径
            abs_source = self.workspace_path / source_path
            abs_dest = self.workspace_path / destination_path

            # 检查源目录是否存在
            if not abs_source.exists():
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Source directory does not exist",
                    "source_path": source_path
                }

            if not abs_source.is_dir():
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Source path is not a directory",
                    "source_path": source_path
                }

            # 安全验证
            source_validation = self.security_manager.validate_operation(str(abs_source), "delete")
            dest_validation = self.security_manager.validate_operation(str(abs_dest), "write")

            if not source_validation["valid"]:
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Source security validation failed",
                    "details": source_validation["errors"],
                    "source_path": source_path
                }

            if not dest_validation["valid"]:
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Destination security validation failed",
                    "details": dest_validation["errors"],
                    "destination_path": destination_path
                }

            # 检查目标目录是否已存在
            if abs_dest.exists():
                if not overwrite:
                    self.operation_stats["failed_operations"] += 1
                    return {
                        "success": False,
                        "error": "Destination directory already exists (use overwrite=True to force)",
                        "destination_path": destination_path
                    }
                else:
                    # 删除已存在的目标目录
                    if abs_dest.is_dir():
                        shutil.rmtree(abs_dest)
                    else:
                        abs_dest.unlink()

            # 统计源目录信息
            file_count = 0
            total_size = 0

            for item in abs_source.rglob('*'):
                if item.is_file():
                    file_count += 1
                    try:
                        total_size += item.stat().st_size
                    except (OSError, IOError):
                        pass

            # 移动目录
            shutil.move(str(abs_source), str(abs_dest))

            self.operation_stats["successful_operations"] += 1
            self.operation_stats["directories_moved"] += 1

            logger.info(f"Successfully moved directory from {source_path} to {destination_path}")

            return {
                "success": True,
                "message": "Directory moved successfully",
                "source_path": source_path,
                "destination_path": destination_path,
                "files_moved": file_count,
                "total_size": total_size,
                "size_formatted": FileUtils.format_file_size(total_size),
                "overwrite": overwrite
            }

        except PermissionError:
            self.operation_stats["failed_operations"] += 1
            error_msg = f"Permission denied during move operation"
            logger.error(error_msg)
            return {
                "success": False,
                "error": "Permission denied",
                "source_path": source_path,
                "destination_path": destination_path
            }
        except Exception as e:
            self.operation_stats["failed_operations"] += 1
            error_msg = f"Failed to move directory from {source_path} to {destination_path}: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": str(e),
                "source_path": source_path,
                "destination_path": destination_path
            }

    async def get_directory_info(self, directory_path: str = "") -> Dict[str, Any]:
        """
        获取目录详细信息

        Args:
            directory_path (str): 目录路径（相对于工作目录），空字符串表示工作目录

        Returns:
            Dict[str, Any]: 目录信息
        """
        try:
            # 构建绝对路径
            abs_path = self.workspace_path / directory_path if directory_path else self.workspace_path

            # 检查目录是否存在
            if not abs_path.exists():
                return {
                    "success": False,
                    "error": "Directory does not exist",
                    "directory_path": directory_path
                }

            if not abs_path.is_dir():
                return {
                    "success": False,
                    "error": "Path is not a directory",
                    "directory_path": directory_path
                }

            # 安全验证
            validation_result = self.security_manager.validate_operation(str(abs_path), "read")
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": "Security validation failed",
                    "details": validation_result["errors"],
                    "directory_path": directory_path
                }

            # 统计目录信息
            file_count = 0
            dir_count = 0
            total_size = 0
            file_types = {}

            # 直接子项统计
            direct_files = 0
            direct_dirs = 0

            for item in abs_path.iterdir():
                if item.is_file():
                    direct_files += 1
                elif item.is_dir():
                    direct_dirs += 1

            # 递归统计
            for item in abs_path.rglob('*'):
                if item.is_file():
                    file_count += 1
                    try:
                        size = item.stat().st_size
                        total_size += size

                        # 统计文件类型
                        category = FileUtils.get_file_category(item)
                        file_types[category] = file_types.get(category, 0) + 1

                    except (OSError, IOError):
                        pass
                elif item.is_dir():
                    dir_count += 1

            # 获取目录属性
            stat = abs_path.stat()

            return {
                "success": True,
                "directory_path": directory_path,
                "absolute_path": str(abs_path),
                "name": abs_path.name,
                "direct_files": direct_files,
                "direct_directories": direct_dirs,
                "total_files": file_count,
                "total_directories": dir_count,
                "total_size": total_size,
                "size_formatted": FileUtils.format_file_size(total_size),
                "file_types": file_types,
                "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "accessed": datetime.fromtimestamp(stat.st_atime).isoformat(),
                "is_readable": os.access(abs_path, os.R_OK),
                "is_writable": os.access(abs_path, os.W_OK),
                "is_executable": os.access(abs_path, os.X_OK)
            }

        except Exception as e:
            error_msg = f"Failed to get directory info for {directory_path}: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": str(e),
                "directory_path": directory_path
            }

    async def list_directory_tree(self, directory_path: str = "", max_depth: int = 3) -> Dict[str, Any]:
        """
        获取目录树结构

        Args:
            directory_path (str): 目录路径（相对于工作目录）
            max_depth (int): 最大深度

        Returns:
            Dict[str, Any]: 目录树结构
        """
        try:
            # 构建绝对路径
            abs_path = self.workspace_path / directory_path if directory_path else self.workspace_path

            # 检查目录是否存在
            if not abs_path.exists() or not abs_path.is_dir():
                return {
                    "success": False,
                    "error": "Directory does not exist or is not a directory",
                    "directory_path": directory_path
                }

            # 安全验证
            validation_result = self.security_manager.validate_operation(str(abs_path), "read")
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": "Security validation failed",
                    "details": validation_result["errors"],
                    "directory_path": directory_path
                }

            def build_tree(path: Path, current_depth: int = 0) -> Dict[str, Any]:
                """递归构建目录树"""
                if current_depth >= max_depth:
                    return {"name": path.name, "type": "directory", "truncated": True}

                try:
                    items = []
                    for item in sorted(path.iterdir(), key=lambda x: (x.is_file(), x.name.lower())):
                        if item.is_dir():
                            items.append(build_tree(item, current_depth + 1))
                        else:
                            file_info = {
                                "name": item.name,
                                "type": "file",
                                "size": item.stat().st_size,
                                "size_formatted": FileUtils.format_file_size(item.stat().st_size),
                                "extension": FileUtils.get_file_extension(item),
                                "category": FileUtils.get_file_category(item)
                            }
                            items.append(file_info)

                    return {
                        "name": path.name,
                        "type": "directory",
                        "children": items,
                        "child_count": len(items)
                    }
                except PermissionError:
                    return {
                        "name": path.name,
                        "type": "directory",
                        "error": "Permission denied"
                    }

            tree = build_tree(abs_path)

            return {
                "success": True,
                "directory_path": directory_path,
                "max_depth": max_depth,
                "tree": tree
            }

        except Exception as e:
            error_msg = f"Failed to list directory tree for {directory_path}: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": str(e),
                "directory_path": directory_path
            }

    def get_operation_stats(self) -> Dict[str, Any]:
        """
        获取操作统计信息

        Returns:
            Dict[str, Any]: 操作统计
        """
        return self.operation_stats.copy()

    def reset_stats(self) -> None:
        """重置操作统计"""
        self.operation_stats = {
            "successful_operations": 0,
            "failed_operations": 0,
            "directories_created": 0,
            "directories_deleted": 0,
            "directories_copied": 0,
            "directories_moved": 0
        }
        logger.info("Operation statistics reset")
