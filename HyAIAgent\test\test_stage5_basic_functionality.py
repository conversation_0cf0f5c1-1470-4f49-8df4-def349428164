#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
第五阶段基础功能测试
测试核心模块的基本功能
"""

import asyncio
import pytest
import sys
import os
import time
import psutil
from pathlib import Path
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 导入核心模块
from core.config_manager import ConfigManager
from core.kv_store import KVStore
from monitoring.performance_monitor import PerformanceMonitor

class TestStage5BasicFunctionality:
    """第五阶段基础功能测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.config_manager = ConfigManager()
        self.kv_store = KVStore(db_path="data/test_basic.json")
        self.test_results = []
    
    def teardown_method(self):
        """测试后清理"""
        try:
            asyncio.run(self.kv_store.close())
            if os.path.exists("data/test_basic.json"):
                os.remove("data/test_basic.json")
        except:
            pass
    
    def record_test_result(self, test_name: str, success: bool, duration: float, details: str = ""):
        """记录测试结果"""
        self.test_results.append({
            'test_name': test_name,
            'success': success,
            'duration': duration,
            'details': details,
            'timestamp': time.time()
        })
    
    def test_config_manager_basic(self):
        """测试配置管理器基本功能"""
        start_time = time.time()
        
        try:
            # 测试配置加载
            config = self.config_manager.get_default_config()
            assert isinstance(config, dict)
            assert 'ai_providers' in config  # 修正配置键名
            
            # 测试配置设置和获取
            test_key = "test.basic.setting"
            test_value = "basic_test_value"
            
            success = self.config_manager.set(test_key, test_value)
            assert success is True
            
            retrieved_value = self.config_manager.get(test_key)
            assert retrieved_value == test_value
            
            duration = time.time() - start_time
            self.record_test_result("config_manager_basic", True, duration, "配置管理基本功能正常")
            
            print(f"✅ 配置管理器基本功能测试通过 - 用时: {duration:.3f}s")
            
        except Exception as e:
            duration = time.time() - start_time
            self.record_test_result("config_manager_basic", False, duration, str(e))
            raise
    
    def test_kv_store_basic(self):
        """测试KV存储基本功能"""
        start_time = time.time()
        
        try:
            # 测试基本存储操作（注意：set方法是同步的）
            test_key = "basic_test_key"
            test_value = {"data": "basic_test_data", "number": 123}
            
            # 设置值
            success = self.kv_store.set(test_key, test_value)
            assert success is True
            
            # 获取值
            retrieved_value = self.kv_store.get(test_key)
            assert retrieved_value == test_value
            
            # 测试键存在性
            exists = self.kv_store.exists(test_key)
            assert exists is True
            
            # 测试删除
            deleted = self.kv_store.delete(test_key)
            assert deleted is True
            
            # 验证删除后不存在
            exists_after_delete = self.kv_store.exists(test_key)
            assert exists_after_delete is False
            
            duration = time.time() - start_time
            self.record_test_result("kv_store_basic", True, duration, "KV存储基本功能正常")
            
            print(f"✅ KV存储基本功能测试通过 - 用时: {duration:.3f}s")
            
        except Exception as e:
            duration = time.time() - start_time
            self.record_test_result("kv_store_basic", False, duration, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_performance_monitor_basic(self):
        """测试性能监控基本功能"""
        start_time = time.time()
        
        try:
            # 创建性能监控器
            performance_monitor = PerformanceMonitor()
            
            # 测试获取当前指标
            metrics = await performance_monitor.get_current_metrics()
            
            # 验证基本指标存在
            assert metrics is not None
            assert hasattr(metrics, 'cpu_usage')
            assert hasattr(metrics, 'memory_usage')
            assert hasattr(metrics, 'disk_usage')
            assert hasattr(metrics, 'timestamp')
            
            # 验证指标值合理性
            assert 0 <= metrics.cpu_usage <= 100
            assert 0 <= metrics.memory_usage <= 100
            assert 0 <= metrics.disk_usage <= 100
            
            # 测试性能统计
            stats = performance_monitor.get_performance_stats()
            assert isinstance(stats, dict)
            
            duration = time.time() - start_time
            self.record_test_result("performance_monitor_basic", True, duration, "性能监控基本功能正常")
            
            print(f"✅ 性能监控基本功能测试通过 - 用时: {duration:.3f}s")
            
        except Exception as e:
            duration = time.time() - start_time
            self.record_test_result("performance_monitor_basic", False, duration, str(e))
            raise
    
    def test_batch_kv_operations(self):
        """测试批量KV操作"""
        start_time = time.time()
        
        try:
            batch_size = 50
            
            # 批量写入
            for i in range(batch_size):
                success = self.kv_store.set(f"batch_key_{i}", f"batch_value_{i}")
                assert success is True
            
            # 批量读取验证
            successful_reads = 0
            for i in range(batch_size):
                value = self.kv_store.get(f"batch_key_{i}")
                if value == f"batch_value_{i}":
                    successful_reads += 1
            
            assert successful_reads == batch_size
            
            # 批量删除
            successful_deletes = 0
            for i in range(batch_size):
                if self.kv_store.delete(f"batch_key_{i}"):
                    successful_deletes += 1
            
            assert successful_deletes == batch_size
            
            duration = time.time() - start_time
            self.record_test_result("batch_kv_operations", True, duration, f"批量处理{batch_size}条记录")
            
            print(f"✅ 批量KV操作测试通过 - 用时: {duration:.3f}s, 处理{batch_size}条记录")
            
        except Exception as e:
            duration = time.time() - start_time
            self.record_test_result("batch_kv_operations", False, duration, str(e))
            raise
    
    def test_memory_usage_monitoring(self):
        """测试内存使用监控"""
        start_time = time.time()
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            # 创建一些数据来测试内存使用
            data_store = []
            for i in range(1000):
                data = {
                    'id': i,
                    'content': f"test_content_{i}" * 10,
                    'metadata': {'index': i, 'timestamp': time.time()}
                }
                data_store.append(data)
                
                # 存储到KV
                self.kv_store.set(f"memory_test_{i}", data)
            
            # 检查内存使用
            final_memory = psutil.Process().memory_info().rss / 1024 / 1024
            memory_increase = final_memory - initial_memory
            
            # 清理数据
            for i in range(1000):
                self.kv_store.delete(f"memory_test_{i}")
            
            duration = time.time() - start_time
            self.record_test_result("memory_usage_monitoring", True, duration, 
                                  f"内存增长: {memory_increase:.2f}MB")
            
            print(f"✅ 内存使用监控测试通过 - 用时: {duration:.3f}s, 内存增长: {memory_increase:.2f}MB")
            
            # 内存使用不应过多
            assert memory_increase < 100, f"内存使用过多: {memory_increase:.2f}MB"
            
        except Exception as e:
            duration = time.time() - start_time
            self.record_test_result("memory_usage_monitoring", False, duration, str(e))
            raise
    
    def test_error_handling(self):
        """测试错误处理"""
        start_time = time.time()
        
        try:
            # 测试获取不存在的配置
            non_existent_value = self.config_manager.get("non.existent.key")
            assert non_existent_value is None
            
            # 测试获取不存在的KV值
            non_existent_kv = self.kv_store.get("non_existent_key")
            assert non_existent_kv is None
            
            # 测试删除不存在的键
            delete_result = self.kv_store.delete("non_existent_key")
            assert delete_result is False
            
            duration = time.time() - start_time
            self.record_test_result("error_handling", True, duration, "错误处理机制正常")
            
            print(f"✅ 错误处理测试通过 - 用时: {duration:.3f}s")
            
        except Exception as e:
            duration = time.time() - start_time
            self.record_test_result("error_handling", False, duration, str(e))
            raise
    
    def test_results_summary(self):
        """测试结果总结"""
        if not self.test_results:
            pytest.skip("没有测试结果")
        
        print("\n" + "="*60)
        print("📊 第五阶段基础功能测试总结")
        print("="*60)
        
        successful_tests = [r for r in self.test_results if r['success']]
        failed_tests = [r for r in self.test_results if not r['success']]
        
        total_duration = sum(r['duration'] for r in self.test_results)
        
        print(f"总测试数量: {len(self.test_results)}")
        print(f"成功测试数量: {len(successful_tests)}")
        print(f"失败测试数量: {len(failed_tests)}")
        print(f"成功率: {len(successful_tests)/len(self.test_results)*100:.1f}%")
        print(f"总测试时间: {total_duration:.3f}秒")
        
        print("\n详细测试结果:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"  {status} {result['test_name']}: {result['duration']:.3f}s")
            if result['details']:
                print(f"     详情: {result['details']}")
        
        # 基础功能测试要求
        success_rate = len(successful_tests) / len(self.test_results)
        assert success_rate >= 0.9, f"基础功能测试成功率过低: {success_rate*100:.1f}%"
        
        print(f"\n✅ 基础功能测试成功率达标: {success_rate*100:.1f}% (要求 ≥ 90%)")

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
