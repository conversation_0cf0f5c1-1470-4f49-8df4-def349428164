@echo off
chcp 65001 >nul 2>&1
title PyQt6 Hello World Application Launcher

echo.
echo ================================================
echo PyQt6 Hello World Application Launcher
echo ================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found
    echo Please install Python 3.8 or higher
    echo Download: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo [OK] Python environment detected
python --version

echo.
echo [INFO] Starting application...
echo.

REM Run Python launcher
python run.py

echo.
echo [INFO] Application finished
pause
