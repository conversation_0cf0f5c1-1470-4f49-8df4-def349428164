"""
学习系统测试模块

测试LearningSystem类的各项功能，包括：
- 从用户交互中学习
- 用户偏好管理
- 响应改进机制
- 行为适应算法
- 学习数据的持久化

作者: HyAIAgent开发团队
创建时间: 2025-07-30
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import tempfile
import shutil
from datetime import datetime, timedelta
from typing import Dict, Any

from advanced.learning_system import (
    LearningSystem, UserInteraction, UserPreference, ResponseFeedback, 
    UsagePattern, InteractionType, LearningMode, PreferenceCategory
)
from core.ai_client import SimpleAIClient
from core.prompt_manager import PromptManager
from core.kv_store import KVStore


class TestLearningSystem:
    """学习系统测试类"""

    async def test_learn_from_interaction(self, learning_system):
        """测试从用户交互中学习"""
        
        # 创建测试交互数据
        interaction = UserInteraction(
            interaction_id="test_interaction_001",
            user_id="test_user_001",
            timestamp=datetime.now(),
            interaction_type=InteractionType.QUESTION,
            content="请详细解释一下机器学习的基本概念",
            response="机器学习是人工智能的一个分支...",
            response_time=2.5,
            user_satisfaction=0.8,
            feedback="回答很详细，很有帮助"
        )
        
        # 执行学习
        result = await learning_system.learn_from_interaction(interaction)
        
        # 验证结果
        assert result["success"] is True
        assert result["interaction_id"] == "test_interaction_001"
        assert result["patterns_discovered"] >= 0
        assert result["preferences_updated"] >= 0
        assert result["improvements_identified"] >= 0
        assert "processing_time" in result
        
        print(f"✅ 学习完成 - 发现模式: {result['patterns_discovered']}, "
              f"更新偏好: {result['preferences_updated']}")

    async def test_update_preferences(self, learning_system):
        """测试更新用户偏好"""
        
        # 创建测试偏好数据
        preferences = {
            "communication_style": {
                "formality": "professional",
                "tone": "friendly"
            },
            "response_length": {
                "preferred_length": "detailed",
                "max_length": 500
            },
            "detail_level": {
                "technical_detail": "high",
                "examples_included": True
            }
        }
        
        # 更新偏好
        result = await learning_system.update_preferences("test_user_001", preferences)
        
        # 验证结果
        assert result["success"] is True
        assert result["user_id"] == "test_user_001"
        assert result["updated_count"] > 0
        assert len(result["preferences"]) > 0
        
        print(f"✅ 偏好更新完成 - 用户: {result['user_id']}, "
              f"更新数量: {result['updated_count']}")

    async def test_improve_responses(self, learning_system):
        """测试响应改进功能"""
        
        # 创建测试反馈数据
        feedback = ResponseFeedback(
            feedback_id="feedback_001",
            user_id="test_user_001",
            response_id="response_001",
            timestamp=datetime.now(),
            rating=4.2,
            feedback_text="回答很好，但可以更简洁一些",
            improvement_suggestions=["减少冗余信息", "突出重点"],
            positive_aspects=["内容准确", "逻辑清晰"],
            negative_aspects=["稍显冗长"]
        )
        
        # 执行响应改进
        result = await learning_system.improve_responses(feedback)
        
        # 验证结果
        assert result["success"] is True
        assert result["feedback_id"] == "feedback_001"
        assert result["rating"] == 4.2
        assert "analysis" in result
        assert "improvements" in result
        assert "strategy_updates" in result
        
        print(f"✅ 响应改进完成 - 反馈ID: {result['feedback_id']}, "
              f"评分: {result['rating']}")

    async def test_adapt_behavior(self, learning_system):
        """测试行为适应功能"""
        
        # 创建测试使用模式
        patterns = [
            UsagePattern(
                pattern_id="pattern_001",
                user_id="test_user_001",
                pattern_type="time_based",
                pattern_data={
                    "hour": 14,
                    "weekday": 1,
                    "interaction_type": "question"
                },
                frequency=5,
                confidence=0.8,
                last_observed=datetime.now(),
                first_observed=datetime.now() - timedelta(days=7)
            ),
            UsagePattern(
                pattern_id="pattern_002",
                user_id="test_user_001",
                pattern_type="content_length",
                pattern_data={
                    "length_category": "long",
                    "actual_length": 250,
                    "interaction_type": "task_request"
                },
                frequency=3,
                confidence=0.7,
                last_observed=datetime.now(),
                first_observed=datetime.now() - timedelta(days=3)
            )
        ]
        
        # 执行行为适应
        result = await learning_system.adapt_behavior(patterns)
        
        # 验证结果
        assert result["success"] is True
        assert result["patterns_processed"] == 2
        assert result["adaptations_generated"] >= 0
        assert result["adaptations_applied"] >= 0
        
        print(f"✅ 行为适应完成 - 处理模式: {result['patterns_processed']}, "
              f"生成适应: {result['adaptations_generated']}")

    async def test_get_user_preferences(self, learning_system):
        """测试获取用户偏好"""
        
        # 先设置一些偏好
        preferences = {
            "communication_style": {
                "formality": "casual",
                "tone": "helpful"
            }
        }
        await learning_system.update_preferences("test_user_002", preferences)
        
        # 获取用户偏好
        result = await learning_system.get_user_preferences("test_user_002")
        
        # 验证结果
        assert result["success"] is True
        assert result["user_id"] == "test_user_002"
        assert "preferences" in result
        assert result["total_preferences"] >= 0
        
        print(f"✅ 获取用户偏好完成 - 用户: {result['user_id']}, "
              f"偏好总数: {result['total_preferences']}")

    async def test_get_learning_stats(self, learning_system):
        """测试获取学习统计"""
        
        # 获取学习统计
        result = await learning_system.get_learning_stats()
        
        # 验证结果
        assert result["success"] is True
        assert "stats" in result
        assert "cache_size" in result["stats"]
        assert "learning_mode" in result["stats"]
        
        stats = result["stats"]
        assert "total_interactions" in stats
        assert "successful_adaptations" in stats
        assert "failed_adaptations" in stats
        assert "user_satisfaction_avg" in stats
        
        print(f"✅ 学习统计获取完成 - 总交互: {stats['total_interactions']}, "
              f"学习模式: {stats['learning_mode']}")

    async def test_reset_learning_data(self, learning_system):
        """测试重置学习数据"""
        
        # 先添加一些数据
        interaction = UserInteraction(
            interaction_id="test_reset_001",
            user_id="test_user_reset",
            timestamp=datetime.now(),
            interaction_type=InteractionType.QUESTION,
            content="测试重置功能"
        )
        await learning_system.learn_from_interaction(interaction)
        
        # 重置特定用户的数据
        result = await learning_system.reset_learning_data("test_user_reset")
        
        # 验证结果
        assert result["success"] is True
        assert result["user_id"] == "test_user_reset"
        assert result["reset_count"] >= 0
        
        print(f"✅ 学习数据重置完成 - 用户: {result['user_id']}, "
              f"删除记录: {result['reset_count']}")

    async def test_export_learning_data(self, learning_system):
        """测试导出学习数据"""
        
        # 先添加一些数据
        interaction = UserInteraction(
            interaction_id="test_export_001",
            user_id="test_user_export",
            timestamp=datetime.now(),
            interaction_type=InteractionType.QUESTION,
            content="测试导出功能"
        )
        await learning_system.learn_from_interaction(interaction)
        
        # 导出学习数据
        result = await learning_system.export_learning_data("test_user_export")
        
        # 验证结果
        assert result["success"] is True
        assert "export_data" in result
        assert "summary" in result
        
        export_data = result["export_data"]
        assert "export_timestamp" in export_data
        assert "user_id" in export_data
        assert "interactions" in export_data
        assert "preferences" in export_data
        assert "patterns" in export_data
        assert "feedback" in export_data
        assert "stats" in export_data
        
        summary = result["summary"]
        assert "interactions_count" in summary
        assert "preferences_count" in summary
        assert "patterns_count" in summary
        assert "feedback_count" in summary
        
        print(f"✅ 学习数据导出完成 - 交互: {summary['interactions_count']}, "
              f"偏好: {summary['preferences_count']}")


async def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始学习系统综合测试...")

    # 创建临时目录
    temp_dir = tempfile.mkdtemp()

    try:
        # 创建测试用的组件
        ai_client = SimpleAIClient(
            api_key="test_key",
            base_url="https://api.openai.com/v1",
            model="gpt-3.5-turbo"
        )

        prompt_manager = PromptManager(
            prompts_dir=os.path.join(temp_dir, "prompts")
        )

        kv_store = KVStore(
            db_path=os.path.join(temp_dir, "test_learning.json")
        )

        # 创建学习系统实例
        learning_system = LearningSystem(
            ai_client=ai_client,
            prompt_manager=prompt_manager,
            kv_store=kv_store,
            learning_mode=LearningMode.ACTIVE
        )

        # 创建测试实例
        test_instance = TestLearningSystem()

        # 运行所有测试
        await test_instance.test_learn_from_interaction(learning_system)
        await test_instance.test_update_preferences(learning_system)
        await test_instance.test_improve_responses(learning_system)
        await test_instance.test_adapt_behavior(learning_system)
        await test_instance.test_get_user_preferences(learning_system)
        await test_instance.test_get_learning_stats(learning_system)
        await test_instance.test_reset_learning_data(learning_system)
        await test_instance.test_export_learning_data(learning_system)

        print("\n🎉 所有学习系统测试通过！")

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        # 清理测试环境
        try:
            shutil.rmtree(temp_dir)
        except:
            pass


if __name__ == "__main__":
    asyncio.run(run_comprehensive_test())
