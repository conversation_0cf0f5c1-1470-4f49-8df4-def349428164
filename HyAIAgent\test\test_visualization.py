"""
可视化展示模块测试

测试任务流程可视化、数据关系图表、性能监控、知识图谱可视化等功能。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import sys
import pytest
import asyncio
import json
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 尝试导入可选依赖
try:
    import pandas as pd
    import numpy as np
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    # 创建模拟的pandas DataFrame
    class MockDataFrame:
        def __init__(self, data=None):
            self.data = data or {}
            self.columns = list(self.data.keys()) if data else []

        def __len__(self):
            return len(list(self.data.values())[0]) if self.data else 0

    pd = Mock()
    pd.DataFrame = MockDataFrame
    np = Mock()
    np.random = Mock()
    np.random.randn = lambda *args: [0.0] * (args[0] if args else 1)

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# PyQt6 imports
from PyQt6.QtWidgets import QApplication, QWidget
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtTest import QTest

# 导入被测试的模块
from ui.visualization import (
    TaskFlowVisualizer, DataRelationshipChart, PerformanceMonitor,
    KnowledgeGraphVisualizer, Visualization
)
from core.config_manager import ConfigManager
from core.kv_store import KVStore


class TestTaskFlowVisualizer:
    """任务流程可视化器测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def task_flow_visualizer(self, app):
        """创建任务流程可视化器实例"""
        widget = TaskFlowVisualizer()
        yield widget
        widget.close()
        
    def test_task_flow_initialization(self, task_flow_visualizer):
        """测试任务流程可视化器初始化"""
        assert task_flow_visualizer.tasks == []
        assert task_flow_visualizer.connections == []
        assert task_flow_visualizer.current_task is None
        assert task_flow_visualizer.view_combo is not None
        assert task_flow_visualizer.canvas is not None
        
    def test_set_tasks(self, task_flow_visualizer):
        """测试设置任务数据"""
        test_tasks = [
            {"id": "task1", "name": "任务1", "status": "completed"},
            {"id": "task2", "name": "任务2", "status": "running"},
            {"id": "task3", "name": "任务3", "status": "pending"}
        ]
        
        task_flow_visualizer.set_tasks(test_tasks)
        assert task_flow_visualizer.tasks == test_tasks
        
    def test_add_task(self, task_flow_visualizer):
        """测试添加任务"""
        initial_tasks = [{"id": "task1", "name": "任务1", "status": "completed"}]
        task_flow_visualizer.set_tasks(initial_tasks)
        
        new_task = {"id": "task2", "name": "任务2", "status": "pending"}
        task_flow_visualizer.add_task(new_task)
        
        assert len(task_flow_visualizer.tasks) == 2
        assert task_flow_visualizer.tasks[1] == new_task
        
    def test_update_task_status(self, task_flow_visualizer):
        """测试更新任务状态"""
        test_tasks = [
            {"id": "task1", "name": "任务1", "status": "pending"},
            {"id": "task2", "name": "任务2", "status": "running"}
        ]
        task_flow_visualizer.set_tasks(test_tasks)
        
        task_flow_visualizer.update_task_status("task1", "completed")
        
        assert task_flow_visualizer.tasks[0]["status"] == "completed"
        
    def test_set_connections(self, task_flow_visualizer):
        """测试设置任务连接"""
        test_connections = [
            {"from": "task1", "to": "task2"},
            {"from": "task2", "to": "task3"}
        ]
        
        task_flow_visualizer.set_connections(test_connections)
        assert task_flow_visualizer.connections == test_connections
        
    def test_view_type_change(self, task_flow_visualizer):
        """测试视图类型切换"""
        # 设置测试数据
        test_tasks = [{"id": "task1", "name": "任务1", "status": "completed"}]
        task_flow_visualizer.set_tasks(test_tasks)
        
        # 测试不同视图类型
        view_types = ["流程图", "甘特图", "网络图", "时间线"]
        for view_type in view_types:
            task_flow_visualizer.view_combo.setCurrentText(view_type)
            # 验证视图切换不会出错
            assert task_flow_visualizer.view_combo.currentText() == view_type


class TestDataRelationshipChart:
    """数据关系图表测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def data_chart(self, app):
        """创建数据关系图表实例"""
        widget = DataRelationshipChart()
        yield widget
        widget.close()
        
    def test_data_chart_initialization(self, data_chart):
        """测试数据关系图表初始化"""
        assert data_chart.data is None
        assert data_chart.chart_type == "correlation"
        assert data_chart.chart_combo is not None
        assert data_chart.canvas is not None
        
    def test_set_data(self, data_chart):
        """测试设置数据"""
        if HAS_PANDAS:
            test_data = pd.DataFrame({
                'A': [1, 2, 3, 4, 5],
                'B': [2, 4, 6, 8, 10],
                'C': [1, 3, 5, 7, 9]
            })
        else:
            test_data = MockDataFrame({
                'A': [1, 2, 3, 4, 5],
                'B': [2, 4, 6, 8, 10],
                'C': [1, 3, 5, 7, 9]
            })

        data_chart.set_data(test_data)
        assert data_chart.data is not None
        assert len(data_chart.data.columns) == 3
        
    def test_chart_type_change(self, data_chart):
        """测试图表类型切换"""
        # 设置测试数据
        if HAS_PANDAS:
            test_data = pd.DataFrame({
                'A': np.random.randn(50),
                'B': np.random.randn(50)
            })
        else:
            test_data = MockDataFrame({
                'A': [0.0] * 50,
                'B': [0.0] * 50
            })
        data_chart.set_data(test_data)

        # 测试不同图表类型
        chart_types = ["相关性矩阵", "散点图", "箱线图", "直方图", "热力图"]
        for chart_type in chart_types:
            data_chart.chart_combo.setCurrentText(chart_type)
            assert data_chart.chart_combo.currentText() == chart_type


class TestPerformanceMonitor:
    """性能监控器测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def performance_monitor(self, app):
        """创建性能监控器实例"""
        widget = PerformanceMonitor()
        yield widget
        widget.close()
        
    def test_performance_monitor_initialization(self, performance_monitor):
        """测试性能监控器初始化"""
        assert performance_monitor.monitoring == False
        assert len(performance_monitor.data_history) == 5
        assert performance_monitor.max_history == 100
        assert performance_monitor.timer is not None
        
    def test_start_stop_monitoring(self, performance_monitor):
        """测试开始和停止监控"""
        # 测试开始监控
        performance_monitor.start_monitoring()
        assert performance_monitor.monitoring == True
        assert performance_monitor.start_button.isEnabled() == False
        assert performance_monitor.stop_button.isEnabled() == True
        
        # 测试停止监控
        performance_monitor.stop_monitoring()
        assert performance_monitor.monitoring == False
        assert performance_monitor.start_button.isEnabled() == True
        assert performance_monitor.stop_button.isEnabled() == False
        
    def test_clear_data(self, performance_monitor):
        """测试清除数据"""
        # 添加一些测试数据
        performance_monitor.data_history['cpu'] = [10, 20, 30]
        performance_monitor.data_history['memory'] = [40, 50, 60]
        
        performance_monitor.clear_data()
        
        assert len(performance_monitor.data_history['cpu']) == 0
        assert len(performance_monitor.data_history['memory']) == 0
        
    def test_update_interval(self, performance_monitor):
        """测试更新监控间隔"""
        new_interval = 10
        performance_monitor.interval_spin.setValue(new_interval)
        performance_monitor.update_interval(new_interval)
        
        assert performance_monitor.interval_spin.value() == new_interval
        
    def test_alert_thresholds(self, performance_monitor):
        """测试警报阈值设置"""
        performance_monitor.set_alert_threshold('cpu', 90.0)
        assert performance_monitor.alert_thresholds['cpu'] == 90.0
        
        performance_monitor.set_alert_threshold('memory', 95.0)
        assert performance_monitor.alert_thresholds['memory'] == 95.0
        
    def test_update_metrics_with_mock(self, performance_monitor):
        """测试使用模拟数据更新指标"""
        # 直接调用update_metrics，它会使用模拟数据
        performance_monitor.update_metrics()

        # 验证数据被添加到历史记录中
        assert len(performance_monitor.data_history['cpu']) == 1
        assert len(performance_monitor.data_history['memory']) == 1
        # 模拟数据应该是固定值
        assert isinstance(performance_monitor.data_history['cpu'][0], (int, float))
        assert isinstance(performance_monitor.data_history['memory'][0], (int, float))


class TestKnowledgeGraphVisualizer:
    """知识图谱可视化器测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def knowledge_graph(self, app):
        """创建知识图谱可视化器实例"""
        widget = KnowledgeGraphVisualizer()
        yield widget
        widget.close()
        
    def test_knowledge_graph_initialization(self, knowledge_graph):
        """测试知识图谱可视化器初始化"""
        assert knowledge_graph.nodes == []
        assert knowledge_graph.edges == []
        assert knowledge_graph.selected_node is None
        assert knowledge_graph.layout_type == "spring"
        
    def test_set_graph_data(self, knowledge_graph):
        """测试设置图谱数据"""
        test_nodes = [
            {"id": "node1", "label": "节点1", "type": "entity"},
            {"id": "node2", "label": "节点2", "type": "concept"}
        ]
        test_edges = [
            {"source": "node1", "target": "node2", "relation": "related_to"}
        ]
        
        knowledge_graph.set_graph_data(test_nodes, test_edges)
        
        assert knowledge_graph.nodes == test_nodes
        assert knowledge_graph.edges == test_edges
        
    def test_add_node(self, knowledge_graph):
        """测试添加节点"""
        initial_nodes = [{"id": "node1", "label": "节点1", "type": "entity"}]
        knowledge_graph.set_graph_data(initial_nodes, [])
        
        new_node = {"id": "node2", "label": "节点2", "type": "concept"}
        knowledge_graph.add_node(new_node)
        
        assert len(knowledge_graph.nodes) == 2
        assert knowledge_graph.nodes[1] == new_node
        
    def test_add_edge(self, knowledge_graph):
        """测试添加边"""
        test_nodes = [
            {"id": "node1", "label": "节点1"},
            {"id": "node2", "label": "节点2"}
        ]
        knowledge_graph.set_graph_data(test_nodes, [])
        
        new_edge = {"source": "node1", "target": "node2", "relation": "connected"}
        knowledge_graph.add_edge(new_edge)
        
        assert len(knowledge_graph.edges) == 1
        assert knowledge_graph.edges[0] == new_edge
        
    def test_remove_node(self, knowledge_graph):
        """测试移除节点"""
        test_nodes = [
            {"id": "node1", "label": "节点1"},
            {"id": "node2", "label": "节点2"}
        ]
        test_edges = [{"source": "node1", "target": "node2"}]
        knowledge_graph.set_graph_data(test_nodes, test_edges)
        
        knowledge_graph.remove_node("node1")
        
        assert len(knowledge_graph.nodes) == 1
        assert len(knowledge_graph.edges) == 0  # 相关边也应该被移除
        
    def test_layout_change(self, knowledge_graph):
        """测试布局类型变化"""
        test_nodes = [{"id": "node1", "label": "节点1"}]
        knowledge_graph.set_graph_data(test_nodes, [])
        
        layout_types = ["spring", "circular", "random", "shell"]
        for layout_type in layout_types:
            knowledge_graph.layout_combo.setCurrentText(layout_type)
            assert knowledge_graph.layout_type == layout_type


class TestVisualization:
    """可视化展示主类测试"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def visualization(self, app):
        """创建可视化展示主类实例"""
        widget = Visualization()
        yield widget
        widget.close()
        
    def test_visualization_initialization(self, visualization):
        """测试可视化展示主类初始化"""
        assert visualization.task_flow_visualizer is not None
        assert visualization.data_relationship_chart is not None
        assert visualization.performance_monitor is not None
        assert visualization.knowledge_graph_visualizer is not None
        assert visualization.tab_widget.count() == 4
        
    def test_set_task_flow_data(self, visualization):
        """测试设置任务流程数据"""
        test_tasks = [
            {"id": "task1", "name": "任务1", "status": "completed"}
        ]
        test_connections = [
            {"from": "task1", "to": "task2"}
        ]
        
        visualization.set_task_flow_data(test_tasks, test_connections)
        
        assert visualization.task_flow_visualizer.tasks == test_tasks
        assert visualization.task_flow_visualizer.connections == test_connections
        
    def test_set_data_relationship_data(self, visualization):
        """测试设置数据关系数据"""
        if HAS_PANDAS:
            test_data = pd.DataFrame({
                'A': [1, 2, 3],
                'B': [4, 5, 6]
            })
        else:
            test_data = MockDataFrame({
                'A': [1, 2, 3],
                'B': [4, 5, 6]
            })

        visualization.set_data_relationship_data(test_data)

        assert visualization.data_relationship_chart.data is not None
        assert len(visualization.data_relationship_chart.data.columns) == 2
        
    def test_set_knowledge_graph_data(self, visualization):
        """测试设置知识图谱数据"""
        test_nodes = [{"id": "node1", "label": "节点1"}]
        test_edges = [{"source": "node1", "target": "node2"}]
        
        visualization.set_knowledge_graph_data(test_nodes, test_edges)
        
        assert visualization.knowledge_graph_visualizer.nodes == test_nodes
        assert visualization.knowledge_graph_visualizer.edges == test_edges
        
    def test_performance_monitoring_control(self, visualization):
        """测试性能监控控制"""
        # 测试开始监控
        visualization.start_performance_monitoring()
        assert visualization.performance_monitor.monitoring == True
        
        # 测试停止监控
        visualization.stop_performance_monitoring()
        assert visualization.performance_monitor.monitoring == False
        
    def test_get_current_visualization_type(self, visualization):
        """测试获取当前可视化类型"""
        # 默认应该是第一个选项卡
        current_type = visualization.get_current_visualization_type()
        assert current_type == "task_flow"
        
        # 切换到其他选项卡
        visualization.tab_widget.setCurrentIndex(1)
        current_type = visualization.get_current_visualization_type()
        assert current_type == "data_relationship"
        
    def test_switch_to_visualization(self, visualization):
        """测试切换到指定可视化类型"""
        visualization.switch_to_visualization("performance_monitor")
        assert visualization.tab_widget.currentIndex() == 2
        
        visualization.switch_to_visualization("knowledge_graph")
        assert visualization.tab_widget.currentIndex() == 3
        
    def test_get_visualization_stats(self, visualization):
        """测试获取可视化统计信息"""
        stats = visualization.get_visualization_stats()
        
        assert "current_tab" in stats
        assert "task_flow" in stats
        assert "performance_monitor" in stats
        assert "knowledge_graph" in stats
        
        assert isinstance(stats["task_flow"]["tasks_count"], int)
        assert isinstance(stats["performance_monitor"]["monitoring"], bool)
        assert isinstance(stats["knowledge_graph"]["nodes_count"], int)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
