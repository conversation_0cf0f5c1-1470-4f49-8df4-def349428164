"""
格式转换工具模块

该模块提供了全面的文件格式转换功能，支持多种文档格式之间的相互转换。
主要功能包括：
- 文本格式转换（TXT、MD、CSV等）
- 配置格式转换（JSON、YAML、INI、TOML等）
- 数据格式转换（JSON、XML、CSV等）
- 编码格式转换（UTF-8、GBK等）
- 批量格式转换
- 转换质量验证

作者: HyAIAgent开发团队
创建时间: 2025-07-28
版本: 1.0.0
"""

import asyncio
import json
import csv
import xml.etree.ElementTree as ET
import configparser
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import re
import chardet
from io import StringIO

# 可选依赖检查
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

try:
    import toml
    TOML_AVAILABLE = True
except ImportError:
    TOML_AVAILABLE = False

# 导入项目模块
from operations.security_manager import SecurityManager
from operations.file_operations import FileOperations

logger = logging.getLogger(__name__)


class FormatConverter:
    """
    格式转换工具类
    
    提供多种文件格式之间的转换功能，包括：
    - 文本格式转换
    - 配置文件格式转换
    - 数据格式转换
    - 编码转换
    - 批量转换
    """
    
    def __init__(self, workspace_path: str = "./workspace"):
        """
        初始化格式转换器
        
        Args:
            workspace_path (str): 工作空间路径
        """
        self.workspace_path = Path(workspace_path)
        self.security_manager = SecurityManager(workspace_path)
        self.file_operations = FileOperations(workspace_path, self.security_manager)
        
        # 支持的格式映射
        self.supported_formats = {
            'text': ['txt', 'md', 'rst', 'log'],
            'config': ['json', 'yaml', 'yml', 'ini', 'cfg', 'conf', 'toml'],
            'data': ['json', 'xml', 'csv', 'tsv'],
            'markup': ['html', 'xml', 'md']
        }
        
        # 转换统计
        self.conversion_stats = {
            'total_conversions': 0,
            'successful_conversions': 0,
            'failed_conversions': 0,
            'formats_converted': {},
            'last_conversion': None
        }
        
        logger.info(f"FormatConverter initialized with workspace: {self.workspace_path}")
    
    async def convert_file(self, source_path: str, target_path: str, 
                          target_format: str = None, 
                          encoding: str = 'utf-8',
                          preserve_structure: bool = True,
                          validation: bool = True) -> Dict[str, Any]:
        """
        转换单个文件格式
        
        Args:
            source_path (str): 源文件路径
            target_path (str): 目标文件路径
            target_format (str): 目标格式，如果为None则从target_path推断
            encoding (str): 目标文件编码
            preserve_structure (bool): 是否保持数据结构
            validation (bool): 是否验证转换结果
            
        Returns:
            Dict[str, Any]: 转换结果
        """
        start_time = datetime.now()
        
        try:
            # 安全验证
            security_result = self.security_manager.validate_operation(source_path, 'read')
            if not security_result.get('allowed', False):
                raise PermissionError(f"Source file access denied: {source_path}")
            
            security_result = self.security_manager.validate_operation(target_path, 'write')
            if not security_result.get('allowed', False):
                raise PermissionError(f"Target file access denied: {target_path}")
            
            # 检测源文件格式
            source_format = self._detect_file_format(source_path)
            if not target_format:
                target_format = self._detect_file_format(target_path)
            
            # 读取源文件
            source_result = await self.file_operations.read_file(source_path)
            if not source_result.get('success'):
                raise ValueError(f"Failed to read source file: {source_result.get('error')}")
            
            source_content = source_result['content']
            
            # 解析源文件内容
            parsed_data = await self._parse_content(source_content, source_format)
            
            # 转换为目标格式
            converted_content = await self._convert_to_format(
                parsed_data, target_format, preserve_structure
            )
            
            # 写入目标文件
            write_result = await self.file_operations.write_file(
                target_path, converted_content, encoding=encoding
            )
            
            if not write_result.get('success'):
                raise ValueError(f"Failed to write target file: {write_result.get('error')}")
            
            # 验证转换结果
            validation_result = {}
            if validation:
                validation_result = await self._validate_conversion(
                    source_path, target_path, source_format, target_format
                )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 更新统计
            self._update_stats('successful', source_format, target_format)
            
            result = {
                'status': 'success',
                'source_path': source_path,
                'target_path': target_path,
                'source_format': source_format,
                'target_format': target_format,
                'source_size': len(source_content),
                'target_size': len(converted_content),
                'processing_time': processing_time,
                'validation': validation_result,
                'metadata': {
                    'encoding': encoding,
                    'preserve_structure': preserve_structure,
                    'timestamp': datetime.now().isoformat()
                }
            }
            
            logger.info(f"File converted successfully: {source_path} -> {target_path} ({source_format} -> {target_format})")
            return result
            
        except Exception as e:
            self._update_stats('failed', source_format if 'source_format' in locals() else None, 
                             target_format if 'target_format' in locals() else None)
            logger.error(f"Failed to convert file {source_path}: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'source_path': source_path,
                'target_path': target_path,
                'processing_time': (datetime.now() - start_time).total_seconds()
            }
    
    async def batch_convert_files(self, file_mappings: List[Dict[str, str]], 
                                 target_format: str = None,
                                 encoding: str = 'utf-8',
                                 max_concurrent: int = 5) -> Dict[str, Any]:
        """
        批量转换文件格式
        
        Args:
            file_mappings (List[Dict[str, str]]): 文件映射列表，每个元素包含source和target
            target_format (str): 统一的目标格式
            encoding (str): 目标文件编码
            max_concurrent (int): 最大并发数
            
        Returns:
            Dict[str, Any]: 批量转换结果
        """
        start_time = datetime.now()
        
        try:
            # 创建转换任务
            tasks = []
            for mapping in file_mappings:
                source_path = mapping['source']
                target_path = mapping['target']
                format_override = mapping.get('format', target_format)
                
                task = self.convert_file(
                    source_path, target_path, format_override, encoding
                )
                tasks.append(task)
            
            # 限制并发数执行
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def limited_convert(task):
                async with semaphore:
                    return await task
            
            # 执行所有转换任务
            results = await asyncio.gather(
                *[limited_convert(task) for task in tasks],
                return_exceptions=True
            )
            
            # 统计结果
            successful = sum(1 for r in results if isinstance(r, dict) and r.get('status') == 'success')
            failed = len(results) - successful
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return {
                'status': 'success',
                'total_files': len(file_mappings),
                'successful_conversions': successful,
                'failed_conversions': failed,
                'results': results,
                'processing_time': processing_time,
                'metadata': {
                    'target_format': target_format,
                    'encoding': encoding,
                    'max_concurrent': max_concurrent,
                    'timestamp': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"Batch conversion failed: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'processing_time': (datetime.now() - start_time).total_seconds()
            }
    
    async def convert_encoding(self, file_path: str, target_encoding: str = 'utf-8',
                              backup: bool = True) -> Dict[str, Any]:
        """
        转换文件编码
        
        Args:
            file_path (str): 文件路径
            target_encoding (str): 目标编码
            backup (bool): 是否创建备份
            
        Returns:
            Dict[str, Any]: 转换结果
        """
        try:
            # 安全验证
            security_result = self.security_manager.validate_operation(file_path, 'write')
            if not security_result.get('allowed', False):
                raise PermissionError(f"File access denied: {file_path}")
            
            # 检测当前编码
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                detected = chardet.detect(raw_data)
                source_encoding = detected['encoding']
                confidence = detected['confidence']
            
            if source_encoding.lower() == target_encoding.lower():
                return {
                    'status': 'success',
                    'message': 'File already in target encoding',
                    'source_encoding': source_encoding,
                    'target_encoding': target_encoding
                }
            
            # 创建备份
            backup_path = None
            if backup:
                backup_path = f"{file_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                # 使用shutil复制文件
                import shutil
                shutil.copy2(file_path, backup_path)
            
            # 转换编码
            content = raw_data.decode(source_encoding)
            
            # 写入新编码
            with open(file_path, 'w', encoding=target_encoding) as f:
                f.write(content)
            
            return {
                'status': 'success',
                'source_encoding': source_encoding,
                'target_encoding': target_encoding,
                'confidence': confidence,
                'backup_path': backup_path,
                'file_size': len(raw_data)
            }
            
        except Exception as e:
            logger.error(f"Encoding conversion failed for {file_path}: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'file_path': file_path
            }
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """获取支持的格式列表"""
        return self.supported_formats.copy()
    
    def get_conversion_stats(self) -> Dict[str, Any]:
        """获取转换统计信息"""
        return self.conversion_stats.copy()
    
    def reset_stats(self) -> None:
        """重置转换统计"""
        self.conversion_stats = {
            'total_conversions': 0,
            'successful_conversions': 0,
            'failed_conversions': 0,
            'formats_converted': {},
            'last_conversion': None
        }

    def _detect_file_format(self, file_path: Union[str, Path]) -> str:
        """
        检测文件格式

        Args:
            file_path (Union[str, Path]): 文件路径

        Returns:
            str: 文件格式
        """
        path = Path(file_path)
        extension = path.suffix.lower().lstrip('.')

        # 特殊处理一些扩展名
        format_mapping = {
            'yml': 'yaml',
            'cfg': 'ini',
            'conf': 'ini',
            'tsv': 'csv'
        }

        return format_mapping.get(extension, extension)

    async def _parse_content(self, content: str, format_type: str) -> Any:
        """
        解析文件内容

        Args:
            content (str): 文件内容
            format_type (str): 文件格式

        Returns:
            Any: 解析后的数据
        """
        try:
            if format_type == 'json':
                return json.loads(content)

            elif format_type == 'yaml':
                if not YAML_AVAILABLE:
                    raise ImportError("PyYAML is required for YAML support")
                return yaml.safe_load(content)

            elif format_type == 'ini':
                parser = configparser.ConfigParser()
                parser.read_string(content)
                # 转换为字典
                result = {}
                for section in parser.sections():
                    result[section] = dict(parser.items(section))
                return result

            elif format_type == 'toml':
                if not TOML_AVAILABLE:
                    raise ImportError("toml library is required for TOML support")
                return toml.loads(content)

            elif format_type == 'xml':
                root = ET.fromstring(content)
                return self._xml_to_dict(root)

            elif format_type == 'csv':
                reader = csv.DictReader(StringIO(content))
                return list(reader)

            elif format_type == 'tsv':
                reader = csv.DictReader(StringIO(content), delimiter='\t')
                return list(reader)

            elif format_type in ['txt', 'md', 'rst', 'log']:
                # 对于文本文件，返回行列表
                return {
                    'content': content,
                    'lines': content.splitlines(),
                    'line_count': len(content.splitlines()),
                    'char_count': len(content)
                }

            else:
                # 未知格式，返回原始内容
                return {'content': content, 'format': 'unknown'}

        except Exception as e:
            logger.error(f"Failed to parse {format_type} content: {str(e)}")
            raise ValueError(f"Failed to parse {format_type} content: {str(e)}")

    async def _convert_to_format(self, data: Any, target_format: str,
                               preserve_structure: bool = True) -> str:
        """
        将数据转换为目标格式

        Args:
            data (Any): 源数据
            target_format (str): 目标格式
            preserve_structure (bool): 是否保持结构

        Returns:
            str: 转换后的内容
        """
        try:
            if target_format == 'json':
                return json.dumps(data, indent=2, ensure_ascii=False)

            elif target_format == 'yaml':
                if not YAML_AVAILABLE:
                    raise ImportError("PyYAML is required for YAML support")
                return yaml.dump(data, default_flow_style=False, allow_unicode=True, indent=2)

            elif target_format == 'ini':
                if isinstance(data, dict):
                    parser = configparser.ConfigParser()
                    for section_name, section_data in data.items():
                        if isinstance(section_data, dict):
                            parser.add_section(section_name)
                            for key, value in section_data.items():
                                parser.set(section_name, key, str(value))

                    output = StringIO()
                    parser.write(output)
                    return output.getvalue()
                else:
                    raise ValueError("INI format requires dictionary data")

            elif target_format == 'toml':
                if not TOML_AVAILABLE:
                    raise ImportError("toml library is required for TOML support")
                return toml.dumps(data)

            elif target_format == 'xml':
                return self._dict_to_xml(data)

            elif target_format == 'csv':
                if isinstance(data, list) and data and isinstance(data[0], dict):
                    output = StringIO()
                    writer = csv.DictWriter(output, fieldnames=data[0].keys())
                    writer.writeheader()
                    writer.writerows(data)
                    return output.getvalue()
                else:
                    raise ValueError("CSV format requires list of dictionaries")

            elif target_format == 'tsv':
                if isinstance(data, list) and data and isinstance(data[0], dict):
                    output = StringIO()
                    writer = csv.DictWriter(output, fieldnames=data[0].keys(), delimiter='\t')
                    writer.writeheader()
                    writer.writerows(data)
                    return output.getvalue()
                else:
                    raise ValueError("TSV format requires list of dictionaries")

            elif target_format in ['txt', 'md', 'rst', 'log']:
                if isinstance(data, dict) and 'content' in data:
                    return data['content']
                elif isinstance(data, dict) and 'lines' in data:
                    return '\n'.join(data['lines'])
                elif isinstance(data, list):
                    return '\n'.join(str(item) for item in data)
                else:
                    return str(data)

            else:
                # 未知格式，尝试转换为字符串
                if isinstance(data, dict) and 'content' in data:
                    return data['content']
                else:
                    return str(data)

        except Exception as e:
            logger.error(f"Failed to convert to {target_format}: {str(e)}")
            raise ValueError(f"Failed to convert to {target_format}: {str(e)}")

    def _xml_to_dict(self, element: ET.Element) -> Dict[str, Any]:
        """将XML元素转换为字典"""
        result = {}

        # 处理属性
        if element.attrib:
            result['@attributes'] = element.attrib

        # 处理文本内容
        if element.text and element.text.strip():
            if len(element) == 0:  # 叶子节点
                return element.text.strip()
            else:
                result['#text'] = element.text.strip()

        # 处理子元素
        for child in element:
            child_data = self._xml_to_dict(child)

            if child.tag in result:
                # 如果已存在同名标签，转换为列表
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data

        return result or element.text

    def _dict_to_xml(self, data: Any, root_name: str = 'root') -> str:
        """将字典转换为XML"""
        def build_element(name: str, value: Any) -> ET.Element:
            element = ET.Element(name)

            if isinstance(value, dict):
                # 处理属性
                if '@attributes' in value:
                    element.attrib.update(value['@attributes'])

                # 处理文本内容
                if '#text' in value:
                    element.text = str(value['#text'])

                # 处理子元素
                for key, val in value.items():
                    if key not in ['@attributes', '#text']:
                        if isinstance(val, list):
                            for item in val:
                                element.append(build_element(key, item))
                        else:
                            element.append(build_element(key, val))

            elif isinstance(value, list):
                for item in value:
                    element.append(build_element('item', item))

            else:
                element.text = str(value)

            return element

        root = build_element(root_name, data)
        return ET.tostring(root, encoding='unicode', xml_declaration=True)

    async def _validate_conversion(self, source_path: str, target_path: str,
                                 source_format: str, target_format: str) -> Dict[str, Any]:
        """
        验证转换结果

        Args:
            source_path (str): 源文件路径
            target_path (str): 目标文件路径
            source_format (str): 源格式
            target_format (str): 目标格式

        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 检查目标文件是否存在
            target_exists = Path(target_path).exists()
            if not target_exists:
                return {
                    'valid': False,
                    'error': 'Target file does not exist'
                }

            # 读取目标文件并尝试解析
            target_result = await self.file_operations.read_file(target_path)
            if not target_result.get('success'):
                return {
                    'valid': False,
                    'error': f"Cannot read target file: {target_result.get('error')}"
                }

            target_content = target_result['content']

            # 尝试解析目标文件
            try:
                parsed_target = await self._parse_content(target_content, target_format)
                parse_success = True
                parse_error = None
            except Exception as e:
                parse_success = False
                parse_error = str(e)

            # 获取文件大小信息
            source_size = Path(source_path).stat().st_size
            target_size = Path(target_path).stat().st_size

            return {
                'valid': parse_success,
                'parse_error': parse_error,
                'source_size': source_size,
                'target_size': target_size,
                'size_ratio': target_size / source_size if source_size > 0 else 0,
                'target_exists': target_exists
            }

        except Exception as e:
            return {
                'valid': False,
                'error': f"Validation error: {str(e)}"
            }

    def _update_stats(self, status: str, source_format: Optional[str] = None,
                     target_format: Optional[str] = None) -> None:
        """更新转换统计"""
        self.conversion_stats['total_conversions'] += 1

        if status == 'successful':
            self.conversion_stats['successful_conversions'] += 1
        elif status == 'failed':
            self.conversion_stats['failed_conversions'] += 1

        if source_format and target_format:
            conversion_key = f"{source_format}_to_{target_format}"
            if conversion_key not in self.conversion_stats['formats_converted']:
                self.conversion_stats['formats_converted'][conversion_key] = 0
            self.conversion_stats['formats_converted'][conversion_key] += 1

        self.conversion_stats['last_conversion'] = datetime.now().isoformat()
