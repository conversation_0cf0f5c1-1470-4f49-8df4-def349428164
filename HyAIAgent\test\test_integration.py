"""
HyAIAgent 第二阶段系统集成测试

测试所有组件的集成和自主执行循环的功能。
"""

import asyncio
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def test_component_imports():
    """测试组件导入"""
    print("🔍 测试组件导入...")
    
    try:
        # 测试第一阶段组件
        from core.config_manager import ConfigManager
        from core.ai_client import SimpleAIClient
        from core.kv_store import KVStore
        from core.prompt_manager import PromptManager
        print("✅ 第一阶段组件导入成功")
        
        # 测试第二阶段组件
        from core.task_manager import TaskManager
        from core.execution_engine import ExecutionEngine
        from core.decision_engine import DecisionEngine
        from core.context_manager import ContextManager
        from core.task_models import Task, TaskStatus, ExecutionPlan
        print("✅ 第二阶段核心组件导入成功")
        
        # 测试操作和提示词组件
        from operations.base_operation import BaseOperation
        from operations.system_operations import SystemOperations
        from prompts.task_prompts import TaskPrompts
        print("✅ 操作和提示词组件导入成功")
        
        # 测试自主代理
        from core.autonomous_agent import AutonomousAgent
        print("✅ 自主代理组件导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 组件导入失败: {str(e)}")
        return False


async def test_basic_initialization():
    """测试基础初始化"""
    print("\n🔍 测试基础组件初始化...")

    try:
        # 重新导入组件
        from core.config_manager import ConfigManager
        from core.ai_client import SimpleAIClient
        from core.kv_store import KVStore
        from core.prompt_manager import PromptManager

        # 初始化配置管理器
        config_manager = ConfigManager("config.json")
        print("✅ ConfigManager初始化成功")

        # 初始化AI客户端
        ai_client = SimpleAIClient(config_manager)
        print("✅ SimpleAIClient初始化成功")

        # 初始化KV存储
        kv_store = KVStore("data/test_kv_store.json")
        print("✅ KVStore初始化成功")

        # 初始化提示词管理器
        prompt_manager = PromptManager("prompts")
        print("✅ PromptManager初始化成功")

        return True, (config_manager, ai_client, kv_store, prompt_manager)

    except Exception as e:
        print(f"❌ 基础组件初始化失败: {str(e)}")
        return False, None


async def test_second_stage_components(components):
    """测试第二阶段组件"""
    print("\n🔍 测试第二阶段组件初始化...")

    config_manager, ai_client, kv_store, prompt_manager = components

    try:
        # 重新导入第二阶段组件
        from core.task_manager import TaskManager
        from core.execution_engine import ExecutionEngine
        from core.decision_engine import DecisionEngine
        from core.context_manager import ContextManager
        from operations.system_operations import SystemOperations
        from prompts.task_prompts import TaskPrompts

        # 初始化任务管理器
        task_manager = TaskManager(ai_client, prompt_manager, kv_store)
        print("✅ TaskManager初始化成功")

        # 初始化执行引擎
        execution_engine = ExecutionEngine(ai_client, prompt_manager)
        print("✅ ExecutionEngine初始化成功")

        # 初始化决策引擎
        decision_engine = DecisionEngine(ai_client, prompt_manager)
        print("✅ DecisionEngine初始化成功")

        # 初始化上下文管理器
        context_manager = ContextManager(kv_store)
        print("✅ ContextManager初始化成功")

        # 初始化操作组件
        system_operations = SystemOperations()
        print("✅ SystemOperations初始化成功")

        # 初始化提示词组件
        task_prompts = TaskPrompts()
        print("✅ TaskPrompts初始化成功")

        return True, (task_manager, execution_engine, decision_engine,
                     context_manager, system_operations, task_prompts)

    except Exception as e:
        print(f"❌ 第二阶段组件初始化失败: {str(e)}")
        return False, None


async def test_autonomous_agent():
    """测试自主代理"""
    print("\n🔍 测试自主代理初始化...")
    
    try:
        from core.autonomous_agent import AutonomousAgent
        
        # 初始化自主代理
        agent = AutonomousAgent("config.json")
        print("✅ AutonomousAgent初始化成功")
        
        # 测试状态获取
        status = agent.get_status()
        print(f"✅ 代理状态获取成功: {status['state']}")
        
        # 测试详细状态
        detailed_status = agent.get_detailed_status()
        print(f"✅ 详细状态获取成功: {len(detailed_status)} 个字段")
        
        return True, agent
        
    except Exception as e:
        print(f"❌ 自主代理测试失败: {str(e)}")
        return False, None


async def test_task_models():
    """测试任务模型"""
    print("\n🔍 测试任务模型...")
    
    try:
        from core.task_models import Task, TaskStatus, ExecutionPlan, ExecutionResult
        
        # 创建测试任务
        task = Task(
            id="test-task-1",
            title="测试任务",
            description="这是一个测试任务",
            task_type="analysis",
            priority=1
        )
        print(f"✅ Task创建成功: {task.title}")
        
        # 创建执行计划
        plan = ExecutionPlan(
            id="test-plan-1",
            name="测试执行计划",
            tasks=[task]
        )
        print(f"✅ ExecutionPlan创建成功: {len(plan.tasks)} 个任务")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务模型测试失败: {str(e)}")
        return False


async def test_prompt_system():
    """测试提示词系统"""
    print("\n🔍 测试提示词系统...")
    
    try:
        from prompts.task_prompts import TaskPrompts, TaskType, PromptCategory
        
        # 初始化提示词系统
        task_prompts = TaskPrompts()
        
        # 测试获取提示词
        prompt = task_prompts.get_prompt(TaskType.ANALYSIS, PromptCategory.TASK_ANALYSIS)
        print(f"✅ 提示词获取成功: {len(prompt)} 字符")
        
        # 测试可用提示词列表
        available = task_prompts.get_available_prompts()
        print(f"✅ 可用提示词列表: {len(available)} 个类别")
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词系统测试失败: {str(e)}")
        return False


async def run_integration_tests():
    """运行集成测试"""
    print("🚀 开始HyAIAgent第二阶段系统集成测试")
    print("=" * 60)
    
    # 测试组件导入
    if not await test_component_imports():
        return False
    
    # 测试基础初始化
    success, components = await test_basic_initialization()
    if not success:
        return False
    
    # 测试第二阶段组件
    success, stage2_components = await test_second_stage_components(components)
    if not success:
        return False
    
    # 测试任务模型
    if not await test_task_models():
        return False
    
    # 测试提示词系统
    if not await test_prompt_system():
        return False
    
    # 测试自主代理
    success, agent = await test_autonomous_agent()
    if not success:
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有集成测试通过！")
    print("✅ HyAIAgent第二阶段系统集成成功")
    print("📊 测试覆盖:")
    print("   - 组件导入: ✅")
    print("   - 基础初始化: ✅")
    print("   - 第二阶段组件: ✅")
    print("   - 任务模型: ✅")
    print("   - 提示词系统: ✅")
    print("   - 自主代理: ✅")
    
    return True


async def main():
    """主函数"""
    try:
        # 确保必要目录存在
        Path("data").mkdir(exist_ok=True)
        Path("logs").mkdir(exist_ok=True)
        
        # 运行集成测试
        success = await run_integration_tests()
        
        if success:
            print("\n🎯 系统已准备就绪，可以进行步骤2.7的详细测试！")
            return 0
        else:
            print("\n❌ 集成测试失败，需要修复问题后重试")
            return 1
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {str(e)}")
        logger.exception("集成测试异常")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
