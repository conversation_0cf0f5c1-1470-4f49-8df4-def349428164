"""
学习系统核心功能模块

该模块实现了自适应学习系统的核心功能，包括：
- 从用户交互中学习和改进
- 用户偏好管理和个性化推荐
- 响应质量改进机制
- 行为模式适应算法
- 学习数据的持久化存储

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import logging
import time
import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Tuple, Any, Union
from dataclasses import dataclass, field, asdict
from enum import Enum
from collections import defaultdict, deque, Counter

from core.ai_client import SimpleAIClient
from core.prompt_manager import PromptManager
from core.kv_store import KVStore


class InteractionType(Enum):
    """交互类型枚举"""
    QUESTION = "question"  # 问题询问
    TASK_REQUEST = "task_request"  # 任务请求
    FEEDBACK = "feedback"  # 用户反馈
    CORRECTION = "correction"  # 纠正
    PREFERENCE = "preference"  # 偏好设置
    RATING = "rating"  # 评分
    COMPLAINT = "complaint"  # 投诉
    PRAISE = "praise"  # 表扬


class LearningMode(Enum):
    """学习模式枚举"""
    PASSIVE = "passive"  # 被动学习
    ACTIVE = "active"  # 主动学习
    REINFORCEMENT = "reinforcement"  # 强化学习
    SUPERVISED = "supervised"  # 监督学习
    UNSUPERVISED = "unsupervised"  # 无监督学习


class PreferenceCategory(Enum):
    """偏好类别枚举"""
    COMMUNICATION_STYLE = "communication_style"  # 沟通风格
    RESPONSE_LENGTH = "response_length"  # 回复长度
    DETAIL_LEVEL = "detail_level"  # 详细程度
    TASK_APPROACH = "task_approach"  # 任务处理方式
    CONTENT_TYPE = "content_type"  # 内容类型偏好
    INTERACTION_FREQUENCY = "interaction_frequency"  # 交互频率
    LANGUAGE_STYLE = "language_style"  # 语言风格
    TOPIC_INTEREST = "topic_interest"  # 话题兴趣


@dataclass
class UserInteraction:
    """用户交互数据模型"""
    interaction_id: str
    user_id: str
    timestamp: datetime
    interaction_type: InteractionType
    content: str
    context: Dict[str, Any] = field(default_factory=dict)
    response: Optional[str] = None
    response_time: Optional[float] = None
    user_satisfaction: Optional[float] = None  # 0-1之间的满意度
    feedback: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['interaction_type'] = self.interaction_type.value
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserInteraction':
        """从字典创建实例"""
        data = data.copy()
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        data['interaction_type'] = InteractionType(data['interaction_type'])
        return cls(**data)


@dataclass
class UserPreference:
    """用户偏好数据模型"""
    user_id: str
    category: PreferenceCategory
    preference_key: str
    preference_value: Any
    confidence: float = 0.5  # 偏好置信度
    last_updated: datetime = field(default_factory=datetime.now)
    update_count: int = 1
    source: str = "inferred"  # explicit, inferred, learned
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['category'] = self.category.value
        data['last_updated'] = self.last_updated.isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserPreference':
        """从字典创建实例"""
        data = data.copy()
        data['category'] = PreferenceCategory(data['category'])
        data['last_updated'] = datetime.fromisoformat(data['last_updated'])
        return cls(**data)


@dataclass
class ResponseFeedback:
    """响应反馈数据模型"""
    feedback_id: str
    user_id: str
    response_id: str
    timestamp: datetime
    rating: float  # 1-5的评分
    feedback_text: Optional[str] = None
    improvement_suggestions: List[str] = field(default_factory=list)
    positive_aspects: List[str] = field(default_factory=list)
    negative_aspects: List[str] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ResponseFeedback':
        """从字典创建实例"""
        data = data.copy()
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


@dataclass
class UsagePattern:
    """使用模式数据模型"""
    pattern_id: str
    user_id: str
    pattern_type: str  # daily, weekly, task_based, etc.
    pattern_data: Dict[str, Any]
    frequency: int
    confidence: float
    last_observed: datetime
    first_observed: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['last_observed'] = self.last_observed.isoformat()
        data['first_observed'] = self.first_observed.isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UsagePattern':
        """从字典创建实例"""
        data = data.copy()
        data['last_observed'] = datetime.fromisoformat(data['last_observed'])
        data['first_observed'] = datetime.fromisoformat(data['first_observed'])
        return cls(**data)


@dataclass
class LearningStats:
    """学习统计数据模型"""
    total_interactions: int = 0
    successful_adaptations: int = 0
    failed_adaptations: int = 0
    user_satisfaction_avg: float = 0.0
    learning_accuracy: float = 0.0
    preference_updates: int = 0
    pattern_discoveries: int = 0
    last_updated: datetime = field(default_factory=datetime.now)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['last_updated'] = self.last_updated.isoformat()
        return data


class LearningSystem:
    """
    自适应学习系统

    该类实现了从用户交互中学习和改进的核心功能，包括：
    - 用户交互数据的收集和分析
    - 用户偏好的学习和管理
    - 响应质量的持续改进
    - 行为模式的识别和适应
    """

    def __init__(self,
                 ai_client: SimpleAIClient,
                 prompt_manager: PromptManager,
                 kv_store: KVStore,
                 learning_mode: LearningMode = LearningMode.ACTIVE,
                 max_interaction_history: int = 10000,
                 preference_confidence_threshold: float = 0.7):
        """
        初始化学习系统

        Args:
            ai_client: AI客户端实例
            prompt_manager: 提示词管理器实例
            kv_store: 键值存储实例
            learning_mode: 学习模式
            max_interaction_history: 最大交互历史记录数
            preference_confidence_threshold: 偏好置信度阈值
        """
        self.ai_client = ai_client
        self.prompt_manager = prompt_manager
        self.kv_store = kv_store
        self.learning_mode = learning_mode
        self.max_interaction_history = max_interaction_history
        self.preference_confidence_threshold = preference_confidence_threshold

        # 内存缓存
        self.interaction_cache: deque = deque(maxlen=1000)
        self.preference_cache: Dict[str, Dict[str, UserPreference]] = defaultdict(dict)
        self.pattern_cache: Dict[str, List[UsagePattern]] = defaultdict(list)

        # 学习统计
        self.learning_stats = LearningStats()

        # 日志设置
        self.logger = logging.getLogger(__name__)

        # 初始化存储键前缀
        self.INTERACTION_PREFIX = "learning:interaction:"
        self.PREFERENCE_PREFIX = "learning:preference:"
        self.PATTERN_PREFIX = "learning:pattern:"
        self.FEEDBACK_PREFIX = "learning:feedback:"
        self.STATS_KEY = "learning:stats"

    async def learn_from_interaction(self, interaction: UserInteraction) -> Dict[str, Any]:
        """
        从用户交互中学习

        Args:
            interaction: 用户交互数据

        Returns:
            Dict[str, Any]: 学习结果，包含学习到的信息和改进建议
        """
        try:
            start_time = time.time()

            # 存储交互数据
            await self._store_interaction(interaction)

            # 更新缓存
            self.interaction_cache.append(interaction)

            # 分析交互模式
            patterns = await self._analyze_interaction_patterns(interaction)

            # 推断用户偏好
            preferences = await self._infer_user_preferences(interaction)

            # 识别改进机会
            improvements = await self._identify_improvements(interaction)

            # 更新学习统计
            self.learning_stats.total_interactions += 1
            self.learning_stats.last_updated = datetime.now()

            # 保存统计信息
            await self._save_learning_stats()

            processing_time = time.time() - start_time

            result = {
                "success": True,
                "interaction_id": interaction.interaction_id,
                "patterns_discovered": len(patterns),
                "preferences_updated": len(preferences),
                "improvements_identified": len(improvements),
                "processing_time": processing_time,
                "patterns": [pattern.to_dict() for pattern in patterns],
                "preferences": [pref.to_dict() for pref in preferences],
                "improvements": improvements
            }

            self.logger.info(f"学习完成 - 交互ID: {interaction.interaction_id}, "
                           f"发现模式: {len(patterns)}, 更新偏好: {len(preferences)}")

            return result

        except Exception as e:
            self.logger.error(f"学习过程出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "interaction_id": interaction.interaction_id
            }

    async def update_preferences(self, user_id: str, preferences: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新用户偏好

        Args:
            user_id: 用户ID
            preferences: 偏好数据字典

        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            updated_preferences = []

            for category_str, pref_data in preferences.items():
                try:
                    category = PreferenceCategory(category_str)
                except ValueError:
                    self.logger.warning(f"未知的偏好类别: {category_str}")
                    continue

                for key, value in pref_data.items():
                    preference = UserPreference(
                        user_id=user_id,
                        category=category,
                        preference_key=key,
                        preference_value=value,
                        confidence=1.0,  # 显式设置的偏好置信度为1.0
                        source="explicit",
                        last_updated=datetime.now()
                    )

                    # 存储偏好
                    await self._store_preference(preference)

                    # 更新缓存
                    cache_key = f"{category.value}:{key}"
                    self.preference_cache[user_id][cache_key] = preference

                    updated_preferences.append(preference)

            # 更新统计
            self.learning_stats.preference_updates += len(updated_preferences)
            await self._save_learning_stats()

            self.logger.info(f"用户偏好更新完成 - 用户: {user_id}, 更新数量: {len(updated_preferences)}")

            return {
                "success": True,
                "user_id": user_id,
                "updated_count": len(updated_preferences),
                "preferences": [pref.to_dict() for pref in updated_preferences]
            }

        except Exception as e:
            self.logger.error(f"更新用户偏好出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id
            }

    async def improve_responses(self, feedback: ResponseFeedback) -> Dict[str, Any]:
        """
        基于反馈改进响应质量

        Args:
            feedback: 响应反馈数据

        Returns:
            Dict[str, Any]: 改进结果
        """
        try:
            # 存储反馈数据
            await self._store_feedback(feedback)

            # 分析反馈内容
            analysis = await self._analyze_feedback(feedback)

            # 生成改进建议
            improvements = await self._generate_improvement_suggestions(feedback, analysis)

            # 更新响应策略
            strategy_updates = await self._update_response_strategies(feedback, improvements)

            # 更新学习统计
            if feedback.rating >= 4.0:
                self.learning_stats.successful_adaptations += 1
            else:
                self.learning_stats.failed_adaptations += 1

            # 更新平均满意度
            await self._update_satisfaction_average(feedback.rating)

            await self._save_learning_stats()

            self.logger.info(f"响应改进完成 - 反馈ID: {feedback.feedback_id}, "
                           f"评分: {feedback.rating}, 改进建议: {len(improvements)}")

            return {
                "success": True,
                "feedback_id": feedback.feedback_id,
                "analysis": analysis,
                "improvements": improvements,
                "strategy_updates": strategy_updates,
                "rating": feedback.rating
            }

        except Exception as e:
            self.logger.error(f"响应改进出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "feedback_id": feedback.feedback_id
            }

    async def adapt_behavior(self, usage_patterns: List[UsagePattern]) -> Dict[str, Any]:
        """
        根据使用模式适应行为

        Args:
            usage_patterns: 使用模式列表

        Returns:
            Dict[str, Any]: 适应结果
        """
        try:
            adaptations = []

            for pattern in usage_patterns:
                # 存储使用模式
                await self._store_usage_pattern(pattern)

                # 更新缓存
                self.pattern_cache[pattern.user_id].append(pattern)

                # 分析模式并生成适应策略
                adaptation = await self._generate_behavior_adaptation(pattern)
                if adaptation:
                    adaptations.append(adaptation)

            # 应用适应策略
            applied_adaptations = await self._apply_behavior_adaptations(adaptations)

            # 更新统计
            self.learning_stats.pattern_discoveries += len(usage_patterns)
            await self._save_learning_stats()

            self.logger.info(f"行为适应完成 - 模式数量: {len(usage_patterns)}, "
                           f"应用适应: {len(applied_adaptations)}")

            return {
                "success": True,
                "patterns_processed": len(usage_patterns),
                "adaptations_generated": len(adaptations),
                "adaptations_applied": len(applied_adaptations),
                "adaptations": adaptations
            }

        except Exception as e:
            self.logger.error(f"行为适应出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "patterns_count": len(usage_patterns)
            }

    async def get_user_preferences(self, user_id: str,
                                 category: Optional[PreferenceCategory] = None) -> Dict[str, Any]:
        """
        获取用户偏好

        Args:
            user_id: 用户ID
            category: 偏好类别（可选）

        Returns:
            Dict[str, Any]: 用户偏好数据
        """
        try:
            # 从缓存获取
            cached_preferences = self.preference_cache.get(user_id, {})

            # 从存储获取
            stored_preferences = await self._load_user_preferences(user_id, category)

            # 合并偏好数据
            all_preferences = {}

            # 添加存储的偏好
            for pref in stored_preferences:
                key = f"{pref.category.value}:{pref.preference_key}"
                all_preferences[key] = pref

            # 更新缓存中的偏好
            for key, pref in cached_preferences.items():
                if category is None or pref.category == category:
                    all_preferences[key] = pref

            # 按类别组织偏好
            organized_preferences = defaultdict(dict)
            for pref in all_preferences.values():
                if category is None or pref.category == category:
                    organized_preferences[pref.category.value][pref.preference_key] = {
                        "value": pref.preference_value,
                        "confidence": pref.confidence,
                        "source": pref.source,
                        "last_updated": pref.last_updated.isoformat()
                    }

            return {
                "success": True,
                "user_id": user_id,
                "category": category.value if category else "all",
                "preferences": dict(organized_preferences),
                "total_preferences": len(all_preferences)
            }

        except Exception as e:
            self.logger.error(f"获取用户偏好出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id
            }

    async def get_learning_stats(self) -> Dict[str, Any]:
        """
        获取学习统计信息

        Returns:
            Dict[str, Any]: 学习统计数据
        """
        try:
            # 计算学习准确率
            total_adaptations = (self.learning_stats.successful_adaptations +
                               self.learning_stats.failed_adaptations)
            if total_adaptations > 0:
                self.learning_stats.learning_accuracy = (
                    self.learning_stats.successful_adaptations / total_adaptations
                )

            stats_dict = self.learning_stats.to_dict()

            # 添加额外统计信息
            stats_dict.update({
                "cache_size": {
                    "interactions": len(self.interaction_cache),
                    "preferences": sum(len(prefs) for prefs in self.preference_cache.values()),
                    "patterns": sum(len(patterns) for patterns in self.pattern_cache.values())
                },
                "learning_mode": self.learning_mode.value,
                "preference_confidence_threshold": self.preference_confidence_threshold
            })

            return {
                "success": True,
                "stats": stats_dict
            }

        except Exception as e:
            self.logger.error(f"获取学习统计出错: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    # 私有方法
    async def _store_interaction(self, interaction: UserInteraction) -> None:
        """存储交互数据"""
        key = f"{self.INTERACTION_PREFIX}{interaction.user_id}:{interaction.interaction_id}"
        self.kv_store.set(key, interaction.to_dict(), ttl=86400 * 30)  # 30天TTL

    async def _store_preference(self, preference: UserPreference) -> None:
        """存储用户偏好"""
        key = f"{self.PREFERENCE_PREFIX}{preference.user_id}:{preference.category.value}:{preference.preference_key}"
        self.kv_store.set(key, preference.to_dict())

    async def _store_feedback(self, feedback: ResponseFeedback) -> None:
        """存储反馈数据"""
        key = f"{self.FEEDBACK_PREFIX}{feedback.user_id}:{feedback.feedback_id}"
        self.kv_store.set(key, feedback.to_dict(), ttl=86400 * 90)  # 90天TTL

    async def _store_usage_pattern(self, pattern: UsagePattern) -> None:
        """存储使用模式"""
        key = f"{self.PATTERN_PREFIX}{pattern.user_id}:{pattern.pattern_id}"
        self.kv_store.set(key, pattern.to_dict(), ttl=86400 * 60)  # 60天TTL

    async def _save_learning_stats(self) -> None:
        """保存学习统计信息"""
        self.kv_store.set(self.STATS_KEY, self.learning_stats.to_dict())

    async def _analyze_interaction_patterns(self, interaction: UserInteraction) -> List[UsagePattern]:
        """分析交互模式"""
        patterns = []

        try:
            # 时间模式分析
            hour = interaction.timestamp.hour
            weekday = interaction.timestamp.weekday()

            # 创建时间使用模式
            time_pattern = UsagePattern(
                pattern_id=f"time_{interaction.user_id}_{hour}",
                user_id=interaction.user_id,
                pattern_type="time_based",
                pattern_data={
                    "hour": hour,
                    "weekday": weekday,
                    "interaction_type": interaction.interaction_type.value
                },
                frequency=1,
                confidence=0.5,
                last_observed=interaction.timestamp,
                first_observed=interaction.timestamp
            )
            patterns.append(time_pattern)

            # 内容长度模式分析
            content_length = len(interaction.content)
            length_category = "short" if content_length < 50 else "medium" if content_length < 200 else "long"

            length_pattern = UsagePattern(
                pattern_id=f"length_{interaction.user_id}_{length_category}",
                user_id=interaction.user_id,
                pattern_type="content_length",
                pattern_data={
                    "length_category": length_category,
                    "actual_length": content_length,
                    "interaction_type": interaction.interaction_type.value
                },
                frequency=1,
                confidence=0.6,
                last_observed=interaction.timestamp,
                first_observed=interaction.timestamp
            )
            patterns.append(length_pattern)

        except Exception as e:
            self.logger.error(f"分析交互模式出错: {str(e)}")

        return patterns

    async def _infer_user_preferences(self, interaction: UserInteraction) -> List[UserPreference]:
        """推断用户偏好"""
        preferences = []

        try:
            # 基于交互类型推断沟通风格偏好
            if interaction.interaction_type == InteractionType.QUESTION:
                if "详细" in interaction.content or "具体" in interaction.content:
                    pref = UserPreference(
                        user_id=interaction.user_id,
                        category=PreferenceCategory.DETAIL_LEVEL,
                        preference_key="detail_preference",
                        preference_value="high",
                        confidence=0.7,
                        source="inferred"
                    )
                    preferences.append(pref)

            # 基于内容长度推断回复长度偏好
            content_length = len(interaction.content)
            if content_length > 200:
                pref = UserPreference(
                    user_id=interaction.user_id,
                    category=PreferenceCategory.RESPONSE_LENGTH,
                    preference_key="preferred_length",
                    preference_value="detailed",
                    confidence=0.6,
                    source="inferred"
                )
                preferences.append(pref)

        except Exception as e:
            self.logger.error(f"推断用户偏好出错: {str(e)}")

        return preferences

    async def _identify_improvements(self, interaction: UserInteraction) -> List[str]:
        """识别改进机会"""
        improvements = []

        try:
            # 基于响应时间识别改进
            if interaction.response_time and interaction.response_time > 5.0:
                improvements.append("优化响应速度，当前响应时间过长")

            # 基于用户满意度识别改进
            if interaction.user_satisfaction and interaction.user_satisfaction < 0.7:
                improvements.append("提高响应质量，用户满意度较低")

            # 基于反馈内容识别改进
            if interaction.feedback:
                if "不够详细" in interaction.feedback:
                    improvements.append("增加回复的详细程度")
                if "太复杂" in interaction.feedback:
                    improvements.append("简化回复内容，使其更易理解")

        except Exception as e:
            self.logger.error(f"识别改进机会出错: {str(e)}")

        return improvements

    async def _analyze_feedback(self, feedback: ResponseFeedback) -> Dict[str, Any]:
        """分析反馈内容"""
        analysis = {
            "sentiment": "neutral",
            "key_issues": [],
            "positive_points": feedback.positive_aspects,
            "negative_points": feedback.negative_aspects,
            "improvement_areas": []
        }

        try:
            # 简单的情感分析
            if feedback.rating >= 4.0:
                analysis["sentiment"] = "positive"
            elif feedback.rating <= 2.0:
                analysis["sentiment"] = "negative"

            # 分析反馈文本
            if feedback.feedback_text:
                text = feedback.feedback_text.lower()

                # 识别关键问题
                if "慢" in text or "slow" in text:
                    analysis["key_issues"].append("response_speed")
                if "错误" in text or "error" in text or "wrong" in text:
                    analysis["key_issues"].append("accuracy")
                if "不清楚" in text or "unclear" in text:
                    analysis["key_issues"].append("clarity")

                # 识别改进领域
                if "更详细" in text or "more detail" in text:
                    analysis["improvement_areas"].append("detail_level")
                if "简单" in text or "simpler" in text:
                    analysis["improvement_areas"].append("simplification")

        except Exception as e:
            self.logger.error(f"分析反馈内容出错: {str(e)}")

        return analysis

    async def _generate_improvement_suggestions(self, feedback: ResponseFeedback,
                                             analysis: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        suggestions = []

        try:
            # 基于评分生成建议
            if feedback.rating < 3.0:
                suggestions.append("需要全面改进响应质量")
            elif feedback.rating < 4.0:
                suggestions.append("响应质量有待提升")

            # 基于分析结果生成建议
            for issue in analysis["key_issues"]:
                if issue == "response_speed":
                    suggestions.append("优化响应速度，减少处理时间")
                elif issue == "accuracy":
                    suggestions.append("提高回答准确性，加强事实验证")
                elif issue == "clarity":
                    suggestions.append("改善表达清晰度，使用更简洁的语言")

            for area in analysis["improvement_areas"]:
                if area == "detail_level":
                    suggestions.append("增加回复的详细程度和深度")
                elif area == "simplification":
                    suggestions.append("简化回复内容，提高可理解性")

            # 基于改进建议生成具体建议
            for suggestion in feedback.improvement_suggestions:
                suggestions.append(f"用户建议: {suggestion}")

        except Exception as e:
            self.logger.error(f"生成改进建议出错: {str(e)}")

        return suggestions

    async def _update_response_strategies(self, feedback: ResponseFeedback,
                                        improvements: List[str]) -> List[str]:
        """更新响应策略"""
        strategy_updates = []

        try:
            # 基于反馈更新策略
            for improvement in improvements:
                if "响应速度" in improvement:
                    strategy_updates.append("启用快速响应模式")
                elif "详细程度" in improvement:
                    strategy_updates.append("增加回复详细度设置")
                elif "简化" in improvement:
                    strategy_updates.append("启用简化表达模式")

            # 存储策略更新
            if strategy_updates:
                strategy_key = f"strategy:{feedback.user_id}"
                existing_strategies = self.kv_store.get(strategy_key, {})

                for update in strategy_updates:
                    existing_strategies[update] = {
                        "enabled": True,
                        "updated_at": datetime.now().isoformat(),
                        "feedback_id": feedback.feedback_id
                    }

                self.kv_store.set(strategy_key, existing_strategies)

        except Exception as e:
            self.logger.error(f"更新响应策略出错: {str(e)}")

        return strategy_updates

    async def _update_satisfaction_average(self, new_rating: float) -> None:
        """更新平均满意度"""
        try:
            total_interactions = self.learning_stats.total_interactions
            current_avg = self.learning_stats.user_satisfaction_avg

            # 计算新的平均值
            if total_interactions > 0:
                new_avg = ((current_avg * (total_interactions - 1)) + new_rating) / total_interactions
                self.learning_stats.user_satisfaction_avg = new_avg
            else:
                self.learning_stats.user_satisfaction_avg = new_rating

        except Exception as e:
            self.logger.error(f"更新平均满意度出错: {str(e)}")

    async def _generate_behavior_adaptation(self, pattern: UsagePattern) -> Optional[Dict[str, Any]]:
        """生成行为适应策略"""
        try:
            adaptation = None

            if pattern.pattern_type == "time_based":
                # 基于时间模式的适应
                hour = pattern.pattern_data.get("hour", 0)
                if 9 <= hour <= 17:  # 工作时间
                    adaptation = {
                        "type": "working_hours_optimization",
                        "description": "工作时间优化响应",
                        "parameters": {
                            "response_style": "professional",
                            "detail_level": "high"
                        }
                    }
                elif 18 <= hour <= 22:  # 晚间时间
                    adaptation = {
                        "type": "evening_optimization",
                        "description": "晚间时间优化响应",
                        "parameters": {
                            "response_style": "casual",
                            "detail_level": "medium"
                        }
                    }

            elif pattern.pattern_type == "content_length":
                # 基于内容长度的适应
                length_category = pattern.pattern_data.get("length_category")
                if length_category == "long":
                    adaptation = {
                        "type": "detailed_response_optimization",
                        "description": "详细回复优化",
                        "parameters": {
                            "response_length": "detailed",
                            "include_examples": True
                        }
                    }
                elif length_category == "short":
                    adaptation = {
                        "type": "concise_response_optimization",
                        "description": "简洁回复优化",
                        "parameters": {
                            "response_length": "concise",
                            "direct_answers": True
                        }
                    }

            return adaptation

        except Exception as e:
            self.logger.error(f"生成行为适应策略出错: {str(e)}")
            return None

    async def _apply_behavior_adaptations(self, adaptations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """应用行为适应策略"""
        applied_adaptations = []

        try:
            for adaptation in adaptations:
                # 这里可以实现具体的适应策略应用逻辑
                # 例如更新AI客户端的参数、调整提示词等

                # 模拟应用过程
                applied_adaptation = {
                    "adaptation_id": f"adapt_{int(time.time())}",
                    "type": adaptation["type"],
                    "description": adaptation["description"],
                    "parameters": adaptation["parameters"],
                    "applied_at": datetime.now().isoformat(),
                    "status": "applied"
                }

                applied_adaptations.append(applied_adaptation)

                # 可以在这里添加实际的适应逻辑
                # 例如：
                # - 更新AI客户端的温度参数
                # - 调整最大token数
                # - 更新系统提示词

        except Exception as e:
            self.logger.error(f"应用行为适应策略出错: {str(e)}")

        return applied_adaptations

    async def _load_user_preferences(self, user_id: str,
                                   category: Optional[PreferenceCategory] = None) -> List[UserPreference]:
        """从存储加载用户偏好"""
        preferences = []

        try:
            # 构建搜索模式
            if category:
                pattern = f"{self.PREFERENCE_PREFIX}{user_id}:{category.value}:*"
            else:
                pattern = f"{self.PREFERENCE_PREFIX}{user_id}:*"

            # 获取所有匹配的键
            keys = self.kv_store.keys()
            matching_keys = [key for key in keys if key.startswith(pattern.replace("*", ""))]

            # 加载偏好数据
            for key in matching_keys:
                pref_data = self.kv_store.get(key)
                if pref_data:
                    try:
                        preference = UserPreference.from_dict(pref_data)
                        preferences.append(preference)
                    except Exception as e:
                        self.logger.warning(f"加载偏好数据失败 - 键: {key}, 错误: {str(e)}")

        except Exception as e:
            self.logger.error(f"加载用户偏好出错: {str(e)}")

        return preferences

    async def reset_learning_data(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        重置学习数据

        Args:
            user_id: 用户ID，如果为None则重置所有数据

        Returns:
            Dict[str, Any]: 重置结果
        """
        try:
            reset_count = 0

            if user_id:
                # 重置特定用户的数据
                patterns = [
                    f"{self.INTERACTION_PREFIX}{user_id}:*",
                    f"{self.PREFERENCE_PREFIX}{user_id}:*",
                    f"{self.PATTERN_PREFIX}{user_id}:*",
                    f"{self.FEEDBACK_PREFIX}{user_id}:*"
                ]

                for pattern in patterns:
                    keys = self.kv_store.keys()
                    matching_keys = [key for key in keys if key.startswith(pattern.replace("*", ""))]

                    for key in matching_keys:
                        self.kv_store.delete(key)
                        reset_count += 1

                # 清理缓存
                if user_id in self.preference_cache:
                    del self.preference_cache[user_id]
                if user_id in self.pattern_cache:
                    del self.pattern_cache[user_id]

                # 清理交互缓存中的用户数据
                self.interaction_cache = deque([
                    interaction for interaction in self.interaction_cache
                    if interaction.user_id != user_id
                ], maxlen=1000)

            else:
                # 重置所有学习数据
                prefixes = [
                    self.INTERACTION_PREFIX,
                    self.PREFERENCE_PREFIX,
                    self.PATTERN_PREFIX,
                    self.FEEDBACK_PREFIX
                ]

                for prefix in prefixes:
                    keys = self.kv_store.keys()
                    matching_keys = [key for key in keys if key.startswith(prefix)]

                    for key in matching_keys:
                        self.kv_store.delete(key)
                        reset_count += 1

                # 清理所有缓存
                self.interaction_cache.clear()
                self.preference_cache.clear()
                self.pattern_cache.clear()

                # 重置统计信息
                self.learning_stats = LearningStats()
                await self._save_learning_stats()

            self.logger.info(f"学习数据重置完成 - 用户: {user_id or 'all'}, 删除记录: {reset_count}")

            return {
                "success": True,
                "user_id": user_id or "all",
                "reset_count": reset_count
            }

        except Exception as e:
            self.logger.error(f"重置学习数据出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id
            }

    async def export_learning_data(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        导出学习数据

        Args:
            user_id: 用户ID，如果为None则导出所有数据

        Returns:
            Dict[str, Any]: 导出的学习数据
        """
        try:
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "user_id": user_id or "all",
                "interactions": [],
                "preferences": [],
                "patterns": [],
                "feedback": [],
                "stats": self.learning_stats.to_dict()
            }

            # 导出交互数据
            interaction_keys = self.kv_store.keys()
            for key in interaction_keys:
                if key.startswith(self.INTERACTION_PREFIX):
                    if user_id is None or f":{user_id}:" in key:
                        data = self.kv_store.get(key)
                        if data:
                            export_data["interactions"].append(data)

            # 导出偏好数据
            preference_keys = self.kv_store.keys()
            for key in preference_keys:
                if key.startswith(self.PREFERENCE_PREFIX):
                    if user_id is None or f":{user_id}:" in key:
                        data = self.kv_store.get(key)
                        if data:
                            export_data["preferences"].append(data)

            # 导出模式数据
            pattern_keys = self.kv_store.keys()
            for key in pattern_keys:
                if key.startswith(self.PATTERN_PREFIX):
                    if user_id is None or f":{user_id}:" in key:
                        data = self.kv_store.get(key)
                        if data:
                            export_data["patterns"].append(data)

            # 导出反馈数据
            feedback_keys = self.kv_store.keys()
            for key in feedback_keys:
                if key.startswith(self.FEEDBACK_PREFIX):
                    if user_id is None or f":{user_id}:" in key:
                        data = self.kv_store.get(key)
                        if data:
                            export_data["feedback"].append(data)

            self.logger.info(f"学习数据导出完成 - 用户: {user_id or 'all'}")

            return {
                "success": True,
                "export_data": export_data,
                "summary": {
                    "interactions_count": len(export_data["interactions"]),
                    "preferences_count": len(export_data["preferences"]),
                    "patterns_count": len(export_data["patterns"]),
                    "feedback_count": len(export_data["feedback"])
                }
            }

        except Exception as e:
            self.logger.error(f"导出学习数据出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "user_id": user_id
            }
