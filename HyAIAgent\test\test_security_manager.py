"""
SecurityManager测试模块
测试文件操作安全管理器的各项功能
"""

import unittest
import tempfile
import shutil
import json
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from operations.security_manager import SecurityManager


class TestSecurityManager(unittest.TestCase):
    """SecurityManager测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录作为工作空间
        self.temp_dir = tempfile.mkdtemp()
        self.workspace_path = os.path.join(self.temp_dir, "workspace")
        self.config_path = os.path.join(self.temp_dir, "config", "file_security.json")
        
        # 创建测试配置
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        test_config = {
            "allowed_extensions": [".txt", ".md", ".json", ".py"],
            "forbidden_extensions": [".exe", ".bat", ".sh"],
            "max_file_size": 1024,  # 1KB for testing
            "max_path_length": 100,
            "allow_hidden_files": False,
            "audit_enabled": True
        }
        
        with open(self.config_path, 'w') as f:
            json.dump(test_config, f)
        
        # 初始化SecurityManager
        self.security_manager = SecurityManager(
            workspace_path=self.workspace_path,
            config_path=self.config_path
        )
        
        # 创建测试文件
        os.makedirs(self.workspace_path, exist_ok=True)
        self.test_file = os.path.join(self.workspace_path, "test.txt")
        with open(self.test_file, 'w') as f:
            f.write("test content")
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_init(self):
        """测试初始化"""
        self.assertIsInstance(self.security_manager, SecurityManager)
        self.assertTrue(Path(self.workspace_path).exists())
        self.assertIsNotNone(self.security_manager.config)
        self.assertEqual(len(self.security_manager.config["allowed_extensions"]), 4)
    
    def test_validate_path_success(self):
        """测试路径验证成功案例"""
        # 工作目录内的文件
        valid_path = os.path.join(self.workspace_path, "valid_file.txt")
        self.assertTrue(self.security_manager.validate_path(valid_path))
        
        # 子目录中的文件
        subdir_path = os.path.join(self.workspace_path, "subdir", "file.txt")
        self.assertTrue(self.security_manager.validate_path(subdir_path))
    
    def test_validate_path_failure(self):
        """测试路径验证失败案例"""
        # 工作目录外的文件
        outside_path = os.path.join(self.temp_dir, "outside.txt")
        self.assertFalse(self.security_manager.validate_path(outside_path))
        
        # 路径遍历攻击
        traversal_path = os.path.join(self.workspace_path, "..", "outside.txt")
        self.assertFalse(self.security_manager.validate_path(traversal_path))
        
        # 隐藏文件（配置不允许）
        hidden_path = os.path.join(self.workspace_path, ".hidden")
        self.assertFalse(self.security_manager.validate_path(hidden_path))
    
    def test_check_file_type_success(self):
        """测试文件类型检查成功案例"""
        # 允许的扩展名
        self.assertTrue(self.security_manager.check_file_type("test.txt"))
        self.assertTrue(self.security_manager.check_file_type("readme.md"))
        self.assertTrue(self.security_manager.check_file_type("config.json"))
        self.assertTrue(self.security_manager.check_file_type("script.py"))
    
    def test_check_file_type_failure(self):
        """测试文件类型检查失败案例"""
        # 禁止的扩展名
        self.assertFalse(self.security_manager.check_file_type("malware.exe"))
        self.assertFalse(self.security_manager.check_file_type("script.bat"))
        self.assertFalse(self.security_manager.check_file_type("shell.sh"))
        
        # 不在白名单中的扩展名
        self.assertFalse(self.security_manager.check_file_type("image.jpg"))
        self.assertFalse(self.security_manager.check_file_type("video.mp4"))
    
    def test_check_file_permissions(self):
        """测试文件权限检查"""
        # 读权限检查
        self.assertTrue(self.security_manager.check_file_permissions(self.test_file, "read"))
        
        # 写权限检查
        self.assertTrue(self.security_manager.check_file_permissions(self.test_file, "write"))
        
        # 删除权限检查
        self.assertTrue(self.security_manager.check_file_permissions(self.test_file, "delete"))
        
        # 不存在的文件
        non_existent = os.path.join(self.workspace_path, "non_existent.txt")
        self.assertFalse(self.security_manager.check_file_permissions(non_existent, "read"))
        self.assertTrue(self.security_manager.check_file_permissions(non_existent, "write"))  # 可以创建
    
    def test_check_file_size(self):
        """测试文件大小检查"""
        # 正常大小的文件
        self.assertTrue(self.security_manager.check_file_size(self.test_file))
        
        # 创建超大文件
        large_file = os.path.join(self.workspace_path, "large.txt")
        with open(large_file, 'w') as f:
            f.write("x" * 2048)  # 2KB，超过配置的1KB限制
        
        self.assertFalse(self.security_manager.check_file_size(large_file))
    
    def test_validate_operation_success(self):
        """测试综合操作验证成功案例"""
        result = self.security_manager.validate_operation(self.test_file, "read")
        
        self.assertTrue(result["valid"])
        self.assertEqual(result["file_path"], self.test_file)
        self.assertEqual(result["operation"], "read")
        self.assertTrue(all(result["checks"].values()))
        self.assertEqual(len(result["errors"]), 0)
    
    def test_validate_operation_failure(self):
        """测试综合操作验证失败案例"""
        # 使用禁止的文件类型
        bad_file = os.path.join(self.workspace_path, "malware.exe")
        result = self.security_manager.validate_operation(bad_file, "write")
        
        self.assertFalse(result["valid"])
        self.assertFalse(result["checks"]["type_allowed"])
        self.assertIn("File type not allowed", result["errors"])
    
    def test_audit_logging(self):
        """测试审计日志功能"""
        # 执行一些操作
        self.security_manager.validate_path(self.test_file)
        self.security_manager.check_file_type(self.test_file)
        
        # 检查审计日志
        audit_log = self.security_manager.get_audit_log()
        self.assertGreater(len(audit_log), 0)
        
        # 检查日志记录格式
        log_entry = audit_log[0]
        self.assertIn("timestamp", log_entry)
        self.assertIn("operation", log_entry)
        self.assertIn("file_path", log_entry)
        self.assertIn("status", log_entry)
        self.assertIn("details", log_entry)
    
    def test_security_stats(self):
        """测试安全统计信息"""
        # 执行一些操作
        self.security_manager.validate_operation(self.test_file, "read")
        self.security_manager.validate_operation("invalid.exe", "write")
        
        stats = self.security_manager.get_security_stats()
        
        self.assertIn("total_operations", stats)
        self.assertIn("success_rate", stats)
        self.assertIn("success_operations", stats)
        self.assertIn("failed_operations", stats)
        self.assertIn("error_operations", stats)
        self.assertGreater(stats["total_operations"], 0)
    
    def test_config_update(self):
        """测试配置更新"""
        new_config = {
            "max_file_size": 2048,
            "allow_hidden_files": True
        }
        
        result = self.security_manager.update_config(new_config)
        self.assertTrue(result)
        
        # 验证配置已更新
        self.assertEqual(self.security_manager.config["max_file_size"], 2048)
        self.assertTrue(self.security_manager.config["allow_hidden_files"])
    
    def test_clear_audit_log(self):
        """测试清空审计日志"""
        # 先产生一些日志
        self.security_manager.validate_path(self.test_file)
        self.assertGreater(len(self.security_manager.get_audit_log()), 0)
        
        # 清空日志
        self.security_manager.clear_audit_log()
        self.assertEqual(len(self.security_manager.get_audit_log()), 0)
    
    def test_edge_cases(self):
        """测试边界情况"""
        # 空路径
        self.assertFalse(self.security_manager.validate_path(""))

        # None路径
        self.assertFalse(self.security_manager.validate_path(None))

        # 空白路径
        self.assertFalse(self.security_manager.validate_path("   "))

        # 非常长的路径
        long_path = os.path.join(self.workspace_path, "x" * 200)
        self.assertFalse(self.security_manager.validate_path(long_path))


def run_security_manager_tests():
    """运行SecurityManager测试"""
    print("🧪 开始SecurityManager测试...")
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestSecurityManager)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print(f"\n📊 测试结果:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n{'✅ 所有测试通过!' if success else '❌ 存在测试失败!'}")
    
    return success


if __name__ == "__main__":
    run_security_manager_tests()
