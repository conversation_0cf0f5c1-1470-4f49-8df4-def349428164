"""
第三阶段最终验收测试

验证第三阶段基础操作模块的完整功能和集成效果
"""

import asyncio
import sys
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

# 导入所有第三阶段模块
from operations.security_manager import SecurityManager
from operations.file_operations import FileOperations
from operations.document_processor import DocumentProcessor
from operations.text_analyzer import TextAnalyzer
from operations.config_processor import ConfigProcessor
from utils.format_converter import FormatConverter
from operations.batch_processor import BatchProcessor
from operations.file_analyzer import FileAnalyzer
from utils.performance_optimizer import PerformanceOptimizer
from utils.performance_monitor import PerformanceMonitor


class Stage3FinalAcceptanceTest:
    """第三阶段最终验收测试类"""
    
    def __init__(self):
        """初始化测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.workspace_path = Path(self.temp_dir)
        self.test_results = []
        self.start_time = datetime.now()
        
        print(f"🚀 第三阶段最终验收测试开始")
        print(f"📁 测试工作空间: {self.temp_dir}")
        print("=" * 80)
    
    async def test_security_manager(self) -> bool:
        """测试安全管理器"""
        try:
            print("🔒 测试安全管理器...")

            # 创建安全管理器
            security_manager = SecurityManager(str(self.workspace_path))

            # 测试路径验证
            test_file = self.workspace_path / "test.txt"
            test_file.write_text("测试内容", encoding='utf-8')

            # 使用实际的方法
            path_valid = security_manager.validate_path(str(test_file))
            assert path_valid, "路径验证失败"

            # 测试文件类型检查
            type_valid = security_manager.check_file_type(str(test_file))
            assert type_valid, "文件类型检查失败"

            # 测试操作验证
            result = security_manager.validate_operation(str(test_file), "read")
            assert result.get('allowed', False), "操作验证失败"

            print("   ✓ 安全管理器功能正常")
            return True

        except Exception as e:
            print(f"   ❌ 安全管理器测试失败: {e}")
            return False
    
    async def test_file_operations(self) -> bool:
        """测试文件操作"""
        try:
            print("📁 测试文件操作...")

            # 创建文件操作实例
            security_manager = SecurityManager(str(self.workspace_path))
            file_ops = FileOperations(str(self.workspace_path), security_manager)

            # 测试文件写入
            test_content = "这是测试内容\n第二行内容"
            result = await file_ops.write_file("test_write.txt", test_content)
            assert result.get('success', False), f"文件写入失败: {result.get('error', '')}"

            # 测试文件读取
            result = await file_ops.read_file("test_write.txt")
            assert result.get('success', False), f"文件读取失败: {result.get('error', '')}"
            assert result.get('content') == test_content, "文件内容不匹配"

            print("   ✓ 文件操作功能正常")
            return True

        except Exception as e:
            print(f"   ❌ 文件操作测试失败: {e}")
            return False
    
    async def test_document_processor(self) -> bool:
        """测试文档处理器"""
        try:
            print("📄 测试文档处理器...")
            
            # 创建文档处理器
            security_manager = SecurityManager(str(self.workspace_path))
            file_ops = FileOperations(str(self.workspace_path), security_manager)
            doc_processor = DocumentProcessor(str(self.workspace_path), security_manager, file_ops)
            
            # 创建测试文档
            test_content = "# 测试文档\n\n这是一个测试文档。\n\n## 章节1\n内容1"
            test_file = self.workspace_path / "test.md"
            test_file.write_text(test_content, encoding='utf-8')
            
            # 测试文档解析
            result = await doc_processor.parse_document(str(test_file))
            assert result.success, "文档解析失败"
            assert result.metadata is not None, "文档元数据为空"
            
            print("   ✓ 文档处理器功能正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 文档处理器测试失败: {e}")
            return False
    
    async def test_text_analyzer(self) -> bool:
        """测试文本分析器"""
        try:
            print("📝 测试文本分析器...")
            
            # 创建文本分析器
            security_manager = SecurityManager(str(self.workspace_path))
            file_ops = FileOperations(str(self.workspace_path), security_manager)
            text_analyzer = TextAnalyzer(str(self.workspace_path), security_manager, file_ops)
            
            # 创建测试文本
            test_text = "这是一个测试文本。包含多个句子。用于测试文本分析功能。"
            test_file = self.workspace_path / "test_text.txt"
            test_file.write_text(test_text, encoding='utf-8')
            
            # 测试文本分析
            result = await text_analyzer.analyze_text(str(test_file))
            assert result.success, "文本分析失败"
            assert result.analysis is not None, "分析结果为空"
            
            print("   ✓ 文本分析器功能正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 文本分析器测试失败: {e}")
            return False
    
    async def test_config_processor(self) -> bool:
        """测试配置处理器"""
        try:
            print("⚙️ 测试配置处理器...")
            
            # 创建配置处理器
            security_manager = SecurityManager(str(self.workspace_path))
            file_ops = FileOperations(str(self.workspace_path), security_manager)
            config_processor = ConfigProcessor(str(self.workspace_path), security_manager, file_ops)
            
            # 创建测试配置
            test_config = {
                "app_name": "HyAIAgent",
                "version": "1.0.0",
                "settings": {
                    "debug": True,
                    "timeout": 30
                }
            }
            test_file = self.workspace_path / "test_config.json"
            test_file.write_text(json.dumps(test_config, indent=2), encoding='utf-8')
            
            # 测试配置解析
            result = await config_processor.parse_config(str(test_file))
            assert result.success, "配置解析失败"
            assert result.config is not None, "配置数据为空"
            
            print("   ✓ 配置处理器功能正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 配置处理器测试失败: {e}")
            return False
    
    async def test_format_converter(self) -> bool:
        """测试格式转换器"""
        try:
            print("🔄 测试格式转换器...")

            # 创建格式转换器
            security_manager = SecurityManager(str(self.workspace_path))
            file_ops = FileOperations(str(self.workspace_path), security_manager)
            format_converter = FormatConverter(str(self.workspace_path), security_manager, file_ops)

            # 创建测试文件
            test_data = {"name": "测试", "value": 123}
            test_file = self.workspace_path / "test.json"
            test_file.write_text(json.dumps(test_data, ensure_ascii=False), encoding='utf-8')

            # 测试格式转换
            result = await format_converter.convert_file(str(test_file), "yaml")
            assert result.success, "格式转换失败"

            print("   ✓ 格式转换器功能正常")
            return True

        except Exception as e:
            print(f"   ❌ 格式转换器测试失败: {e}")
            return False
    
    async def test_batch_processor(self) -> bool:
        """测试批量处理器"""
        try:
            print("📦 测试批量处理器...")
            
            # 创建批量处理器
            security_manager = SecurityManager(str(self.workspace_path))
            file_ops = FileOperations(str(self.workspace_path), security_manager)
            batch_processor = BatchProcessor(str(self.workspace_path), security_manager, file_ops)
            
            # 创建测试文件
            test_files = []
            for i in range(3):
                file_path = self.workspace_path / f"batch_test_{i}.txt"
                file_path.write_text(f"批量测试内容 {i}", encoding='utf-8')
                test_files.append(str(file_path))
            
            # 测试批量处理
            result = await batch_processor.process_files(test_files, "copy", {"target_dir": "batch_output"})
            assert result.success, "批量处理失败"
            assert result.processed_count > 0, "处理文件数量为0"
            
            print("   ✓ 批量处理器功能正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 批量处理器测试失败: {e}")
            return False
    
    async def test_file_analyzer(self) -> bool:
        """测试文件分析器"""
        try:
            print("🔬 测试文件分析器...")
            
            # 创建文件分析器
            file_analyzer = FileAnalyzer(str(self.workspace_path))
            
            # 创建测试文件
            test_file = self.workspace_path / "analyze_test.txt"
            test_file.write_text("这是用于分析的测试文件内容", encoding='utf-8')
            
            # 测试文件分析
            result = await file_analyzer.analyze_file(str(test_file), ["basic", "content"])
            assert result.success, "文件分析失败"
            assert result.analysis is not None, "分析结果为空"
            
            print("   ✓ 文件分析器功能正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 文件分析器测试失败: {e}")
            return False
    
    async def test_performance_optimization(self) -> bool:
        """测试性能优化"""
        try:
            print("⚡ 测试性能优化...")
            
            # 创建性能优化器
            optimizer = PerformanceOptimizer(str(self.workspace_path))
            
            # 测试性能指标收集
            metrics = await optimizer.collect_metrics()
            assert metrics is not None, "性能指标收集失败"
            
            # 测试系统优化
            result = await optimizer.optimize_system()
            assert result is not None, "系统优化失败"
            
            print("   ✓ 性能优化功能正常")
            return True
            
        except Exception as e:
            print(f"   ❌ 性能优化测试失败: {e}")
            return False
    
    async def test_integration_workflow(self) -> bool:
        """测试集成工作流"""
        try:
            print("🔄 测试集成工作流...")

            # 创建完整的工作流测试
            security_manager = SecurityManager(str(self.workspace_path))
            file_ops = FileOperations(str(self.workspace_path), security_manager)
            doc_processor = DocumentProcessor(str(self.workspace_path), security_manager, file_ops)
            text_analyzer = TextAnalyzer(str(self.workspace_path), security_manager, file_ops)

            # 1. 创建文档
            doc_content = "# 集成测试文档\n\n这是一个集成测试文档。包含多种内容用于测试。"
            result = await file_ops.write_file("integration_test.md", doc_content)
            assert result.success, "文档创建失败"

            # 2. 处理文档
            result = await doc_processor.parse_document("integration_test.md")
            assert result.success, "文档处理失败"

            # 3. 分析文档文本
            result = await text_analyzer.analyze_text("integration_test.md")
            assert result.success, "文本分析失败"

            print("   ✓ 集成工作流功能正常")
            return True

        except Exception as e:
            print(f"   ❌ 集成工作流测试失败: {e}")
            return False
    
    async def run_all_tests(self) -> bool:
        """运行所有验收测试"""
        tests = [
            ("安全管理器", self.test_security_manager),
            ("文件操作", self.test_file_operations),
            ("文档处理器", self.test_document_processor),
            ("文本分析器", self.test_text_analyzer),
            ("配置处理器", self.test_config_processor),
            ("格式转换器", self.test_format_converter),
            ("批量处理器", self.test_batch_processor),
            ("文件分析器", self.test_file_analyzer),
            ("性能优化", self.test_performance_optimization),
            ("集成工作流", self.test_integration_workflow)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                if result:
                    passed_tests += 1
                    self.test_results.append({"name": test_name, "status": "PASS"})
                else:
                    self.test_results.append({"name": test_name, "status": "FAIL"})
            except Exception as e:
                print(f"   ❌ {test_name}测试异常: {e}")
                self.test_results.append({"name": test_name, "status": "ERROR", "error": str(e)})
        
        # 生成测试报告
        self.generate_test_report(passed_tests, total_tests)
        
        return passed_tests == total_tests
    
    def generate_test_report(self, passed_tests: int, total_tests: int):
        """生成测试报告"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        print("\n" + "=" * 80)
        print("📊 第三阶段最终验收测试报告")
        print("=" * 80)
        print(f"⏱️  测试耗时: {duration:.2f}秒")
        print(f"📈 测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"📁 测试工作空间: {self.temp_dir}")
        
        print("\n📋 详细测试结果:")
        for result in self.test_results:
            status_icon = "✅" if result["status"] == "PASS" else "❌"
            print(f"   {status_icon} {result['name']}: {result['status']}")
            if "error" in result:
                print(f"      错误: {result['error']}")
        
        if passed_tests == total_tests:
            print("\n🎉 第三阶段基础操作模块验收测试全部通过！")
            print("✅ HyAIAgent第三阶段开发功能验证成功")
        else:
            failed_tests = total_tests - passed_tests
            print(f"\n❌ {failed_tests}个测试失败，需要进一步修复")
    
    def cleanup(self):
        """清理测试环境"""
        try:
            shutil.rmtree(self.temp_dir)
            print(f"\n🧹 测试环境已清理: {self.temp_dir}")
        except Exception as e:
            print(f"\n⚠️ 清理测试环境失败: {e}")


async def main():
    """主函数"""
    test_suite = Stage3FinalAcceptanceTest()
    
    try:
        success = await test_suite.run_all_tests()
        return 0 if success else 1
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        test_suite.cleanup()


if __name__ == "__main__":
    # 运行最终验收测试
    result = asyncio.run(main())
    exit(result)
