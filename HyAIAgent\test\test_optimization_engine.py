"""
OptimizationEngine 测试模块

测试优化引擎的各项功能，包括规则引擎、智能分析、自动优化等。
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import json
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from HyAIAgent.monitoring.optimization_engine import (
    OptimizationEngine, OptimizationRule, OptimizationSuggestion, OptimizationResult
)


class TestOptimizationRule:
    """OptimizationRule 数据模型测试"""
    
    def test_optimization_rule_creation(self):
        """测试优化规则创建"""
        rule = OptimizationRule(
            rule_id="test_rule",
            rule_name="测试规则",
            category="performance",
            condition="CPU使用率超过80%",
            action="优化CPU使用",
            priority=8,
            enabled=True,
            auto_apply=False
        )
        
        assert rule.rule_id == "test_rule"
        assert rule.rule_name == "测试规则"
        assert rule.category == "performance"
        assert rule.condition == "CPU使用率超过80%"
        assert rule.action == "优化CPU使用"
        assert rule.priority == 8
        assert rule.enabled is True
        assert rule.auto_apply is False
    
    def test_optimization_rule_to_dict(self):
        """测试优化规则转换为字典"""
        rule = OptimizationRule(
            rule_id="test_rule",
            rule_name="测试规则",
            category="performance",
            condition="测试条件",
            action="测试动作",
            priority=5
        )
        
        data = rule.to_dict()
        assert isinstance(data, dict)
        assert data['rule_id'] == "test_rule"
        assert data['rule_name'] == "测试规则"
        assert data['priority'] == 5


class TestOptimizationSuggestion:
    """OptimizationSuggestion 数据模型测试"""
    
    def test_optimization_suggestion_creation(self):
        """测试优化建议创建"""
        timestamp = datetime.now()
        suggestion = OptimizationSuggestion(
            suggestion_id="suggestion_001",
            rule_id="rule_001",
            category="performance",
            title="优化CPU使用率",
            description="当前CPU使用率过高，建议优化",
            impact_level="high",
            estimated_improvement="提升20%性能",
            implementation_effort="medium",
            timestamp=timestamp,
            applied=False
        )
        
        assert suggestion.suggestion_id == "suggestion_001"
        assert suggestion.rule_id == "rule_001"
        assert suggestion.category == "performance"
        assert suggestion.title == "优化CPU使用率"
        assert suggestion.description == "当前CPU使用率过高，建议优化"
        assert suggestion.impact_level == "high"
        assert suggestion.estimated_improvement == "提升20%性能"
        assert suggestion.implementation_effort == "medium"
        assert suggestion.timestamp == timestamp
        assert suggestion.applied is False
    
    def test_optimization_suggestion_to_dict(self):
        """测试优化建议转换为字典"""
        timestamp = datetime.now()
        suggestion = OptimizationSuggestion(
            suggestion_id="suggestion_001",
            rule_id="rule_001",
            category="performance",
            title="测试建议",
            description="测试描述",
            impact_level="medium",
            estimated_improvement="测试改进",
            implementation_effort="easy",
            timestamp=timestamp
        )
        
        data = suggestion.to_dict()
        assert isinstance(data, dict)
        assert data['suggestion_id'] == "suggestion_001"
        assert data['timestamp'] == timestamp.isoformat()


class TestOptimizationResult:
    """OptimizationResult 数据模型测试"""
    
    def test_optimization_result_creation(self):
        """测试优化结果创建"""
        applied_at = datetime.now()
        before_metrics = {'cpu_usage': 85.0, 'memory_usage': 70.0}
        after_metrics = {'cpu_usage': 75.0, 'memory_usage': 65.0}
        
        result = OptimizationResult(
            result_id="result_001",
            suggestion_id="suggestion_001",
            applied_at=applied_at,
            success=True,
            before_metrics=before_metrics,
            after_metrics=after_metrics,
            improvement_summary="CPU使用率降低10%"
        )
        
        assert result.result_id == "result_001"
        assert result.suggestion_id == "suggestion_001"
        assert result.applied_at == applied_at
        assert result.success is True
        assert result.before_metrics == before_metrics
        assert result.after_metrics == after_metrics
        assert result.improvement_summary == "CPU使用率降低10%"
    
    def test_optimization_result_to_dict(self):
        """测试优化结果转换为字典"""
        applied_at = datetime.now()
        result = OptimizationResult(
            result_id="result_001",
            suggestion_id="suggestion_001",
            applied_at=applied_at,
            success=True,
            before_metrics={},
            after_metrics={},
            improvement_summary="测试改进"
        )
        
        data = result.to_dict()
        assert isinstance(data, dict)
        assert data['result_id'] == "result_001"
        assert data['applied_at'] == applied_at.isoformat()


class TestOptimizationEngine:
    """OptimizationEngine 主类测试"""
    
    @pytest.fixture
    def mock_config_manager(self):
        """模拟配置管理器"""
        return Mock()
    
    @pytest.fixture
    def mock_kv_store(self):
        """模拟键值存储"""
        return AsyncMock()
    
    @pytest.fixture
    def mock_performance_monitor(self):
        """模拟性能监控器"""
        monitor = Mock()
        monitor.get_performance_stats.return_value = {
            'avg_cpu_usage': 75.0,
            'avg_memory_usage': 60.0,
            'avg_response_time': 1.5,
            'total_samples': 100,
            'alert_count': 5
        }
        return monitor
    
    @pytest.fixture
    def mock_usage_tracker(self):
        """模拟使用跟踪器"""
        tracker = Mock()
        tracker.get_usage_stats.return_value = {
            'total_events': 1000,
            'total_sessions': 50,
            'features_count': 10
        }
        tracker.get_feature_usage.return_value = {
            'feature1': {'last_used': datetime.now().isoformat()},
            'feature2': {'last_used': (datetime.now() - timedelta(days=10)).isoformat()}
        }
        return tracker
    
    @pytest.fixture
    def mock_ai_client(self):
        """模拟AI客户端"""
        ai_client = AsyncMock()
        ai_suggestions = [
            {
                'title': 'AI优化建议1',
                'description': 'AI生成的优化建议',
                'impact_level': 'high',
                'estimated_improvement': '提升性能',
                'implementation_effort': 'medium'
            }
        ]
        ai_client.chat.return_value = json.dumps(ai_suggestions)
        return ai_client
    
    @pytest.fixture
    def optimization_engine(self, mock_config_manager, mock_kv_store, 
                           mock_performance_monitor, mock_usage_tracker, mock_ai_client):
        """创建优化引擎实例"""
        return OptimizationEngine(
            config_manager=mock_config_manager,
            kv_store=mock_kv_store,
            performance_monitor=mock_performance_monitor,
            usage_tracker=mock_usage_tracker,
            ai_client=mock_ai_client
        )
    
    def test_optimization_engine_initialization(self, optimization_engine):
        """测试优化引擎初始化"""
        assert len(optimization_engine.optimization_rules) > 0  # 应该有内置规则
        assert len(optimization_engine.suggestions_history) == 0
        assert len(optimization_engine.optimization_results) == 0
        assert len(optimization_engine.optimization_callbacks) == 0
        assert 'total_suggestions' in optimization_engine.stats
        assert 'applied_optimizations' in optimization_engine.stats
    
    def test_built_in_optimization_rules(self, optimization_engine):
        """测试内置优化规则"""
        rules = optimization_engine.optimization_rules
        
        # 检查是否包含预期的内置规则
        assert 'high_cpu_usage' in rules
        assert 'high_memory_usage' in rules
        assert 'slow_response_time' in rules
        assert 'unused_features' in rules
        assert 'frequent_errors' in rules
        assert 'resource_leak' in rules
        
        # 检查规则结构
        cpu_rule = rules['high_cpu_usage']
        assert cpu_rule.rule_name == "高CPU使用率优化"
        assert cpu_rule.category == "performance"
        assert cpu_rule.priority == 8
    
    @pytest.mark.asyncio
    async def test_analyze_and_suggest_basic(self, optimization_engine):
        """测试基础分析和建议生成"""
        suggestions = await optimization_engine.analyze_and_suggest()
        
        assert isinstance(suggestions, list)
        assert optimization_engine.stats['total_suggestions'] >= len(suggestions)
        assert optimization_engine.stats['last_analysis_time'] is not None
    
    @pytest.mark.asyncio
    async def test_analyze_and_suggest_with_high_cpu(self, optimization_engine):
        """测试高CPU使用率触发优化建议"""
        # 模拟高CPU使用率
        optimization_engine.performance_monitor.get_performance_stats.return_value = {
            'avg_cpu_usage': 85.0,  # 超过80%阈值
            'avg_memory_usage': 60.0,
            'avg_response_time': 1.5,
            'total_samples': 100,
            'alert_count': 2
        }
        
        suggestions = await optimization_engine.analyze_and_suggest()
        
        # 应该生成CPU优化建议
        cpu_suggestions = [s for s in suggestions if s.rule_id == 'high_cpu_usage']
        assert len(cpu_suggestions) > 0
        
        cpu_suggestion = cpu_suggestions[0]
        assert cpu_suggestion.title == "CPU使用率过高"
        assert cpu_suggestion.impact_level == "high"
    
    @pytest.mark.asyncio
    async def test_analyze_and_suggest_with_high_memory(self, optimization_engine):
        """测试高内存使用率触发优化建议"""
        # 模拟高内存使用率
        optimization_engine.performance_monitor.get_performance_stats.return_value = {
            'avg_cpu_usage': 70.0,
            'avg_memory_usage': 90.0,  # 超过85%阈值
            'avg_response_time': 1.5,
            'total_samples': 100,
            'alert_count': 2
        }
        
        suggestions = await optimization_engine.analyze_and_suggest()
        
        # 应该生成内存优化建议
        memory_suggestions = [s for s in suggestions if s.rule_id == 'high_memory_usage']
        assert len(memory_suggestions) > 0
        
        memory_suggestion = memory_suggestions[0]
        assert memory_suggestion.title == "内存使用率过高"
        assert memory_suggestion.impact_level == "critical"
    
    @pytest.mark.asyncio
    async def test_analyze_and_suggest_with_slow_response(self, optimization_engine):
        """测试慢响应时间触发优化建议"""
        # 模拟慢响应时间
        optimization_engine.performance_monitor.get_performance_stats.return_value = {
            'avg_cpu_usage': 70.0,
            'avg_memory_usage': 60.0,
            'avg_response_time': 3.0,  # 超过2秒阈值
            'total_samples': 100,
            'alert_count': 2
        }
        
        suggestions = await optimization_engine.analyze_and_suggest()
        
        # 应该生成响应时间优化建议
        response_suggestions = [s for s in suggestions if s.rule_id == 'slow_response_time']
        assert len(response_suggestions) > 0
        
        response_suggestion = response_suggestions[0]
        assert response_suggestion.title == "响应时间过长"
        assert response_suggestion.impact_level == "high"
    
    @pytest.mark.asyncio
    async def test_analyze_and_suggest_with_ai(self, optimization_engine):
        """测试AI智能分析"""
        suggestions = await optimization_engine.analyze_and_suggest()
        
        # 应该包含AI生成的建议
        ai_suggestions = [s for s in suggestions if s.rule_id == 'ai_analysis']
        assert len(ai_suggestions) > 0
        
        ai_suggestion = ai_suggestions[0]
        assert ai_suggestion.category == "ai_generated"
        assert ai_suggestion.title == "AI优化建议1"
    
    @pytest.mark.asyncio
    async def test_apply_optimization_success(self, optimization_engine):
        """测试成功应用优化"""
        # 先生成建议
        suggestions = await optimization_engine.analyze_and_suggest()
        assert len(suggestions) > 0
        
        suggestion = suggestions[0]
        
        # 应用优化
        result = await optimization_engine.apply_optimization(suggestion.suggestion_id)
        
        assert isinstance(result, OptimizationResult)
        assert result.suggestion_id == suggestion.suggestion_id
        assert suggestion.applied is True
        assert optimization_engine.stats['applied_optimizations'] >= 1
    
    @pytest.mark.asyncio
    async def test_apply_optimization_with_custom_implementation(self, optimization_engine):
        """测试使用自定义实现应用优化"""
        # 先生成建议
        suggestions = await optimization_engine.analyze_and_suggest()
        assert len(suggestions) > 0
        
        suggestion = suggestions[0]
        
        # 自定义实现函数
        async def custom_implementation(suggestion):
            return True
        
        # 应用优化
        result = await optimization_engine.apply_optimization(
            suggestion.suggestion_id, 
            custom_implementation
        )
        
        assert result.success is True
        assert suggestion.applied is True
    
    @pytest.mark.asyncio
    async def test_apply_optimization_nonexistent(self, optimization_engine):
        """测试应用不存在的优化建议"""
        result = await optimization_engine.apply_optimization("nonexistent_suggestion")
        
        assert result.success is False
        assert "未找到优化建议" in result.improvement_summary
    
    def test_get_optimization_suggestions(self, optimization_engine):
        """测试获取优化建议列表"""
        # 添加测试建议
        suggestion1 = OptimizationSuggestion(
            suggestion_id="suggestion_1",
            rule_id="rule_1",
            category="performance",
            title="建议1",
            description="描述1",
            impact_level="high",
            estimated_improvement="改进1",
            implementation_effort="easy",
            timestamp=datetime.now(),
            applied=False
        )
        
        suggestion2 = OptimizationSuggestion(
            suggestion_id="suggestion_2",
            rule_id="rule_2",
            category="resource",
            title="建议2",
            description="描述2",
            impact_level="medium",
            estimated_improvement="改进2",
            implementation_effort="hard",
            timestamp=datetime.now(),
            applied=True
        )
        
        optimization_engine.suggestions_history.extend([suggestion1, suggestion2])
        
        # 测试获取全部建议
        all_suggestions = optimization_engine.get_optimization_suggestions()
        assert len(all_suggestions) == 2
        
        # 测试按类别过滤
        perf_suggestions = optimization_engine.get_optimization_suggestions(category="performance")
        assert len(perf_suggestions) == 1
        assert perf_suggestions[0]['category'] == "performance"
        
        # 测试按影响级别过滤
        high_suggestions = optimization_engine.get_optimization_suggestions(impact_level="high")
        assert len(high_suggestions) == 1
        assert high_suggestions[0]['impact_level'] == "high"
        
        # 测试按应用状态过滤
        applied_suggestions = optimization_engine.get_optimization_suggestions(applied=True)
        assert len(applied_suggestions) == 1
        assert applied_suggestions[0]['applied'] is True
    
    def test_get_optimization_stats(self, optimization_engine):
        """测试获取优化统计信息"""
        # 添加测试数据
        optimization_engine.stats['total_suggestions'] = 10
        optimization_engine.stats['applied_optimizations'] = 5
        optimization_engine.stats['successful_optimizations'] = 4
        
        stats = optimization_engine.get_optimization_stats()
        
        assert isinstance(stats, dict)
        assert stats['total_suggestions'] == 10
        assert stats['applied_optimizations'] == 5
        assert stats['successful_optimizations'] == 4
        assert stats['success_rate'] == 80.0  # 4/5 * 100
        assert 'pending_suggestions' in stats
        assert 'rules_count' in stats
        assert 'enabled_rules_count' in stats
    
    def test_add_remove_optimization_rule(self, optimization_engine):
        """测试添加和移除优化规则"""
        # 创建测试规则
        rule = OptimizationRule(
            rule_id="custom_rule",
            rule_name="自定义规则",
            category="custom",
            condition="自定义条件",
            action="自定义动作",
            priority=5
        )
        
        # 添加规则
        result = optimization_engine.add_optimization_rule(rule)
        assert result is True
        assert 'custom_rule' in optimization_engine.optimization_rules
        
        # 移除规则
        result = optimization_engine.remove_optimization_rule('custom_rule')
        assert result is True
        assert 'custom_rule' not in optimization_engine.optimization_rules
        
        # 移除不存在的规则
        result = optimization_engine.remove_optimization_rule('nonexistent_rule')
        assert result is False
    
    def test_enable_disable_rule(self, optimization_engine):
        """测试启用和禁用规则"""
        rule_id = 'high_cpu_usage'
        
        # 禁用规则
        result = optimization_engine.disable_rule(rule_id)
        assert result is True
        assert optimization_engine.optimization_rules[rule_id].enabled is False
        
        # 启用规则
        result = optimization_engine.enable_rule(rule_id)
        assert result is True
        assert optimization_engine.optimization_rules[rule_id].enabled is True
        
        # 操作不存在的规则
        result = optimization_engine.enable_rule('nonexistent_rule')
        assert result is False
    
    def test_optimization_callback_management(self, optimization_engine):
        """测试优化回调管理"""
        async def test_callback(result):
            pass
        
        # 添加回调
        optimization_engine.add_optimization_callback(test_callback)
        assert test_callback in optimization_engine.optimization_callbacks
        
        # 移除回调
        result = optimization_engine.remove_optimization_callback(test_callback)
        assert result is True
        assert test_callback not in optimization_engine.optimization_callbacks
        
        # 移除不存在的回调
        result = optimization_engine.remove_optimization_callback(test_callback)
        assert result is False
    
    def test_clear_suggestions_history(self, optimization_engine):
        """测试清理建议历史"""
        # 添加测试建议
        old_suggestion = OptimizationSuggestion(
            suggestion_id="old_suggestion",
            rule_id="rule_1",
            category="performance",
            title="旧建议",
            description="旧描述",
            impact_level="medium",
            estimated_improvement="旧改进",
            implementation_effort="easy",
            timestamp=datetime.now() - timedelta(hours=25)  # 25小时前
        )
        
        new_suggestion = OptimizationSuggestion(
            suggestion_id="new_suggestion",
            rule_id="rule_2",
            category="performance",
            title="新建议",
            description="新描述",
            impact_level="high",
            estimated_improvement="新改进",
            implementation_effort="medium",
            timestamp=datetime.now()
        )
        
        optimization_engine.suggestions_history.extend([old_suggestion, new_suggestion])
        
        # 清理24小时前的建议
        cleared_count = optimization_engine.clear_suggestions_history(older_than_hours=24)
        
        assert cleared_count == 1
        assert len(optimization_engine.suggestions_history) == 1
        assert optimization_engine.suggestions_history[0].suggestion_id == "new_suggestion"
        
        # 清理全部建议
        cleared_count = optimization_engine.clear_suggestions_history()
        assert cleared_count == 1
        assert len(optimization_engine.suggestions_history) == 0
    
    @pytest.mark.asyncio
    async def test_cleanup(self, optimization_engine):
        """测试资源清理"""
        # 添加测试数据
        optimization_engine.suggestions_history.append(Mock())
        optimization_engine.optimization_results.append(Mock())
        optimization_engine.optimization_callbacks.append(Mock())
        optimization_engine.stats['test'] = 'value'
        
        await optimization_engine.cleanup()
        
        assert len(optimization_engine.suggestions_history) == 0
        assert len(optimization_engine.optimization_results) == 0
        assert len(optimization_engine.optimization_callbacks) == 0
        assert len(optimization_engine.stats) == 0


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
