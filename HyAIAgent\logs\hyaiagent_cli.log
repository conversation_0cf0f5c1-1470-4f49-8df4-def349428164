2025-07-28 00:09:43,053 - __main__ - INFO - 启动HyAIAgent CLI
2025-07-28 00:09:43,210 - __main__ - ERROR - CLI启动失败: expected str, bytes or os.PathLike object, not ConfigManager
2025-07-28 00:09:43,211 - __main__ - INFO - 资源清理完成
2025-07-28 00:09:43,215 - asyncio - ERROR - Cancelling an overlapped future failed
future: <_OverlappedFuture pending overlapped=<pending, 0x1f7e4182550> cb=[BaseProactorEventLoop._loop_self_reading()]>
Traceback (most recent call last):
  File "cli_main.py", line 47, in start
    self.agent = await get_agent()
  File ".\core\autonomous_agent.py", line 499, in get_agent
    _global_agent = AutonomousAgent(config_path)
  File ".\core\autonomous_agent.py", line 64, in __init__
    self.prompt_manager = PromptManager(self.config_manager)
  File ".\core\prompt_manager.py", line 26, in __init__
    self.prompts_dir = Path(prompts_dir)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py", line 1038, in __new__
    self = cls._from_parts(args, init=False)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py", line 679, in _from_parts
    drv, root, parts = self._parse_args(args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\pathlib.py", line 663, in _parse_args
    a = os.fspath(a)
TypeError: expected str, bytes or os.PathLike object, not ConfigManager

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\asyncio\runners.py", line 43, in run
    return loop.run_until_complete(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\asyncio\base_events.py", line 603, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\asyncio\windows_events.py", line 316, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\asyncio\base_events.py", line 570, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\asyncio\base_events.py", line 1859, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\asyncio\events.py", line 81, in _run
    self._context.run(self._callback, *self._args)
  File "cli_main.py", line 273, in main
    await cli.start()
  File "cli_main.py", line 58, in start
    sys.exit(1)
SystemExit: 1

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\asyncio\windows_events.py", line 66, in _cancel_overlapped
    self._ov.cancel()
OSError: [WinError 6] 句柄无效。
