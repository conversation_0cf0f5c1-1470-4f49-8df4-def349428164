"""
调试项目符号摘要功能
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from operations.content_processor import ContentProcessor
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager


async def debug_bullet_points():
    """调试项目符号摘要"""
    print("开始调试项目符号摘要...")
    
    # 初始化
    config_manager = ConfigManager()
    security_manager = SecurityManager("./workspace")
    processor = ContentProcessor(config_manager, security_manager)
    
    # 测试内容
    test_contents = [
        "人工智能技术在近年来取得了显著进展。深度学习算法的发展推动了计算机视觉、自然语言处理等领域的突破。机器学习模型的准确性不断提高，应用场景也越来越广泛。",
        "企业数字化转型已成为当前商业发展的重要趋势。云计算、大数据分析、物联网等技术为企业提供了新的发展机遇。数字化转型不仅提高了运营效率，还创造了新的商业模式。"
    ]
    
    # 生成项目符号摘要
    result = await processor.generate_intelligent_summary(
        contents=test_contents,
        summary_type="bullet_points",
        target_audience="general"
    )
    
    print("摘要结果:")
    print(f"摘要类型: {result.get('summary_type', 'unknown')}")
    print(f"摘要内容:\n{result.get('summary', '')}")
    print(f"是否包含项目符号: {'•' in result.get('summary', '')}")
    print(f"后处理错误: {result.get('post_process_error', 'None')}")
    print(f"策略: {result.get('strategy', 'unknown')}")
    print(f"置信度: {result.get('confidence', 0)}")

    # 测试句子提取
    summary_text = result.get('summary', '')
    sentences = processor._extract_sentences(summary_text)
    print(f"\n句子提取测试:")
    print(f"句子数量: {len(sentences)}")
    for i, sentence in enumerate(sentences):
        print(f"  {i+1}: {sentence}")

    # 测试项目符号格式化
    if sentences:
        formatted = processor._format_as_bullets(summary_text)
        print(f"\n格式化后的项目符号:")
        print(formatted)

    return result


if __name__ == "__main__":
    asyncio.run(debug_bullet_points())
