"""
HyAIAgent 第四阶段 - 内容处理器测试
测试搜索结果解析和内容处理功能
"""

import asyncio
import pytest
import json
import tempfile
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch

# 导入被测试的模块
from operations.content_processor import (
    ContentProcessor,
    ProcessedContent,
    ProcessedResponse
)
from operations.search_operations import SearchResult, SearchResponse
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager


class TestContentProcessor:
    """内容处理器测试"""
    
    @pytest.fixture
    def temp_config_file(self):
        """创建临时配置文件"""
        config_data = {
            "content_processor": {
                "reading_speed": 200,
                "max_summary_length": 200,
                "max_keywords": 10
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            temp_file = f.name
        
        yield temp_file
        
        # 清理临时文件
        import os
        os.unlink(temp_file)
    
    @pytest.fixture
    def config_manager(self, temp_config_file):
        """创建配置管理器"""
        return ConfigManager(config_path=temp_config_file)
    
    @pytest.fixture
    def processor(self, config_manager):
        """创建内容处理器"""
        return ContentProcessor(config_manager=config_manager)
    
    def test_initialization(self, processor):
        """测试初始化"""
        assert processor.config_manager is not None
        assert processor.processor_config is not None
        assert "total_processed" in processor.processing_stats
        assert processor.patterns is not None
    
    def test_clean_content(self, processor):
        """测试内容清理"""
        # 测试HTML标签清理
        html_content = "<p>这是一个<strong>测试</strong>内容</p>"
        cleaned = processor._clean_content(html_content)
        assert "<p>" not in cleaned
        assert "<strong>" not in cleaned
        assert "这是一个测试内容" in cleaned
        
        # 测试HTML实体清理
        entity_content = "这是&nbsp;一个&amp;测试"
        cleaned = processor._clean_content(entity_content)
        assert "&nbsp;" not in cleaned
        assert "&amp;" not in cleaned
        
        # 测试空白字符标准化
        space_content = "这是   一个\n\n\n测试"
        cleaned = processor._clean_content(space_content)
        assert "   " not in cleaned
        assert "\n\n\n" not in cleaned
    
    def test_clean_title(self, processor):
        """测试标题清理"""
        html_title = "<title>测试标题</title>"
        cleaned = processor._clean_title(html_title)
        assert cleaned == "测试标题"
        
        entity_title = "测试&amp;标题"
        cleaned = processor._clean_title(entity_title)
        assert "&amp;" not in cleaned
    
    def test_generate_summary(self, processor):
        """测试摘要生成"""
        content = "这是第一句话。这是第二句话。这是第三句话。这是第四句话。"
        summary = processor._generate_summary(content, max_length=50)
        
        assert len(summary) <= 50
        assert "这是第一句话" in summary
        
        # 测试空内容
        empty_summary = processor._generate_summary("")
        assert empty_summary == ""
    
    def test_extract_keywords(self, processor):
        """测试关键词提取"""
        content = "Python是一种编程语言。Python很流行。编程语言有很多种。"
        keywords = processor._extract_keywords(content, max_keywords=5)
        
        assert isinstance(keywords, list)
        assert len(keywords) <= 5
        # 检查是否包含python相关关键词
        has_python = any("python" in kw.lower() for kw in keywords)
        assert has_python
        
        # 测试空内容
        empty_keywords = processor._extract_keywords("")
        assert empty_keywords == []
    
    def test_extract_entities(self, processor):
        """测试实体提取"""
        content = "访问 https://example.com 或发邮件到 <EMAIL>。日期是2025-01-01，数量是100。"
        entities = processor._extract_entities(content)
        
        assert isinstance(entities, list)
        
        # 检查URL实体
        url_entities = [e for e in entities if e["type"] == "url"]
        assert len(url_entities) > 0
        assert "https://example.com" in [e["value"] for e in url_entities]
        
        # 检查邮箱实体
        email_entities = [e for e in entities if e["type"] == "email"]
        assert len(email_entities) > 0
        assert "<EMAIL>" in [e["value"] for e in email_entities]
        
        # 检查日期实体
        date_entities = [e for e in entities if e["type"] == "date"]
        assert len(date_entities) > 0
        assert "2025-01-01" in [e["value"] for e in date_entities]
    
    def test_detect_content_type(self, processor):
        """测试内容类型检测"""
        news_content = "这是一条新闻报道，记者采访了相关人员。"
        assert processor._detect_content_type(news_content) == "news"
        
        blog_content = "这是我的个人博客分享。"
        assert processor._detect_content_type(blog_content) == "blog"
        
        academic_content = "这是一篇学术研究论文。"
        assert processor._detect_content_type(academic_content) == "academic"
        
        general_content = "这是一般性内容。"
        assert processor._detect_content_type(general_content) == "general"
    
    def test_detect_language(self, processor):
        """测试语言检测"""
        chinese_content = "这是中文内容，包含很多中文字符。"
        assert processor._detect_language(chinese_content) == "zh"
        
        english_content = "This is English content with many English words."
        assert processor._detect_language(english_content) == "en"
        
        mixed_content = "This is mixed content 包含中英文。"
        # 应该根据主要语言判断
        language = processor._detect_language(mixed_content)
        assert language in ["zh", "en"]
    
    def test_count_words(self, processor):
        """测试词数统计"""
        chinese_content = "这是中文内容"
        chinese_count = processor._count_words(chinese_content)
        assert chinese_count == 6  # 6个中文字符
        
        english_content = "This is English content"
        english_count = processor._count_words(english_content)
        assert english_count == 4  # 4个英文单词
        
        mixed_content = "This is 中文 content"
        mixed_count = processor._count_words(mixed_content)
        assert mixed_count == 5  # 3个英文单词 + 2个中文字符
    
    def test_estimate_reading_time(self, processor):
        """测试阅读时间估算"""
        # 200字的内容应该需要1分钟
        reading_time = processor._estimate_reading_time(200)
        assert reading_time == 1
        
        # 400字的内容应该需要2分钟
        reading_time = processor._estimate_reading_time(400)
        assert reading_time == 2
        
        # 少于200字的内容至少需要1分钟
        reading_time = processor._estimate_reading_time(50)
        assert reading_time == 1
    
    def test_calculate_quality_score(self, processor):
        """测试质量评分计算"""
        # 创建高质量搜索结果
        high_quality_result = SearchResult(
            title="这是一个详细的标题",
            url="https://example.com/article",
            content="这是一个很长的内容，包含了很多有用的信息。" * 20,
            score=0.9
        )
        
        quality_score = processor._calculate_quality_score(
            high_quality_result, 
            "这是一个很长的内容，包含了很多有用的信息。" * 20
        )
        
        assert 0.0 <= quality_score <= 1.0
        assert quality_score > 0.5  # 高质量内容应该有较高评分
        
        # 创建低质量搜索结果
        low_quality_result = SearchResult(
            title="",
            url="",
            content="短",
            score=0.1
        )
        
        low_quality_score = processor._calculate_quality_score(low_quality_result, "短")
        assert low_quality_score < quality_score  # 低质量内容评分应该更低
    
    @pytest.mark.asyncio
    async def test_process_single_result(self, processor):
        """测试单个结果处理"""
        # 创建测试搜索结果
        test_result = SearchResult(
            title="测试标题",
            url="https://example.com",
            content="这是一个测试内容，包含了一些关键信息。Python是一种编程语言。",
            score=0.8
        )
        
        # 处理结果
        processed = await processor._process_single_result(test_result)
        
        # 验证处理结果
        assert processed is not None
        assert isinstance(processed, ProcessedContent)
        assert processed.title == "测试标题"
        assert processed.original_url == "https://example.com"
        assert len(processed.cleaned_content) > 0
        assert len(processed.summary) > 0
        assert len(processed.keywords) > 0
        assert processed.word_count > 0
        assert processed.reading_time >= 1
        assert 0.0 <= processed.quality_score <= 1.0
        assert processed.language in ["zh", "en", "unknown"]
        assert processed.content_type in ["news", "blog", "academic", "commercial", "general"]
    
    @pytest.mark.asyncio
    async def test_process_search_response(self, processor):
        """测试搜索响应处理"""
        # 创建测试搜索结果
        test_results = [
            SearchResult(
                title="第一个结果",
                url="https://example1.com",
                content="这是第一个测试内容，包含Python编程相关信息。",
                score=0.9
            ),
            SearchResult(
                title="第二个结果",
                url="https://example2.com", 
                content="这是第二个测试内容，也包含Python开发相关信息。",
                score=0.8
            )
        ]
        
        # 创建测试搜索响应
        test_response = SearchResponse(
            query="Python编程",
            results=test_results,
            total_results=2,
            search_time=1.5,
            timestamp=datetime.now(),
            search_depth="basic"
        )
        
        # 处理搜索响应
        processed_response = await processor.process_search_response(test_response)
        
        # 验证处理结果
        assert processed_response is not None
        assert isinstance(processed_response, ProcessedResponse)
        assert processed_response.original_query == "Python编程"
        assert processed_response.total_processed == 2
        assert len(processed_response.processed_results) == 2
        assert processed_response.processing_time > 0
        assert len(processed_response.summary) > 0
        assert len(processed_response.key_insights) >= 0
        assert len(processed_response.related_topics) >= 0
        assert 0.0 <= processed_response.confidence_score <= 1.0
        
        # 验证每个处理结果
        for result in processed_response.processed_results:
            assert isinstance(result, ProcessedContent)
            assert len(result.cleaned_content) > 0
    
    @pytest.mark.asyncio
    async def test_generate_overall_summary(self, processor):
        """测试整体摘要生成"""
        # 创建测试处理结果
        processed_results = [
            ProcessedContent(
                original_url="https://example1.com",
                title="测试1",
                cleaned_content="内容1",
                summary="摘要1",
                keywords=["python", "编程"],
                entities=[],
                content_type="blog",
                language="zh",
                word_count=100,
                reading_time=1,
                quality_score=0.8,
                timestamp=datetime.now()
            ),
            ProcessedContent(
                original_url="https://example2.com",
                title="测试2",
                cleaned_content="内容2",
                summary="摘要2",
                keywords=["python", "开发"],
                entities=[],
                content_type="blog",
                language="zh",
                word_count=150,
                reading_time=1,
                quality_score=0.9,
                timestamp=datetime.now()
            )
        ]
        
        summary = await processor._generate_overall_summary(processed_results, "Python编程")
        
        assert isinstance(summary, str)
        assert len(summary) > 0
        assert "Python编程" in summary
        assert "2个" in summary  # 应该提到结果数量
    
    @pytest.mark.asyncio
    async def test_extract_key_insights(self, processor):
        """测试关键洞察提取"""
        # 创建测试处理结果
        processed_results = [
            ProcessedContent(
                original_url="https://example1.com",
                title="测试1",
                cleaned_content="内容1",
                summary="摘要1",
                keywords=["python", "编程", "开发"],
                entities=[],
                content_type="blog",
                language="zh",
                word_count=100,
                reading_time=1,
                quality_score=0.9,  # 高质量
                timestamp=datetime.now()
            ),
            ProcessedContent(
                original_url="https://example2.com",
                title="测试2",
                cleaned_content="内容2",
                summary="摘要2",
                keywords=["python", "编程", "学习"],
                entities=[],
                content_type="blog",
                language="zh",
                word_count=150,
                reading_time=1,
                quality_score=0.8,  # 高质量
                timestamp=datetime.now()
            )
        ]
        
        insights = await processor._extract_key_insights(processed_results)
        
        assert isinstance(insights, list)
        assert len(insights) > 0
        
        # 应该包含关键词洞察
        keyword_insight = next((insight for insight in insights if "关键词" in insight), None)
        assert keyword_insight is not None
        assert "python" in keyword_insight or "编程" in keyword_insight
        
        # 应该包含质量洞察
        quality_insight = next((insight for insight in insights if "高质量" in insight), None)
        assert quality_insight is not None
    
    @pytest.mark.asyncio
    async def test_identify_related_topics(self, processor):
        """测试相关主题识别"""
        # 创建测试处理结果
        processed_results = [
            ProcessedContent(
                original_url="https://example1.com",
                title="测试1",
                cleaned_content="内容1",
                summary="摘要1",
                keywords=["python", "编程", "开发", "学习"],
                entities=[],
                content_type="blog",
                language="zh",
                word_count=100,
                reading_time=1,
                quality_score=0.8,
                timestamp=datetime.now()
            ),
            ProcessedContent(
                original_url="https://example2.com",
                title="测试2",
                cleaned_content="内容2",
                summary="摘要2",
                keywords=["python", "编程", "框架", "学习"],
                entities=[],
                content_type="blog",
                language="zh",
                word_count=150,
                reading_time=1,
                quality_score=0.9,
                timestamp=datetime.now()
            )
        ]
        
        related_topics = await processor._identify_related_topics(processed_results)
        
        assert isinstance(related_topics, list)
        assert len(related_topics) > 0
        
        # 高频关键词应该成为相关主题
        assert "python" in related_topics
        assert "编程" in related_topics
        assert "学习" in related_topics
    
    def test_calculate_confidence_score(self, processor):
        """测试置信度评分计算"""
        # 创建高质量结果
        high_quality_results = [
            ProcessedContent(
                original_url="https://example.com",
                title="测试",
                cleaned_content="内容",
                summary="摘要",
                keywords=["test"],
                entities=[],
                content_type="blog",
                language="zh",
                word_count=100,
                reading_time=1,
                quality_score=0.9,
                timestamp=datetime.now()
            ) for _ in range(5)
        ]
        
        confidence = processor._calculate_confidence_score(high_quality_results)
        assert 0.0 <= confidence <= 1.0
        assert confidence > 0.7  # 5个高质量结果应该有高置信度
        
        # 测试空结果
        empty_confidence = processor._calculate_confidence_score([])
        assert empty_confidence == 0.0
    
    @pytest.mark.asyncio
    async def test_get_processing_stats(self, processor):
        """测试处理统计信息"""
        # 初始统计
        stats = await processor.get_processing_stats()
        assert stats["total_processed"] == 0
        assert stats["success_rate"] == 0.0
        assert stats["average_quality_score"] == 0.0
        
        # 模拟一些处理操作
        processor.processing_stats["total_processed"] = 10
        processor.processing_stats["successful_processed"] = 8
        processor.processing_stats["failed_processed"] = 2
        processor.processing_stats["quality_scores"] = [0.8, 0.9, 0.7, 0.6, 0.8]
        
        stats = await processor.get_processing_stats()
        assert stats["total_processed"] == 10
        assert stats["success_rate"] == 0.8
        assert abs(stats["average_quality_score"] - 0.76) < 0.01  # 允许浮点数精度误差


# 运行测试的主函数
if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
