"""
HyAIAgent 第四阶段 - 搜索缓存机制
负责搜索结果的缓存管理，包括TTL缓存、LRU缓存和持久化缓存
"""

import json
import hashlib
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
from loguru import logger

from operations.search_operations import SearchResponse
from operations.content_processor import ProcessedResponse
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager
from core.kv_store import KVStore


@dataclass
class CacheEntry:
    """缓存条目数据模型"""
    key: str
    data: Dict[str, Any]
    created_at: datetime
    expires_at: datetime
    access_count: int
    last_accessed: datetime
    cache_type: str  # 'search' or 'processed'
    size_bytes: int
    
    def is_expired(self) -> bool:
        """检查是否已过期"""
        return datetime.now() > self.expires_at
    
    def is_valid(self) -> bool:
        """检查是否有效（未过期且数据完整）"""
        return not self.is_expired() and self.data is not None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['expires_at'] = self.expires_at.isoformat()
        data['last_accessed'] = self.last_accessed.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CacheEntry':
        """从字典创建缓存条目"""
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['expires_at'] = datetime.fromisoformat(data['expires_at'])
        data['last_accessed'] = datetime.fromisoformat(data['last_accessed'])
        return cls(**data)


@dataclass
class CacheStats:
    """缓存统计信息"""
    total_entries: int
    total_size_bytes: int
    hit_count: int
    miss_count: int
    eviction_count: int
    expired_count: int
    hit_rate: float
    average_entry_size: float
    oldest_entry_age: float  # 秒
    newest_entry_age: float  # 秒


class SearchCache:
    """搜索缓存管理器"""
    
    def __init__(self, 
                 config_manager: ConfigManager,
                 security_manager: Optional[SecurityManager] = None,
                 kv_store: Optional[KVStore] = None):
        """
        初始化搜索缓存管理器
        
        Args:
            config_manager: 配置管理器
            security_manager: 安全管理器（可选）
            kv_store: 键值存储（可选）
        """
        self.config_manager = config_manager
        self.security_manager = security_manager
        self.kv_store = kv_store
        
        # 加载缓存配置
        self.cache_config = self.config_manager.get("search.cache", {})
        
        # 缓存设置
        self.max_entries = self.cache_config.get("max_entries", 1000)
        self.default_ttl = self.cache_config.get("default_ttl_seconds", 3600)  # 1小时
        self.max_size_bytes = self.cache_config.get("max_size_bytes", 100 * 1024 * 1024)  # 100MB
        self.cleanup_interval = self.cache_config.get("cleanup_interval_seconds", 300)  # 5分钟
        self.enable_persistent = self.cache_config.get("enable_persistent", True)
        
        # 内存缓存
        self.memory_cache: Dict[str, CacheEntry] = {}
        
        # 统计信息
        self.stats = {
            "hit_count": 0,
            "miss_count": 0,
            "eviction_count": 0,
            "expired_count": 0,
            "total_size_bytes": 0
        }
        
        # 清理任务（延迟启动）
        self._cleanup_task = None
        self._cleanup_started = False

        logger.info("搜索缓存管理器初始化完成")
    
    def _generate_cache_key(self, query: str, **kwargs) -> str:
        """生成缓存键"""
        # 创建包含查询和参数的字符串
        key_data = {
            "query": query.lower().strip(),
            **{k: v for k, v in sorted(kwargs.items()) if v is not None}
        }
        
        # 生成MD5哈希
        key_string = json.dumps(key_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()
    
    def _calculate_size(self, data: Any) -> int:
        """计算数据大小（字节）"""
        try:
            if isinstance(data, dict):
                return len(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            elif isinstance(data, str):
                return len(data.encode('utf-8'))
            else:
                return len(str(data).encode('utf-8'))
        except Exception:
            return 0
    
    async def get_search_result(self, query: str, **kwargs) -> Optional[SearchResponse]:
        """
        获取搜索结果缓存

        Args:
            query: 搜索查询
            **kwargs: 搜索参数

        Returns:
            SearchResponse: 缓存的搜索结果，如果不存在或已过期返回None
        """
        # 确保清理任务已启动
        self._start_cleanup_task()

        cache_key = self._generate_cache_key(query, **kwargs)
        
        # 先检查内存缓存
        if cache_key in self.memory_cache:
            entry = self.memory_cache[cache_key]
            
            if entry.is_valid():
                # 更新访问信息
                entry.access_count += 1
                entry.last_accessed = datetime.now()
                self.stats["hit_count"] += 1
                
                logger.debug(f"内存缓存命中: {cache_key}")
                
                # 从缓存数据重建SearchResponse对象
                try:
                    return SearchResponse(**entry.data)
                except Exception as e:
                    logger.warning(f"重建SearchResponse失败: {e}")
                    # 删除损坏的缓存条目
                    del self.memory_cache[cache_key]
            else:
                # 删除过期条目
                del self.memory_cache[cache_key]
                self.stats["expired_count"] += 1
        
        # 检查持久化缓存
        if self.enable_persistent and self.kv_store:
            try:
                cached_data = self.kv_store.get(f"search_cache:{cache_key}")
                if cached_data:
                    entry = CacheEntry.from_dict(cached_data)
                    
                    if entry.is_valid():
                        # 恢复到内存缓存
                        self.memory_cache[cache_key] = entry
                        entry.access_count += 1
                        entry.last_accessed = datetime.now()
                        self.stats["hit_count"] += 1
                        
                        logger.debug(f"持久化缓存命中: {cache_key}")
                        return SearchResponse(**entry.data)
                    else:
                        # 删除过期的持久化缓存
                        self.kv_store.delete(f"search_cache:{cache_key}")
                        self.stats["expired_count"] += 1
            except Exception as e:
                logger.warning(f"读取持久化缓存失败: {e}")
        
        self.stats["miss_count"] += 1
        return None
    
    async def set_search_result(self, query: str, result: SearchResponse, ttl_seconds: Optional[int] = None, **kwargs) -> bool:
        """
        设置搜索结果缓存

        Args:
            query: 搜索查询
            result: 搜索结果
            ttl_seconds: 生存时间（秒），如果为None使用默认值
            **kwargs: 搜索参数

        Returns:
            bool: 是否成功设置缓存
        """
        # 确保清理任务已启动
        self._start_cleanup_task()

        try:
            cache_key = self._generate_cache_key(query, **kwargs)
            ttl = ttl_seconds or self.default_ttl
            
            # 准备缓存数据
            cache_data = result.to_dict() if hasattr(result, 'to_dict') else asdict(result)
            data_size = self._calculate_size(cache_data)
            
            # 创建缓存条目
            now = datetime.now()
            entry = CacheEntry(
                key=cache_key,
                data=cache_data,
                created_at=now,
                expires_at=now + timedelta(seconds=ttl),
                access_count=1,
                last_accessed=now,
                cache_type='search',
                size_bytes=data_size
            )
            
            # 检查是否需要清理空间
            await self._ensure_cache_space(data_size)
            
            # 存储到内存缓存
            self.memory_cache[cache_key] = entry
            self.stats["total_size_bytes"] += data_size
            
            # 存储到持久化缓存
            if self.enable_persistent and self.kv_store:
                try:
                    self.kv_store.set(f"search_cache:{cache_key}", entry.to_dict())
                except Exception as e:
                    logger.warning(f"持久化缓存存储失败: {e}")
            
            logger.debug(f"缓存已设置: {cache_key}, TTL: {ttl}秒")
            return True
            
        except Exception as e:
            logger.error(f"设置搜索结果缓存失败: {e}")
            return False
    
    async def get_processed_result(self, query: str, **kwargs) -> Optional[ProcessedResponse]:
        """
        获取处理结果缓存
        
        Args:
            query: 搜索查询
            **kwargs: 搜索参数
            
        Returns:
            ProcessedResponse: 缓存的处理结果，如果不存在或已过期返回None
        """
        cache_key = self._generate_cache_key(f"processed:{query}", **kwargs)
        
        # 检查内存缓存
        if cache_key in self.memory_cache:
            entry = self.memory_cache[cache_key]
            
            if entry.is_valid():
                entry.access_count += 1
                entry.last_accessed = datetime.now()
                self.stats["hit_count"] += 1
                
                logger.debug(f"处理结果缓存命中: {cache_key}")
                
                try:
                    return ProcessedResponse(**entry.data)
                except Exception as e:
                    logger.warning(f"重建ProcessedResponse失败: {e}")
                    del self.memory_cache[cache_key]
            else:
                del self.memory_cache[cache_key]
                self.stats["expired_count"] += 1
        
        # 检查持久化缓存
        if self.enable_persistent and self.kv_store:
            try:
                cached_data = self.kv_store.get(f"processed_cache:{cache_key}")
                if cached_data:
                    entry = CacheEntry.from_dict(cached_data)
                    
                    if entry.is_valid():
                        self.memory_cache[cache_key] = entry
                        entry.access_count += 1
                        entry.last_accessed = datetime.now()
                        self.stats["hit_count"] += 1
                        
                        logger.debug(f"处理结果持久化缓存命中: {cache_key}")
                        return ProcessedResponse(**entry.data)
                    else:
                        self.kv_store.delete(f"processed_cache:{cache_key}")
                        self.stats["expired_count"] += 1
            except Exception as e:
                logger.warning(f"读取处理结果持久化缓存失败: {e}")
        
        self.stats["miss_count"] += 1
        return None
    
    async def set_processed_result(self, query: str, result: ProcessedResponse, ttl_seconds: Optional[int] = None, **kwargs) -> bool:
        """
        设置处理结果缓存
        
        Args:
            query: 搜索查询
            result: 处理结果
            ttl_seconds: 生存时间（秒），如果为None使用默认值
            **kwargs: 搜索参数
            
        Returns:
            bool: 是否成功设置缓存
        """
        try:
            cache_key = self._generate_cache_key(f"processed:{query}", **kwargs)
            ttl = ttl_seconds or self.default_ttl
            
            # 准备缓存数据
            cache_data = result.to_dict() if hasattr(result, 'to_dict') else asdict(result)
            data_size = self._calculate_size(cache_data)
            
            # 创建缓存条目
            now = datetime.now()
            entry = CacheEntry(
                key=cache_key,
                data=cache_data,
                created_at=now,
                expires_at=now + timedelta(seconds=ttl),
                access_count=1,
                last_accessed=now,
                cache_type='processed',
                size_bytes=data_size
            )
            
            # 检查是否需要清理空间
            await self._ensure_cache_space(data_size)
            
            # 存储到内存缓存
            self.memory_cache[cache_key] = entry
            self.stats["total_size_bytes"] += data_size
            
            # 存储到持久化缓存
            if self.enable_persistent and self.kv_store:
                try:
                    self.kv_store.set(f"processed_cache:{cache_key}", entry.to_dict())
                except Exception as e:
                    logger.warning(f"处理结果持久化缓存存储失败: {e}")
            
            logger.debug(f"处理结果缓存已设置: {cache_key}, TTL: {ttl}秒")
            return True
            
        except Exception as e:
            logger.error(f"设置处理结果缓存失败: {e}")
            return False
    
    async def _ensure_cache_space(self, required_size: int) -> None:
        """确保缓存有足够空间"""
        # 检查条目数量限制
        while len(self.memory_cache) >= self.max_entries:
            await self._evict_lru_entry()
        
        # 检查大小限制
        while self.stats["total_size_bytes"] + required_size > self.max_size_bytes:
            await self._evict_lru_entry()
    
    async def _evict_lru_entry(self) -> None:
        """驱逐最近最少使用的条目"""
        if not self.memory_cache:
            return

        # 找到最近最少使用的条目
        lru_key = min(
            self.memory_cache.keys(),
            key=lambda k: (self.memory_cache[k].last_accessed, self.memory_cache[k].access_count)
        )

        # 删除条目
        entry = self.memory_cache.pop(lru_key)
        self.stats["total_size_bytes"] -= entry.size_bytes
        self.stats["eviction_count"] += 1

        # 同时删除持久化缓存
        if self.enable_persistent and self.kv_store:
            try:
                if entry.cache_type == 'search':
                    self.kv_store.delete(f"search_cache:{lru_key}")
                elif entry.cache_type == 'processed':
                    self.kv_store.delete(f"processed_cache:{lru_key}")
            except Exception as e:
                logger.warning(f"删除持久化缓存失败: {e}")

        logger.debug(f"驱逐缓存条目: {lru_key}")
    
    def _start_cleanup_task(self) -> None:
        """启动清理任务"""
        if not self._cleanup_started:
            try:
                if self._cleanup_task is None or self._cleanup_task.done():
                    self._cleanup_task = asyncio.create_task(self._cleanup_loop())
                    self._cleanup_started = True
            except RuntimeError:
                # 没有运行的事件循环，延迟启动
                pass
    
    async def _cleanup_loop(self) -> None:
        """清理循环"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_expired_entries()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"缓存清理任务错误: {e}")
    
    async def _cleanup_expired_entries(self) -> None:
        """清理过期条目"""
        expired_keys = []
        
        for key, entry in self.memory_cache.items():
            if entry.is_expired():
                expired_keys.append(key)
        
        for key in expired_keys:
            entry = self.memory_cache.pop(key)
            self.stats["total_size_bytes"] -= entry.size_bytes
            self.stats["expired_count"] += 1
        
        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存条目")
    
    async def clear_cache(self, cache_type: Optional[str] = None) -> int:
        """
        清理缓存
        
        Args:
            cache_type: 缓存类型，'search'或'processed'，如果为None则清理所有
            
        Returns:
            int: 清理的条目数量
        """
        cleared_count = 0
        keys_to_remove = []
        
        for key, entry in self.memory_cache.items():
            if cache_type is None or entry.cache_type == cache_type:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            entry = self.memory_cache.pop(key)
            self.stats["total_size_bytes"] -= entry.size_bytes
            cleared_count += 1
            
            # 同时清理持久化缓存
            if self.enable_persistent and self.kv_store:
                try:
                    if entry.cache_type == 'search':
                        self.kv_store.delete(f"search_cache:{key}")
                    elif entry.cache_type == 'processed':
                        self.kv_store.delete(f"processed_cache:{key}")
                except Exception as e:
                    logger.warning(f"清理持久化缓存失败: {e}")
        
        logger.info(f"清理了 {cleared_count} 个缓存条目")
        return cleared_count
    
    async def get_cache_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        total_entries = len(self.memory_cache)
        total_requests = self.stats["hit_count"] + self.stats["miss_count"]
        hit_rate = self.stats["hit_count"] / total_requests if total_requests > 0 else 0.0
        
        # 计算平均条目大小
        avg_entry_size = self.stats["total_size_bytes"] / total_entries if total_entries > 0 else 0.0
        
        # 计算最老和最新条目的年龄
        now = datetime.now()
        oldest_age = 0.0
        newest_age = 0.0
        
        if self.memory_cache:
            oldest_entry = min(self.memory_cache.values(), key=lambda e: e.created_at)
            newest_entry = max(self.memory_cache.values(), key=lambda e: e.created_at)
            oldest_age = (now - oldest_entry.created_at).total_seconds()
            newest_age = (now - newest_entry.created_at).total_seconds()
        
        return CacheStats(
            total_entries=total_entries,
            total_size_bytes=self.stats["total_size_bytes"],
            hit_count=self.stats["hit_count"],
            miss_count=self.stats["miss_count"],
            eviction_count=self.stats["eviction_count"],
            expired_count=self.stats["expired_count"],
            hit_rate=hit_rate,
            average_entry_size=avg_entry_size,
            oldest_entry_age=oldest_age,
            newest_entry_age=newest_age
        )
    
    async def close(self) -> None:
        """关闭缓存管理器"""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("搜索缓存管理器已关闭")
