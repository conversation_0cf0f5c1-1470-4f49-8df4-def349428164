"""
FileUtils测试模块
测试文件处理工具功能
"""

import unittest
import tempfile
import shutil
import os
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from operations.file_utils import FileUtils


class TestFileUtils(unittest.TestCase):
    """FileUtils测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录作为测试环境
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试文件
        self.test_file = os.path.join(self.temp_dir, "test.txt")
        with open(self.test_file, 'w', encoding='utf-8') as f:
            f.write("Hello, World!\nThis is a test file.")
        
        # 创建不同类型的测试文件
        self.image_file = os.path.join(self.temp_dir, "test.png")
        with open(self.image_file, 'wb') as f:
            f.write(b'\x89PNG\r\n\x1a\n')  # PNG文件头
        
        self.code_file = os.path.join(self.temp_dir, "test.py")
        with open(self.code_file, 'w', encoding='utf-8') as f:
            f.write("print('Hello, Python!')")
        
        # 创建子目录和文件
        self.subdir = os.path.join(self.temp_dir, "subdir")
        os.makedirs(self.subdir, exist_ok=True)
        
        self.subfile = os.path.join(self.subdir, "subfile.txt")
        with open(self.subfile, 'w', encoding='utf-8') as f:
            f.write("Subfile content")
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_format_file_size(self):
        """测试文件大小格式化"""
        # 字节
        self.assertEqual(FileUtils.format_file_size(0), "0 B")
        self.assertEqual(FileUtils.format_file_size(512), "512 B")
        
        # KB
        self.assertEqual(FileUtils.format_file_size(1024), "1.0 KB")
        self.assertEqual(FileUtils.format_file_size(1536), "1.5 KB")
        
        # MB
        self.assertEqual(FileUtils.format_file_size(1024 * 1024), "1.0 MB")
        self.assertEqual(FileUtils.format_file_size(1024 * 1024 * 2.5), "2.5 MB")
        
        # GB
        self.assertEqual(FileUtils.format_file_size(1024 * 1024 * 1024), "1.0 GB")
        
        # TB
        self.assertEqual(FileUtils.format_file_size(1024 * 1024 * 1024 * 1024), "1.0 TB")
    
    def test_get_file_extension(self):
        """测试文件扩展名获取"""
        # 正常文件
        self.assertEqual(FileUtils.get_file_extension("test.txt"), ".txt")
        self.assertEqual(FileUtils.get_file_extension("image.PNG"), ".png")  # 应该转为小写
        self.assertEqual(FileUtils.get_file_extension("archive.tar.gz"), ".gz")
        
        # 无扩展名
        self.assertEqual(FileUtils.get_file_extension("filename"), "")
        
        # 隐藏文件
        self.assertEqual(FileUtils.get_file_extension(".hidden"), "")
        self.assertEqual(FileUtils.get_file_extension(".hidden.txt"), ".txt")
        
        # Path对象
        self.assertEqual(FileUtils.get_file_extension(Path("test.py")), ".py")
    
    def test_get_file_category(self):
        """测试文件类别获取"""
        # 文本文件
        self.assertEqual(FileUtils.get_file_category("test.txt"), "text")
        self.assertEqual(FileUtils.get_file_category("readme.md"), "text")
        
        # 代码文件
        self.assertEqual(FileUtils.get_file_category("script.py"), "code")
        self.assertEqual(FileUtils.get_file_category("style.css"), "code")
        
        # 图片文件
        self.assertEqual(FileUtils.get_file_category("image.jpg"), "image")
        self.assertEqual(FileUtils.get_file_category("icon.png"), "image")
        
        # 文档文件
        self.assertEqual(FileUtils.get_file_category("document.pdf"), "document")
        self.assertEqual(FileUtils.get_file_category("spreadsheet.xlsx"), "document")
        
        # 压缩文件
        self.assertEqual(FileUtils.get_file_category("archive.zip"), "archive")
        
        # 其他文件
        self.assertEqual(FileUtils.get_file_category("unknown.xyz"), "other")
    
    def test_is_text_file(self):
        """测试文本文件判断"""
        # 文本文件
        self.assertTrue(FileUtils.is_text_file("test.txt"))
        self.assertTrue(FileUtils.is_text_file("readme.md"))
        self.assertTrue(FileUtils.is_text_file("script.py"))
        self.assertTrue(FileUtils.is_text_file("config.ini"))
        
        # 非文本文件
        self.assertFalse(FileUtils.is_text_file("image.jpg"))
        self.assertFalse(FileUtils.is_text_file("archive.zip"))
        self.assertFalse(FileUtils.is_text_file("video.mp4"))
    
    def test_is_image_file(self):
        """测试图片文件判断"""
        # 图片文件
        self.assertTrue(FileUtils.is_image_file("photo.jpg"))
        self.assertTrue(FileUtils.is_image_file("icon.png"))
        self.assertTrue(FileUtils.is_image_file("vector.svg"))
        
        # 非图片文件
        self.assertFalse(FileUtils.is_image_file("document.txt"))
        self.assertFalse(FileUtils.is_image_file("video.mp4"))
    
    def test_is_dangerous_file(self):
        """测试危险文件判断"""
        # 危险文件
        self.assertTrue(FileUtils.is_dangerous_file("virus.exe"))
        self.assertTrue(FileUtils.is_dangerous_file("script.bat"))
        self.assertTrue(FileUtils.is_dangerous_file("malware.vbs"))
        self.assertTrue(FileUtils.is_dangerous_file("installer.msi"))
        
        # 安全文件
        self.assertFalse(FileUtils.is_dangerous_file("document.txt"))
        self.assertFalse(FileUtils.is_dangerous_file("image.jpg"))
        self.assertFalse(FileUtils.is_dangerous_file("script.py"))
    
    def test_get_mime_type(self):
        """测试MIME类型获取"""
        # 常见文件类型
        self.assertEqual(FileUtils.get_mime_type("test.txt"), "text/plain")
        self.assertEqual(FileUtils.get_mime_type("image.jpg"), "image/jpeg")
        self.assertEqual(FileUtils.get_mime_type("page.html"), "text/html")
        self.assertEqual(FileUtils.get_mime_type("data.json"), "application/json")
        
        # 未知类型
        self.assertEqual(FileUtils.get_mime_type("unknown.xyz"), "application/octet-stream")
    
    def test_calculate_file_hash(self):
        """测试文件哈希计算"""
        # MD5哈希
        md5_hash = FileUtils.calculate_file_hash(self.test_file, "md5")
        self.assertIsNotNone(md5_hash)
        self.assertEqual(len(md5_hash), 32)  # MD5哈希长度
        
        # SHA1哈希
        sha1_hash = FileUtils.calculate_file_hash(self.test_file, "sha1")
        self.assertIsNotNone(sha1_hash)
        self.assertEqual(len(sha1_hash), 40)  # SHA1哈希长度
        
        # SHA256哈希
        sha256_hash = FileUtils.calculate_file_hash(self.test_file, "sha256")
        self.assertIsNotNone(sha256_hash)
        self.assertEqual(len(sha256_hash), 64)  # SHA256哈希长度
        
        # 不支持的算法
        unsupported_hash = FileUtils.calculate_file_hash(self.test_file, "unsupported")
        self.assertIsNone(unsupported_hash)
        
        # 不存在的文件
        nonexistent_hash = FileUtils.calculate_file_hash("nonexistent.txt", "md5")
        self.assertIsNone(nonexistent_hash)
    
    def test_get_file_info(self):
        """测试文件信息获取"""
        # 存在的文件
        info = FileUtils.get_file_info(self.test_file)
        
        self.assertNotIn("error", info)
        self.assertEqual(info["name"], "test.txt")
        self.assertIn("path", info)
        self.assertIn("size", info)
        self.assertIn("size_formatted", info)
        self.assertEqual(info["extension"], ".txt")
        self.assertEqual(info["category"], "text")
        self.assertTrue(info["is_text"])
        self.assertFalse(info["is_image"])
        self.assertFalse(info["is_dangerous"])
        self.assertIn("created", info)
        self.assertIn("modified", info)
        self.assertIn("accessed", info)
        
        # 不存在的文件
        info = FileUtils.get_file_info("nonexistent.txt")
        self.assertIn("error", info)
        self.assertEqual(info["error"], "File does not exist")
    
    def test_compare_files(self):
        """测试文件比较"""
        # 创建相同内容的文件
        file2 = os.path.join(self.temp_dir, "test2.txt")
        with open(file2, 'w', encoding='utf-8') as f:
            f.write("Hello, World!\nThis is a test file.")
        
        # 比较相同文件
        result = FileUtils.compare_files(self.test_file, file2)
        self.assertNotIn("error", result)
        self.assertTrue(result["same_size"])
        self.assertEqual(result["size_difference"], 0)
        self.assertTrue(result["same_content"])
        
        # 创建不同内容的文件
        file3 = os.path.join(self.temp_dir, "test3.txt")
        with open(file3, 'w', encoding='utf-8') as f:
            f.write("Different content")
        
        # 比较不同文件
        result = FileUtils.compare_files(self.test_file, file3)
        self.assertNotIn("error", result)
        self.assertFalse(result["same_size"])
        self.assertGreater(result["size_difference"], 0)
        self.assertFalse(result["same_content"])
        
        # 比较不存在的文件
        result = FileUtils.compare_files(self.test_file, "nonexistent.txt")
        self.assertIn("error", result)
    
    def test_get_files_by_extension(self):
        """测试按扩展名获取文件"""
        # 获取txt文件
        txt_files = FileUtils.get_files_by_extension(self.temp_dir, ".txt", recursive=False)
        self.assertIn(self.test_file, txt_files)
        self.assertNotIn(self.subfile, txt_files)  # 不递归，不应包含子目录文件
        
        # 递归获取txt文件
        txt_files_recursive = FileUtils.get_files_by_extension(self.temp_dir, ".txt", recursive=True)
        self.assertIn(self.test_file, txt_files_recursive)
        self.assertIn(self.subfile, txt_files_recursive)  # 递归，应包含子目录文件
        
        # 获取py文件
        py_files = FileUtils.get_files_by_extension(self.temp_dir, ".py")
        self.assertIn(self.code_file, py_files)
        
        # 不存在的扩展名
        xyz_files = FileUtils.get_files_by_extension(self.temp_dir, ".xyz")
        self.assertEqual(len(xyz_files), 0)
        
        # 不存在的目录
        files = FileUtils.get_files_by_extension("nonexistent_dir", ".txt")
        self.assertEqual(len(files), 0)
    
    def test_get_directory_size(self):
        """测试目录大小计算"""
        # 计算目录大小
        size = FileUtils.get_directory_size(self.temp_dir)
        self.assertGreater(size, 0)
        
        # 空目录
        empty_dir = os.path.join(self.temp_dir, "empty")
        os.makedirs(empty_dir, exist_ok=True)
        empty_size = FileUtils.get_directory_size(empty_dir)
        self.assertEqual(empty_size, 0)
        
        # 不存在的目录
        nonexistent_size = FileUtils.get_directory_size("nonexistent_dir")
        self.assertEqual(nonexistent_size, 0)
    
    def test_clean_filename_for_url(self):
        """测试URL文件名清理"""
        # 包含空格的文件名
        cleaned = FileUtils.clean_filename_for_url("file name.txt")
        self.assertEqual(cleaned, "file_name.txt")
        
        # 包含特殊字符的文件名
        cleaned = FileUtils.clean_filename_for_url("file@#$%name.txt")
        self.assertEqual(cleaned, "filename.txt")
        
        # 空文件名
        cleaned = FileUtils.clean_filename_for_url("")
        self.assertEqual(cleaned, "unnamed")
        
        # 中文文件名
        cleaned = FileUtils.clean_filename_for_url("测试文件.txt")
        self.assertIsNotNone(cleaned)
        self.assertNotEqual(cleaned, "")


def run_file_utils_tests():
    """运行FileUtils测试"""
    print("🧪 开始FileUtils测试...")
    
    # 创建测试套件
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestFileUtils)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print(f"\n📊 测试结果:")
    print(f"   总测试数: {result.testsRun}")
    print(f"   成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    
    if result.failures:
        print(f"\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
    
    if result.errors:
        print(f"\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n{'✅ 所有测试通过!' if success else '❌ 存在测试失败!'}")
    
    return success


if __name__ == "__main__":
    run_file_utils_tests()
