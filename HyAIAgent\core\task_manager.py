"""
任务管理器模块

本模块实现了智能任务管理器，负责任务分解、执行计划创建和进度监控。
"""

import asyncio
import json
import logging
import sys
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor

from .task_models import Task, ExecutionPlan, ExecutionResult, ProgressInfo, TaskStatus, TaskType, TaskPriority
from .ai_client import SimpleAIClient
from .prompt_manager import PromptManager
from .kv_store import KVStore

logger = logging.getLogger(__name__)

# Python 3.8兼容性：asyncio.to_thread在Python 3.9中引入
if sys.version_info >= (3, 9):
    to_thread = asyncio.to_thread
else:
    async def to_thread(func, *args, **kwargs):
        """Python 3.8兼容的to_thread实现"""
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor() as executor:
            return await loop.run_in_executor(executor, lambda: func(*args, **kwargs))


class TaskManager:
    """智能任务管理器
    
    负责任务分解、执行计划创建、任务调度和进度监控。
    使用AI进行智能任务分析和分解。
    """
    
    def __init__(self, ai_client: SimpleAIClient, prompt_manager: PromptManager, 
                 kv_store: KVStore):
        """初始化任务管理器
        
        Args:
            ai_client: AI客户端实例
            prompt_manager: 提示词管理器实例
            kv_store: KV数据库实例
        """
        self.ai_client = ai_client
        self.prompt_manager = prompt_manager
        self.kv_store = kv_store
        
        # 任务存储
        self.active_plans: Dict[str, ExecutionPlan] = {}
        self.task_registry: Dict[str, Task] = {}
        self.execution_results: Dict[str, List[ExecutionResult]] = {}
        
        # 执行状态
        self.running_plans: Dict[str, bool] = {}
        self.plan_progress: Dict[str, ProgressInfo] = {}
        
        logger.info("TaskManager initialized successfully")
    
    async def decompose_task(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> List[Task]:
        """将用户输入分解为可执行任务
        
        Args:
            user_input: 用户输入的任务描述
            context: 可选的上下文信息
            
        Returns:
            List[Task]: 分解后的任务列表
        """
        try:
            logger.info(f"开始分解任务: {user_input}")
            
            # 获取任务分析提示词
            analysis_prompt = self.prompt_manager.get_global_prompt("task_analyzer")
            if not analysis_prompt:
                analysis_prompt = self._get_default_analysis_prompt()
            
            # 构建分析请求
            analysis_request = f"""
用户请求: {user_input}

上下文信息: {json.dumps(context or {}, ensure_ascii=False, indent=2)}

请分析这个请求并将其分解为具体的可执行任务。
"""
            
            # 调用AI进行任务分析
            response = await to_thread(
                self.ai_client.chat,
                analysis_request,
                analysis_prompt
            )
            
            # 解析AI响应并创建任务
            tasks = await self._parse_ai_response_to_tasks(response, user_input, context)
            
            # 存储任务到注册表
            for task in tasks:
                self.task_registry[task.id] = task
                await self._save_task_to_storage(task)
            
            logger.info(f"任务分解完成，共生成 {len(tasks)} 个任务")
            return tasks
            
        except Exception as e:
            logger.error(f"任务分解失败: {str(e)}")
            # 创建一个默认任务作为fallback
            fallback_task = Task(
                title="用户请求处理",
                description=user_input,
                task_type=TaskType.GENERAL,
                context=context or {}
            )
            return [fallback_task]
    
    async def create_execution_plan(self, tasks: List[Task], plan_name: str = "", 
                                  parallel_execution: bool = False) -> ExecutionPlan:
        """创建任务执行计划
        
        Args:
            tasks: 任务列表
            plan_name: 执行计划名称
            parallel_execution: 是否支持并行执行
            
        Returns:
            ExecutionPlan: 执行计划对象
        """
        try:
            logger.info(f"开始创建执行计划，包含 {len(tasks)} 个任务")
            
            # 创建执行计划
            plan = ExecutionPlan(
                name=plan_name or f"执行计划_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                description=f"包含 {len(tasks)} 个任务的执行计划",
                tasks=tasks,
                parallel_execution=parallel_execution,
                max_concurrent_tasks=3 if parallel_execution else 1
            )
            
            # 分析任务依赖关系并确定执行顺序
            plan.task_order = await self._analyze_task_dependencies(tasks)
            
            # 估算执行时间
            plan.estimated_duration = self._estimate_plan_duration(tasks)
            
            # 存储执行计划
            self.active_plans[plan.id] = plan
            await self._save_plan_to_storage(plan)
            
            # 初始化进度信息
            progress = ProgressInfo(
                plan_id=plan.id,
                total_tasks=len(tasks),
                pending_tasks=len(tasks),
                start_time=datetime.now()
            )
            self.plan_progress[plan.id] = progress
            
            logger.info(f"执行计划创建完成: {plan.id}")
            return plan
            
        except Exception as e:
            logger.error(f"创建执行计划失败: {str(e)}")
            raise
    
    async def execute_plan(self, plan: ExecutionPlan) -> ExecutionResult:
        """执行任务计划
        
        Args:
            plan: 执行计划对象
            
        Returns:
            ExecutionResult: 执行结果
        """
        try:
            logger.info(f"开始执行计划: {plan.id}")
            
            # 标记计划为运行状态
            plan.status = TaskStatus.RUNNING
            plan.started_at = datetime.now()
            self.running_plans[plan.id] = True
            
            # 创建总体执行结果
            overall_result = ExecutionResult(
                plan_id=plan.id,
                started_at=datetime.now()
            )
            
            # 执行任务（这里先返回一个占位结果，实际执行逻辑在ExecutionEngine中）
            overall_result.status = TaskStatus.COMPLETED
            overall_result.success = True
            overall_result.completed_at = datetime.now()
            overall_result.result_data = {
                "message": "执行计划已提交，等待ExecutionEngine处理",
                "plan_id": plan.id,
                "task_count": len(plan.tasks)
            }
            
            logger.info(f"计划执行提交完成: {plan.id}")
            return overall_result
            
        except Exception as e:
            logger.error(f"执行计划失败: {str(e)}")
            overall_result = ExecutionResult(
                plan_id=plan.id,
                status=TaskStatus.FAILED,
                success=False,
                error_message=str(e),
                completed_at=datetime.now()
            )
            return overall_result
    
    async def monitor_progress(self, execution_id: str) -> ProgressInfo:
        """监控执行进度
        
        Args:
            execution_id: 执行计划ID
            
        Returns:
            ProgressInfo: 进度信息
        """
        try:
            if execution_id not in self.plan_progress:
                logger.warning(f"未找到执行计划: {execution_id}")
                return ProgressInfo(plan_id=execution_id)
            
            progress = self.plan_progress[execution_id]
            
            # 更新进度信息
            if execution_id in self.active_plans:
                plan = self.active_plans[execution_id]
                
                # 统计任务状态
                completed = sum(1 for task in plan.tasks if task.status == TaskStatus.COMPLETED)
                failed = sum(1 for task in plan.tasks if task.status == TaskStatus.FAILED)
                running = sum(1 for task in plan.tasks if task.status == TaskStatus.RUNNING)
                pending = sum(1 for task in plan.tasks if task.status == TaskStatus.PENDING)
                
                # 更新进度
                progress.completed_tasks = completed
                progress.failed_tasks = failed
                progress.running_tasks = running
                progress.pending_tasks = pending
                progress.overall_progress = (completed / len(plan.tasks)) * 100 if plan.tasks else 0
                progress.last_update_time = datetime.now()
                
                # 估算剩余时间
                if progress.start_time and completed > 0:
                    elapsed = (datetime.now() - progress.start_time).total_seconds()
                    avg_time_per_task = elapsed / completed
                    remaining_tasks = len(plan.tasks) - completed
                    progress.estimated_remaining_time = int(avg_time_per_task * remaining_tasks)
            
            return progress
            
        except Exception as e:
            logger.error(f"监控进度失败: {str(e)}")
            return ProgressInfo(plan_id=execution_id)
    
    def _get_default_analysis_prompt(self) -> str:
        """获取默认的任务分析提示词"""
        return """
你是一个智能任务分析助手。请将用户的请求分解为具体的可执行任务。

分析要求：
1. 理解用户的真实意图
2. 将复杂任务分解为简单的子任务
3. 确定任务的类型和优先级
4. 考虑任务之间的依赖关系

请以JSON格式返回任务列表，每个任务包含：
- title: 任务标题
- description: 详细描述
- task_type: 任务类型 (analysis/planning/execution/communication/research/general)
- priority: 优先级 (1-4, 1最低4最高)
- estimated_duration: 预估时长（秒）
- dependencies: 依赖的任务索引列表

示例格式：
{
  "tasks": [
    {
      "title": "分析需求",
      "description": "详细分析用户需求",
      "task_type": "analysis",
      "priority": 3,
      "estimated_duration": 300,
      "dependencies": []
    }
  ]
}
"""
    
    async def _parse_ai_response_to_tasks(self, response: str, original_input: str, 
                                        context: Optional[Dict[str, Any]]) -> List[Task]:
        """解析AI响应并创建任务对象"""
        try:
            # 尝试解析JSON响应
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
            else:
                json_str = response.strip()
            
            data = json.loads(json_str)
            tasks = []
            
            for i, task_data in enumerate(data.get("tasks", [])):
                task = Task(
                    title=task_data.get("title", f"任务 {i+1}"),
                    description=task_data.get("description", ""),
                    task_type=TaskType(task_data.get("task_type", "general")),
                    priority=TaskPriority(task_data.get("priority", 2)),
                    estimated_duration=task_data.get("estimated_duration", 300),
                    context=context or {}
                )
                
                # 处理依赖关系（暂时存储索引，后续转换为实际ID）
                task.dependencies = task_data.get("dependencies", [])
                tasks.append(task)
            
            # 转换依赖关系索引为实际任务ID
            for i, task in enumerate(tasks):
                task.dependencies = [tasks[dep_idx].id for dep_idx in task.dependencies 
                                   if isinstance(dep_idx, int) and 0 <= dep_idx < len(tasks)]
            
            return tasks
            
        except Exception as e:
            logger.warning(f"解析AI响应失败，使用默认任务: {str(e)}")
            # 创建默认任务
            return [Task(
                title="处理用户请求",
                description=original_input,
                task_type=TaskType.GENERAL,
                context=context or {}
            )]
    
    async def _analyze_task_dependencies(self, tasks: List[Task]) -> List[str]:
        """分析任务依赖关系并确定执行顺序"""
        # 简单的拓扑排序实现
        task_map = {task.id: task for task in tasks}
        visited = set()
        order = []
        
        def dfs(task_id: str):
            if task_id in visited:
                return
            visited.add(task_id)
            
            task = task_map.get(task_id)
            if task:
                for dep_id in task.dependencies:
                    if dep_id in task_map:
                        dfs(dep_id)
                order.append(task_id)
        
        for task in tasks:
            dfs(task.id)
        
        return order
    
    def _estimate_plan_duration(self, tasks: List[Task]) -> int:
        """估算执行计划总时长"""
        total_duration = 0
        for task in tasks:
            if task.estimated_duration:
                total_duration += task.estimated_duration
            else:
                total_duration += 300  # 默认5分钟
        return total_duration
    
    async def _save_task_to_storage(self, task: Task):
        """保存任务到存储"""
        try:
            key = f"task:{task.id}"
            await to_thread(self.kv_store.set, key, task.to_dict())
        except Exception as e:
            logger.error(f"保存任务失败: {str(e)}")
    
    async def _save_plan_to_storage(self, plan: ExecutionPlan):
        """保存执行计划到存储"""
        try:
            key = f"plan:{plan.id}"
            await to_thread(self.kv_store.set, key, plan.to_dict())
        except Exception as e:
            logger.error(f"保存执行计划失败: {str(e)}")
