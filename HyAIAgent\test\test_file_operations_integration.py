"""
文件操作集成测试套件
测试所有文件操作模块的集成性和安全性
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
import json
import time
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from operations.security_manager import SecurityManager
from operations.file_operations import FileOperations
from operations.directory_operations import DirectoryOperations
from operations.path_utils import PathUtils
from operations.file_utils import FileUtils


class TestFileOperationsIntegration:
    """文件操作集成测试类"""
    
    @pytest.fixture
    def test_workspace(self):
        """创建测试工作空间"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        # 清理
        if Path(temp_dir).exists():
            shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def integrated_operations(self, test_workspace):
        """创建集成的操作实例"""
        security_manager = SecurityManager(workspace_path=test_workspace)
        file_ops = FileOperations(workspace_path=test_workspace, security_manager=security_manager)
        dir_ops = DirectoryOperations(workspace_path=test_workspace, security_manager=security_manager)
        path_utils = PathUtils()
        file_utils = FileUtils()

        return {
            'security': security_manager,
            'file_ops': file_ops,
            'dir_ops': dir_ops,
            'path_utils': path_utils,
            'file_utils': file_utils,
            'workspace': test_workspace
        }
    
    @pytest.mark.asyncio
    async def test_complete_file_workflow(self, integrated_operations):
        """测试完整的文件操作工作流"""
        ops = integrated_operations
        
        # 1. 创建目录结构
        await ops['dir_ops'].create_directory("project/src", parents=True)
        await ops['dir_ops'].create_directory("project/docs", parents=True)
        
        # 2. 创建文件
        test_content = "# 测试项目\n\n这是一个测试文件。"
        result = await ops['file_ops'].write_file("project/README.md", test_content)
        assert result['success'] is True
        
        # 3. 读取文件
        read_result = await ops['file_ops'].read_file("project/README.md")
        assert read_result['success'] is True
        assert read_result['content'] == test_content
        
        # 4. 列出文件
        list_result = await ops['file_ops'].list_files("project", recursive=True)
        assert list_result['success'] is True
        assert len(list_result['files']) > 0

        # 5. 搜索文件
        search_result = await ops['file_ops'].search_files("README", file_pattern="*.md")
        assert search_result['success'] is True
        assert len(search_result['results']) > 0

        # 6. 验证文件存在性
        readme_path = Path(ops['workspace']) / "project" / "README.md"
        assert readme_path.exists()

        # 7. 删除文件
        delete_result = await ops['file_ops'].delete_file("project/README.md")
        assert delete_result['success'] is True
        assert not readme_path.exists()
    
    @pytest.mark.asyncio
    async def test_security_integration(self, integrated_operations):
        """测试安全机制集成"""
        ops = integrated_operations
        
        # 测试路径遍历攻击防护
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/etc/shadow",
            "C:\\Windows\\System32\\config\\SAM"
        ]
        
        for malicious_path in malicious_paths:
            result = await ops['file_ops'].read_file(malicious_path)
            assert result['success'] is False
            assert "Security validation failed" in result['error']
    
    @pytest.mark.asyncio
    async def test_file_type_restrictions(self, integrated_operations):
        """测试文件类型限制"""
        ops = integrated_operations
        
        # 测试危险文件类型
        dangerous_files = [
            "malware.exe",
            "script.bat",
            "virus.com",
            "trojan.scr"
        ]
        
        for dangerous_file in dangerous_files:
            result = await ops['file_ops'].write_file(dangerous_file, "malicious content")
            # 根据安全配置，可能被拒绝或允许
            if not result['success']:
                assert "Security validation failed" in result['error']
    
    @pytest.mark.asyncio
    async def test_large_file_handling(self, integrated_operations):
        """测试大文件处理"""
        ops = integrated_operations
        
        # 创建较大的测试内容（1MB）
        large_content = "A" * (1024 * 1024)
        
        # 写入大文件
        start_time = time.time()
        result = await ops['file_ops'].write_file("large_file.txt", large_content)
        write_time = time.time() - start_time
        
        assert result['success'] is True
        assert write_time < 5.0  # 写入时间应该在5秒内
        
        # 读取大文件
        start_time = time.time()
        read_result = await ops['file_ops'].read_file("large_file.txt")
        read_time = time.time() - start_time
        
        assert read_result['success'] is True
        assert len(read_result['content']) == len(large_content)
        assert read_time < 5.0  # 读取时间应该在5秒内
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, integrated_operations):
        """测试并发操作"""
        ops = integrated_operations
        
        # 创建多个并发文件操作任务
        tasks = []
        for i in range(10):
            content = f"File {i} content"
            task = ops['file_ops'].write_file(f"concurrent_file_{i}.txt", content)
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证所有操作都成功
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                pytest.fail(f"Concurrent operation {i} failed with exception: {result}")
            assert result['success'] is True
    
    @pytest.mark.asyncio
    async def test_error_recovery(self, integrated_operations):
        """测试错误恢复机制"""
        ops = integrated_operations
        
        # 测试读取不存在的文件
        result = await ops['file_ops'].read_file("nonexistent.txt")
        assert result['success'] is False
        # 可能是安全验证失败或文件不存在
        assert ("not found" in result['error'].lower() or
                "security validation failed" in result['error'].lower())
        
        # 测试写入到只读目录（如果可能）
        readonly_path = Path(ops['workspace']) / "readonly"
        readonly_path.mkdir(exist_ok=True)
        
        try:
            readonly_path.chmod(0o444)  # 设置为只读
            result = await ops['file_ops'].write_file("readonly/test.txt", "content")
            # 根据系统权限，可能成功或失败
            if not result['success']:
                assert "permission" in result['error'].lower() or "access" in result['error'].lower()
        except (OSError, PermissionError):
            # 在某些系统上可能无法设置权限
            pass
        finally:
            try:
                readonly_path.chmod(0o755)  # 恢复权限以便清理
            except (OSError, PermissionError):
                pass
    
    @pytest.mark.asyncio
    async def test_path_utils_integration(self, integrated_operations):
        """测试路径工具集成"""
        ops = integrated_operations

        # 测试路径规范化
        test_paths = [
            "project/../project/file.txt",
            "project/./file.txt",
            "project//file.txt"
        ]

        for test_path in test_paths:
            normalized = ops['path_utils'].normalize_path(test_path)
            # 验证路径规范化结果是字符串且不为空
            assert isinstance(normalized, (str, Path))
            assert len(str(normalized)) > 0
            # 验证路径处理正常
            normalized_str = str(normalized)
            assert normalized_str  # 不为空
    
    @pytest.mark.asyncio
    async def test_file_utils_integration(self, integrated_operations):
        """测试文件工具集成"""
        ops = integrated_operations
        
        # 创建测试文件
        await ops['file_ops'].write_file("test.txt", "Hello World")
        await ops['file_ops'].write_file("test.json", '{"key": "value"}')
        await ops['file_ops'].write_file("test.py", "print('Hello')")
        
        # 测试文件分类
        txt_category = ops['file_utils'].get_file_category("test.txt")
        json_category = ops['file_utils'].get_file_category("test.json")
        py_category = ops['file_utils'].get_file_category("test.py")

        # 验证分类结果不为空且为字符串
        assert isinstance(txt_category, str) and len(txt_category) > 0
        assert isinstance(json_category, str) and len(json_category) > 0
        assert isinstance(py_category, str) and len(py_category) > 0
    
    @pytest.mark.asyncio
    async def test_operation_statistics(self, integrated_operations):
        """测试操作统计"""
        ops = integrated_operations
        
        # 执行一系列操作
        await ops['file_ops'].write_file("stats_test.txt", "content")
        await ops['file_ops'].read_file("stats_test.txt")
        await ops['file_ops'].list_files(".")
        await ops['dir_ops'].create_directory("stats_dir")

        # 获取统计信息
        file_stats = ops['file_ops'].get_operation_stats()
        dir_stats = ops['dir_ops'].get_operation_stats()

        # 验证统计信息
        assert file_stats['total_operations'] >= 1
        assert file_stats['successful_operations'] >= 1
        # 检查目录统计信息
        assert isinstance(dir_stats, dict)
        assert len(dir_stats) > 0
        # 检查目录创建统计
        if 'directories_created' in dir_stats:
            assert dir_stats['directories_created'] >= 1
        elif 'create_operations' in dir_stats:
            assert dir_stats['create_operations'] >= 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
