#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python PyQt6 GUI 示例程序 - 程序入口
采用MVC架构模式，界面、逻辑、应用分离
展示PyQt6最佳实践和专业项目结构
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入应用程序模块
from app import run_application


def main():
    """
    程序主入口
    简洁明了，只负责启动应用程序
    """
    try:
        # 运行应用程序
        exit_code = run_application()
        return exit_code

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装PyQt6: pip install PyQt6")
        return 1

    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
