"""
系统集成测试套件
测试任务系统与文件操作的完整集成，验证端到端工作流程
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List
import json
import time

# 导入核心组件
from core.task_models import Task, TaskType, TaskPriority, TaskStatus, ExecutionResult
from core.task_manager import TaskManager
from core.execution_engine import ExecutionEngine
from core.ai_client import SimpleAIClient
from core.prompt_manager import PromptManager
from core.kv_store import KVStore
from operations.task_integration import FileOperationTaskExecutor, TaskSystemIntegrator
from operations.security_manager import SecurityManager
from operations.file_operations import FileOperations


class TestSystemIntegration:
    """系统集成测试类"""
    
    @pytest.fixture
    def setup_test_environment(self):
        """设置测试环境"""
        # 创建临时工作目录
        temp_dir = tempfile.mkdtemp()
        workspace = Path(temp_dir) / "integration_test_workspace"
        workspace.mkdir(exist_ok=True)
        
        # 创建测试数据目录结构
        (workspace / "documents").mkdir()
        (workspace / "configs").mkdir()
        (workspace / "logs").mkdir()
        (workspace / "temp").mkdir()
        
        # 创建测试文件
        test_files = {
            "documents/readme.txt": "This is a test readme file.\nIt contains multiple lines.\nFor testing purposes.",
            "documents/data.json": '{"name": "test", "version": "1.0", "items": [1, 2, 3]}',
            "configs/app.yaml": "app:\n  name: TestApp\n  version: 1.0\n  debug: true",
            "logs/app.log": "2025-07-29 08:00:00 INFO Application started\n2025-07-29 08:01:00 DEBUG Processing request\n2025-07-29 08:02:00 ERROR Connection failed"
        }
        
        for file_path, content in test_files.items():
            full_path = workspace / file_path
            full_path.write_text(content, encoding='utf-8')
        
        # 初始化核心组件
        kv_store = KVStore(str(workspace / "test_kv.json"))
        ai_client = SimpleAIClient(api_key="test_key_for_integration_test")
        prompt_manager = PromptManager()
        
        # 初始化任务管理组件
        task_manager = TaskManager(ai_client, prompt_manager, kv_store)
        execution_engine = ExecutionEngine(ai_client, prompt_manager)
        
        # 初始化文件操作组件
        file_task_executor = FileOperationTaskExecutor(str(workspace))
        task_integrator = TaskSystemIntegrator(task_manager, execution_engine, str(workspace))
        
        yield {
            "workspace": workspace,
            "task_manager": task_manager,
            "execution_engine": execution_engine,
            "file_task_executor": file_task_executor,
            "task_integrator": task_integrator,
            "test_files": test_files
        }
        
        # 清理测试环境
        shutil.rmtree(temp_dir, ignore_errors=True)

    @pytest.mark.asyncio
    async def test_end_to_end_file_read_workflow(self, setup_test_environment):
        """测试端到端文件读取工作流程"""
        env = setup_test_environment
        
        # 创建文件读取任务
        task = await env["task_integrator"].create_file_operation_task(
            operation_type="file_read",
            parameters={
                "file_path": "documents/readme.txt",
                "encoding": "utf-8"
            },
            description="Read test readme file",
            priority="NORMAL"
        )
        
        # 验证任务创建
        assert task.task_type == TaskType.FILE_OPERATION
        assert task.priority == TaskPriority.NORMAL
        assert task.input_data["operation_type"] == "file_read"
        
        # 执行任务
        result = await env["file_task_executor"].execute_task(task)
        
        # 验证执行结果
        assert result.status == TaskStatus.COMPLETED
        assert result.success is True
        assert "This is a test readme file" in result.result_data["content"]
        assert result.result_data["file_path"] == "documents/readme.txt"
        assert result.result_data["encoding"] == "utf-8"

    @pytest.mark.asyncio
    async def test_end_to_end_file_write_workflow(self, setup_test_environment):
        """测试端到端文件写入工作流程"""
        env = setup_test_environment
        
        # 创建文件写入任务
        new_content = "This is a new test file.\nCreated by integration test.\nWith multiple lines."
        task = await env["task_integrator"].create_file_operation_task(
            operation_type="file_write",
            parameters={
                "file_path": "temp/new_file.txt",
                "content": new_content,
                "encoding": "utf-8",
                "create_dirs": True
            },
            description="Write new test file",
            priority="HIGH"
        )
        
        # 执行任务
        result = await env["file_task_executor"].execute_task(task)
        
        # 验证执行结果
        assert result.status == TaskStatus.COMPLETED
        assert result.success is True
        assert result.result_data["file_path"] == "temp/new_file.txt"
        assert result.result_data["size"] == len(new_content.encode('utf-8'))
        
        # 验证文件确实被创建
        created_file = env["workspace"] / "temp/new_file.txt"
        assert created_file.exists()
        assert created_file.read_text(encoding='utf-8') == new_content

    @pytest.mark.asyncio
    async def test_complex_workflow_execution(self, setup_test_environment):
        """测试复杂工作流程执行"""
        env = setup_test_environment
        
        # 定义复杂工作流程配置
        workflow_config = {
            "name": "Document Processing Workflow",
            "description": "Read, analyze, and backup documents",
            "steps": [
                {
                    "operation_type": "file_read",
                    "parameters": {
                        "file_path": "documents/data.json",
                        "encoding": "utf-8"
                    },
                    "description": "Read JSON data file"
                },
                {
                    "operation_type": "file_write",
                    "parameters": {
                        "file_path": "temp/data_backup.json",
                        "content": "PLACEHOLDER",  # 将被工作流程替换
                        "encoding": "utf-8",
                        "create_dirs": True
                    },
                    "description": "Create backup of JSON data"
                },
                {
                    "operation_type": "file_search",
                    "parameters": {
                        "query": "test",
                        "search_options": {
                            "search_patterns": ["*.txt"],
                            "search_path": "documents",
                            "include_content": True,
                            "content_pattern": "test"
                        }
                    },
                    "description": "Search for text files containing 'test'"
                }
            ]
        }
        
        # 创建批量操作计划
        operations = []
        for step in workflow_config["steps"]:
            operations.append({
                "operation_type": step["operation_type"],
                "parameters": step["parameters"],
                "description": step["description"]
            })

        # 执行批量操作计划
        plan_id = await env["task_integrator"].create_batch_file_operation_plan(
            operations=operations,
            plan_name=workflow_config["name"],
            parallel_execution=False  # 顺序执行以确保依赖关系
        )

        workflow_result = await env["task_integrator"].execute_file_operation_plan(plan_id)
        
        # 验证工作流程结果
        assert workflow_result.success is True
        assert workflow_result.status == TaskStatus.COMPLETED

        # 验证备份文件是否创建成功
        backup_file = env["workspace"] / "temp" / "data_backup.json"
        assert backup_file.exists()

        # 验证进度监控
        progress = await env["task_integrator"].monitor_file_operation_progress(plan_id)
        assert progress["total_tasks"] == 3
        assert progress["completed_tasks"] >= 0

    @pytest.mark.asyncio
    async def test_batch_task_execution(self, setup_test_environment):
        """测试批量任务执行"""
        env = setup_test_environment
        
        # 创建多个任务
        tasks = []
        
        # 任务1：读取配置文件
        task1 = await env["task_integrator"].create_file_operation_task(
            operation_type="file_read",
            parameters={"file_path": "configs/app.yaml", "encoding": "utf-8"},
            description="Read YAML config"
        )
        tasks.append(task1)
        
        # 任务2：读取日志文件
        task2 = await env["task_integrator"].create_file_operation_task(
            operation_type="file_read",
            parameters={"file_path": "logs/app.log", "encoding": "utf-8"},
            description="Read log file"
        )
        tasks.append(task2)
        
        # 任务3：创建摘要文件
        task3 = await env["task_integrator"].create_file_operation_task(
            operation_type="file_write",
            parameters={
                "file_path": "temp/summary.txt",
                "content": "System integration test summary\nMultiple tasks executed successfully",
                "encoding": "utf-8",
                "create_dirs": True
            },
            description="Create summary file"
        )
        tasks.append(task3)
        
        # 创建批量操作计划
        operations = []
        for task in tasks:
            task_params = task.input_data
            operations.append({
                "operation_type": task_params["operation_type"],
                "parameters": task_params,
                "description": task.description
            })

        # 执行批量操作计划
        plan_id = await env["task_integrator"].create_batch_file_operation_plan(
            operations=operations,
            plan_name="Batch Task Execution Test",
            parallel_execution=True
        )

        batch_result = await env["task_integrator"].execute_file_operation_plan(plan_id)

        # 验证批量执行结果
        assert batch_result.success is True
        assert batch_result.status == TaskStatus.COMPLETED

        # 验证文件是否创建成功
        summary_file = env["workspace"] / "temp" / "summary.txt"
        assert summary_file.exists()

    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self, setup_test_environment):
        """测试错误处理和恢复机制"""
        env = setup_test_environment
        
        # 测试1：读取不存在的文件
        task1 = await env["task_integrator"].create_file_operation_task(
            operation_type="file_read",
            parameters={"file_path": "nonexistent/file.txt", "encoding": "utf-8"},
            description="Read non-existent file"
        )
        
        result1 = await env["file_task_executor"].execute_task(task1)
        assert result1.success is False
        assert result1.status == TaskStatus.FAILED
        assert "security" in result1.error_message.lower() or "validation" in result1.error_message.lower() or "not found" in result1.error_message.lower()
        
        # 测试2：写入到受保护的路径
        task2 = await env["task_integrator"].create_file_operation_task(
            operation_type="file_write",
            parameters={
                "file_path": "../../../etc/passwd",  # 尝试路径遍历攻击
                "content": "malicious content",
                "encoding": "utf-8"
            },
            description="Attempt path traversal attack"
        )
        
        result2 = await env["file_task_executor"].execute_task(task2)
        assert result2.success is False
        assert result2.status == TaskStatus.FAILED
        assert "security" in result2.error_message.lower() or "validation" in result2.error_message.lower()
        
        # 测试3：无效的操作类型
        task3 = Task(
            description="Invalid operation test",
            task_type=TaskType.FILE_OPERATION,
            priority=TaskPriority.NORMAL,
            input_data={
                "operation_type": "invalid_operation",
                "parameters": {}
            }
        )
        
        result3 = await env["file_task_executor"].execute_task(task3)
        assert result3.success is False
        assert result3.status == TaskStatus.FAILED
        assert "unsupported" in result3.error_message.lower() or "invalid" in result3.error_message.lower()

    @pytest.mark.asyncio
    async def test_concurrent_task_execution(self, setup_test_environment):
        """测试并发任务执行的稳定性"""
        env = setup_test_environment
        
        # 创建多个并发任务
        concurrent_tasks = []
        
        for i in range(5):
            # 创建读取任务
            read_task = await env["task_integrator"].create_file_operation_task(
                operation_type="file_read",
                parameters={"file_path": "documents/readme.txt", "encoding": "utf-8"},
                description=f"Concurrent read task {i+1}"
            )
            concurrent_tasks.append(read_task)
            
            # 创建写入任务
            write_task = await env["task_integrator"].create_file_operation_task(
                operation_type="file_write",
                parameters={
                    "file_path": f"temp/concurrent_{i+1}.txt",
                    "content": f"Concurrent test file {i+1}\nCreated at {time.time()}",
                    "encoding": "utf-8",
                    "create_dirs": True
                },
                description=f"Concurrent write task {i+1}"
            )
            concurrent_tasks.append(write_task)
        
        # 并发执行所有任务
        start_time = time.time()
        results = await asyncio.gather(*[
            env["file_task_executor"].execute_task(task) 
            for task in concurrent_tasks
        ])
        end_time = time.time()
        
        # 验证并发执行结果
        assert len(results) == 10  # 5个读取 + 5个写入
        assert all(result.success for result in results)
        assert all(result.status == TaskStatus.COMPLETED for result in results)
        
        # 验证执行时间合理（并发执行应该比串行快）
        execution_time = end_time - start_time
        assert execution_time < 10.0  # 应该在10秒内完成
        
        # 验证文件确实被创建
        for i in range(5):
            concurrent_file = env["workspace"] / f"temp/concurrent_{i+1}.txt"
            assert concurrent_file.exists()
            content = concurrent_file.read_text(encoding='utf-8')
            assert f"Concurrent test file {i+1}" in content

    @pytest.mark.asyncio
    async def test_performance_benchmarks(self, setup_test_environment):
        """测试性能基准"""
        env = setup_test_environment
        
        # 创建大量小文件进行性能测试
        large_content = "x" * 1000  # 1KB content
        
        # 测试批量写入性能
        write_tasks = []
        for i in range(20):
            task = await env["task_integrator"].create_file_operation_task(
                operation_type="file_write",
                parameters={
                    "file_path": f"temp/perf_test_{i:03d}.txt",
                    "content": f"Performance test file {i}\n{large_content}",
                    "encoding": "utf-8",
                    "create_dirs": True
                },
                description=f"Performance write test {i}"
            )
            write_tasks.append(task)
        
        # 创建批量写入操作计划
        write_operations = []
        for task in write_tasks:
            task_params = task.input_data
            write_operations.append({
                "operation_type": task_params["operation_type"],
                "parameters": task_params,
                "description": task.description
            })

        # 执行批量写入
        start_time = time.time()
        write_plan_id = await env["task_integrator"].create_batch_file_operation_plan(
            operations=write_operations,
            plan_name="Performance Write Test",
            parallel_execution=True
        )
        write_result = await env["task_integrator"].execute_file_operation_plan(write_plan_id)
        write_time = time.time() - start_time
        
        # 验证写入结果
        assert write_result.success is True
        assert write_result.status == TaskStatus.COMPLETED
        assert write_time < 10.0  # 应该在10秒内完成20个文件写入
        
        # 测试批量读取性能
        read_tasks = []
        for i in range(20):
            task = await env["task_integrator"].create_file_operation_task(
                operation_type="file_read",
                parameters={
                    "file_path": f"temp/perf_test_{i:03d}.txt",
                    "encoding": "utf-8"
                },
                description=f"Performance read test {i}"
            )
            read_tasks.append(task)
        
        # 创建批量读取操作计划
        read_operations = []
        for task in read_tasks:
            task_params = task.input_data
            read_operations.append({
                "operation_type": task_params["operation_type"],
                "parameters": task_params,
                "description": task.description
            })

        # 执行批量读取
        start_time = time.time()
        read_plan_id = await env["task_integrator"].create_batch_file_operation_plan(
            operations=read_operations,
            plan_name="Performance Read Test",
            parallel_execution=True
        )
        read_result = await env["task_integrator"].execute_file_operation_plan(read_plan_id)
        read_time = time.time() - start_time

        # 验证读取结果
        assert read_result.success is True
        assert read_result.status == TaskStatus.COMPLETED
        assert read_time < 5.0  # 应该在5秒内完成20个文件读取
        
        # 验证文件确实被创建和读取
        for i in range(20):
            test_file = env["workspace"] / f"temp/perf_test_{i:03d}.txt"
            assert test_file.exists()

    def test_supported_operations_coverage(self, setup_test_environment):
        """测试支持的操作类型覆盖度"""
        env = setup_test_environment
        
        # 获取支持的操作类型
        supported_ops = env["task_integrator"].get_supported_operations()
        
        # 验证基本操作类型都被支持
        expected_operations = [
            "file_read", "file_write", "file_delete", "file_search",
            "batch_checksum", "batch_rename"
        ]
        
        # 将支持的操作转换为操作类型列表
        supported_op_types = [op["operation_type"] for op in supported_ops]

        for op in expected_operations:
            assert op in supported_op_types, f"Operation {op} should be supported"
        
        # 验证操作类型数量合理
        assert len(supported_ops) >= 6, "Should support at least 6 operation types"
