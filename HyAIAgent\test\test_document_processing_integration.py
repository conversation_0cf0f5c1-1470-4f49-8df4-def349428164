"""
文档处理集成测试模块

该模块测试文档处理相关组件的集成功能，包括：
- DocumentProcessor与其他组件的协作
- TextAnalyzer与DocumentProcessor的集成
- ConfigProcessor与FormatConverter的协作
- 端到端的文档处理工作流
- 多格式文档的综合处理能力

作者: HyAIAgent开发团队
创建时间: 2025-07-28
版本: 1.0.0
"""

import pytest
import json
import tempfile
import shutil
from pathlib import Path
from unittest.mock import MagicMock
import asyncio

# 导入被测试的类
from operations.document_processor import DocumentProcessor
from operations.text_analyzer import TextAnalyzer
from operations.config_processor import ConfigProcessor
from utils.format_converter import FormatConverter


class TestDocumentProcessingIntegration:
    """文档处理集成测试类"""
    
    @pytest.fixture
    def temp_workspace(self):
        """创建临时工作空间"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def document_processor(self, temp_workspace):
        """创建DocumentProcessor实例"""
        processor = DocumentProcessor(temp_workspace)
        # Mock安全管理器以允许测试文件访问
        processor.security_manager.validate_operation = MagicMock(return_value={'allowed': True, 'reason': 'test'})
        return processor
    
    @pytest.fixture
    def text_analyzer(self, temp_workspace):
        """创建TextAnalyzer实例"""
        analyzer = TextAnalyzer(temp_workspace)
        # Mock安全管理器以允许测试文件访问
        analyzer.security_manager.validate_operation = MagicMock(return_value={'allowed': True, 'reason': 'test'})
        return analyzer
    
    @pytest.fixture
    def config_processor(self, temp_workspace):
        """创建ConfigProcessor实例"""
        processor = ConfigProcessor(temp_workspace)
        # Mock安全管理器以允许测试文件访问
        processor.security_manager.validate_operation = MagicMock(return_value={'allowed': True, 'reason': 'test'})
        return processor
    
    @pytest.fixture
    def format_converter(self, temp_workspace):
        """创建FormatConverter实例"""
        converter = FormatConverter(temp_workspace)
        # Mock安全管理器以允许测试文件访问
        converter.security_manager.validate_operation = MagicMock(return_value={'allowed': True, 'reason': 'test'})
        return converter
    
    @pytest.fixture
    def sample_documents(self, temp_workspace):
        """创建示例文档文件"""
        documents = {}

        # 创建Markdown文档
        md_content = """# 项目文档

## 概述
这是一个测试项目的文档。

## 功能特性
- 文档处理
- 文本分析
- 格式转换

## 配置说明
项目使用JSON配置文件进行配置管理。

### 代码示例
```python
def hello_world():
    print("Hello, World!")
```

## 总结
这个项目展示了文档处理的各种功能。
"""

        md_path = Path(temp_workspace) / "project_doc.md"
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write(md_content)
        documents['markdown'] = "project_doc.md"  # 返回相对路径
        
        # 创建JSON配置文件
        config_data = {
            "app_name": "DocumentProcessor",
            "version": "1.0.0",
            "settings": {
                "debug": True,
                "max_file_size": 10485760,
                "supported_formats": ["md", "txt", "json", "yaml"]
            },
            "features": {
                "text_analysis": True,
                "format_conversion": True,
                "batch_processing": True
            }
        }
        
        json_path = Path(temp_workspace) / "config.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2)
        documents['config'] = "config.json"  # 返回相对路径
        
        # 创建CSV数据文件
        csv_content = """name,type,size,created
project_doc.md,markdown,1024,2025-07-28
config.json,configuration,512,2025-07-28
data.csv,data,256,2025-07-28
README.txt,text,128,2025-07-28"""
        
        csv_path = Path(temp_workspace) / "data.csv"
        with open(csv_path, 'w', encoding='utf-8') as f:
            f.write(csv_content)
        documents['data'] = "data.csv"  # 返回相对路径
        
        # 创建文本文件
        txt_content = """这是一个包含中文内容的文本文件。
文件用于测试文本分析功能。

内容包括：
1. 中文字符处理
2. 行数统计
3. 字符统计
4. 语言检测

测试完成。"""
        
        txt_path = Path(temp_workspace) / "README.txt"
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(txt_content)
        documents['text'] = "README.txt"  # 返回相对路径
        
        return documents
    
    @pytest.mark.asyncio
    async def test_document_processor_text_analyzer_integration(self, document_processor, text_analyzer, sample_documents):
        """测试DocumentProcessor与TextAnalyzer的集成"""
        # 使用DocumentProcessor处理Markdown文档
        doc_result = await document_processor.process_document(sample_documents['markdown'])
        assert doc_result['success'] == True
        assert doc_result['format'] == 'text'  # .md文件被归类为text格式

        # 使用TextAnalyzer分析同一文档（需要绝对路径）
        md_abs_path = str(Path(text_analyzer.workspace_path) / sample_documents['markdown'])
        text_result = await text_analyzer.analyze_file(md_abs_path)
        assert text_result['status'] == 'success'

        # 验证两个组件的结果一致性
        assert 'content' in doc_result
        assert 'results' in text_result

        # 验证文档内容分析
        doc_content = doc_result['content']
        text_results = text_result['results']

        assert text_results['basic_stats']['line_count'] > 0
        assert text_results['basic_stats']['character_count'] > 0
        assert text_results['language_detection']['primary_language'] in ['chinese', 'english']
    
    @pytest.mark.asyncio
    async def test_config_processor_format_converter_integration(self, config_processor, format_converter, sample_documents, temp_workspace):
        """测试ConfigProcessor与FormatConverter的集成"""
        # 使用ConfigProcessor处理JSON配置
        config_result = await config_processor.process_config(sample_documents['config'])
        assert config_result['status'] == 'success'
        assert 'config' in config_result
        
        # 使用FormatConverter将JSON转换为YAML
        yaml_path = Path(temp_workspace) / "config.yaml"
        convert_result = await format_converter.convert_file(
            sample_documents['config'],
            "config.yaml",
            'yaml'
        )
        
        # 如果YAML转换成功，验证结果
        if convert_result['status'] == 'success':
            assert convert_result['source_format'] == 'json'
            assert convert_result['target_format'] == 'yaml'
            assert yaml_path.exists()
            
            # 使用ConfigProcessor处理转换后的YAML文件
            yaml_config_result = await config_processor.process_config("config.yaml")
            if yaml_config_result['status'] == 'success':
                # 验证配置内容一致性
                original_config = config_result['config']
                converted_config = yaml_config_result['config']
                assert original_config['app_name'] == converted_config['app_name']
                assert original_config['version'] == converted_config['version']
        else:
            # YAML库不可用时跳过
            pytest.skip("YAML conversion not available")
    
    @pytest.mark.asyncio
    async def test_batch_document_processing_workflow(self, document_processor, text_analyzer, sample_documents):
        """测试批量文档处理工作流"""
        # 准备文档列表（TextAnalyzer需要绝对路径）
        document_paths = [
            str(Path(text_analyzer.workspace_path) / sample_documents['markdown']),
            str(Path(text_analyzer.workspace_path) / sample_documents['text'])
        ]
        
        # 使用DocumentProcessor批量处理
        batch_doc_result = await document_processor.batch_process_documents(
            file_patterns=["*.md", "*.txt"]
        )
        assert batch_doc_result['success'] == True
        assert batch_doc_result['processed_count'] >= 2
        
        # 使用TextAnalyzer批量分析（使用文件模式）
        batch_text_result = await text_analyzer.batch_analyze_files(["*.md", "*.txt"])
        assert batch_text_result['status'] == 'success'
        assert len(batch_text_result['results']) >= 2
        
        # 验证批量处理结果
        for result in batch_text_result['results']:
            assert result['status'] == 'success'
            assert 'results' in result
    
    @pytest.mark.asyncio
    async def test_format_conversion_chain(self, format_converter, temp_workspace, sample_documents):
        """测试格式转换链"""
        # CSV -> JSON -> TXT 转换链
        json_path = Path(temp_workspace) / "converted.json"
        txt_path = Path(temp_workspace) / "final.txt"
        
        # 第一步：CSV转JSON
        csv_to_json = await format_converter.convert_file(
            sample_documents['data'],
            "converted.json",
            'json'
        )
        assert csv_to_json['status'] == 'success'
        assert json_path.exists()
        
        # 第二步：JSON转TXT
        json_to_txt = await format_converter.convert_file(
            "converted.json",
            "final.txt",
            'txt'
        )
        assert json_to_txt['status'] == 'success'
        assert txt_path.exists()
        
        # 验证转换链的完整性
        with open(txt_path, 'r', encoding='utf-8') as f:
            final_content = f.read()
        assert len(final_content) > 0
    
    @pytest.mark.asyncio
    async def test_document_search_and_analysis(self, document_processor, text_analyzer, sample_documents):
        """测试文档搜索和分析集成"""
        # 在文档中搜索特定内容
        search_result = await document_processor.search_in_documents(
            query="文档处理",
            file_patterns=["*.md", "*.txt"]
        )
        assert search_result['success'] == True
        assert search_result['total_matches'] > 0
        
        # 对搜索结果进行文本分析
        if search_result['results']:
            first_match = search_result['results'][0]
            file_path = first_match['file_path']
            
            analysis_result = await text_analyzer.analyze_file(file_path)
            assert analysis_result['status'] == 'success'
            
            # 验证分析结果包含搜索关键词
            analysis_results = analysis_result['results']
            assert 'word_frequency' in analysis_results
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, document_processor, text_analyzer, config_processor, format_converter, temp_workspace):
        """测试集成错误处理"""
        # 测试不存在的文件
        nonexistent_file = str(Path(temp_workspace) / "nonexistent.txt")
        
        # DocumentProcessor错误处理
        doc_result = await document_processor.process_document(nonexistent_file)
        assert doc_result['success'] == False
        
        # TextAnalyzer错误处理
        text_result = await text_analyzer.analyze_file(nonexistent_file)
        # TextAnalyzer可能返回success但内容为空，这是正常的
        assert text_result['status'] in ['success', 'error']
        
        # ConfigProcessor错误处理
        config_result = await config_processor.process_config(nonexistent_file)
        assert config_result['status'] == 'error'
        
        # FormatConverter错误处理
        convert_result = await format_converter.convert_file(
            nonexistent_file,
            str(Path(temp_workspace) / "output.txt"),
            'txt'
        )
        assert convert_result['status'] == 'error'
    
    def test_statistics_integration(self, document_processor, text_analyzer, config_processor, format_converter):
        """测试统计信息集成"""
        # 获取各组件的统计信息
        doc_stats = document_processor.get_processing_stats()
        text_stats = text_analyzer.get_analysis_stats()
        config_stats = config_processor.get_processing_stats()
        convert_stats = format_converter.get_conversion_stats()
        
        # 验证统计信息结构
        assert isinstance(doc_stats, dict)
        assert isinstance(text_stats, dict)
        assert isinstance(config_stats, dict)
        assert isinstance(convert_stats, dict)
        
        # 验证统计信息包含必要字段
        assert 'total_processed' in doc_stats
        assert 'total_analyses' in text_stats
        assert 'total_processed' in config_stats
        assert 'total_conversions' in convert_stats
    
    @pytest.mark.asyncio
    async def test_end_to_end_document_workflow(self, document_processor, text_analyzer, config_processor, format_converter, sample_documents, temp_workspace):
        """测试端到端文档处理工作流"""
        # 步骤1：处理配置文件
        config_result = await config_processor.process_config(sample_documents['config'])
        assert config_result['status'] == 'success'
        
        # 步骤2：根据配置处理文档
        doc_result = await document_processor.process_document(sample_documents['markdown'])
        assert doc_result['success'] == True
        
        # 步骤3：分析文档内容
        md_abs_path = str(Path(text_analyzer.workspace_path) / sample_documents['markdown'])
        text_result = await text_analyzer.analyze_file(md_abs_path)
        assert text_result['status'] == 'success'
        
        # 步骤4：转换文档格式
        output_path = Path(temp_workspace) / "processed_output.txt"
        convert_result = await format_converter.convert_file(
            sample_documents['markdown'],
            "processed_output.txt",
            'txt'
        )
        assert convert_result['status'] == 'success'
        
        # 步骤5：验证最终输出
        assert output_path.exists()
        with open(output_path, 'r', encoding='utf-8') as f:
            final_content = f.read()
        assert len(final_content) > 0
        assert '项目文档' in final_content  # 验证内容保持完整
