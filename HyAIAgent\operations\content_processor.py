"""
HyAIAgent 第四阶段 - 内容处理器模块
负责搜索结果的解析、清理和结构化处理
"""

import re
import html
import json
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from urllib.parse import urlparse, urljoin
from loguru import logger

from operations.search_operations import SearchResult, SearchResponse
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager


@dataclass
class ProcessedContent:
    """处理后的内容数据模型"""
    original_url: str
    title: str
    cleaned_content: str
    summary: str
    keywords: List[str]
    entities: List[Dict[str, Any]]
    content_type: str
    language: str
    word_count: int
    reading_time: int  # 预估阅读时间（分钟）
    quality_score: float  # 内容质量评分
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class ProcessedResponse:
    """处理后的搜索响应数据模型"""
    original_query: str
    processed_results: List[ProcessedContent]
    total_processed: int
    processing_time: float
    summary: str
    key_insights: List[str]
    related_topics: List[str]
    confidence_score: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['processed_results'] = [result.to_dict() for result in self.processed_results]
        return data


class ContentProcessor:
    """内容处理器 - 负责搜索结果的解析和处理"""
    
    def __init__(self, 
                 config_manager: ConfigManager,
                 security_manager: Optional[SecurityManager] = None):
        """
        初始化内容处理器
        
        Args:
            config_manager: 配置管理器
            security_manager: 安全管理器（可选）
        """
        self.config_manager = config_manager
        self.security_manager = security_manager
        
        # 加载处理配置
        self.processor_config = self.config_manager.get("content_processor", {})
        
        # 初始化处理统计
        self.processing_stats = {
            "total_processed": 0,
            "successful_processed": 0,
            "failed_processed": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0,
            "quality_scores": []
        }
        
        # 编译正则表达式模式
        self._compile_patterns()
        
        logger.info("内容处理器初始化完成")
    
    def _compile_patterns(self):
        """编译常用的正则表达式模式"""
        self.patterns = {
            # HTML标签清理
            'html_tags': re.compile(r'<[^>]+>'),
            'html_entities': re.compile(r'&[a-zA-Z0-9#]+;'),
            
            # 文本清理
            'multiple_spaces': re.compile(r'\s+'),
            'multiple_newlines': re.compile(r'\n\s*\n'),
            'special_chars': re.compile(r'[^\w\s\u4e00-\u9fff.,!?;:()\-\'""]'),
            
            # 内容提取
            'sentences': re.compile(r'[.!?]+\s+'),
            'words': re.compile(r'\b\w+\b'),
            'chinese_words': re.compile(r'[\u4e00-\u9fff]+'),
            'english_words': re.compile(r'\b[a-zA-Z]+\b'),
            
            # 实体识别
            'urls': re.compile(r'https?://[^\s]+'),
            'emails': re.compile(r'[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}'),
            'dates': re.compile(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}'),
            'numbers': re.compile(r'\d+(?:\.\d+)?'),
        }
    
    async def process_search_response(self, search_response: SearchResponse) -> ProcessedResponse:
        """
        处理搜索响应，返回结构化的处理结果
        
        Args:
            search_response: 原始搜索响应
            
        Returns:
            ProcessedResponse: 处理后的响应数据
        """
        start_time = datetime.now()
        
        try:
            logger.info(f"开始处理搜索响应: {search_response.query}")
            
            # 处理每个搜索结果
            processed_results = []
            for result in search_response.results:
                try:
                    processed_content = await self._process_single_result(result)
                    if processed_content:
                        processed_results.append(processed_content)
                        self.processing_stats["successful_processed"] += 1
                except Exception as e:
                    logger.warning(f"处理单个结果失败: {result.url} - {str(e)}")
                    self.processing_stats["failed_processed"] += 1
                    continue
            
            # 生成整体摘要和洞察
            summary = await self._generate_overall_summary(processed_results, search_response.query)
            key_insights = await self._extract_key_insights(processed_results)
            related_topics = await self._identify_related_topics(processed_results)
            confidence_score = self._calculate_confidence_score(processed_results)
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 更新统计信息
            self.processing_stats["total_processed"] += 1
            self.processing_stats["total_processing_time"] += processing_time
            self.processing_stats["average_processing_time"] = (
                self.processing_stats["total_processing_time"] / 
                self.processing_stats["total_processed"]
            )
            
            # 创建处理后的响应
            processed_response = ProcessedResponse(
                original_query=search_response.query,
                processed_results=processed_results,
                total_processed=len(processed_results),
                processing_time=processing_time,
                summary=summary,
                key_insights=key_insights,
                related_topics=related_topics,
                confidence_score=confidence_score,
                timestamp=datetime.now()
            )
            
            logger.info(f"搜索响应处理完成: {len(processed_results)}个结果")
            return processed_response
            
        except Exception as e:
            logger.error(f"处理搜索响应失败: {str(e)}")
            self.processing_stats["failed_processed"] += 1
            raise
    
    async def _process_single_result(self, result: SearchResult) -> Optional[ProcessedContent]:
        """
        处理单个搜索结果
        
        Args:
            result: 单个搜索结果
            
        Returns:
            ProcessedContent: 处理后的内容，如果处理失败返回None
        """
        try:
            # 清理和标准化内容
            cleaned_content = self._clean_content(result.content)
            
            # 生成摘要
            summary = self._generate_summary(cleaned_content)
            
            # 提取关键词
            keywords = self._extract_keywords(cleaned_content)
            
            # 识别实体
            entities = self._extract_entities(cleaned_content)
            
            # 检测内容类型和语言
            content_type = self._detect_content_type(cleaned_content)
            language = self._detect_language(cleaned_content)
            
            # 计算统计信息
            word_count = self._count_words(cleaned_content)
            reading_time = self._estimate_reading_time(word_count)
            quality_score = self._calculate_quality_score(result, cleaned_content)
            
            # 记录质量评分用于统计
            self.processing_stats["quality_scores"].append(quality_score)
            
            return ProcessedContent(
                original_url=result.url,
                title=self._clean_title(result.title),
                cleaned_content=cleaned_content,
                summary=summary,
                keywords=keywords,
                entities=entities,
                content_type=content_type,
                language=language,
                word_count=word_count,
                reading_time=reading_time,
                quality_score=quality_score,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.warning(f"处理单个结果失败: {result.url} - {str(e)}")
            return None
    
    def _clean_content(self, content: str) -> str:
        """清理和标准化内容文本"""
        if not content:
            return ""
        
        # HTML解码
        cleaned = html.unescape(content)
        
        # 移除HTML标签
        cleaned = self.patterns['html_tags'].sub('', cleaned)
        
        # 移除HTML实体
        cleaned = self.patterns['html_entities'].sub('', cleaned)
        
        # 标准化空白字符
        cleaned = self.patterns['multiple_spaces'].sub(' ', cleaned)
        cleaned = self.patterns['multiple_newlines'].sub('\n\n', cleaned)
        
        # 移除特殊字符（保留基本标点）
        cleaned = self.patterns['special_chars'].sub('', cleaned)
        
        # 去除首尾空白
        cleaned = cleaned.strip()
        
        return cleaned
    
    def _clean_title(self, title: str) -> str:
        """清理标题文本"""
        if not title:
            return ""
        
        # 基本清理
        cleaned = html.unescape(title)
        cleaned = self.patterns['html_tags'].sub('', cleaned)
        cleaned = self.patterns['multiple_spaces'].sub(' ', cleaned)
        cleaned = cleaned.strip()
        
        return cleaned
    
    def _generate_summary(self, content: str, max_length: int = 200) -> str:
        """生成内容摘要"""
        if not content:
            return ""
        
        # 简单的摘要生成：取前几句话
        sentences = self.patterns['sentences'].split(content)
        summary = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and len(summary + sentence) <= max_length:
                summary += sentence + ". "
            else:
                break
        
        return summary.strip()
    
    def _extract_keywords(self, content: str, max_keywords: int = 10) -> List[str]:
        """提取关键词"""
        if not content:
            return []
        
        # 简单的关键词提取：基于词频
        words = self.patterns['words'].findall(content.lower())
        
        # 过滤停用词（简化版）
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'}
        
        # 统计词频
        word_freq = {}
        for word in words:
            if len(word) > 2 and word not in stop_words:
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 按频率排序并返回前N个
        keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in keywords[:max_keywords]]
    
    def _extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """提取实体信息"""
        entities = []
        
        # 提取URL
        urls = self.patterns['urls'].findall(content)
        for url in urls:
            entities.append({"type": "url", "value": url})
        
        # 提取邮箱
        emails = self.patterns['emails'].findall(content)
        for email in emails:
            entities.append({"type": "email", "value": email})
        
        # 提取日期
        dates = self.patterns['dates'].findall(content)
        for date in dates:
            entities.append({"type": "date", "value": date})
        
        # 提取数字
        numbers = self.patterns['numbers'].findall(content)
        for number in numbers[:5]:  # 限制数量
            entities.append({"type": "number", "value": number})
        
        return entities
    
    def _detect_content_type(self, content: str) -> str:
        """检测内容类型"""
        if not content:
            return "unknown"
        
        content_lower = content.lower()
        
        # 简单的内容类型检测
        if any(word in content_lower for word in ['新闻', 'news', '报道', '记者']):
            return "news"
        elif any(word in content_lower for word in ['博客', 'blog', '个人', '分享']):
            return "blog"
        elif any(word in content_lower for word in ['学术', 'research', '论文', '研究']):
            return "academic"
        elif any(word in content_lower for word in ['产品', 'product', '服务', '公司']):
            return "commercial"
        else:
            return "general"
    
    def _detect_language(self, content: str) -> str:
        """检测内容语言"""
        if not content:
            return "unknown"
        
        # 简单的语言检测
        chinese_chars = len(self.patterns['chinese_words'].findall(content))
        english_words = len(self.patterns['english_words'].findall(content))
        
        if chinese_chars > english_words:
            return "zh"
        elif english_words > 0:
            return "en"
        else:
            return "unknown"
    
    def _count_words(self, content: str) -> int:
        """统计词数"""
        if not content:
            return 0

        # 中英文混合词数统计
        chinese_matches = self.patterns['chinese_words'].findall(content)
        chinese_chars = sum(len(match) for match in chinese_matches)  # 统计中文字符总数
        english_words = len(self.patterns['english_words'].findall(content))

        # 中文按字符计算，英文按单词计算
        return chinese_chars + english_words
    
    def _estimate_reading_time(self, word_count: int) -> int:
        """估算阅读时间（分钟）"""
        # 假设中文200字/分钟，英文250词/分钟
        reading_speed = self.processor_config.get("reading_speed", 200)
        return max(1, word_count // reading_speed)
    
    def _calculate_quality_score(self, result: SearchResult, cleaned_content: str) -> float:
        """计算内容质量评分"""
        score = 0.0
        
        # 基于搜索结果评分
        if hasattr(result, 'score') and result.score:
            score += result.score * 0.3
        
        # 基于内容长度
        content_length = len(cleaned_content)
        if content_length > 500:
            score += 0.3
        elif content_length > 200:
            score += 0.2
        elif content_length > 50:
            score += 0.1
        
        # 基于标题质量
        if result.title and len(result.title) > 10:
            score += 0.2
        
        # 基于URL质量
        if result.url:
            parsed_url = urlparse(result.url)
            if parsed_url.netloc:
                score += 0.2
        
        return min(1.0, score)
    
    async def _generate_overall_summary(self, processed_results: List[ProcessedContent], query: str) -> str:
        """生成整体摘要"""
        if not processed_results:
            return f"未找到关于'{query}'的相关信息。"
        
        # 简单的整体摘要生成
        total_results = len(processed_results)
        avg_quality = sum(result.quality_score for result in processed_results) / total_results
        
        summary = f"关于'{query}'的搜索找到了{total_results}个相关结果，平均质量评分为{avg_quality:.2f}。"
        
        # 添加主要内容类型
        content_types = {}
        for result in processed_results:
            content_types[result.content_type] = content_types.get(result.content_type, 0) + 1
        
        if content_types:
            main_type = max(content_types.items(), key=lambda x: x[1])
            summary += f" 主要内容类型为{main_type[0]}。"
        
        return summary
    
    async def _extract_key_insights(self, processed_results: List[ProcessedContent]) -> List[str]:
        """提取关键洞察"""
        insights = []
        
        if not processed_results:
            return insights
        
        # 统计关键词频率
        all_keywords = []
        for result in processed_results:
            all_keywords.extend(result.keywords)
        
        keyword_freq = {}
        for keyword in all_keywords:
            keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1
        
        # 生成基于关键词的洞察
        top_keywords = sorted(keyword_freq.items(), key=lambda x: x[1], reverse=True)[:5]
        if top_keywords:
            insights.append(f"最频繁提及的关键词：{', '.join([kw for kw, freq in top_keywords])}")
        
        # 质量分析洞察
        high_quality_count = sum(1 for result in processed_results if result.quality_score > 0.7)
        if high_quality_count > 0:
            insights.append(f"发现{high_quality_count}个高质量内容源")
        
        return insights
    
    async def _identify_related_topics(self, processed_results: List[ProcessedContent]) -> List[str]:
        """识别相关主题"""
        if not processed_results:
            return []
        
        # 基于关键词聚类识别相关主题
        all_keywords = []
        for result in processed_results:
            all_keywords.extend(result.keywords)
        
        # 简单的主题识别：返回高频关键词作为相关主题
        keyword_freq = {}
        for keyword in all_keywords:
            keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1
        
        # 过滤低频词并返回
        related_topics = [kw for kw, freq in keyword_freq.items() if freq >= 2]
        return related_topics[:10]  # 限制数量
    
    def _calculate_confidence_score(self, processed_results: List[ProcessedContent]) -> float:
        """计算整体置信度评分"""
        if not processed_results:
            return 0.0
        
        # 基于结果数量和质量计算置信度
        result_count_score = min(1.0, len(processed_results) / 5.0)  # 5个结果为满分
        avg_quality_score = sum(result.quality_score for result in processed_results) / len(processed_results)
        
        confidence = (result_count_score * 0.4 + avg_quality_score * 0.6)
        return round(confidence, 2)
    
    async def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        stats = self.processing_stats.copy()
        
        # 计算成功率
        total = stats["total_processed"]
        if total > 0:
            stats["success_rate"] = stats["successful_processed"] / total
        else:
            stats["success_rate"] = 0.0
        
        # 计算平均质量评分
        if stats["quality_scores"]:
            stats["average_quality_score"] = sum(stats["quality_scores"]) / len(stats["quality_scores"])
        else:
            stats["average_quality_score"] = 0.0
        
        return stats

    async def extract_key_information(self, content: str) -> Dict[str, Any]:
        """
        提取内容中的关键信息

        Args:
            content: 原始内容文本

        Returns:
            Dict[str, Any]: 提取的关键信息
        """
        try:
            # 清理内容
            cleaned_content = self._clean_content(content)

            # 提取各种信息
            keywords = self._extract_keywords(cleaned_content)
            entities = self._extract_entities(cleaned_content)

            # 提取主题和要点
            topics = self._extract_topics(cleaned_content)
            key_points = self._extract_key_points(cleaned_content)

            # 统计信息
            stats = {
                "word_count": self._count_words(cleaned_content),
                "sentence_count": len(self.patterns['sentences'].split(cleaned_content)),
                "paragraph_count": len(cleaned_content.split('\n\n')),
                "language": self._detect_language(cleaned_content),
                "content_type": self._detect_content_type(cleaned_content)
            }

            return {
                "keywords": keywords,
                "entities": entities,
                "topics": topics,
                "key_points": key_points,
                "statistics": stats,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"提取关键信息失败: {str(e)}")
            return {
                "keywords": [],
                "entities": [],
                "topics": [],
                "key_points": [],
                "statistics": {},
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def generate_summary(self, contents: List[str], max_length: int = 500) -> Dict[str, Any]:
        """
        生成多个内容的综合摘要

        Args:
            contents: 内容列表
            max_length: 摘要最大长度

        Returns:
            Dict[str, Any]: 生成的摘要信息
        """
        try:
            if not contents:
                return {
                    "summary": "",
                    "key_themes": [],
                    "confidence": 0.0,
                    "source_count": 0,
                    "timestamp": datetime.now().isoformat()
                }

            # 清理所有内容
            cleaned_contents = [self._clean_content(content) for content in contents]
            cleaned_contents = [content for content in cleaned_contents if content]

            if not cleaned_contents:
                return {
                    "summary": "",
                    "key_themes": [],
                    "confidence": 0.0,
                    "source_count": 0,
                    "timestamp": datetime.now().isoformat()
                }

            # 提取所有关键词和主题
            all_keywords = []
            all_topics = []

            for content in cleaned_contents:
                keywords = self._extract_keywords(content)
                topics = self._extract_topics(content)
                all_keywords.extend(keywords)
                all_topics.extend(topics)

            # 统计关键词频率
            keyword_freq = {}
            for keyword in all_keywords:
                keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1

            # 获取最重要的关键词
            top_keywords = sorted(keyword_freq.items(), key=lambda x: x[1], reverse=True)[:10]
            key_themes = [keyword for keyword, freq in top_keywords]

            # 生成综合摘要
            summary = self._generate_comprehensive_summary(cleaned_contents, max_length)

            # 计算置信度
            confidence = self._calculate_summary_confidence(cleaned_contents, summary)

            return {
                "summary": summary,
                "key_themes": key_themes,
                "confidence": confidence,
                "source_count": len(cleaned_contents),
                "word_count": len(summary.split()),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"生成摘要失败: {str(e)}")
            return {
                "summary": "",
                "key_themes": [],
                "confidence": 0.0,
                "source_count": len(contents) if contents else 0,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def generate_intelligent_summary(self, contents: List[str],
                                         summary_type: str = "comprehensive",
                                         target_audience: str = "general",
                                         focus_areas: List[str] = None) -> Dict[str, Any]:
        """
        智能摘要生成 - 根据不同策略生成定制化摘要

        Args:
            contents: 内容列表
            summary_type: 摘要类型 (comprehensive, executive, technical, bullet_points)
            target_audience: 目标受众 (general, technical, executive, academic)
            focus_areas: 重点关注领域列表

        Returns:
            智能摘要结果字典
        """
        try:
            if not contents:
                return self._empty_summary_result("无内容可供摘要")

            # 预处理内容
            processed_contents = await self._preprocess_contents_for_summary(contents)
            if not processed_contents:
                return self._empty_summary_result("预处理后无有效内容")

            # 根据摘要类型选择策略
            summary_strategy = self._get_summary_strategy(summary_type, target_audience)

            # 应用焦点过滤
            if focus_areas:
                processed_contents = self._apply_focus_filter(processed_contents, focus_areas)

            # 生成摘要
            summary_result = await self._generate_strategic_summary(
                processed_contents, summary_strategy
            )

            # 后处理和质量评估
            final_result = await self._post_process_summary(summary_result, summary_type)

            # 更新统计信息
            self._update_summary_stats(final_result, summary_type)

            return final_result

        except Exception as e:
            logger.error(f"智能摘要生成失败: {e}")
            return self._empty_summary_result(f"生成失败: {str(e)}")

    async def generate_multi_perspective_summary(self, contents: List[str],
                                               perspectives: List[str] = None) -> Dict[str, Any]:
        """
        多角度摘要生成 - 从不同视角生成摘要

        Args:
            contents: 内容列表
            perspectives: 视角列表 (factual, analytical, critical, comparative)

        Returns:
            多角度摘要结果
        """
        try:
            if not contents:
                return {"multi_perspective_summary": {}, "error": "无内容可供摘要"}

            if not perspectives:
                perspectives = ["factual", "analytical", "critical"]

            # 预处理内容
            processed_contents = await self._preprocess_contents_for_summary(contents)

            multi_summary = {}
            overall_insights = []

            for perspective in perspectives:
                try:
                    # 为每个视角生成专门的摘要
                    perspective_summary = await self._generate_perspective_summary(
                        processed_contents, perspective
                    )
                    multi_summary[perspective] = perspective_summary

                    # 收集洞察
                    if "insights" in perspective_summary:
                        overall_insights.extend(perspective_summary["insights"])

                except Exception as e:
                    logger.warning(f"视角 {perspective} 摘要生成失败: {e}")
                    multi_summary[perspective] = {"error": str(e)}

            # 生成综合洞察
            comprehensive_insights = self._synthesize_insights(overall_insights)

            return {
                "multi_perspective_summary": multi_summary,
                "comprehensive_insights": comprehensive_insights,
                "perspectives_count": len([p for p in multi_summary.values() if "error" not in p]),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"多角度摘要生成失败: {e}")
            return {"multi_perspective_summary": {}, "error": str(e)}

    async def generate_adaptive_summary(self, contents: List[str],
                                      context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        自适应摘要生成 - 根据内容特征自动选择最佳摘要策略

        Args:
            contents: 内容列表
            context: 上下文信息（用户偏好、历史记录等）

        Returns:
            自适应摘要结果
        """
        try:
            if not contents:
                return self._empty_summary_result("无内容可供摘要")

            # 分析内容特征
            content_analysis = await self._analyze_content_characteristics(contents)

            # 根据特征选择最佳策略
            optimal_strategy = self._select_optimal_strategy(content_analysis, context)

            # 生成摘要
            summary_result = await self._generate_strategic_summary(contents, optimal_strategy)

            # 添加策略说明
            summary_result["strategy_used"] = optimal_strategy
            summary_result["content_analysis"] = content_analysis

            return summary_result

        except Exception as e:
            logger.error(f"自适应摘要生成失败: {e}")
            return self._empty_summary_result(f"生成失败: {str(e)}")

    async def structure_data(self, raw_content: str) -> Dict[str, Any]:
        """
        将非结构化内容转换为结构化数据

        Args:
            raw_content: 原始非结构化内容

        Returns:
            Dict[str, Any]: 结构化数据
        """
        try:
            if not raw_content:
                return {
                    "structured_data": {},
                    "confidence": 0.0,
                    "data_type": "empty",
                    "timestamp": datetime.now().isoformat()
                }

            # 先检测数据类型（使用原始内容）
            data_type = self._detect_data_structure_type(raw_content)

            # 然后清理内容用于处理
            cleaned_content = self._clean_content(raw_content)

            if not cleaned_content:
                return {
                    "structured_data": {},
                    "confidence": 0.0,
                    "data_type": "empty",
                    "timestamp": datetime.now().isoformat()
                }

            # 根据类型进行结构化处理（使用原始内容以保留格式）
            if data_type == "list":
                structured_data = self._structure_as_list(raw_content)
            elif data_type == "table":
                structured_data = self._structure_as_table(raw_content)
            elif data_type == "article":
                structured_data = self._structure_as_article(cleaned_content)
            elif data_type == "qa":
                structured_data = self._structure_as_qa(raw_content)
            else:
                structured_data = self._structure_as_general(cleaned_content)

            # 计算结构化置信度
            confidence = self._calculate_structure_confidence(structured_data)

            return {
                "structured_data": structured_data,
                "confidence": confidence,
                "data_type": data_type,
                "original_length": len(raw_content),
                "processed_length": len(cleaned_content),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"数据结构化失败: {str(e)}")
            return {
                "structured_data": {},
                "confidence": 0.0,
                "data_type": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    # ==================== 数据结构化处理系统 (步骤4.9) ====================

    async def extract_structured_entities(self, content: str) -> Dict[str, Any]:
        """
        从内容中提取结构化实体信息

        Args:
            content: 输入内容

        Returns:
            Dict[str, Any]: 提取的实体信息
        """
        try:
            if not content:
                return self._empty_entities_result("内容为空")

            cleaned_content = self._clean_content(content)
            if not cleaned_content:
                return self._empty_entities_result("清理后内容为空")

            # 提取各种类型的实体
            entities = {
                "persons": self._extract_person_entities(cleaned_content),
                "organizations": self._extract_organization_entities(cleaned_content),
                "locations": self._extract_location_entities(cleaned_content),
                "dates": self._extract_date_entities(cleaned_content),
                "numbers": self._extract_number_entities(cleaned_content),
                "urls": self._extract_url_entities(cleaned_content),
                "emails": self._extract_email_entities(cleaned_content),
                "phone_numbers": self._extract_phone_entities(cleaned_content)
            }

            # 计算实体提取置信度
            confidence = self._calculate_entity_confidence(entities)

            # 统计信息
            total_entities = sum(len(entity_list) for entity_list in entities.values())

            return {
                "entities": entities,
                "total_entities": total_entities,
                "confidence": confidence,
                "extraction_method": "pattern_based",
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            return self._empty_entities_result(f"提取失败: {str(e)}")

    async def convert_to_json_schema(self, content: str, schema_type: str = "auto") -> Dict[str, Any]:
        """
        将内容转换为JSON Schema格式

        Args:
            content: 输入内容
            schema_type: Schema类型 (auto/object/array/table)

        Returns:
            Dict[str, Any]: JSON Schema结果
        """
        try:
            if not content:
                return self._empty_schema_result("内容为空")

            cleaned_content = self._clean_content(content)
            if not cleaned_content:
                return self._empty_schema_result("清理后内容为空")

            # 自动检测最佳schema类型
            if schema_type == "auto":
                schema_type = self._detect_optimal_schema_type(cleaned_content)

            # 根据类型生成schema
            if schema_type == "object":
                schema = self._generate_object_schema(cleaned_content)
            elif schema_type == "array":
                schema = self._generate_array_schema(cleaned_content)
            elif schema_type == "table":
                schema = self._generate_table_schema(cleaned_content)
            else:
                schema = self._generate_generic_schema(cleaned_content)

            # 验证生成的schema
            validation_result = self._validate_json_schema(schema)

            return {
                "schema": schema,
                "schema_type": schema_type,
                "validation": validation_result,
                "confidence": validation_result.get("confidence", 0.0),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"JSON Schema转换失败: {e}")
            return self._empty_schema_result(f"转换失败: {str(e)}")

    async def extract_data_relationships(self, content: str) -> Dict[str, Any]:
        """
        提取内容中的数据关系

        Args:
            content: 输入内容

        Returns:
            Dict[str, Any]: 数据关系分析结果
        """
        try:
            if not content:
                return self._empty_relationships_result("内容为空")

            cleaned_content = self._clean_content(content)
            if not cleaned_content:
                return self._empty_relationships_result("清理后内容为空")

            # 提取各种关系类型
            relationships = {
                "hierarchical": self._extract_hierarchical_relationships(cleaned_content),
                "causal": self._extract_causal_relationships(cleaned_content),
                "temporal": self._extract_temporal_relationships(cleaned_content),
                "spatial": self._extract_spatial_relationships(cleaned_content),
                "associative": self._extract_associative_relationships(cleaned_content)
            }

            # 构建关系图
            relationship_graph = self._build_relationship_graph(relationships)

            # 计算关系强度
            relationship_strength = self._calculate_relationship_strength(relationships)

            return {
                "relationships": relationships,
                "relationship_graph": relationship_graph,
                "relationship_strength": relationship_strength,
                "total_relationships": sum(len(rel_list) for rel_list in relationships.values()),
                "confidence": self._calculate_relationship_confidence(relationships),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"数据关系提取失败: {e}")
            return self._empty_relationships_result(f"提取失败: {str(e)}")

    async def normalize_data_format(self, content: str, target_format: str = "standard") -> Dict[str, Any]:
        """
        标准化数据格式

        Args:
            content: 输入内容
            target_format: 目标格式 (standard/csv/json/xml/yaml)

        Returns:
            Dict[str, Any]: 格式化结果
        """
        try:
            if not content:
                return self._empty_normalization_result("内容为空")

            # 检测原始格式
            original_format = self._detect_data_format(content)

            # 解析原始数据
            parsed_data = self._parse_data_by_format(content, original_format)
            if not parsed_data:
                return self._empty_normalization_result("数据解析失败")

            # 转换为目标格式
            if target_format == "standard":
                normalized_data = self._normalize_to_standard(parsed_data)
            elif target_format == "csv":
                normalized_data = self._normalize_to_csv(parsed_data)
            elif target_format == "json":
                normalized_data = self._normalize_to_json(parsed_data)
            elif target_format == "xml":
                normalized_data = self._normalize_to_xml(parsed_data)
            elif target_format == "yaml":
                normalized_data = self._normalize_to_yaml(parsed_data)
            else:
                normalized_data = self._normalize_to_standard(parsed_data)

            # 验证转换结果
            validation_result = self._validate_normalized_data(normalized_data, target_format)

            return {
                "original_format": original_format,
                "target_format": target_format,
                "normalized_data": normalized_data,
                "validation": validation_result,
                "data_quality": self._assess_data_quality(normalized_data),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"数据格式标准化失败: {e}")
            return self._empty_normalization_result(f"标准化失败: {str(e)}")

    async def detect_duplicates(self, contents: List[str], similarity_threshold: float = 0.8) -> Dict[str, Any]:
        """
        检测内容列表中的重复内容

        Args:
            contents: 内容列表
            similarity_threshold: 相似度阈值

        Returns:
            Dict[str, Any]: 重复检测报告
        """
        try:
            if not contents or len(contents) < 2:
                return {
                    "duplicates": [],
                    "unique_count": len(contents) if contents else 0,
                    "duplicate_count": 0,
                    "similarity_matrix": [],
                    "timestamp": datetime.now().isoformat()
                }

            # 清理所有内容
            cleaned_contents = [self._clean_content(content) for content in contents]

            # 先计算完整的相似度矩阵
            similarity_matrix = []
            for i in range(len(cleaned_contents)):
                row = []
                for j in range(len(cleaned_contents)):
                    if i == j:
                        similarity = 1.0
                    else:
                        similarity = self._calculate_content_similarity(
                            cleaned_contents[i],
                            cleaned_contents[j]
                        )
                    row.append(similarity)
                similarity_matrix.append(row)

            # 然后检测重复组
            duplicates = []
            processed_indices = set()

            for i in range(len(cleaned_contents)):
                if i in processed_indices:
                    continue

                duplicate_group = [i]

                for j in range(i + 1, len(cleaned_contents)):
                    if j in processed_indices:
                        continue

                    similarity = similarity_matrix[i][j]

                    # 如果相似度超过阈值，加入重复组
                    if similarity >= similarity_threshold:
                        duplicate_group.append(j)
                        processed_indices.add(j)

                # 如果找到重复内容
                if len(duplicate_group) > 1:
                    similarity_scores = []
                    for j in duplicate_group[1:]:
                        similarity_scores.append(similarity_matrix[i][j])

                    duplicates.append({
                        "indices": duplicate_group,
                        "similarity_scores": similarity_scores,
                        "representative_index": i,  # 使用第一个作为代表
                        "content_preview": cleaned_contents[i][:100] + "..." if len(cleaned_contents[i]) > 100 else cleaned_contents[i]
                    })

            unique_count = len(contents) - sum(len(group["indices"]) - 1 for group in duplicates)
            duplicate_count = len(contents) - unique_count

            return {
                "duplicates": duplicates,
                "unique_count": unique_count,
                "duplicate_count": duplicate_count,
                "similarity_matrix": similarity_matrix,
                "threshold_used": similarity_threshold,
                "total_comparisons": len(contents) * (len(contents) - 1) // 2,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"重复检测失败: {str(e)}")
            return {
                "duplicates": [],
                "unique_count": len(contents) if contents else 0,
                "duplicate_count": 0,
                "similarity_matrix": [],
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _extract_topics(self, content: str) -> List[str]:
        """提取内容主题"""
        if not content:
            return []

        # 简单的主题提取：基于关键词聚类
        keywords = self._extract_keywords(content)

        # 主题词典（可以扩展）
        topic_keywords = {
            "技术": ["技术", "开发", "编程", "软件", "系统", "算法", "数据", "AI", "人工智能"],
            "商业": ["商业", "市场", "销售", "营销", "客户", "产品", "服务", "公司"],
            "教育": ["教育", "学习", "培训", "课程", "教学", "学生", "老师", "知识"],
            "健康": ["健康", "医疗", "疾病", "治疗", "药物", "医生", "患者", "保健"],
            "科学": ["科学", "研究", "实验", "发现", "理论", "学术", "论文", "科研"],
            "娱乐": ["娱乐", "游戏", "电影", "音乐", "体育", "休闲", "文化", "艺术"]
        }

        topics = []
        for topic, topic_words in topic_keywords.items():
            if any(word in content.lower() for word in topic_words):
                topics.append(topic)

        return topics[:5]  # 最多返回5个主题

    def _extract_key_points(self, content: str) -> List[str]:
        """提取关键要点"""
        if not content:
            return []

        # 按句子分割
        sentences = self.patterns['sentences'].split(content)
        sentences = [s.strip() for s in sentences if s.strip()]

        # 简单的要点提取：选择较长且包含关键词的句子
        key_points = []
        for sentence in sentences:
            if (len(sentence) > 20 and
                len(sentence) < 200 and
                any(word in sentence.lower() for word in ['重要', '关键', '主要', '核心', '首先', '其次', '最后', '因此', '所以'])):
                key_points.append(sentence)

        return key_points[:10]  # 最多返回10个要点

    def _generate_comprehensive_summary(self, contents: List[str], max_length: int) -> str:
        """生成综合摘要"""
        if not contents:
            return ""

        # 合并所有内容
        combined_content = " ".join(contents)

        # 提取最重要的句子
        sentences = self.patterns['sentences'].split(combined_content)
        sentences = [s.strip() for s in sentences if s.strip()]

        # 简单的句子重要性评分
        sentence_scores = []
        for sentence in sentences:
            score = 0
            # 基于长度评分
            if 20 <= len(sentence) <= 150:
                score += 1
            # 基于关键词评分
            if any(word in sentence.lower() for word in ['重要', '关键', '主要', '核心', '显示', '表明', '发现']):
                score += 2
            # 基于数字和统计评分
            if self.patterns['numbers'].search(sentence):
                score += 1

            sentence_scores.append((sentence, score))

        # 按评分排序并选择最佳句子
        sentence_scores.sort(key=lambda x: x[1], reverse=True)

        summary = ""
        for sentence, score in sentence_scores:
            if len(summary + sentence) <= max_length:
                summary += sentence + " "
            else:
                break

        return summary.strip()

    def _calculate_summary_confidence(self, contents: List[str], summary: str) -> float:
        """计算摘要置信度"""
        if not contents or not summary:
            return 0.0

        # 基于内容覆盖度计算置信度
        total_words = sum(len(content.split()) for content in contents)
        summary_words = len(summary.split())

        # 覆盖度评分
        coverage_score = min(1.0, summary_words / max(1, total_words * 0.1))

        # 质量评分（基于摘要长度和结构）
        quality_score = 0.5
        if 50 <= len(summary) <= 500:
            quality_score += 0.3
        if '.' in summary:
            quality_score += 0.2

        return round((coverage_score * 0.6 + quality_score * 0.4), 2)

    def _detect_data_structure_type(self, content: str) -> str:
        """检测数据结构类型"""
        if not content:
            return "empty"

        content_lower = content.lower()

        # 检测列表结构 - 改进检测逻辑
        list_indicators = 0

        # 检查数字列表模式
        if re.search(r'\n\s*\d+\.', content):  # 换行后数字列表
            list_indicators += 2
        if re.search(r'^\s*\d+\.\s+', content, re.MULTILINE):  # 行首数字列表
            list_indicators += 2

        # 检查符号列表模式
        if content.count('\n-') >= 1 or content.count('\n•') >= 1:  # 换行后符号列表
            list_indicators += 2
        if re.search(r'^\s*[-•]\s+', content, re.MULTILINE):  # 行首符号列表
            list_indicators += 1

        # 检查是否有多个列表项
        numbered_items = len(re.findall(r'\d+\.\s+', content))
        if numbered_items >= 2:
            list_indicators += 2

        # 调试输出
        logger.debug(f"列表检测 - 内容: {repr(content[:100])}")
        logger.debug(f"列表指标: {list_indicators}, 数字项: {numbered_items}")

        if list_indicators >= 2:
            return "list"

        # 检测表格结构 - 改进检测逻辑
        table_indicators = 0
        pipe_count = content.count('|')
        tab_count = content.count('\t')

        if pipe_count >= 3:  # 至少3个管道符
            table_indicators += 2
        if tab_count >= 3:  # 至少3个制表符
            table_indicators += 2
        if re.search(r'\|.*\|.*\|', content):  # 表格行模式
            table_indicators += 1
        if '表格' in content_lower or 'table' in content_lower:
            table_indicators += 1

        # 检查是否有多行包含管道符
        lines_with_pipes = len([line for line in content.split('\n') if '|' in line])
        if lines_with_pipes >= 2:
            table_indicators += 1

        # 调试输出
        logger.debug(f"表格检测 - 管道符: {pipe_count}, 制表符: {tab_count}, 表格行: {lines_with_pipes}, 指标: {table_indicators}")

        if table_indicators >= 2:
            return "table"

        # 检测问答结构
        qa_indicators = 0
        if content.count('?') >= 2 or content.count('？') >= 2:
            qa_indicators += 1
        if '问:' in content or '答:' in content:
            qa_indicators += 2
        if 'q:' in content_lower or 'a:' in content_lower:
            qa_indicators += 2

        if qa_indicators >= 2:
            return "qa"

        # 检测文章结构
        if (len(content) > 500 and
            content.count('\n\n') > 2):
            return "article"

        return "general"

    def _structure_as_list(self, content: str) -> Dict[str, Any]:
        """将内容结构化为列表"""
        lines = content.split('\n')
        items = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查各种列表格式
            if (line.startswith('-') or
                line.startswith('•') or
                re.match(r'^\d+\.', line)):
                # 清理列表标记
                item = re.sub(r'^[-•]\s*', '', line)
                item = re.sub(r'^\d+\.\s*', '', item)
                if item:
                    items.append(item.strip())
            # 检查是否包含数字列表项（即使不在行首）
            elif re.search(r'\d+\.\s+', line):
                # 提取所有数字列表项
                matches = re.findall(r'\d+\.\s+([^0-9]+?)(?=\d+\.|$)', line)
                for match in matches:
                    if match.strip():
                        items.append(match.strip())

        # 如果没有找到标准格式的列表项，尝试按数字模式分割
        if not items and re.search(r'\d+\.\s+', content):
            # 按数字模式分割整个内容
            parts = re.split(r'\d+\.\s+', content)
            for i, part in enumerate(parts[1:], 1):  # 跳过第一个空部分
                # 清理并提取有效内容
                item = part.strip()
                # 移除下一个数字项之前的内容
                next_num_match = re.search(r'(\d+\.\s+)', item)
                if next_num_match:
                    item = item[:next_num_match.start()].strip()
                if item:
                    items.append(item)

        return {
            "type": "list",
            "items": items,
            "count": len(items)
        }

    def _structure_as_table(self, content: str) -> Dict[str, Any]:
        """将内容结构化为表格"""
        lines = content.split('\n')
        rows = []
        headers = []

        for i, line in enumerate(lines):
            line = line.strip()
            if '|' in line:
                cells = [cell.strip() for cell in line.split('|') if cell.strip()]
                if cells:
                    if i == 0 or not headers:
                        headers = cells
                    else:
                        rows.append(cells)
            elif '\t' in line:
                cells = [cell.strip() for cell in line.split('\t') if cell.strip()]
                if cells:
                    if i == 0 or not headers:
                        headers = cells
                    else:
                        rows.append(cells)

        return {
            "type": "table",
            "headers": headers,
            "rows": rows,
            "row_count": len(rows),
            "column_count": len(headers)
        }

    def _structure_as_article(self, content: str) -> Dict[str, Any]:
        """将内容结构化为文章"""
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]

        # 尝试识别标题
        title = ""
        if paragraphs:
            first_para = paragraphs[0]
            if len(first_para) < 100 and not first_para.endswith('.'):
                title = first_para
                paragraphs = paragraphs[1:]

        return {
            "type": "article",
            "title": title,
            "paragraphs": paragraphs,
            "paragraph_count": len(paragraphs),
            "word_count": sum(len(p.split()) for p in paragraphs)
        }

    def _structure_as_qa(self, content: str) -> Dict[str, Any]:
        """将内容结构化为问答"""
        lines = content.split('\n')
        qa_pairs = []
        current_question = ""
        current_answer = ""

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测问题
            if (line.startswith('问:') or line.startswith('Q:') or
                line.startswith('问：') or line.endswith('?') or line.endswith('？')):
                if current_question and current_answer:
                    qa_pairs.append({
                        "question": current_question,
                        "answer": current_answer
                    })
                current_question = line
                current_answer = ""
            # 检测答案
            elif (line.startswith('答:') or line.startswith('A:') or
                  line.startswith('答：')):
                current_answer = line
            else:
                if current_question and not current_answer:
                    current_answer = line
                elif current_answer:
                    current_answer += " " + line

        # 添加最后一对
        if current_question and current_answer:
            qa_pairs.append({
                "question": current_question,
                "answer": current_answer
            })

        return {
            "type": "qa",
            "qa_pairs": qa_pairs,
            "pair_count": len(qa_pairs)
        }

    def _structure_as_general(self, content: str) -> Dict[str, Any]:
        """将内容结构化为通用格式"""
        # 提取基本信息
        sentences = self.patterns['sentences'].split(content)
        sentences = [s.strip() for s in sentences if s.strip()]

        keywords = self._extract_keywords(content)
        entities = self._extract_entities(content)

        return {
            "type": "general",
            "sentences": sentences,
            "sentence_count": len(sentences),
            "keywords": keywords,
            "entities": entities,
            "word_count": self._count_words(content)
        }

    def _calculate_structure_confidence(self, structured_data: Dict[str, Any]) -> float:
        """计算结构化置信度"""
        if not structured_data:
            return 0.0

        data_type = structured_data.get("type", "unknown")
        confidence = 0.5  # 基础置信度

        if data_type == "list":
            item_count = structured_data.get("count", 0)
            if item_count > 0:
                confidence += min(0.4, item_count * 0.1)
        elif data_type == "table":
            row_count = structured_data.get("row_count", 0)
            col_count = structured_data.get("column_count", 0)
            if row_count > 0 and col_count > 0:
                confidence += min(0.4, (row_count * col_count) * 0.02)
        elif data_type == "article":
            para_count = structured_data.get("paragraph_count", 0)
            if para_count > 1:
                confidence += min(0.4, para_count * 0.05)
        elif data_type == "qa":
            pair_count = structured_data.get("pair_count", 0)
            if pair_count > 0:
                confidence += min(0.4, pair_count * 0.1)

        return round(min(1.0, confidence), 2)

    def _calculate_content_similarity(self, content1: str, content2: str) -> float:
        """计算两个内容的相似度"""
        if not content1 or not content2:
            return 0.0

        # 如果内容完全相同
        if content1 == content2:
            return 1.0

        # 清理和标准化内容
        clean1 = self._clean_content(content1).lower()
        clean2 = self._clean_content(content2).lower()

        if not clean1 or not clean2:
            return 0.0

        # 提取词汇
        words1 = set(self.patterns['words'].findall(clean1))
        words2 = set(self.patterns['words'].findall(clean2))

        # 移除停用词和短词
        stop_words = {'的', '是', '在', '和', '与', '或', '但', '而', '了', '着', '过', 'the', 'a', 'an', 'and', 'or', 'but'}
        words1 = {w for w in words1 if len(w) > 1 and w not in stop_words}
        words2 = {w for w in words2 if len(w) > 1 and w not in stop_words}

        if not words1 or not words2:
            return 0.0

        # 计算Jaccard相似度
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        jaccard_similarity = intersection / union if union > 0 else 0.0

        # 计算字符级相似度（编辑距离）
        char_similarity = self._calculate_edit_distance_similarity(clean1, clean2)

        # 计算长度相似度
        len1, len2 = len(clean1), len(clean2)
        length_similarity = min(len1, len2) / max(len1, len2) if max(len1, len2) > 0 else 0.0

        # 综合相似度 - 调整权重
        similarity = (jaccard_similarity * 0.5 + char_similarity * 0.3 + length_similarity * 0.2)

        return round(similarity, 3)

    def _calculate_edit_distance_similarity(self, s1: str, s2: str) -> float:
        """基于编辑距离计算相似度"""
        if not s1 or not s2:
            return 0.0

        # 简化的编辑距离计算
        len1, len2 = len(s1), len(s2)
        if len1 == 0:
            return 0.0 if len2 > 0 else 1.0
        if len2 == 0:
            return 0.0

        # 使用动态规划计算编辑距离
        dp = [[0] * (len2 + 1) for _ in range(len1 + 1)]

        for i in range(len1 + 1):
            dp[i][0] = i
        for j in range(len2 + 1):
            dp[0][j] = j

        for i in range(1, len1 + 1):
            for j in range(1, len2 + 1):
                if s1[i-1] == s2[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                else:
                    dp[i][j] = min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1]) + 1

        edit_distance = dp[len1][len2]
        max_len = max(len1, len2)

        # 转换为相似度
        similarity = 1.0 - (edit_distance / max_len)
        return max(0.0, similarity)

    async def batch_process_contents(self, contents: List[str],
                                   operations: List[str] = None) -> Dict[str, Any]:
        """
        批量处理多个内容

        Args:
            contents: 内容列表
            operations: 要执行的操作列表 ['extract', 'summarize', 'structure', 'deduplicate']

        Returns:
            Dict[str, Any]: 批量处理结果
        """
        if operations is None:
            operations = ['extract', 'summarize', 'structure', 'deduplicate']

        start_time = datetime.now()
        results = {
            "total_contents": len(contents),
            "operations_performed": operations,
            "results": {},
            "processing_time": 0.0,
            "timestamp": start_time.isoformat()
        }

        try:
            # 关键信息提取
            if 'extract' in operations:
                extraction_results = []
                for i, content in enumerate(contents):
                    result = await self.extract_key_information(content)
                    extraction_results.append({
                        "index": i,
                        "result": result
                    })
                results["results"]["extraction"] = extraction_results

            # 摘要生成
            if 'summarize' in operations:
                summary_result = await self.generate_summary(contents)
                results["results"]["summary"] = summary_result

            # 数据结构化
            if 'structure' in operations:
                structure_results = []
                for i, content in enumerate(contents):
                    result = await self.structure_data(content)
                    structure_results.append({
                        "index": i,
                        "result": result
                    })
                results["results"]["structure"] = structure_results

            # 重复检测
            if 'deduplicate' in operations:
                duplicate_result = await self.detect_duplicates(contents)
                results["results"]["duplicates"] = duplicate_result

            # 计算处理时间
            end_time = datetime.now()
            results["processing_time"] = (end_time - start_time).total_seconds()

            logger.info(f"批量处理完成: {len(contents)}个内容, 耗时{results['processing_time']:.2f}秒")

            return results

        except Exception as e:
            logger.error(f"批量处理失败: {str(e)}")
            results["error"] = str(e)
            results["processing_time"] = (datetime.now() - start_time).total_seconds()
            return results

    # ==================== 智能摘要生成辅助方法 ====================

    def _empty_summary_result(self, message: str) -> Dict[str, Any]:
        """返回空摘要结果"""
        return {
            "summary": "",
            "key_points": [],
            "confidence": 0.0,
            "word_count": 0,
            "strategy": "none",
            "message": message,
            "timestamp": datetime.now().isoformat()
        }

    async def _preprocess_contents_for_summary(self, contents: List[str]) -> List[str]:
        """预处理内容用于摘要生成"""
        processed = []
        for content in contents:
            cleaned = self._clean_content(content)
            if cleaned and len(cleaned.strip()) > 30:  # 过滤过短内容
                processed.append(cleaned)
        return processed

    def _get_summary_strategy(self, summary_type: str, target_audience: str) -> Dict[str, Any]:
        """根据摘要类型和目标受众获取摘要策略"""
        strategies = {
            "comprehensive": {
                "max_length": 800,
                "include_details": True,
                "focus_on": ["main_points", "supporting_details", "conclusions"],
                "tone": "informative"
            },
            "executive": {
                "max_length": 300,
                "include_details": False,
                "focus_on": ["key_outcomes", "recommendations", "impact"],
                "tone": "decisive"
            },
            "technical": {
                "max_length": 600,
                "include_details": True,
                "focus_on": ["methodology", "data", "technical_details"],
                "tone": "precise"
            },
            "bullet_points": {
                "max_length": 400,
                "include_details": False,
                "focus_on": ["key_points", "action_items"],
                "tone": "concise",
                "format": "bullets"
            }
        }

        base_strategy = strategies.get(summary_type, strategies["comprehensive"])

        # 根据目标受众调整策略
        if target_audience == "technical":
            base_strategy["include_technical_terms"] = True
            base_strategy["detail_level"] = "high"
        elif target_audience == "executive":
            base_strategy["include_business_impact"] = True
            base_strategy["detail_level"] = "low"
        elif target_audience == "academic":
            base_strategy["include_references"] = True
            base_strategy["detail_level"] = "high"
        else:  # general
            base_strategy["detail_level"] = "medium"

        return base_strategy

    def _apply_focus_filter(self, contents: List[str], focus_areas: List[str]) -> List[str]:
        """应用焦点过滤，突出重点关注领域"""
        if not focus_areas:
            return contents

        filtered_contents = []
        focus_keywords = [area.lower() for area in focus_areas]

        for content in contents:
            content_lower = content.lower()
            # 计算内容与焦点领域的相关性
            relevance_score = 0
            for keyword in focus_keywords:
                if keyword in content_lower:
                    relevance_score += content_lower.count(keyword)

            # 如果内容与焦点领域相关，则保留
            if relevance_score > 0:
                filtered_contents.append(content)

        # 如果过滤后没有内容，返回原始内容
        return filtered_contents if filtered_contents else contents

    async def _generate_strategic_summary(self, contents: List[str],
                                        strategy: Dict[str, Any]) -> Dict[str, Any]:
        """根据策略生成摘要"""
        try:
            if not contents:
                return self._empty_summary_result("无内容可供摘要")

            # 合并内容
            combined_content = "\n\n".join(contents)

            # 根据策略提取关键信息
            key_info = self._extract_strategic_information(combined_content, strategy)

            # 生成摘要文本
            summary_text = self._generate_summary_text(key_info, strategy)

            # 提取关键点
            key_points = self._extract_strategic_key_points(key_info, strategy)

            # 计算置信度
            confidence = self._calculate_strategic_confidence(summary_text, combined_content, strategy)

            return {
                "summary": summary_text,
                "key_points": key_points,
                "confidence": confidence,
                "word_count": len(summary_text.split()),
                "strategy": strategy.get("tone", "informative"),
                "source_count": len(contents),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"策略摘要生成失败: {e}")
            return self._empty_summary_result(f"生成失败: {str(e)}")

    async def _post_process_summary(self, summary_result: Dict[str, Any],
                                  summary_type: str) -> Dict[str, Any]:
        """后处理摘要结果"""
        try:
            # 质量检查
            quality_score = self._assess_summary_quality(summary_result)
            summary_result["quality_score"] = quality_score

            # 格式化处理
            if summary_type == "bullet_points":
                summary_result["summary"] = self._format_as_bullets(summary_result["summary"])

            # 添加元数据
            summary_result["summary_type"] = summary_type
            summary_result["processing_version"] = "intelligent_v1.0"

            return summary_result

        except Exception as e:
            logger.error(f"摘要后处理失败: {e}")
            summary_result["post_process_error"] = str(e)
            return summary_result

    def _update_summary_stats(self, result: Dict[str, Any], summary_type: str):
        """更新摘要统计信息"""
        if "summaries_by_type" not in self.processing_stats:
            self.processing_stats["summaries_by_type"] = {}

        if summary_type not in self.processing_stats["summaries_by_type"]:
            self.processing_stats["summaries_by_type"][summary_type] = 0

        self.processing_stats["summaries_by_type"][summary_type] += 1
        self.processing_stats["intelligent_summaries_generated"] = \
            self.processing_stats.get("intelligent_summaries_generated", 0) + 1

    async def _generate_perspective_summary(self, contents: List[str],
                                          perspective: str) -> Dict[str, Any]:
        """为特定视角生成摘要"""
        try:
            perspective_strategies = {
                "factual": {
                    "focus": ["facts", "data", "statistics", "evidence"],
                    "tone": "objective",
                    "avoid": ["opinions", "speculation"]
                },
                "analytical": {
                    "focus": ["patterns", "trends", "relationships", "implications"],
                    "tone": "analytical",
                    "include": ["analysis", "interpretation"]
                },
                "critical": {
                    "focus": ["limitations", "weaknesses", "gaps", "concerns"],
                    "tone": "critical",
                    "include": ["evaluation", "critique"]
                },
                "comparative": {
                    "focus": ["differences", "similarities", "contrasts"],
                    "tone": "comparative",
                    "include": ["comparison", "benchmarking"]
                }
            }

            strategy = perspective_strategies.get(perspective, perspective_strategies["factual"])

            # 根据视角过滤和处理内容
            filtered_content = self._filter_content_by_perspective(contents, strategy)

            # 生成视角特定的摘要
            summary = self._generate_perspective_specific_summary(filtered_content, strategy)

            # 提取视角特定的洞察
            insights = self._extract_perspective_insights(filtered_content, strategy)

            return {
                "summary": summary,
                "insights": insights,
                "perspective": perspective,
                "confidence": self._calculate_perspective_confidence(summary, filtered_content),
                "word_count": len(summary.split()),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"视角摘要生成失败 ({perspective}): {e}")
            return {"error": str(e), "perspective": perspective}

    def _synthesize_insights(self, insights_list: List[str]) -> List[str]:
        """综合多个视角的洞察"""
        if not insights_list:
            return []

        # 去重和合并相似洞察
        unique_insights = []
        for insight in insights_list:
            if insight and not any(self._are_insights_similar(insight, existing)
                                 for existing in unique_insights):
                unique_insights.append(insight)

        # 按重要性排序（简单实现）
        return sorted(unique_insights, key=len, reverse=True)[:5]

    async def _analyze_content_characteristics(self, contents: List[str]) -> Dict[str, Any]:
        """分析内容特征"""
        try:
            if not contents:
                return {"type": "empty", "complexity": "none"}

            combined_content = "\n".join(contents)

            # 基本特征
            word_count = len(combined_content.split())
            sentence_count = len(self._extract_sentences(combined_content))

            # 内容类型检测
            content_type = self._detect_content_type(combined_content)

            # 复杂度评估
            complexity = self._assess_content_complexity(combined_content)

            # 主题分析
            topics = self._extract_topics(combined_content)

            return {
                "type": content_type,
                "complexity": complexity,
                "word_count": word_count,
                "sentence_count": sentence_count,
                "topics": topics[:5],  # 前5个主题
                "has_technical_content": self._has_technical_content(combined_content),
                "has_numerical_data": self._has_numerical_data(combined_content),
                "language_complexity": self._assess_language_complexity(combined_content)
            }

        except Exception as e:
            logger.error(f"内容特征分析失败: {e}")
            return {"type": "unknown", "complexity": "unknown", "error": str(e)}

    def _select_optimal_strategy(self, content_analysis: Dict[str, Any],
                               context: Dict[str, Any] = None) -> Dict[str, Any]:
        """根据内容特征选择最佳摘要策略"""
        # 基础策略
        strategy = {
            "max_length": 500,
            "tone": "informative",
            "detail_level": "medium"
        }

        # 根据内容类型调整
        content_type = content_analysis.get("type", "general")
        if content_type == "technical":
            strategy.update({
                "max_length": 600,
                "tone": "precise",
                "detail_level": "high",
                "include_technical_terms": True
            })
        elif content_type == "news":
            strategy.update({
                "max_length": 400,
                "tone": "informative",
                "focus_on": ["who", "what", "when", "where", "why"]
            })
        elif content_type == "academic":
            strategy.update({
                "max_length": 700,
                "tone": "academic",
                "detail_level": "high",
                "include_methodology": True
            })

        # 根据复杂度调整
        complexity = content_analysis.get("complexity", "medium")
        if complexity == "high":
            strategy["max_length"] = min(strategy["max_length"] + 200, 800)
            strategy["detail_level"] = "high"
        elif complexity == "low":
            strategy["max_length"] = max(strategy["max_length"] - 100, 200)
            strategy["detail_level"] = "low"

        # 根据上下文调整
        if context:
            user_preference = context.get("summary_preference", "balanced")
            if user_preference == "brief":
                strategy["max_length"] = min(strategy["max_length"], 300)
            elif user_preference == "detailed":
                strategy["max_length"] = max(strategy["max_length"], 600)

        return strategy

    # ==================== 辅助方法实现 ====================

    def _extract_strategic_information(self, content: str, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """根据策略提取关键信息"""
        focus_areas = strategy.get("focus_on", ["main_points"])

        info = {
            "sentences": self._extract_sentences(content),
            "keywords": self._extract_keywords(content),
            "topics": self._extract_topics(content)
        }

        # 根据焦点领域提取特定信息
        if "main_points" in focus_areas:
            info["main_points"] = self._extract_main_points(content)
        if "conclusions" in focus_areas:
            info["conclusions"] = self._extract_conclusions(content)
        if "recommendations" in focus_areas:
            info["recommendations"] = self._extract_recommendations(content)
        if "technical_details" in focus_areas:
            info["technical_details"] = self._extract_technical_details(content)

        return info

    def _generate_summary_text(self, key_info: Dict[str, Any], strategy: Dict[str, Any]) -> str:
        """根据关键信息和策略生成摘要文本"""
        max_length = strategy.get("max_length", 500)
        tone = strategy.get("tone", "informative")

        # 选择最重要的句子
        sentences = key_info.get("sentences", [])
        if not sentences:
            return "无法生成摘要"

        # 根据策略选择句子
        selected_sentences = self._select_sentences_by_strategy(sentences, strategy)

        # 生成摘要文本
        summary = " ".join(selected_sentences)

        # 确保长度不超过限制
        if len(summary.split()) > max_length:
            words = summary.split()[:max_length]
            summary = " ".join(words)

        return summary

    def _extract_strategic_key_points(self, key_info: Dict[str, Any],
                                    strategy: Dict[str, Any]) -> List[str]:
        """提取策略性关键点"""
        key_points = []

        # 从不同信息源提取关键点
        if "main_points" in key_info:
            key_points.extend(key_info["main_points"][:3])

        if "conclusions" in key_info:
            key_points.extend(key_info["conclusions"][:2])

        if "recommendations" in key_info:
            key_points.extend(key_info["recommendations"][:2])

        # 如果没有特定关键点，从关键词生成
        if not key_points and "keywords" in key_info:
            keywords = key_info["keywords"][:5]
            key_points = [f"关键概念: {keyword}" for keyword in keywords]

        return key_points[:5]  # 最多5个关键点

    def _calculate_strategic_confidence(self, summary: str, original_content: str,
                                      strategy: Dict[str, Any]) -> float:
        """计算策略摘要的置信度"""
        if not summary or not original_content:
            return 0.0

        base_confidence = 0.6

        # 长度适当性
        target_length = strategy.get("max_length", 500)
        actual_length = len(summary.split())
        length_ratio = min(actual_length / target_length, 1.0)
        length_score = 0.2 * length_ratio

        # 内容覆盖度
        summary_words = set(summary.lower().split())
        original_words = set(original_content.lower().split())
        coverage = len(summary_words.intersection(original_words)) / len(original_words) if original_words else 0
        coverage_score = 0.2 * min(coverage * 5, 1.0)  # 放大覆盖度影响

        return min(base_confidence + length_score + coverage_score, 1.0)

    def _assess_summary_quality(self, summary_result: Dict[str, Any]) -> float:
        """评估摘要质量"""
        quality_score = 0.0

        # 基础质量检查
        summary = summary_result.get("summary", "")
        if not summary:
            return 0.0

        # 长度合理性 (0.3权重)
        word_count = summary_result.get("word_count", 0)
        if 50 <= word_count <= 800:
            quality_score += 0.3
        elif word_count > 0:
            quality_score += 0.15

        # 置信度 (0.4权重)
        confidence = summary_result.get("confidence", 0.0)
        quality_score += 0.4 * confidence

        # 关键点数量 (0.2权重)
        key_points = summary_result.get("key_points", [])
        if 2 <= len(key_points) <= 5:
            quality_score += 0.2
        elif len(key_points) > 0:
            quality_score += 0.1

        # 内容完整性 (0.1权重)
        if summary and len(summary.strip()) > 20:
            quality_score += 0.1

        return min(quality_score, 1.0)

    def _format_as_bullets(self, text: str) -> str:
        """将文本格式化为项目符号列表"""
        if not text:
            return text

        # 首先尝试按句子分割
        sentences = self._extract_sentences(text)

        # 如果句子太少，尝试按段落分割
        if len(sentences) <= 1:
            paragraphs = [p.strip() for p in text.split('\n') if p.strip()]
            if len(paragraphs) > 1:
                sentences = paragraphs
            else:
                # 如果还是只有一个，按长度分割
                words = text.split()
                if len(words) > 20:
                    mid = len(words) // 2
                    sentences = [
                        " ".join(words[:mid]),
                        " ".join(words[mid:])
                    ]
                else:
                    # 最后回退：直接添加项目符号
                    return f"• {text.strip()}"

        # 将句子转换为项目符号
        bullets = []
        for sentence in sentences:
            if sentence.strip():
                bullets.append(f"• {sentence.strip()}")

        return "\n".join(bullets)

    def _filter_content_by_perspective(self, contents: List[str],
                                     strategy: Dict[str, Any]) -> List[str]:
        """根据视角过滤内容"""
        focus_keywords = strategy.get("focus", [])
        if not focus_keywords:
            return contents

        filtered = []
        for content in contents:
            content_lower = content.lower()
            relevance = sum(1 for keyword in focus_keywords if keyword in content_lower)
            if relevance > 0:
                filtered.append(content)

        return filtered if filtered else contents

    def _generate_perspective_specific_summary(self, contents: List[str],
                                             strategy: Dict[str, Any]) -> str:
        """生成视角特定的摘要"""
        if not contents:
            return ""

        combined = "\n".join(contents)
        sentences = self._extract_sentences(combined)

        # 根据视角选择句子
        focus_areas = strategy.get("focus", [])
        selected_sentences = []

        for sentence in sentences:
            sentence_lower = sentence.lower()
            relevance = sum(1 for focus in focus_areas if focus in sentence_lower)
            if relevance > 0:
                selected_sentences.append(sentence)

        if not selected_sentences:
            selected_sentences = sentences[:3]  # 回退到前3句

        return " ".join(selected_sentences[:5])  # 最多5句

    def _extract_perspective_insights(self, contents: List[str],
                                    strategy: Dict[str, Any]) -> List[str]:
        """提取视角特定的洞察"""
        if not contents:
            return []

        perspective = strategy.get("focus", [])
        insights = []

        combined = "\n".join(contents)

        # 根据不同视角提取洞察
        if "facts" in perspective:
            insights.extend(self._extract_factual_insights(combined))
        if "patterns" in perspective:
            insights.extend(self._extract_pattern_insights(combined))
        if "limitations" in perspective:
            insights.extend(self._extract_limitation_insights(combined))
        if "differences" in perspective:
            insights.extend(self._extract_comparison_insights(combined))

        return insights[:3]  # 最多3个洞察

    def _calculate_perspective_confidence(self, summary: str, contents: List[str]) -> float:
        """计算视角摘要的置信度"""
        if not summary or not contents:
            return 0.0

        # 基础置信度
        base_confidence = 0.5

        # 内容长度合理性
        if 20 <= len(summary.split()) <= 200:
            base_confidence += 0.2

        # 与原内容的相关性
        summary_words = set(summary.lower().split())
        all_content_words = set(" ".join(contents).lower().split())

        if all_content_words:
            relevance = len(summary_words.intersection(all_content_words)) / len(all_content_words)
            base_confidence += 0.3 * min(relevance * 10, 1.0)

        return min(base_confidence, 1.0)

    def _are_insights_similar(self, insight1: str, insight2: str) -> bool:
        """判断两个洞察是否相似"""
        if not insight1 or not insight2:
            return False

        words1 = set(insight1.lower().split())
        words2 = set(insight2.lower().split())

        if not words1 or not words2:
            return False

        # 计算词汇重叠度
        overlap = len(words1.intersection(words2))
        min_length = min(len(words1), len(words2))

        return overlap / min_length > 0.6 if min_length > 0 else False

    def _detect_content_type(self, content: str) -> str:
        """检测内容类型"""
        content_lower = content.lower()

        # 技术内容特征
        technical_indicators = ["api", "algorithm", "function", "method", "class", "variable",
                              "database", "server", "client", "protocol", "framework"]
        tech_count = sum(1 for indicator in technical_indicators if indicator in content_lower)

        # 新闻内容特征
        news_indicators = ["报道", "消息", "新闻", "据悉", "记者", "采访", "发布会", "声明"]
        news_count = sum(1 for indicator in news_indicators if indicator in content_lower)

        # 学术内容特征
        academic_indicators = ["研究", "分析", "实验", "数据", "结果", "结论", "方法", "理论"]
        academic_count = sum(1 for indicator in academic_indicators if indicator in content_lower)

        # 根据特征数量判断类型
        if tech_count >= 3:
            return "technical"
        elif news_count >= 2:
            return "news"
        elif academic_count >= 3:
            return "academic"
        else:
            return "general"

    def _assess_content_complexity(self, content: str) -> str:
        """评估内容复杂度"""
        if not content:
            return "none"

        # 计算各种复杂度指标
        sentences = self._extract_sentences(content)
        words = content.split()

        # 平均句子长度
        avg_sentence_length = len(words) / len(sentences) if sentences else 0

        # 复杂词汇比例（长度>6的词）
        complex_words = [w for w in words if len(w) > 6]
        complex_ratio = len(complex_words) / len(words) if words else 0

        # 技术术语数量
        technical_terms = self._count_technical_terms(content)

        # 综合评估
        complexity_score = 0
        if avg_sentence_length > 20:
            complexity_score += 1
        if complex_ratio > 0.3:
            complexity_score += 1
        if technical_terms > 5:
            complexity_score += 1

        if complexity_score >= 2:
            return "high"
        elif complexity_score == 1:
            return "medium"
        else:
            return "low"

    def _has_technical_content(self, content: str) -> bool:
        """检查是否包含技术内容"""
        technical_patterns = [
            r'\b\w+\(\)',  # 函数调用
            r'\b[A-Z][a-zA-Z]*[A-Z][a-zA-Z]*\b',  # 驼峰命名
            r'\b\d+\.\d+\.\d+\b',  # 版本号
            r'\b[a-zA-Z]+://\b',  # 协议
        ]

        for pattern in technical_patterns:
            if re.search(pattern, content):
                return True

        return False

    def _has_numerical_data(self, content: str) -> bool:
        """检查是否包含数值数据"""
        numerical_patterns = [
            r'\b\d+%\b',  # 百分比
            r'\b\d+\.\d+\b',  # 小数
            r'\b\d{4}\b',  # 年份
            r'\$\d+',  # 金额
        ]

        for pattern in numerical_patterns:
            if re.search(pattern, content):
                return True

        return False

    def _assess_language_complexity(self, content: str) -> str:
        """评估语言复杂度"""
        if not content:
            return "simple"

        words = content.split()
        if not words:
            return "simple"

        # 平均词长
        avg_word_length = sum(len(word) for word in words) / len(words)

        # 长词比例
        long_words = [w for w in words if len(w) > 8]
        long_word_ratio = len(long_words) / len(words)

        if avg_word_length > 6 and long_word_ratio > 0.2:
            return "complex"
        elif avg_word_length > 4 or long_word_ratio > 0.1:
            return "medium"
        else:
            return "simple"

    # ==================== 基础提取方法 ====================

    def _extract_sentences(self, content: str) -> List[str]:
        """提取句子"""
        if not content:
            return []

        # 使用正则表达式分割句子
        sentence_pattern = r'[.!?。！？]+\s*'
        sentences = re.split(sentence_pattern, content)

        # 清理和过滤句子
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and len(sentence) > 10:  # 过滤过短的句子
                cleaned_sentences.append(sentence)

        return cleaned_sentences

    def _generate_comprehensive_summary(self, contents: List[str], max_length: int) -> str:
        """生成综合摘要"""
        if not contents:
            return ""

        # 合并所有内容
        combined_content = "\n".join(contents)

        # 提取句子
        sentences = self._extract_sentences(combined_content)
        if not sentences:
            return "无法生成摘要"

        # 选择重要句子
        important_sentences = []

        # 优先选择包含关键词的句子
        keywords = ["重要", "主要", "关键", "核心", "显著", "明显"]
        for sentence in sentences:
            if any(keyword in sentence for keyword in keywords):
                important_sentences.append(sentence)

        # 如果没有找到关键句子，选择前几句
        if not important_sentences:
            important_sentences = sentences[:3]

        # 生成摘要
        summary = " ".join(important_sentences)

        # 控制长度
        words = summary.split()
        if len(words) > max_length:
            summary = " ".join(words[:max_length])

        return summary

    def _calculate_summary_confidence(self, contents: List[str], summary: str) -> float:
        """计算摘要置信度"""
        if not contents or not summary:
            return 0.0

        # 基础置信度
        base_confidence = 0.5

        # 内容覆盖度
        all_content = " ".join(contents)
        summary_words = set(summary.lower().split())
        content_words = set(all_content.lower().split())

        if content_words:
            coverage = len(summary_words.intersection(content_words)) / len(content_words)
            coverage_score = min(coverage * 2, 0.4)  # 最多贡献0.4
        else:
            coverage_score = 0

        # 长度合理性
        summary_length = len(summary.split())
        if 20 <= summary_length <= 200:
            length_score = 0.1
        else:
            length_score = 0

        return min(base_confidence + coverage_score + length_score, 1.0)

    def _extract_main_points(self, content: str) -> List[str]:
        """提取主要观点"""
        sentences = self._extract_sentences(content)
        main_points = []

        # 寻找包含关键指示词的句子
        indicators = ["主要", "重要", "关键", "核心", "首先", "其次", "最后", "总之"]

        for sentence in sentences:
            if any(indicator in sentence for indicator in indicators):
                main_points.append(sentence.strip())

        # 如果没有找到，选择前几个句子
        if not main_points:
            main_points = sentences[:3]

        return main_points[:5]

    def _extract_conclusions(self, content: str) -> List[str]:
        """提取结论"""
        sentences = self._extract_sentences(content)
        conclusions = []

        conclusion_indicators = ["因此", "所以", "总之", "综上", "结论", "总结", "最终"]

        for sentence in sentences:
            if any(indicator in sentence for indicator in conclusion_indicators):
                conclusions.append(sentence.strip())

        return conclusions[:3]

    def _extract_recommendations(self, content: str) -> List[str]:
        """提取建议"""
        sentences = self._extract_sentences(content)
        recommendations = []

        rec_indicators = ["建议", "推荐", "应该", "需要", "可以", "最好", "建议"]

        for sentence in sentences:
            if any(indicator in sentence for indicator in rec_indicators):
                recommendations.append(sentence.strip())

        return recommendations[:3]

    def _extract_technical_details(self, content: str) -> List[str]:
        """提取技术细节"""
        sentences = self._extract_sentences(content)
        tech_details = []

        tech_indicators = ["技术", "方法", "算法", "实现", "架构", "设计", "开发"]

        for sentence in sentences:
            if any(indicator in sentence for indicator in tech_indicators):
                tech_details.append(sentence.strip())

        return tech_details[:3]

    def _select_sentences_by_strategy(self, sentences: List[str],
                                    strategy: Dict[str, Any]) -> List[str]:
        """根据策略选择句子"""
        if not sentences:
            return []

        max_sentences = min(len(sentences), 5)  # 最多5句

        # 根据策略调整选择逻辑
        tone = strategy.get("tone", "informative")

        if tone == "decisive":
            # 选择包含决策性词汇的句子
            decisive_words = ["决定", "确定", "必须", "应该", "将会"]
            selected = [s for s in sentences if any(word in s for word in decisive_words)]
            if selected:
                return selected[:max_sentences]

        elif tone == "precise":
            # 选择包含具体数据或技术术语的句子
            precise_patterns = [r'\d+', r'[A-Z][a-z]+[A-Z]', r'技术|方法|算法']
            selected = []
            for sentence in sentences:
                if any(re.search(pattern, sentence) for pattern in precise_patterns):
                    selected.append(sentence)
            if selected:
                return selected[:max_sentences]

        # 默认选择前几句
        return sentences[:max_sentences]

    def _count_technical_terms(self, content: str) -> int:
        """计算技术术语数量"""
        tech_terms = [
            "API", "SDK", "HTTP", "JSON", "XML", "SQL", "NoSQL",
            "算法", "数据结构", "架构", "框架", "库", "接口", "协议",
            "服务器", "客户端", "数据库", "缓存", "队列", "微服务"
        ]

        content_upper = content.upper()
        count = 0
        for term in tech_terms:
            count += content_upper.count(term.upper())

        return count

    def _extract_factual_insights(self, content: str) -> List[str]:
        """提取事实性洞察"""
        insights = []
        sentences = self._extract_sentences(content)

        # 寻找包含数据、统计或事实的句子
        fact_indicators = ["数据显示", "统计", "事实", "研究表明", "调查发现"]

        for sentence in sentences:
            if any(indicator in sentence for indicator in fact_indicators):
                insights.append(f"事实洞察: {sentence.strip()}")

        return insights[:2]

    def _extract_pattern_insights(self, content: str) -> List[str]:
        """提取模式洞察"""
        insights = []
        sentences = self._extract_sentences(content)

        pattern_indicators = ["趋势", "模式", "规律", "变化", "增长", "下降"]

        for sentence in sentences:
            if any(indicator in sentence for indicator in pattern_indicators):
                insights.append(f"模式洞察: {sentence.strip()}")

        return insights[:2]

    def _extract_limitation_insights(self, content: str) -> List[str]:
        """提取局限性洞察"""
        insights = []
        sentences = self._extract_sentences(content)

        limitation_indicators = ["局限", "限制", "不足", "缺点", "问题", "挑战"]

        for sentence in sentences:
            if any(indicator in sentence for indicator in limitation_indicators):
                insights.append(f"局限性洞察: {sentence.strip()}")

        return insights[:2]

    def _extract_comparison_insights(self, content: str) -> List[str]:
        """提取比较洞察"""
        insights = []
        sentences = self._extract_sentences(content)

        comparison_indicators = ["相比", "对比", "比较", "差异", "相似", "不同"]

        for sentence in sentences:
            if any(indicator in sentence for indicator in comparison_indicators):
                insights.append(f"比较洞察: {sentence.strip()}")

        return insights[:2]

    # ==================== 信息验证系统 (步骤4.8) ====================

    async def validate_information(self, information: str, sources: List[str] = None,
                                 validation_type: str = "comprehensive") -> Dict[str, Any]:
        """
        验证信息的准确性和可靠性

        Args:
            information (str): 需要验证的信息
            sources (List[str]): 信息来源列表
            validation_type (str): 验证类型 (comprehensive/basic/cross_reference)

        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            logger.info(f"开始信息验证: {validation_type}")

            if not information or not information.strip():
                return self._empty_validation_result("信息内容为空")

            # 预处理信息
            processed_info = self._preprocess_information(information)

            # 根据验证类型执行不同的验证策略
            if validation_type == "comprehensive":
                result = await self._comprehensive_validation(processed_info, sources)
            elif validation_type == "basic":
                result = await self._basic_validation(processed_info, sources)
            elif validation_type == "cross_reference":
                result = await self._cross_reference_validation(processed_info, sources)
            else:
                result = await self._basic_validation(processed_info, sources)

            # 计算综合可信度分数
            result["credibility_score"] = self._calculate_credibility_score(result)

            # 生成验证报告
            result["validation_report"] = self._generate_validation_report(result)

            # 更新统计信息
            self._update_validation_stats(validation_type, result["credibility_score"])

            logger.info(f"信息验证完成，可信度: {result['credibility_score']:.2f}")
            return result

        except Exception as e:
            logger.error(f"信息验证失败: {e}")
            return self._empty_validation_result(f"验证失败: {str(e)}")

    async def cross_validate_sources(self, information: str, sources: List[str]) -> Dict[str, Any]:
        """
        交叉验证多个信息源

        Args:
            information (str): 需要验证的信息
            sources (List[str]): 多个信息源

        Returns:
            Dict[str, Any]: 交叉验证结果
        """
        try:
            logger.info(f"开始交叉验证，信息源数量: {len(sources)}")

            if not sources or len(sources) < 2:
                return {
                    "validation_status": "insufficient_sources",
                    "consistency_score": 0.0,
                    "source_count": len(sources),
                    "message": "需要至少2个信息源进行交叉验证"
                }

            # 分析每个信息源
            source_analyses = []
            for i, source in enumerate(sources):
                analysis = await self._analyze_information_source(information, source, i)
                source_analyses.append(analysis)

            # 计算源间一致性
            consistency_matrix = self._calculate_source_consistency(source_analyses)

            # 识别冲突信息
            conflicts = self._identify_information_conflicts(source_analyses)

            # 评估源可靠性
            source_reliability = self._assess_source_reliability(source_analyses)

            # 生成交叉验证结论
            conclusion = self._generate_cross_validation_conclusion(
                source_analyses, consistency_matrix, conflicts, source_reliability
            )

            result = {
                "validation_status": "completed",
                "information": information,
                "source_count": len(sources),
                "source_analyses": source_analyses,
                "consistency_score": consistency_matrix.get("overall_score", 0.0),
                "consistency_matrix": consistency_matrix,
                "conflicts": conflicts,
                "source_reliability": source_reliability,
                "conclusion": conclusion,
                "timestamp": datetime.now().isoformat()
            }

            logger.info(f"交叉验证完成，一致性分数: {result['consistency_score']:.2f}")
            return result

        except Exception as e:
            logger.error(f"交叉验证失败: {e}")
            return {
                "validation_status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def verify_factual_claims(self, claims: List[str], context: str = None) -> Dict[str, Any]:
        """
        验证事实性声明

        Args:
            claims (List[str]): 需要验证的声明列表
            context (str): 上下文信息

        Returns:
            Dict[str, Any]: 事实验证结果
        """
        try:
            logger.info(f"开始事实验证，声明数量: {len(claims)}")

            if not claims:
                return {
                    "verification_status": "no_claims",
                    "verified_claims": [],
                    "message": "没有需要验证的声明"
                }

            verified_claims = []

            for i, claim in enumerate(claims):
                claim_result = await self._verify_single_claim(claim, context, i)
                verified_claims.append(claim_result)

            # 计算整体可信度
            overall_credibility = self._calculate_overall_claim_credibility(verified_claims)

            # 识别高风险声明
            high_risk_claims = self._identify_high_risk_claims(verified_claims)

            # 生成验证摘要
            verification_summary = self._generate_verification_summary(verified_claims)

            result = {
                "verification_status": "completed",
                "claims_count": len(claims),
                "verified_claims": verified_claims,
                "overall_credibility": overall_credibility,
                "high_risk_claims": high_risk_claims,
                "verification_summary": verification_summary,
                "context": context,
                "timestamp": datetime.now().isoformat()
            }

            logger.info(f"事实验证完成，整体可信度: {overall_credibility:.2f}")
            return result

        except Exception as e:
            logger.error(f"事实验证失败: {e}")
            return {
                "verification_status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def assess_information_quality(self, information: str,
                                       quality_criteria: List[str] = None) -> Dict[str, Any]:
        """
        评估信息质量

        Args:
            information (str): 需要评估的信息
            quality_criteria (List[str]): 质量评估标准

        Returns:
            Dict[str, Any]: 质量评估结果
        """
        try:
            logger.info("开始信息质量评估")

            if not information or not information.strip():
                return {
                    "assessment_status": "empty_information",
                    "quality_score": 0.0,
                    "message": "信息内容为空"
                }

            # 默认质量标准
            if not quality_criteria:
                quality_criteria = [
                    "completeness",    # 完整性
                    "accuracy",        # 准确性
                    "clarity",         # 清晰度
                    "relevance",       # 相关性
                    "timeliness",      # 时效性
                    "objectivity"      # 客观性
                ]

            # 评估各项质量指标
            quality_scores = {}
            for criterion in quality_criteria:
                score = await self._assess_quality_criterion(information, criterion)
                quality_scores[criterion] = score

            # 计算综合质量分数
            overall_score = sum(quality_scores.values()) / len(quality_scores)

            # 识别质量问题
            quality_issues = self._identify_quality_issues(information, quality_scores)

            # 生成改进建议
            improvement_suggestions = self._generate_improvement_suggestions(quality_issues)

            result = {
                "assessment_status": "completed",
                "information": information[:200] + "..." if len(information) > 200 else information,
                "quality_criteria": quality_criteria,
                "quality_scores": quality_scores,
                "overall_score": overall_score,
                "quality_level": self._determine_quality_level(overall_score),
                "quality_issues": quality_issues,
                "improvement_suggestions": improvement_suggestions,
                "timestamp": datetime.now().isoformat()
            }

            logger.info(f"信息质量评估完成，综合分数: {overall_score:.2f}")
            return result

        except Exception as e:
            logger.error(f"信息质量评估失败: {e}")
            return {
                "assessment_status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    # ==================== 信息验证辅助方法 ====================

    def _empty_validation_result(self, message: str) -> Dict[str, Any]:
        """生成空的验证结果"""
        return {
            "validation_status": "failed",
            "credibility_score": 0.0,
            "validation_checks": {},
            "issues": [],
            "message": message,
            "timestamp": datetime.now().isoformat()
        }

    def _preprocess_information(self, information: str) -> str:
        """预处理待验证信息"""
        # 清理文本
        cleaned = self._clean_content(information)

        # 标准化格式
        cleaned = re.sub(r'\s+', ' ', cleaned)  # 合并多个空格
        cleaned = cleaned.strip()

        return cleaned

    async def _comprehensive_validation(self, information: str, sources: List[str]) -> Dict[str, Any]:
        """综合验证"""
        validation_checks = {}
        issues = []

        # 内容完整性检查
        completeness_check = self._check_information_completeness(information)
        validation_checks["completeness"] = completeness_check
        if completeness_check["score"] < 0.6:
            issues.append("信息完整性不足")

        # 逻辑一致性检查
        consistency_check = self._check_logical_consistency(information)
        validation_checks["consistency"] = consistency_check
        if consistency_check["score"] < 0.7:
            issues.append("逻辑一致性存在问题")

        # 事实性检查
        factual_check = await self._check_factual_accuracy(information)
        validation_checks["factual_accuracy"] = factual_check
        if factual_check["score"] < 0.8:
            issues.append("事实准确性需要验证")

        # 来源可靠性检查
        if sources:
            source_check = self._check_source_reliability(sources)
            validation_checks["source_reliability"] = source_check
            if source_check["score"] < 0.7:
                issues.append("信息来源可靠性不足")

        return {
            "validation_status": "completed",
            "validation_type": "comprehensive",
            "validation_checks": validation_checks,
            "issues": issues,
            "information": information,
            "sources": sources or [],
            "timestamp": datetime.now().isoformat()
        }

    async def _basic_validation(self, information: str, sources: List[str]) -> Dict[str, Any]:
        """基础验证"""
        validation_checks = {}
        issues = []

        # 基本格式检查
        format_check = self._check_information_format(information)
        validation_checks["format"] = format_check
        if format_check["score"] < 0.5:
            issues.append("信息格式存在问题")

        # 长度合理性检查
        length_check = self._check_information_length(information)
        validation_checks["length"] = length_check
        if length_check["score"] < 0.6:
            issues.append("信息长度不合理")

        return {
            "validation_status": "completed",
            "validation_type": "basic",
            "validation_checks": validation_checks,
            "issues": issues,
            "information": information,
            "sources": sources or [],
            "timestamp": datetime.now().isoformat()
        }

    async def _cross_reference_validation(self, information: str, sources: List[str]) -> Dict[str, Any]:
        """交叉引用验证"""
        if not sources or len(sources) < 2:
            return {
                "validation_status": "insufficient_sources",
                "message": "交叉引用验证需要至少2个信息源",
                "timestamp": datetime.now().isoformat()
            }

        # 执行交叉验证
        cross_validation_result = await self.cross_validate_sources(information, sources)

        validation_checks = {
            "cross_reference": {
                "score": cross_validation_result.get("consistency_score", 0.0),
                "details": cross_validation_result
            }
        }

        issues = []
        if cross_validation_result.get("consistency_score", 0.0) < 0.7:
            issues.append("信息源之间存在不一致")

        return {
            "validation_status": "completed",
            "validation_type": "cross_reference",
            "validation_checks": validation_checks,
            "issues": issues,
            "information": information,
            "sources": sources,
            "timestamp": datetime.now().isoformat()
        }

    def _calculate_credibility_score(self, validation_result: Dict[str, Any]) -> float:
        """计算综合可信度分数"""
        checks = validation_result.get("validation_checks", {})
        if not checks:
            return 0.0

        total_score = 0.0
        total_weight = 0.0

        # 权重配置
        weights = {
            "completeness": 0.2,
            "consistency": 0.25,
            "factual_accuracy": 0.3,
            "source_reliability": 0.15,
            "format": 0.05,
            "length": 0.05
        }

        for check_name, check_result in checks.items():
            if isinstance(check_result, dict) and "score" in check_result:
                weight = weights.get(check_name, 0.1)
                total_score += check_result["score"] * weight
                total_weight += weight

        return total_score / total_weight if total_weight > 0 else 0.0

    def _generate_validation_report(self, validation_result: Dict[str, Any]) -> str:
        """生成验证报告"""
        report_lines = []

        # 基本信息
        credibility = validation_result.get("credibility_score", 0.0)
        report_lines.append(f"信息可信度评分: {credibility:.2f}/1.0")

        # 验证类型
        validation_type = validation_result.get("validation_type", "unknown")
        report_lines.append(f"验证类型: {validation_type}")

        # 检查结果
        checks = validation_result.get("validation_checks", {})
        if checks:
            report_lines.append("\n检查结果:")
            for check_name, check_result in checks.items():
                if isinstance(check_result, dict) and "score" in check_result:
                    score = check_result["score"]
                    status = "通过" if score >= 0.7 else "需要关注" if score >= 0.5 else "不通过"
                    report_lines.append(f"  - {check_name}: {score:.2f} ({status})")

        # 问题列表
        issues = validation_result.get("issues", [])
        if issues:
            report_lines.append("\n发现的问题:")
            for issue in issues:
                report_lines.append(f"  - {issue}")

        # 建议
        if credibility < 0.6:
            report_lines.append("\n建议: 该信息可信度较低，建议进一步验证或寻找更可靠的信息源。")
        elif credibility < 0.8:
            report_lines.append("\n建议: 该信息基本可信，但建议交叉验证关键事实。")
        else:
            report_lines.append("\n建议: 该信息可信度较高，可以作为参考使用。")

        return "\n".join(report_lines)

    def _update_validation_stats(self, validation_type: str, credibility_score: float):
        """更新验证统计信息"""
        if "validation_stats" not in self.processing_stats:
            self.processing_stats["validation_stats"] = {
                "total_validations": 0,
                "by_type": {},
                "average_credibility": 0.0,
                "high_credibility_count": 0
            }

        stats = self.processing_stats["validation_stats"]
        stats["total_validations"] += 1

        # 按类型统计
        if validation_type not in stats["by_type"]:
            stats["by_type"][validation_type] = {"count": 0, "avg_score": 0.0}

        type_stats = stats["by_type"][validation_type]
        old_avg = type_stats["avg_score"]
        old_count = type_stats["count"]

        type_stats["count"] += 1
        type_stats["avg_score"] = (old_avg * old_count + credibility_score) / type_stats["count"]

        # 整体统计
        old_total_avg = stats["average_credibility"]
        old_total_count = stats["total_validations"] - 1

        if old_total_count > 0:
            stats["average_credibility"] = (old_total_avg * old_total_count + credibility_score) / stats["total_validations"]
        else:
            stats["average_credibility"] = credibility_score

        # 高可信度计数
        if credibility_score >= 0.8:
            stats["high_credibility_count"] += 1

    # ==================== 具体检查方法 ====================

    def _check_information_completeness(self, information: str) -> Dict[str, Any]:
        """检查信息完整性"""
        score = 0.0
        details = []

        # 长度检查
        if len(information) > 50:
            score += 0.3
            details.append("信息长度适中")
        else:
            details.append("信息过短")

        # 结构检查
        sentences = self._extract_sentences(information)
        if len(sentences) >= 2:
            score += 0.3
            details.append("包含多个句子")
        else:
            details.append("信息结构简单")

        # 关键要素检查
        key_elements = ["什么", "为什么", "如何", "何时", "何地"]
        found_elements = 0
        for element in key_elements:
            if any(keyword in information for keyword in self._get_element_keywords(element)):
                found_elements += 1

        if found_elements >= 2:
            score += 0.4
            details.append(f"包含{found_elements}个关键要素")
        else:
            details.append("关键要素不足")

        return {
            "score": min(score, 1.0),
            "details": details,
            "found_elements": found_elements
        }

    def _get_element_keywords(self, element: str) -> List[str]:
        """获取要素关键词"""
        keywords_map = {
            "什么": ["是什么", "什么是", "定义", "概念"],
            "为什么": ["为什么", "原因", "因为", "由于"],
            "如何": ["如何", "怎么", "方法", "步骤"],
            "何时": ["何时", "什么时候", "时间", "日期"],
            "何地": ["何地", "哪里", "地点", "位置"]
        }
        return keywords_map.get(element, [])

    def _check_logical_consistency(self, information: str) -> Dict[str, Any]:
        """检查逻辑一致性"""
        score = 0.8  # 默认较高分数
        details = []
        issues = []

        # 检查矛盾表述
        contradiction_patterns = [
            (r"不是.*但是.*是", "存在否定后肯定的矛盾"),
            (r"没有.*然而.*有", "存在否定后肯定的矛盾"),
            (r"不能.*却.*能", "存在能力矛盾"),
            (r"不会.*但.*会", "存在能力矛盾")
        ]

        for pattern, issue_desc in contradiction_patterns:
            if re.search(pattern, information):
                score -= 0.2
                issues.append(issue_desc)

        # 检查时间逻辑
        time_patterns = re.findall(r'\d{4}年|\d+年前|\d+年后', information)
        if len(time_patterns) > 1:
            # 简单的时间逻辑检查
            details.append("包含多个时间表述")

        # 检查因果关系
        causal_indicators = ["因为", "由于", "所以", "因此", "导致", "造成"]
        causal_count = sum(1 for indicator in causal_indicators if indicator in information)
        if causal_count > 0:
            details.append(f"包含{causal_count}个因果关系表述")
            score += 0.1  # 有因果关系通常逻辑性更强

        if issues:
            details.extend(issues)
        else:
            details.append("未发现明显逻辑矛盾")

        return {
            "score": max(min(score, 1.0), 0.0),
            "details": details,
            "issues": issues
        }

    async def _check_factual_accuracy(self, information: str) -> Dict[str, Any]:
        """检查事实准确性"""
        score = 0.7  # 默认中等分数
        details = []

        # 检查数字和统计数据
        numbers = re.findall(r'\d+(?:\.\d+)?%|\d+(?:,\d{3})*(?:\.\d+)?', information)
        if numbers:
            details.append(f"包含{len(numbers)}个数字/统计数据")
            score += 0.1

        # 检查日期和时间
        dates = re.findall(r'\d{4}年\d{1,2}月|\d{4}-\d{1,2}-\d{1,2}|\d{1,2}/\d{1,2}/\d{4}', information)
        if dates:
            details.append(f"包含{len(dates)}个具体日期")
            score += 0.1

        # 检查专有名词
        proper_nouns = re.findall(r'[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*|[A-Z]{2,}', information)
        if proper_nouns:
            details.append(f"包含{len(proper_nouns)}个专有名词")
            score += 0.1

        # 检查引用和来源
        citation_patterns = ["根据", "据", "来源", "引用", "参考"]
        citations = sum(1 for pattern in citation_patterns if pattern in information)
        if citations > 0:
            details.append(f"包含{citations}个引用/来源指示")
            score += 0.1

        return {
            "score": min(score, 1.0),
            "details": details,
            "numbers_count": len(numbers),
            "dates_count": len(dates),
            "citations_count": citations
        }

    def _check_source_reliability(self, sources: List[str]) -> Dict[str, Any]:
        """检查来源可靠性"""
        if not sources:
            return {"score": 0.0, "details": ["无信息来源"]}

        score = 0.0
        details = []
        reliable_indicators = 0

        for source in sources:
            source_lower = source.lower()

            # 检查权威域名
            authoritative_domains = [
                ".gov", ".edu", ".org", "wikipedia", "reuters", "bbc",
                "cnn", "nature", "science", "ieee", "acm"
            ]

            if any(domain in source_lower for domain in authoritative_domains):
                reliable_indicators += 1
                details.append(f"包含权威来源: {source[:50]}...")

            # 检查学术指标
            academic_indicators = ["doi:", "arxiv", "pubmed", "scholar", "journal"]
            if any(indicator in source_lower for indicator in academic_indicators):
                reliable_indicators += 1
                details.append(f"包含学术来源: {source[:50]}...")

        # 计算分数
        if len(sources) > 0:
            score = min(reliable_indicators / len(sources), 1.0)

        # 来源数量加分
        if len(sources) >= 3:
            score += 0.2
            details.append(f"来源数量充足: {len(sources)}个")
        elif len(sources) >= 2:
            score += 0.1
            details.append(f"来源数量适中: {len(sources)}个")

        return {
            "score": min(score, 1.0),
            "details": details,
            "source_count": len(sources),
            "reliable_count": reliable_indicators
        }

    def _check_information_format(self, information: str) -> Dict[str, Any]:
        """检查信息格式"""
        score = 0.5  # 基础分数
        details = []

        # 检查基本格式
        if information.strip():
            score += 0.2
            details.append("信息非空")

        # 检查标点符号
        punctuation_count = len(re.findall(r'[。！？，；：]', information))
        if punctuation_count > 0:
            score += 0.2
            details.append(f"包含{punctuation_count}个标点符号")

        # 检查段落结构
        paragraphs = information.split('\n')
        if len(paragraphs) > 1:
            score += 0.1
            details.append(f"包含{len(paragraphs)}个段落")

        return {
            "score": min(score, 1.0),
            "details": details
        }

    def _check_information_length(self, information: str) -> Dict[str, Any]:
        """检查信息长度合理性"""
        length = len(information)
        score = 0.0
        details = []

        if length < 10:
            score = 0.2
            details.append("信息过短")
        elif length < 50:
            score = 0.5
            details.append("信息较短")
        elif length < 500:
            score = 1.0
            details.append("信息长度适中")
        elif length < 2000:
            score = 0.8
            details.append("信息较长")
        else:
            score = 0.6
            details.append("信息过长")

        return {
            "score": score,
            "details": details,
            "length": length
        }

    # ==================== 交叉验证相关方法 ====================

    async def _analyze_information_source(self, information: str, source: str, index: int) -> Dict[str, Any]:
        """分析单个信息源"""
        try:
            # 提取源中与信息相关的内容
            relevant_content = self._extract_relevant_content(source, information)

            # 分析内容一致性
            consistency_score = self._calculate_content_consistency(information, relevant_content)

            # 评估源的权威性
            authority_score = self._assess_source_authority(source)

            # 检查时效性
            timeliness_score = self._assess_information_timeliness(source)

            return {
                "source_index": index,
                "source": source[:100] + "..." if len(source) > 100 else source,
                "relevant_content": relevant_content,
                "consistency_score": consistency_score,
                "authority_score": authority_score,
                "timeliness_score": timeliness_score,
                "overall_score": (consistency_score + authority_score + timeliness_score) / 3
            }

        except Exception as e:
            logger.error(f"分析信息源失败: {e}")
            return {
                "source_index": index,
                "source": source[:100] + "..." if len(source) > 100 else source,
                "error": str(e),
                "overall_score": 0.0
            }

    def _extract_relevant_content(self, source: str, target_info: str) -> str:
        """从源中提取相关内容"""
        # 简单的关键词匹配提取
        target_words = set(re.findall(r'\w+', target_info.lower()))
        source_sentences = self._extract_sentences(source)

        relevant_sentences = []
        for sentence in source_sentences:
            sentence_words = set(re.findall(r'\w+', sentence.lower()))
            overlap = len(target_words & sentence_words)
            if overlap >= 2:  # 至少2个词重叠
                relevant_sentences.append(sentence)

        return " ".join(relevant_sentences[:3])  # 最多3个相关句子

    def _calculate_content_consistency(self, info1: str, info2: str) -> float:
        """计算内容一致性"""
        if not info1 or not info2:
            return 0.0

        # 提取关键词
        words1 = set(re.findall(r'\w+', info1.lower()))
        words2 = set(re.findall(r'\w+', info2.lower()))

        # 计算Jaccard相似度
        intersection = len(words1 & words2)
        union = len(words1 | words2)

        return intersection / union if union > 0 else 0.0

    def _assess_source_authority(self, source: str) -> float:
        """评估源权威性"""
        authority_indicators = [
            (".gov", 0.9), (".edu", 0.8), (".org", 0.7),
            ("wikipedia", 0.6), ("reuters", 0.8), ("bbc", 0.8),
            ("nature", 0.9), ("science", 0.9), ("ieee", 0.8)
        ]

        source_lower = source.lower()
        max_score = 0.5  # 默认分数

        for indicator, score in authority_indicators:
            if indicator in source_lower:
                max_score = max(max_score, score)

        return max_score

    def _assess_information_timeliness(self, source: str) -> float:
        """评估信息时效性"""
        # 查找日期信息
        current_year = datetime.now().year

        # 匹配年份
        years = re.findall(r'\b(20\d{2})\b', source)
        if years:
            latest_year = max(int(year) for year in years)
            age = current_year - latest_year

            if age <= 1:
                return 1.0
            elif age <= 3:
                return 0.8
            elif age <= 5:
                return 0.6
            else:
                return 0.4

        return 0.5  # 无法确定时间时的默认分数

    def _calculate_source_consistency(self, source_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算源间一致性"""
        if len(source_analyses) < 2:
            return {"overall_score": 0.0, "pairwise_scores": []}

        pairwise_scores = []
        total_score = 0.0
        comparison_count = 0

        # 计算两两之间的一致性
        for i in range(len(source_analyses)):
            for j in range(i + 1, len(source_analyses)):
                source1 = source_analyses[i]
                source2 = source_analyses[j]

                # 计算一致性分数
                consistency = self._calculate_content_consistency(
                    source1.get("relevant_content", ""),
                    source2.get("relevant_content", "")
                )

                pairwise_scores.append({
                    "source1_index": i,
                    "source2_index": j,
                    "consistency_score": consistency
                })

                total_score += consistency
                comparison_count += 1

        overall_score = total_score / comparison_count if comparison_count > 0 else 0.0

        return {
            "overall_score": overall_score,
            "pairwise_scores": pairwise_scores,
            "comparison_count": comparison_count
        }

    def _identify_information_conflicts(self, source_analyses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """识别信息冲突"""
        conflicts = []

        # 简单的冲突检测：寻找矛盾的关键词
        contradiction_pairs = [
            ("是", "不是"), ("有", "没有"), ("能", "不能"),
            ("会", "不会"), ("可以", "不可以"), ("正确", "错误")
        ]

        for i, source1 in enumerate(source_analyses):
            for j, source2 in enumerate(source_analyses[i+1:], i+1):
                content1 = source1.get("relevant_content", "").lower()
                content2 = source2.get("relevant_content", "").lower()

                for pos_word, neg_word in contradiction_pairs:
                    if pos_word in content1 and neg_word in content2:
                        conflicts.append({
                            "type": "contradiction",
                            "source1_index": i,
                            "source2_index": j,
                            "conflict_words": [pos_word, neg_word],
                            "description": f"源{i+1}包含'{pos_word}'，源{j+1}包含'{neg_word}'"
                        })
                    elif neg_word in content1 and pos_word in content2:
                        conflicts.append({
                            "type": "contradiction",
                            "source1_index": i,
                            "source2_index": j,
                            "conflict_words": [neg_word, pos_word],
                            "description": f"源{i+1}包含'{neg_word}'，源{j+1}包含'{pos_word}'"
                        })

        return conflicts

    def _assess_source_reliability(self, source_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """评估源可靠性"""
        reliability_scores = []

        for analysis in source_analyses:
            authority = analysis.get("authority_score", 0.0)
            timeliness = analysis.get("timeliness_score", 0.0)
            consistency = analysis.get("consistency_score", 0.0)

            # 综合可靠性分数
            reliability = (authority * 0.4 + timeliness * 0.3 + consistency * 0.3)
            reliability_scores.append(reliability)

        return {
            "individual_scores": reliability_scores,
            "average_reliability": sum(reliability_scores) / len(reliability_scores) if reliability_scores else 0.0,
            "most_reliable_index": reliability_scores.index(max(reliability_scores)) if reliability_scores else -1,
            "least_reliable_index": reliability_scores.index(min(reliability_scores)) if reliability_scores else -1
        }

    def _generate_cross_validation_conclusion(self, source_analyses: List[Dict[str, Any]],
                                            consistency_matrix: Dict[str, Any],
                                            conflicts: List[Dict[str, Any]],
                                            source_reliability: Dict[str, Any]) -> str:
        """生成交叉验证结论"""
        conclusion_parts = []

        # 整体一致性评估
        overall_consistency = consistency_matrix.get("overall_score", 0.0)
        if overall_consistency >= 0.8:
            conclusion_parts.append("信息源之间高度一致，信息可信度很高。")
        elif overall_consistency >= 0.6:
            conclusion_parts.append("信息源之间基本一致，信息具有较高可信度。")
        elif overall_consistency >= 0.4:
            conclusion_parts.append("信息源之间存在一定差异，建议进一步验证。")
        else:
            conclusion_parts.append("信息源之间差异较大，信息可信度较低。")

        # 冲突分析
        if conflicts:
            conclusion_parts.append(f"发现{len(conflicts)}个潜在冲突，需要特别关注。")
        else:
            conclusion_parts.append("未发现明显冲突。")

        # 可靠性分析
        avg_reliability = source_reliability.get("average_reliability", 0.0)
        if avg_reliability >= 0.7:
            conclusion_parts.append("信息源整体可靠性较高。")
        else:
            conclusion_parts.append("信息源可靠性有待提升。")

        return " ".join(conclusion_parts)

    # ==================== 事实验证相关方法 ====================

    async def _verify_single_claim(self, claim: str, context: str, index: int) -> Dict[str, Any]:
        """验证单个声明"""
        try:
            # 分析声明类型
            claim_type = self._classify_claim_type(claim)

            # 提取关键事实
            key_facts = self._extract_key_facts(claim)

            # 评估可验证性
            verifiability = self._assess_claim_verifiability(claim)

            # 检查与上下文的一致性
            context_consistency = self._check_context_consistency(claim, context) if context else 0.5

            # 计算声明可信度
            credibility = (verifiability + context_consistency) / 2

            return {
                "claim_index": index,
                "claim": claim,
                "claim_type": claim_type,
                "key_facts": key_facts,
                "verifiability_score": verifiability,
                "context_consistency": context_consistency,
                "credibility_score": credibility,
                "risk_level": self._determine_risk_level(credibility)
            }

        except Exception as e:
            logger.error(f"验证声明失败: {e}")
            return {
                "claim_index": index,
                "claim": claim,
                "error": str(e),
                "credibility_score": 0.0,
                "risk_level": "high"
            }

    def _classify_claim_type(self, claim: str) -> str:
        """分类声明类型"""
        claim_lower = claim.lower()

        # 数字/统计类
        if re.search(r'\d+%|\d+(?:,\d{3})*(?:\.\d+)?', claim):
            return "statistical"

        # 时间/历史类
        if re.search(r'\d{4}年|\d+年前|\d+年后|历史|过去|未来', claim):
            return "temporal"

        # 因果关系类
        if any(word in claim_lower for word in ["因为", "由于", "导致", "造成", "引起"]):
            return "causal"

        # 比较类
        if any(word in claim_lower for word in ["比", "更", "最", "超过", "低于"]):
            return "comparative"

        # 定义类
        if any(word in claim_lower for word in ["是", "定义", "概念", "指"]):
            return "definitional"

        return "general"

    def _extract_key_facts(self, claim: str) -> List[str]:
        """提取关键事实"""
        facts = []

        # 提取数字事实
        numbers = re.findall(r'\d+(?:\.\d+)?%|\d+(?:,\d{3})*(?:\.\d+)?', claim)
        for number in numbers:
            facts.append(f"数字: {number}")

        # 提取日期事实
        dates = re.findall(r'\d{4}年\d{1,2}月|\d{4}年|\d{1,2}月\d{1,2}日', claim)
        for date in dates:
            facts.append(f"日期: {date}")

        # 提取专有名词
        proper_nouns = re.findall(r'[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*', claim)
        for noun in proper_nouns[:3]:  # 最多3个
            facts.append(f"专有名词: {noun}")

        return facts

    def _assess_claim_verifiability(self, claim: str) -> float:
        """评估声明可验证性"""
        score = 0.5  # 基础分数

        # 具体数字增加可验证性
        if re.search(r'\d+', claim):
            score += 0.2

        # 具体日期增加可验证性
        if re.search(r'\d{4}年|\d{1,2}月', claim):
            score += 0.2

        # 专有名词增加可验证性
        if re.search(r'[A-Z][a-z]+', claim):
            score += 0.1

        # 模糊词汇降低可验证性
        vague_words = ["可能", "大概", "也许", "似乎", "据说", "传说"]
        if any(word in claim for word in vague_words):
            score -= 0.2

        # 绝对词汇需要谨慎
        absolute_words = ["所有", "全部", "从不", "总是", "绝对", "完全"]
        if any(word in claim for word in absolute_words):
            score -= 0.1

        return max(min(score, 1.0), 0.0)

    def _check_context_consistency(self, claim: str, context: str) -> float:
        """检查与上下文的一致性"""
        if not context:
            return 0.5

        # 简单的词汇重叠检查
        claim_words = set(re.findall(r'\w+', claim.lower()))
        context_words = set(re.findall(r'\w+', context.lower()))

        overlap = len(claim_words & context_words)
        union = len(claim_words | context_words)

        return overlap / union if union > 0 else 0.0

    def _determine_risk_level(self, credibility: float) -> str:
        """确定风险等级"""
        if credibility >= 0.8:
            return "low"
        elif credibility >= 0.6:
            return "medium"
        elif credibility >= 0.4:
            return "high"
        else:
            return "very_high"

    def _calculate_overall_claim_credibility(self, verified_claims: List[Dict[str, Any]]) -> float:
        """计算整体声明可信度"""
        if not verified_claims:
            return 0.0

        total_credibility = sum(claim.get("credibility_score", 0.0) for claim in verified_claims)
        return total_credibility / len(verified_claims)

    def _identify_high_risk_claims(self, verified_claims: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """识别高风险声明"""
        high_risk_claims = []

        for claim in verified_claims:
            risk_level = claim.get("risk_level", "medium")
            if risk_level in ["high", "very_high"]:
                high_risk_claims.append({
                    "claim_index": claim.get("claim_index", -1),
                    "claim": claim.get("claim", ""),
                    "risk_level": risk_level,
                    "credibility_score": claim.get("credibility_score", 0.0),
                    "issues": self._identify_claim_issues(claim)
                })

        return high_risk_claims

    def _identify_claim_issues(self, claim_result: Dict[str, Any]) -> List[str]:
        """识别声明问题"""
        issues = []

        credibility = claim_result.get("credibility_score", 0.0)
        verifiability = claim_result.get("verifiability_score", 0.0)

        if credibility < 0.4:
            issues.append("整体可信度较低")

        if verifiability < 0.5:
            issues.append("可验证性不足")

        claim = claim_result.get("claim", "")
        if any(word in claim for word in ["可能", "也许", "据说"]):
            issues.append("包含不确定表述")

        if any(word in claim for word in ["所有", "从不", "总是"]):
            issues.append("包含绝对化表述")

        return issues

    def _generate_verification_summary(self, verified_claims: List[Dict[str, Any]]) -> str:
        """生成验证摘要"""
        if not verified_claims:
            return "无声明需要验证。"

        total_claims = len(verified_claims)
        high_credibility = sum(1 for claim in verified_claims if claim.get("credibility_score", 0.0) >= 0.8)
        medium_credibility = sum(1 for claim in verified_claims if 0.6 <= claim.get("credibility_score", 0.0) < 0.8)
        low_credibility = total_claims - high_credibility - medium_credibility

        summary_parts = [
            f"共验证{total_claims}个声明：",
            f"高可信度: {high_credibility}个",
            f"中等可信度: {medium_credibility}个",
            f"低可信度: {low_credibility}个"
        ]

        if low_credibility > 0:
            summary_parts.append("建议对低可信度声明进行进一步验证。")

        return " ".join(summary_parts)

    # ==================== 信息质量评估相关方法 ====================

    async def _assess_quality_criterion(self, information: str, criterion: str) -> float:
        """评估单个质量标准"""
        try:
            if criterion == "completeness":
                return self._assess_completeness(information)
            elif criterion == "accuracy":
                return await self._assess_accuracy(information)
            elif criterion == "clarity":
                return self._assess_clarity(information)
            elif criterion == "relevance":
                return self._assess_relevance(information)
            elif criterion == "timeliness":
                return self._assess_timeliness(information)
            elif criterion == "objectivity":
                return self._assess_objectivity(information)
            else:
                return 0.5  # 未知标准的默认分数

        except Exception as e:
            logger.error(f"评估质量标准失败 {criterion}: {e}")
            return 0.0

    def _assess_completeness(self, information: str) -> float:
        """评估完整性"""
        score = 0.0

        # 长度评估
        length = len(information)
        if length > 100:
            score += 0.3
        elif length > 50:
            score += 0.2
        else:
            score += 0.1

        # 结构评估
        sentences = self._extract_sentences(information)
        if len(sentences) >= 3:
            score += 0.3
        elif len(sentences) >= 2:
            score += 0.2

        # 信息要素评估
        elements = ["什么", "为什么", "如何", "何时", "何地"]
        found_elements = 0
        for element in elements:
            if any(keyword in information for keyword in self._get_element_keywords(element)):
                found_elements += 1

        score += (found_elements / len(elements)) * 0.4

        return min(score, 1.0)

    async def _assess_accuracy(self, information: str) -> float:
        """评估准确性"""
        # 这里可以集成外部事实检查API
        # 目前使用启发式方法

        score = 0.7  # 基础分数

        # 检查数字和统计
        numbers = re.findall(r'\d+(?:\.\d+)?%|\d+(?:,\d{3})*(?:\.\d+)?', information)
        if numbers:
            score += 0.1

        # 检查具体日期
        dates = re.findall(r'\d{4}年|\d{1,2}月\d{1,2}日', information)
        if dates:
            score += 0.1

        # 检查引用来源
        if any(word in information for word in ["根据", "据", "来源于"]):
            score += 0.1

        # 检查不确定表述（降低准确性）
        uncertain_words = ["可能", "也许", "大概", "似乎"]
        if any(word in information for word in uncertain_words):
            score -= 0.1

        return max(min(score, 1.0), 0.0)

    def _assess_clarity(self, information: str) -> float:
        """评估清晰度"""
        score = 0.5

        # 句子长度评估
        sentences = self._extract_sentences(information)
        if sentences:
            avg_length = sum(len(s) for s in sentences) / len(sentences)
            if 10 <= avg_length <= 50:  # 适中的句子长度
                score += 0.2
            elif avg_length > 100:  # 句子过长
                score -= 0.1

        # 标点符号使用
        punctuation_count = len(re.findall(r'[。！？，；：]', information))
        if punctuation_count > 0:
            score += 0.2

        # 专业术语密度
        complex_words = re.findall(r'[a-zA-Z]{8,}', information)  # 长英文词汇
        if len(complex_words) / len(information.split()) > 0.3:
            score -= 0.1

        return max(min(score, 1.0), 0.0)

    def _assess_relevance(self, information: str) -> float:
        """评估相关性"""
        # 这里需要上下文信息来准确评估
        # 目前使用通用指标

        score = 0.6  # 基础分数

        # 检查主题一致性（简单的关键词密度）
        words = information.split()
        if len(words) > 0:
            unique_words = set(words)
            repetition_ratio = 1 - (len(unique_words) / len(words))
            if 0.1 <= repetition_ratio <= 0.3:  # 适度重复表示主题集中
                score += 0.2

        # 检查结构化程度
        if re.search(r'第[一二三四五六七八九十\d]+[章节条]|[1-9]\d*[\.、]', information):
            score += 0.2

        return min(score, 1.0)

    def _assess_timeliness(self, information: str) -> float:
        """评估时效性"""
        current_year = datetime.now().year

        # 查找年份信息
        years = re.findall(r'\b(20\d{2})\b', information)
        if years:
            latest_year = max(int(year) for year in years)
            age = current_year - latest_year

            if age <= 1:
                return 1.0
            elif age <= 2:
                return 0.8
            elif age <= 5:
                return 0.6
            else:
                return 0.4

        # 查找时间相关词汇
        time_indicators = ["最近", "近期", "目前", "现在", "当前", "今年"]
        if any(indicator in information for indicator in time_indicators):
            return 0.8

        return 0.5  # 无法确定时间的默认分数

    def _assess_objectivity(self, information: str) -> float:
        """评估客观性"""
        score = 0.7  # 基础分数

        # 主观词汇检查（降低客观性）
        subjective_words = ["我认为", "我觉得", "个人认为", "明显", "显然", "毫无疑问"]
        subjective_count = sum(1 for word in subjective_words if word in information)
        score -= subjective_count * 0.1

        # 情感词汇检查
        emotional_words = ["惊人", "震惊", "可怕", "美好", "糟糕", "完美"]
        emotional_count = sum(1 for word in emotional_words if word in information)
        score -= emotional_count * 0.05

        # 数据和事实增加客观性
        numbers = re.findall(r'\d+', information)
        if len(numbers) > 0:
            score += 0.1

        # 引用来源增加客观性
        if any(word in information for word in ["根据", "数据显示", "研究表明"]):
            score += 0.1

        return max(min(score, 1.0), 0.0)

    def _identify_quality_issues(self, information: str, quality_scores: Dict[str, float]) -> List[Dict[str, Any]]:
        """识别质量问题"""
        issues = []

        for criterion, score in quality_scores.items():
            if score < 0.6:
                issue = {
                    "criterion": criterion,
                    "score": score,
                    "severity": "high" if score < 0.4 else "medium",
                    "description": self._get_quality_issue_description(criterion, score)
                }
                issues.append(issue)

        return issues

    def _get_quality_issue_description(self, criterion: str, score: float) -> str:
        """获取质量问题描述"""
        descriptions = {
            "completeness": f"信息完整性不足 (分数: {score:.2f})，缺少关键要素",
            "accuracy": f"准确性存疑 (分数: {score:.2f})，需要进一步验证",
            "clarity": f"表达不够清晰 (分数: {score:.2f})，可能影响理解",
            "relevance": f"相关性不足 (分数: {score:.2f})，偏离主题",
            "timeliness": f"时效性较差 (分数: {score:.2f})，信息可能过时",
            "objectivity": f"客观性不足 (分数: {score:.2f})，包含主观表述"
        }

        return descriptions.get(criterion, f"{criterion}质量不足 (分数: {score:.2f})")

    def _generate_improvement_suggestions(self, quality_issues: List[Dict[str, Any]]) -> List[str]:
        """生成改进建议"""
        suggestions = []

        for issue in quality_issues:
            criterion = issue["criterion"]

            if criterion == "completeness":
                suggestions.append("补充缺失的关键信息，如时间、地点、原因等要素")
            elif criterion == "accuracy":
                suggestions.append("添加可靠的信息来源和引用，验证关键数据")
            elif criterion == "clarity":
                suggestions.append("简化复杂句子，使用更清晰的表达方式")
            elif criterion == "relevance":
                suggestions.append("聚焦核心主题，删除无关信息")
            elif criterion == "timeliness":
                suggestions.append("更新过时信息，添加最新数据和发展")
            elif criterion == "objectivity":
                suggestions.append("减少主观表述，使用更中性的语言")

        # 去重
        return list(set(suggestions))

    def _determine_quality_level(self, overall_score: float) -> str:
        """确定质量等级"""
        if overall_score >= 0.8:
            return "excellent"
        elif overall_score >= 0.7:
            return "good"
        elif overall_score >= 0.6:
            return "fair"
        elif overall_score >= 0.4:
            return "poor"
        else:
            return "very_poor"

    # ==================== 数据结构化处理辅助方法 (步骤4.9) ====================

    def _empty_entities_result(self, reason: str) -> Dict[str, Any]:
        """返回空的实体提取结果"""
        return {
            "entities": {
                "persons": [],
                "organizations": [],
                "locations": [],
                "dates": [],
                "numbers": [],
                "urls": [],
                "emails": [],
                "phone_numbers": []
            },
            "total_entities": 0,
            "confidence": 0.0,
            "extraction_method": "pattern_based",
            "error": reason,
            "timestamp": datetime.now().isoformat()
        }

    def _extract_person_entities(self, content: str) -> List[Dict[str, Any]]:
        """提取人名实体"""
        try:
            persons = []

            # 中文人名模式 - 修复边界问题
            chinese_name_pattern = r'[\u4e00-\u9fff]{2,3}(?:先生|女士|教授|博士|医生|老师|主任|经理|总裁|董事长)?'

            # 英文人名模式
            english_name_pattern = r'\b[A-Z][a-z]+\s+[A-Z][a-z]+(?:\s+[A-Z][a-z]+)?\b'

            # 提取中文人名 - 使用更精确的正则表达式
            # 匹配常见的中文人名模式
            name_patterns = [
                r'[\u4e00-\u9fff]{2,3}(?:先生|女士|教授|博士|医生|老师|主任|经理|总裁|董事长)',
                r'[\u4e00-\u9fff]{2,3}(?=\s|，|。|和|与|及)'  # 人名后跟分隔符
            ]

            for pattern in name_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    name = match.group().strip()
                    if len(name) >= 2 and not re.match(r'^[\d\s]+$', name):
                        persons.append({
                            "name": name,
                            "type": "chinese",
                            "position": match.span(),
                            "confidence": 0.8
                        })

            # 提取英文人名
            english_matches = re.finditer(english_name_pattern, content)
            for match in english_matches:
                name = match.group().strip()
                persons.append({
                    "name": name,
                    "type": "english",
                    "position": match.span(),
                    "confidence": 0.7
                })

            # 去重
            unique_persons = []
            seen_names = set()
            for person in persons:
                if person["name"] not in seen_names:
                    seen_names.add(person["name"])
                    unique_persons.append(person)

            return unique_persons

        except Exception as e:
            logger.error(f"人名实体提取失败: {e}")
            return []

    def _extract_organization_entities(self, content: str) -> List[Dict[str, Any]]:
        """提取组织机构实体"""
        try:
            organizations = []

            # 组织机构关键词
            org_keywords = [
                "公司", "企业", "集团", "有限公司", "股份有限公司", "科技", "技术",
                "大学", "学院", "研究所", "实验室", "中心", "基金会", "协会",
                "政府", "部门", "委员会", "局", "署", "厅", "处",
                "银行", "保险", "证券", "投资", "基金"
            ]

            # 英文组织机构
            eng_org_pattern = r'\b[A-Z][a-zA-Z\s&]+(?:Inc|Corp|Ltd|LLC|Company|University|Institute|Foundation|Association)\b'

            # 中文组织机构模式 - 改进匹配逻辑
            for keyword in org_keywords:
                # 更精确的模式，避免匹配整个句子
                pattern = rf'[\u4e00-\u9fff]{{2,8}}{keyword}'
                matches = re.finditer(pattern, content)
                for match in matches:
                    org_name = match.group().strip()
                    if len(org_name) >= 3 and len(org_name) <= 20:
                        organizations.append({
                            "name": org_name,
                            "type": "chinese",
                            "category": self._classify_organization(org_name),
                            "position": match.span(),
                            "confidence": 0.7
                        })

            # 英文组织机构
            eng_matches = re.finditer(eng_org_pattern, content)
            for match in eng_matches:
                org_name = match.group().strip()
                organizations.append({
                    "name": org_name,
                    "type": "english",
                    "category": self._classify_organization(org_name),
                    "position": match.span(),
                    "confidence": 0.8
                })

            # 去重
            unique_orgs = []
            seen_names = set()
            for org in organizations:
                if org["name"] not in seen_names:
                    seen_names.add(org["name"])
                    unique_orgs.append(org)

            return unique_orgs[:20]  # 限制数量

        except Exception as e:
            logger.error(f"组织机构实体提取失败: {e}")
            return []

    def _extract_location_entities(self, content: str) -> List[Dict[str, Any]]:
        """提取地理位置实体"""
        try:
            locations = []

            # 中国地名模式
            china_locations = [
                "北京", "上海", "天津", "重庆", "广东", "江苏", "浙江", "山东",
                "河南", "四川", "湖北", "湖南", "河北", "福建", "安徽", "陕西",
                "广西", "云南", "江西", "辽宁", "黑龙江", "山西", "吉林", "贵州",
                "甘肃", "内蒙古", "新疆", "海南", "宁夏", "青海", "西藏", "香港", "澳门", "台湾"
            ]

            # 国际地名模式
            international_locations = [
                "美国", "英国", "法国", "德国", "日本", "韩国", "俄罗斯", "加拿大",
                "澳大利亚", "新西兰", "意大利", "西班牙", "荷兰", "瑞士", "瑞典",
                "挪威", "丹麦", "芬兰", "比利时", "奥地利", "新加坡", "马来西亚",
                "泰国", "印度", "巴西", "阿根廷", "墨西哥", "南非", "埃及"
            ]

            all_locations = china_locations + international_locations

            for location in all_locations:
                if location in content:
                    # 查找所有出现位置
                    start = 0
                    while True:
                        pos = content.find(location, start)
                        if pos == -1:
                            break

                        locations.append({
                            "name": location,
                            "type": "china" if location in china_locations else "international",
                            "position": (pos, pos + len(location)),
                            "confidence": 0.9
                        })
                        start = pos + 1

            # 去重
            unique_locations = []
            seen_names = set()
            for loc in locations:
                if loc["name"] not in seen_names:
                    seen_names.add(loc["name"])
                    unique_locations.append(loc)

            return unique_locations

        except Exception as e:
            logger.error(f"地理位置实体提取失败: {e}")
            return []

    def _extract_date_entities(self, content: str) -> List[Dict[str, Any]]:
        """提取日期时间实体"""
        try:
            dates = []

            # 日期模式
            date_patterns = [
                (r'\d{4}年\d{1,2}月\d{1,2}日', 'chinese_full'),
                (r'\d{4}-\d{1,2}-\d{1,2}', 'iso_date'),
                (r'\d{1,2}/\d{1,2}/\d{4}', 'us_date'),
                (r'\d{1,2}\.\d{1,2}\.\d{4}', 'eu_date'),
                (r'\d{4}年\d{1,2}月', 'chinese_month'),
                (r'\d{4}年', 'chinese_year'),
                (r'今天|昨天|明天|前天|后天', 'relative_day'),
                (r'本周|上周|下周|本月|上月|下月|今年|去年|明年', 'relative_period')
            ]

            for pattern, date_type in date_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    date_str = match.group().strip()
                    dates.append({
                        "date": date_str,
                        "type": date_type,
                        "position": match.span(),
                        "confidence": 0.9 if 'chinese' in date_type else 0.8
                    })

            return dates

        except Exception as e:
            logger.error(f"日期实体提取失败: {e}")
            return []

    def _extract_number_entities(self, content: str) -> List[Dict[str, Any]]:
        """提取数字实体"""
        try:
            numbers = []

            # 数字模式
            number_patterns = [
                (r'\d+\.\d+%', 'percentage'),
                (r'\d+%', 'percentage'),
                (r'\d+\.\d+万', 'chinese_decimal'),
                (r'\d+万', 'chinese_wan'),
                (r'\d+\.\d+亿', 'chinese_decimal'),
                (r'\d+亿', 'chinese_yi'),
                (r'\d+\.\d+', 'decimal'),
                (r'\d+', 'integer')
            ]

            for pattern, number_type in number_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    number_str = match.group().strip()
                    numbers.append({
                        "number": number_str,
                        "type": number_type,
                        "position": match.span(),
                        "confidence": 0.95
                    })

            return numbers[:50]  # 限制数量

        except Exception as e:
            logger.error(f"数字实体提取失败: {e}")
            return []

    def _extract_url_entities(self, content: str) -> List[Dict[str, Any]]:
        """提取URL实体"""
        try:
            urls = []

            # URL模式
            url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+|www\.[^\s<>"{}|\\^`\[\]]+'

            matches = re.finditer(url_pattern, content)
            for match in matches:
                url = match.group().strip()
                urls.append({
                    "url": url,
                    "domain": self._extract_domain(url),
                    "position": match.span(),
                    "confidence": 0.95
                })

            return urls

        except Exception as e:
            logger.error(f"URL实体提取失败: {e}")
            return []

    def _extract_email_entities(self, content: str) -> List[Dict[str, Any]]:
        """提取邮箱实体"""
        try:
            emails = []

            # 邮箱模式
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'

            matches = re.finditer(email_pattern, content)
            for match in matches:
                email = match.group().strip()
                emails.append({
                    "email": email,
                    "domain": email.split('@')[1] if '@' in email else '',
                    "position": match.span(),
                    "confidence": 0.95
                })

            return emails

        except Exception as e:
            logger.error(f"邮箱实体提取失败: {e}")
            return []

    def _extract_phone_entities(self, content: str) -> List[Dict[str, Any]]:
        """提取电话号码实体"""
        try:
            phones = []

            # 电话号码模式
            phone_patterns = [
                (r'1[3-9]\d{9}', 'mobile_china'),
                (r'\d{3}-\d{4}-\d{4}', 'landline_us'),
                (r'\d{4}-\d{7,8}', 'landline_china'),
                (r'\(\d{3}\)\s*\d{3}-\d{4}', 'formatted_us')
            ]

            for pattern, phone_type in phone_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    phone = match.group().strip()
                    phones.append({
                        "phone": phone,
                        "type": phone_type,
                        "position": match.span(),
                        "confidence": 0.9
                    })

            return phones

        except Exception as e:
            logger.error(f"电话号码实体提取失败: {e}")
            return []

    def _calculate_entity_confidence(self, entities: Dict[str, List]) -> float:
        """计算实体提取置信度"""
        try:
            total_entities = sum(len(entity_list) for entity_list in entities.values())
            if total_entities == 0:
                return 0.0

            # 基于实体数量和类型多样性计算置信度
            entity_types = sum(1 for entity_list in entities.values() if entity_list)

            # 基础分数
            base_score = min(0.8, total_entities * 0.1)

            # 多样性加分
            diversity_bonus = entity_types * 0.05

            return min(1.0, base_score + diversity_bonus)

        except Exception as e:
            logger.error(f"实体置信度计算失败: {e}")
            return 0.0

    def _classify_organization(self, org_name: str) -> str:
        """分类组织机构"""
        try:
            if any(keyword in org_name for keyword in ["公司", "企业", "集团", "科技", "技术"]):
                return "company"
            elif any(keyword in org_name for keyword in ["大学", "学院", "研究所", "实验室"]):
                return "education"
            elif any(keyword in org_name for keyword in ["政府", "部门", "委员会", "局", "署"]):
                return "government"
            elif any(keyword in org_name for keyword in ["银行", "保险", "证券", "投资"]):
                return "finance"
            else:
                return "other"

        except Exception as e:
            logger.error(f"组织机构分类失败: {e}")
            return "unknown"

    def _extract_domain(self, url: str) -> str:
        """从URL中提取域名"""
        try:
            if not url.startswith(('http://', 'https://')):
                url = 'http://' + url

            parsed = urlparse(url)
            return parsed.netloc

        except Exception as e:
            logger.error(f"域名提取失败: {e}")
            return ""

    # ==================== JSON Schema处理辅助方法 ====================

    def _empty_schema_result(self, reason: str) -> Dict[str, Any]:
        """返回空的Schema结果"""
        return {
            "schema": {},
            "schema_type": "empty",
            "validation": {"valid": False, "confidence": 0.0},
            "confidence": 0.0,
            "error": reason,
            "timestamp": datetime.now().isoformat()
        }

    def _detect_optimal_schema_type(self, content: str) -> str:
        """检测最佳Schema类型"""
        try:
            # 检查是否为表格数据
            if self._is_table_like(content):
                return "table"

            # 检查是否为数组数据
            if self._is_array_like(content):
                return "array"

            # 检查是否为对象数据
            if self._is_object_like(content):
                return "object"

            return "object"  # 默认为对象类型

        except Exception as e:
            logger.error(f"Schema类型检测失败: {e}")
            return "object"

    def _is_table_like(self, content: str) -> bool:
        """检查是否为表格类数据"""
        try:
            lines = content.strip().split('\n')
            if len(lines) < 2:
                return False

            # 检查是否有表格分隔符
            separators = ['|', '\t', ',']
            for separator in separators:
                if separator in lines[0] and separator in lines[1]:
                    return True

            return False

        except Exception as e:
            return False

    def _is_array_like(self, content: str) -> bool:
        """检查是否为数组类数据"""
        try:
            lines = content.strip().split('\n')
            if len(lines) < 2:
                return False

            # 检查是否为列表格式
            list_indicators = ['•', '-', '*', '1.', '2.', '3.']
            for line in lines[:5]:  # 检查前5行
                if any(line.strip().startswith(indicator) for indicator in list_indicators):
                    return True

            return False

        except Exception as e:
            return False

    def _is_object_like(self, content: str) -> bool:
        """检查是否为对象类数据"""
        try:
            # 检查是否包含键值对模式
            kv_patterns = [r'[\w\u4e00-\u9fff]+[:：]\s*[\w\u4e00-\u9fff]', r'[\w\u4e00-\u9fff]+\s*=\s*[\w\u4e00-\u9fff]']

            for pattern in kv_patterns:
                if re.search(pattern, content):
                    return True

            return False

        except Exception as e:
            return False

    def _generate_object_schema(self, content: str) -> Dict[str, Any]:
        """生成对象Schema"""
        try:
            schema = {
                "type": "object",
                "properties": {},
                "required": []
            }

            # 提取键值对 - 改进模式
            lines = content.strip().split('\n')
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 匹配冒号分隔的键值对
                if ':' in line or '：' in line:
                    parts = re.split(r'[:：]', line, 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()

                        if key and len(key) <= 20:  # 合理的键长度
                            # 推断值类型
                            value_type = self._infer_value_type(value)

                            schema["properties"][key] = {
                                "type": value_type,
                                "description": f"从内容中提取的{key}字段"
                            }

                            if value:  # 如果有值，则为必需字段
                                schema["required"].append(key)

            return schema

        except Exception as e:
            logger.error(f"对象Schema生成失败: {e}")
            return {"type": "object", "properties": {}}

    def _generate_array_schema(self, content: str) -> Dict[str, Any]:
        """生成数组Schema"""
        try:
            schema = {
                "type": "array",
                "items": {
                    "type": "string",
                    "description": "列表项"
                }
            }

            # 提取列表项
            lines = content.strip().split('\n')
            items = []

            for line in lines:
                line = line.strip()
                if line:
                    # 移除列表标记
                    cleaned_line = re.sub(r'^[•\-*\d+\.]\s*', '', line)
                    if cleaned_line:
                        items.append(cleaned_line)

            if items:
                # 分析项目类型
                item_types = [self._infer_value_type(item) for item in items[:5]]
                most_common_type = max(set(item_types), key=item_types.count)

                schema["items"]["type"] = most_common_type
                schema["minItems"] = 1
                schema["maxItems"] = len(items)

            return schema

        except Exception as e:
            logger.error(f"数组Schema生成失败: {e}")
            return {"type": "array", "items": {"type": "string"}}

    def _generate_table_schema(self, content: str) -> Dict[str, Any]:
        """生成表格Schema"""
        try:
            schema = {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }

            lines = content.strip().split('\n')
            if len(lines) < 2:
                return schema

            # 检测分隔符
            separator = self._detect_table_separator(lines[0])
            if not separator:
                return schema

            # 解析表头
            headers = [h.strip() for h in lines[0].split(separator)]

            # 分析数据类型
            for header in headers:
                if header:
                    schema["items"]["properties"][header] = {
                        "type": "string",
                        "description": f"表格列: {header}"
                    }
                    schema["items"]["required"].append(header)

            return schema

        except Exception as e:
            logger.error(f"表格Schema生成失败: {e}")
            return {"type": "array", "items": {"type": "object"}}

    def _generate_generic_schema(self, content: str) -> Dict[str, Any]:
        """生成通用Schema"""
        return {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string",
                    "description": "原始内容"
                },
                "length": {
                    "type": "integer",
                    "description": "内容长度"
                }
            },
            "required": ["content"]
        }

    def _infer_value_type(self, value: str) -> str:
        """推断值的数据类型"""
        try:
            if not value:
                return "string"

            # 检查是否为数字
            if re.match(r'^\d+$', value):
                return "integer"

            if re.match(r'^\d+\.\d+$', value):
                return "number"

            # 检查是否为布尔值
            if value.lower() in ['true', 'false', '是', '否', '真', '假']:
                return "boolean"

            # 检查是否为日期
            if re.match(r'\d{4}-\d{2}-\d{2}', value):
                return "string"  # 日期作为字符串处理

            return "string"

        except Exception as e:
            return "string"

    def _detect_table_separator(self, line: str) -> Optional[str]:
        """检测表格分隔符"""
        separators = ['|', '\t', ',', ';']

        for sep in separators:
            if sep in line and len(line.split(sep)) > 1:
                return sep

        return None

    def _validate_json_schema(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """验证JSON Schema"""
        try:
            validation_result = {
                "valid": True,
                "confidence": 0.8,
                "issues": []
            }

            # 基本结构检查
            if "type" not in schema:
                validation_result["issues"].append("缺少type字段")
                validation_result["valid"] = False

            # 对象类型检查
            if schema.get("type") == "object":
                if "properties" not in schema:
                    validation_result["issues"].append("对象类型缺少properties字段")
                    validation_result["valid"] = False
                elif not schema["properties"]:
                    validation_result["issues"].append("properties为空")
                    validation_result["confidence"] *= 0.5

            # 数组类型检查
            if schema.get("type") == "array":
                if "items" not in schema:
                    validation_result["issues"].append("数组类型缺少items字段")
                    validation_result["valid"] = False

            return validation_result

        except Exception as e:
            logger.error(f"Schema验证失败: {e}")
            return {
                "valid": False,
                "confidence": 0.0,
                "issues": [f"验证失败: {str(e)}"]
            }

    # ==================== 数据关系处理辅助方法 ====================

    def _empty_relationships_result(self, reason: str) -> Dict[str, Any]:
        """返回空的关系结果"""
        return {
            "relationships": {
                "hierarchical": [],
                "causal": [],
                "temporal": [],
                "spatial": [],
                "associative": []
            },
            "relationship_graph": {},
            "relationship_strength": {},
            "total_relationships": 0,
            "confidence": 0.0,
            "error": reason,
            "timestamp": datetime.now().isoformat()
        }

    def _extract_hierarchical_relationships(self, content: str) -> List[Dict[str, Any]]:
        """提取层次关系"""
        try:
            relationships = []

            # 层次关系关键词
            hierarchy_patterns = [
                (r'([\w\u4e00-\u9fff]+)包含([\w\u4e00-\u9fff]+)', 'contains'),
                (r'([\w\u4e00-\u9fff]+)属于([\w\u4e00-\u9fff]+)', 'belongs_to'),
                (r'([\w\u4e00-\u9fff]+)是([\w\u4e00-\u9fff]+)的一部分', 'part_of'),
                (r'([\w\u4e00-\u9fff]+)下属([\w\u4e00-\u9fff]+)', 'subordinate'),
                (r'([\w\u4e00-\u9fff]+)管理([\w\u4e00-\u9fff]+)', 'manages')
            ]

            for pattern, relation_type in hierarchy_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    relationships.append({
                        "source": match.group(1).strip(),
                        "target": match.group(2).strip(),
                        "relation": relation_type,
                        "confidence": 0.8,
                        "position": match.span()
                    })

            return relationships[:20]  # 限制数量

        except Exception as e:
            logger.error(f"层次关系提取失败: {e}")
            return []

    def _extract_causal_relationships(self, content: str) -> List[Dict[str, Any]]:
        """提取因果关系"""
        try:
            relationships = []

            # 因果关系关键词
            causal_patterns = [
                (r'([\w\u4e00-\u9fff]+)导致([\w\u4e00-\u9fff]+)', 'causes'),
                (r'([\w\u4e00-\u9fff]+)引起([\w\u4e00-\u9fff]+)', 'triggers'),
                (r'由于([\w\u4e00-\u9fff]+)，([\w\u4e00-\u9fff]+)', 'because_of'),
                (r'([\w\u4e00-\u9fff]+)的结果是([\w\u4e00-\u9fff]+)', 'results_in'),
                (r'([\w\u4e00-\u9fff]+)影响([\w\u4e00-\u9fff]+)', 'affects')
            ]

            for pattern, relation_type in causal_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    relationships.append({
                        "source": match.group(1).strip(),
                        "target": match.group(2).strip(),
                        "relation": relation_type,
                        "confidence": 0.7,
                        "position": match.span()
                    })

            return relationships[:20]  # 限制数量

        except Exception as e:
            logger.error(f"因果关系提取失败: {e}")
            return []

    def _extract_temporal_relationships(self, content: str) -> List[Dict[str, Any]]:
        """提取时间关系"""
        try:
            relationships = []

            # 时间关系关键词
            temporal_patterns = [
                (r'([\w\u4e00-\u9fff]+)之前([\w\u4e00-\u9fff]+)', 'before'),
                (r'([\w\u4e00-\u9fff]+)之后([\w\u4e00-\u9fff]+)', 'after'),
                (r'([\w\u4e00-\u9fff]+)同时([\w\u4e00-\u9fff]+)', 'simultaneous'),
                (r'首先([\w\u4e00-\u9fff]+)，然后([\w\u4e00-\u9fff]+)', 'sequence'),
                (r'([\w\u4e00-\u9fff]+)期间([\w\u4e00-\u9fff]+)', 'during')
            ]

            for pattern, relation_type in temporal_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    relationships.append({
                        "source": match.group(1).strip(),
                        "target": match.group(2).strip(),
                        "relation": relation_type,
                        "confidence": 0.8,
                        "position": match.span()
                    })

            return relationships[:20]  # 限制数量

        except Exception as e:
            logger.error(f"时间关系提取失败: {e}")
            return []

    def _extract_spatial_relationships(self, content: str) -> List[Dict[str, Any]]:
        """提取空间关系"""
        try:
            relationships = []

            # 空间关系关键词
            spatial_patterns = [
                (r'([\w\u4e00-\u9fff]+)位于([\w\u4e00-\u9fff]+)', 'located_at'),
                (r'([\w\u4e00-\u9fff]+)在([\w\u4e00-\u9fff]+)附近', 'near'),
                (r'([\w\u4e00-\u9fff]+)上方([\w\u4e00-\u9fff]+)', 'above'),
                (r'([\w\u4e00-\u9fff]+)下方([\w\u4e00-\u9fff]+)', 'below'),
                (r'([\w\u4e00-\u9fff]+)旁边([\w\u4e00-\u9fff]+)', 'beside')
            ]

            for pattern, relation_type in spatial_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    relationships.append({
                        "source": match.group(1).strip(),
                        "target": match.group(2).strip(),
                        "relation": relation_type,
                        "confidence": 0.7,
                        "position": match.span()
                    })

            return relationships[:20]  # 限制数量

        except Exception as e:
            logger.error(f"空间关系提取失败: {e}")
            return []

    def _extract_associative_relationships(self, content: str) -> List[Dict[str, Any]]:
        """提取关联关系"""
        try:
            relationships = []

            # 关联关系关键词
            associative_patterns = [
                (r'([\w\u4e00-\u9fff]+)与([\w\u4e00-\u9fff]+)相关', 'related_to'),
                (r'([\w\u4e00-\u9fff]+)和([\w\u4e00-\u9fff]+)类似', 'similar_to'),
                (r'([\w\u4e00-\u9fff]+)对应([\w\u4e00-\u9fff]+)', 'corresponds_to'),
                (r'([\w\u4e00-\u9fff]+)配合([\w\u4e00-\u9fff]+)', 'cooperates_with'),
                (r'([\w\u4e00-\u9fff]+)依赖([\w\u4e00-\u9fff]+)', 'depends_on')
            ]

            for pattern, relation_type in associative_patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    relationships.append({
                        "source": match.group(1).strip(),
                        "target": match.group(2).strip(),
                        "relation": relation_type,
                        "confidence": 0.6,
                        "position": match.span()
                    })

            return relationships[:20]  # 限制数量

        except Exception as e:
            logger.error(f"关联关系提取失败: {e}")
            return []

    def _build_relationship_graph(self, relationships: Dict[str, List]) -> Dict[str, Any]:
        """构建关系图"""
        try:
            graph = {
                "nodes": set(),
                "edges": []
            }

            for relation_type, relations in relationships.items():
                for relation in relations:
                    source = relation["source"]
                    target = relation["target"]

                    graph["nodes"].add(source)
                    graph["nodes"].add(target)

                    graph["edges"].append({
                        "source": source,
                        "target": target,
                        "type": relation["relation"],
                        "category": relation_type,
                        "confidence": relation["confidence"]
                    })

            # 转换nodes为列表
            graph["nodes"] = list(graph["nodes"])

            return graph

        except Exception as e:
            logger.error(f"关系图构建失败: {e}")
            return {"nodes": [], "edges": []}

    def _calculate_relationship_strength(self, relationships: Dict[str, List]) -> Dict[str, float]:
        """计算关系强度"""
        try:
            strength = {}

            for relation_type, relations in relationships.items():
                if relations:
                    avg_confidence = sum(r["confidence"] for r in relations) / len(relations)
                    strength[relation_type] = avg_confidence
                else:
                    strength[relation_type] = 0.0

            return strength

        except Exception as e:
            logger.error(f"关系强度计算失败: {e}")
            return {}

    def _calculate_relationship_confidence(self, relationships: Dict[str, List]) -> float:
        """计算关系提取置信度"""
        try:
            total_relations = sum(len(relations) for relations in relationships.values())
            if total_relations == 0:
                return 0.0

            # 基于关系数量和类型多样性
            relation_types = sum(1 for relations in relationships.values() if relations)

            base_score = min(0.8, total_relations * 0.05)
            diversity_bonus = relation_types * 0.1

            return min(1.0, base_score + diversity_bonus)

        except Exception as e:
            logger.error(f"关系置信度计算失败: {e}")
            return 0.0

    # ==================== 数据格式标准化辅助方法 ====================

    def _empty_normalization_result(self, reason: str) -> Dict[str, Any]:
        """返回空的标准化结果"""
        return {
            "original_format": "unknown",
            "target_format": "standard",
            "normalized_data": {},
            "validation": {"valid": False, "confidence": 0.0},
            "data_quality": {"score": 0.0, "issues": []},
            "error": reason,
            "timestamp": datetime.now().isoformat()
        }

    def _detect_data_format(self, content: str) -> str:
        """检测数据格式"""
        try:
            content = content.strip()

            # JSON格式检测
            if content.startswith(('{', '[')):
                try:
                    json.loads(content)
                    return "json"
                except:
                    pass

            # CSV格式检测
            if ',' in content and '\n' in content:
                lines = content.split('\n')
                if len(lines) > 1 and all(',' in line for line in lines[:3]):
                    return "csv"

            # XML格式检测
            if content.startswith('<') and content.endswith('>'):
                return "xml"

            # 表格格式检测
            if '|' in content or '\t' in content:
                return "table"

            # 键值对格式检测
            if ':' in content or '=' in content:
                return "key_value"

            return "text"

        except Exception as e:
            logger.error(f"数据格式检测失败: {e}")
            return "unknown"

    def _parse_data_by_format(self, content: str, format_type: str) -> Optional[Dict[str, Any]]:
        """根据格式解析数据"""
        try:
            if format_type == "json":
                return {"data": json.loads(content), "type": "json"}

            elif format_type == "csv":
                lines = content.strip().split('\n')
                headers = [h.strip() for h in lines[0].split(',')]
                rows = []
                for line in lines[1:]:
                    if line.strip():
                        row = dict(zip(headers, [c.strip() for c in line.split(',')]))
                        rows.append(row)
                return {"data": rows, "type": "csv", "headers": headers}

            elif format_type == "key_value":
                data = {}
                for line in content.split('\n'):
                    if ':' in line:
                        key, value = line.split(':', 1)
                        data[key.strip()] = value.strip()
                    elif '=' in line:
                        key, value = line.split('=', 1)
                        data[key.strip()] = value.strip()
                return {"data": data, "type": "key_value"}

            elif format_type == "table":
                return self._parse_table_data(content)

            else:
                return {"data": content, "type": "text"}

        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return None

    def _parse_table_data(self, content: str) -> Dict[str, Any]:
        """解析表格数据"""
        try:
            lines = content.strip().split('\n')
            if len(lines) < 2:
                return {"data": [], "type": "table"}

            # 检测分隔符
            separator = self._detect_table_separator(lines[0])
            if not separator:
                return {"data": [], "type": "table"}

            # 解析表头
            headers = [h.strip() for h in lines[0].split(separator)]

            # 解析数据行
            rows = []
            for line in lines[1:]:
                if line.strip():
                    values = [v.strip() for v in line.split(separator)]
                    if len(values) == len(headers):
                        row = dict(zip(headers, values))
                        rows.append(row)

            return {"data": rows, "type": "table", "headers": headers}

        except Exception as e:
            logger.error(f"表格数据解析失败: {e}")
            return {"data": [], "type": "table"}

    def _normalize_to_standard(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化为标准格式"""
        try:
            data_type = parsed_data.get("type", "unknown")
            data = parsed_data.get("data", {})

            if data_type in ["csv", "table"]:
                return {
                    "format": "structured_records",
                    "records": data,
                    "count": len(data) if isinstance(data, list) else 0
                }

            elif data_type == "key_value":
                return {
                    "format": "key_value_pairs",
                    "pairs": data,
                    "count": len(data) if isinstance(data, dict) else 0
                }

            elif data_type == "json":
                return {
                    "format": "json_object",
                    "object": data,
                    "type": type(data).__name__
                }

            else:
                return {
                    "format": "raw_text",
                    "content": str(data),
                    "length": len(str(data))
                }

        except Exception as e:
            logger.error(f"标准格式转换失败: {e}")
            return {"format": "error", "error": str(e)}

    def _normalize_to_csv(self, parsed_data: Dict[str, Any]) -> str:
        """标准化为CSV格式"""
        try:
            data_type = parsed_data.get("type", "unknown")
            data = parsed_data.get("data", {})

            if data_type in ["csv", "table"] and isinstance(data, list):
                if not data:
                    return ""

                headers = list(data[0].keys())
                csv_lines = [','.join(headers)]

                for row in data:
                    values = [str(row.get(h, '')) for h in headers]
                    csv_lines.append(','.join(values))

                return '\n'.join(csv_lines)

            elif data_type == "key_value" and isinstance(data, dict):
                csv_lines = ['key,value']
                for key, value in data.items():
                    csv_lines.append(f'{key},{value}')
                return '\n'.join(csv_lines)

            else:
                return f"content\n{str(data)}"

        except Exception as e:
            logger.error(f"CSV格式转换失败: {e}")
            return f"error\n{str(e)}"

    def _normalize_to_json(self, parsed_data: Dict[str, Any]) -> str:
        """标准化为JSON格式"""
        try:
            data = parsed_data.get("data", {})
            return json.dumps(data, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"JSON格式转换失败: {e}")
            return json.dumps({"error": str(e)}, ensure_ascii=False)

    def _normalize_to_xml(self, parsed_data: Dict[str, Any]) -> str:
        """标准化为XML格式"""
        try:
            data_type = parsed_data.get("type", "unknown")
            data = parsed_data.get("data", {})

            xml_lines = ['<?xml version="1.0" encoding="UTF-8"?>']
            xml_lines.append('<data>')

            if data_type in ["csv", "table"] and isinstance(data, list):
                xml_lines.append('<records>')
                for i, row in enumerate(data):
                    xml_lines.append(f'<record id="{i}">')
                    for key, value in row.items():
                        xml_lines.append(f'<{key}>{html.escape(str(value))}</{key}>')
                    xml_lines.append('</record>')
                xml_lines.append('</records>')

            elif data_type == "key_value" and isinstance(data, dict):
                for key, value in data.items():
                    xml_lines.append(f'<{key}>{html.escape(str(value))}</{key}>')

            else:
                xml_lines.append(f'<content>{html.escape(str(data))}</content>')

            xml_lines.append('</data>')
            return '\n'.join(xml_lines)

        except Exception as e:
            logger.error(f"XML格式转换失败: {e}")
            return f'<error>{html.escape(str(e))}</error>'

    def _normalize_to_yaml(self, parsed_data: Dict[str, Any]) -> str:
        """标准化为YAML格式"""
        try:
            data = parsed_data.get("data", {})

            # 简单的YAML格式化（不使用外部库）
            def dict_to_yaml(obj, indent=0):
                lines = []
                spaces = '  ' * indent

                if isinstance(obj, dict):
                    for key, value in obj.items():
                        if isinstance(value, (dict, list)):
                            lines.append(f'{spaces}{key}:')
                            lines.extend(dict_to_yaml(value, indent + 1))
                        else:
                            lines.append(f'{spaces}{key}: {value}')

                elif isinstance(obj, list):
                    for item in obj:
                        if isinstance(item, (dict, list)):
                            lines.append(f'{spaces}-')
                            lines.extend(dict_to_yaml(item, indent + 1))
                        else:
                            lines.append(f'{spaces}- {item}')

                return lines

            yaml_lines = dict_to_yaml(data)
            return '\n'.join(yaml_lines)

        except Exception as e:
            logger.error(f"YAML格式转换失败: {e}")
            return f'error: {str(e)}'

    def _validate_normalized_data(self, normalized_data: Any, target_format: str) -> Dict[str, Any]:
        """验证标准化数据"""
        try:
            validation = {
                "valid": True,
                "confidence": 0.8,
                "issues": []
            }

            if not normalized_data:
                validation["valid"] = False
                validation["issues"].append("标准化数据为空")
                validation["confidence"] = 0.0

            elif target_format == "json":
                try:
                    if isinstance(normalized_data, str):
                        json.loads(normalized_data)
                except:
                    validation["valid"] = False
                    validation["issues"].append("JSON格式无效")

            elif target_format == "csv":
                if isinstance(normalized_data, str):
                    lines = normalized_data.split('\n')
                    if len(lines) < 2:
                        validation["issues"].append("CSV数据行数不足")
                        validation["confidence"] *= 0.7

            return validation

        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return {
                "valid": False,
                "confidence": 0.0,
                "issues": [f"验证失败: {str(e)}"]
            }

    def _assess_data_quality(self, normalized_data: Any) -> Dict[str, Any]:
        """评估数据质量"""
        try:
            quality = {
                "score": 0.0,
                "issues": [],
                "strengths": []
            }

            if not normalized_data:
                quality["issues"].append("数据为空")
                return quality

            score = 0.5  # 基础分数

            # 数据完整性检查
            if isinstance(normalized_data, dict):
                if normalized_data:
                    score += 0.2
                    quality["strengths"].append("数据结构完整")

                # 检查嵌套结构
                if any(isinstance(v, (dict, list)) for v in normalized_data.values()):
                    score += 0.1
                    quality["strengths"].append("包含结构化数据")

            elif isinstance(normalized_data, str):
                if len(normalized_data) > 10:
                    score += 0.2
                    quality["strengths"].append("内容长度适中")

                if '\n' in normalized_data:
                    score += 0.1
                    quality["strengths"].append("包含多行数据")

            quality["score"] = min(1.0, score)
            return quality

        except Exception as e:
            logger.error(f"数据质量评估失败: {e}")
            return {
                "score": 0.0,
                "issues": [f"评估失败: {str(e)}"],
                "strengths": []
            }

    def _detect_table_separator(self, line: str) -> Optional[str]:
        """检测表格分隔符"""
        try:
            separators = ['\t', '|', ',', ';']
            for sep in separators:
                if sep in line and line.count(sep) >= 1:
                    return sep
            return None
        except:
            return None
