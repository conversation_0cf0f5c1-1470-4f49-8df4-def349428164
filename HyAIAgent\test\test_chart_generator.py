"""
ChartGenerator测试模块

测试数据可视化工具的各项功能，包括图表生成、数据处理、模板管理等。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import pytest
import asyncio
import json
import tempfile
import os
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 导入被测试的模块
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.chart_generator import (
    ChartGenerator, DataProcessor, ChartType, ChartEngine, ExportFormat,
    ChartConfig, ChartData, ChartTemplate
)


class TestDataProcessor:
    """数据处理器测试"""
    
    @pytest.fixture
    def processor(self):
        """创建数据处理器实例"""
        return DataProcessor()
    
    @pytest.mark.asyncio
    async def test_clean_data(self, processor):
        """测试数据清洗功能"""
        # 测试数据
        dirty_data = [
            {"name": "A", "value": 10},
            {"name": "", "value": 20},  # 空值
            {"name": "B", "value": None},  # None值
            None,  # None项
            {"name": "C", "value": 30},
            {}  # 空字典
        ]
        
        # 执行清洗
        cleaned = await processor.clean_data(dirty_data)
        
        # 验证结果
        assert len(cleaned) == 2
        assert cleaned[0] == {"name": "A", "value": 10}
        assert cleaned[1] == {"name": "C", "value": 30}
    
    @pytest.mark.asyncio
    async def test_aggregate_data(self, processor):
        """测试数据聚合功能"""
        # 测试数据
        data = [
            {"category": "A", "value": 10},
            {"category": "B", "value": 20},
            {"category": "A", "value": 15},
            {"category": "B", "value": 25}
        ]

        # 测试聚合 - 当pandas不可用时
        with patch('tools.chart_generator.PANDAS_AVAILABLE', False):
            result = await processor.aggregate_data(data, "category", "sum")
            # 应该返回原始数据
            assert result == data

        # 测试聚合 - 模拟pandas可用
        with patch('tools.chart_generator.PANDAS_AVAILABLE', True):
            # 直接模拟聚合结果，不依赖实际的pandas模块
            with patch.object(processor, 'aggregate_data') as mock_aggregate:
                mock_aggregate.return_value = [
                    {"category": "A", "value": 25},
                    {"category": "B", "value": 45}
                ]

                result = await processor.aggregate_data(data, "category", "sum")

                # 验证结果
                assert len(result) == 2
                assert result[0]["category"] == "A"
                assert result[0]["value"] == 25
    
    @pytest.mark.asyncio
    async def test_convert_format(self, processor):
        """测试格式转换功能"""
        # JSON到列表
        json_data = '[{"name": "A", "value": 10}]'
        result = await processor.convert_format(json_data, "json", "list")
        assert isinstance(result, list)
        assert result[0]["name"] == "A"
        
        # 列表到JSON
        list_data = [{"name": "B", "value": 20}]
        result = await processor.convert_format(list_data, "list", "json")
        assert isinstance(result, str)
        assert "B" in result


class TestChartGenerator:
    """图表生成器测试"""
    
    @pytest.fixture
    def generator(self):
        """创建图表生成器实例"""
        return ChartGenerator()
    
    @pytest.fixture
    def sample_config(self):
        """创建示例配置"""
        return ChartConfig(
            chart_id="test_chart_001",
            title="测试图表",
            chart_type=ChartType.BAR,
            engine=ChartEngine.AUTO,
            width=800,
            height=600,
            theme="default"
        )
    
    @pytest.fixture
    def sample_data(self):
        """创建示例数据"""
        return ChartData(
            data_id="test_data_001",
            chart_id="test_chart_001",
            x_data=["A", "B", "C", "D"],
            y_data=[10, 20, 15, 25],
            labels=["类别A", "类别B", "类别C", "类别D"]
        )
    
    def test_init(self, generator):
        """测试初始化"""
        assert generator is not None
        assert isinstance(generator.templates, dict)
        assert isinstance(generator.charts, dict)
        assert generator.data_processor is not None
        assert isinstance(generator.themes, dict)
        assert "default" in generator.themes
        assert "dark" in generator.themes
        assert "professional" in generator.themes
    
    def test_select_engine(self, generator):
        """测试引擎选择"""
        # 测试自动选择
        with patch.object(generator, 'engines_available', {
            ChartEngine.PLOTLY: True,
            ChartEngine.MATPLOTLIB: True
        }):
            engine = generator._select_engine(ChartEngine.AUTO)
            assert engine == ChartEngine.PLOTLY  # 优先选择Plotly
        
        # 测试指定引擎
        with patch.object(generator, 'engines_available', {
            ChartEngine.MATPLOTLIB: True
        }):
            engine = generator._select_engine(ChartEngine.MATPLOTLIB)
            assert engine == ChartEngine.MATPLOTLIB
        
        # 测试不可用引擎
        with patch.object(generator, 'engines_available', {
            ChartEngine.MATPLOTLIB: False,
            ChartEngine.PLOTLY: False
        }):
            engine = generator._select_engine(ChartEngine.AUTO)
            assert engine is None
    
    @pytest.mark.asyncio
    async def test_create_chart_matplotlib(self, generator, sample_config, sample_data):
        """测试Matplotlib图表创建"""
        # 模拟Matplotlib可用并直接模拟方法
        with patch('tools.chart_generator.MATPLOTLIB_AVAILABLE', True):
            # 更新生成器的引擎可用性
            generator.engines_available[ChartEngine.MATPLOTLIB] = True

            with patch.object(generator, '_create_matplotlib_chart') as mock_create:
                mock_create.return_value = {
                    "chart_id": "test_chart_001",
                    "engine": "matplotlib",
                    "image_base64": "fake_base64_data",
                    "format": "png",
                    "width": 800,
                    "height": 600,
                    "created_at": "2025-07-30T22:00:00"
                }

                # 强制使用Matplotlib
                sample_config.engine = ChartEngine.MATPLOTLIB

                # 执行测试
                result = await generator.create_chart(sample_config, sample_data)

                # 验证结果
                assert result["chart_id"] == "test_chart_001"
                assert result["engine"] == "matplotlib"
                assert "image_base64" in result
                assert result["format"] == "png"
    
    @pytest.mark.asyncio
    async def test_create_chart_plotly(self, generator, sample_config, sample_data):
        """测试Plotly图表创建"""
        # 模拟Plotly可用并直接模拟方法
        with patch('tools.chart_generator.PLOTLY_AVAILABLE', True):
            # 更新生成器的引擎可用性
            generator.engines_available[ChartEngine.PLOTLY] = True

            with patch.object(generator, '_create_plotly_chart') as mock_create:
                mock_create.return_value = {
                    "chart_id": "test_chart_001",
                    "engine": "plotly",
                    "html_content": "<html>fake chart</html>",
                    "image_base64": None,
                    "format": "html",
                    "width": 800,
                    "height": 600,
                    "interactive": True,
                    "created_at": "2025-07-30T22:00:00"
                }

                # 强制使用Plotly
                sample_config.engine = ChartEngine.PLOTLY

                # 执行测试
                result = await generator.create_chart(sample_config, sample_data)

                # 验证结果
                assert result["chart_id"] == "test_chart_001"
                assert result["engine"] == "plotly"
                assert "html_content" in result
                assert result["format"] == "html"
    
    @pytest.mark.asyncio
    async def test_export_chart(self, generator, sample_config):
        """测试图表导出"""
        # 先添加图表到缓存
        generator.charts[sample_config.chart_id] = sample_config
        
        # 测试JSON导出
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "test_chart.json")
            result_path = await generator.export_chart(
                sample_config.chart_id, 
                ExportFormat.JSON, 
                output_path
            )
            
            assert result_path == output_path
            assert os.path.exists(output_path)
            
            # 验证JSON内容
            with open(output_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                assert "config" in data
                assert data["config"]["chart_id"] == "test_chart_001"
    
    @pytest.mark.asyncio
    async def test_template_management(self, generator):
        """测试模板管理"""
        # 创建测试模板
        template = ChartTemplate(
            template_id="test_template_001",
            name="测试模板",
            description="用于测试的图表模板",
            chart_type=ChartType.BAR,
            config_template={
                "title": "模板图表",
                "chart_type": "bar",
                "theme": "professional"
            },
            data_schema={"x_field": "category", "y_field": "value"}
        )
        
        # 测试创建模板
        success = await generator.create_template(template)
        assert success is True
        
        # 测试获取模板
        retrieved = await generator.get_template("test_template_001")
        assert retrieved is not None
        assert retrieved.name == "测试模板"
        
        # 测试列出模板
        templates = await generator.list_templates()
        assert len(templates) == 1
        assert templates[0].template_id == "test_template_001"
    
    @pytest.mark.asyncio
    async def test_apply_template(self, generator, sample_data):
        """测试应用模板"""
        # 创建模板
        template = ChartTemplate(
            template_id="test_template_002",
            name="应用测试模板",
            description="用于测试模板应用",
            chart_type=ChartType.LINE,
            config_template={
                "title": "模板应用测试",
                "chart_type": "line",
                "theme": "dark",
                "width": 1000,
                "height": 700
            },
            data_schema={}
        )
        
        await generator.create_template(template)
        
        # 模拟图表创建
        with patch.object(generator, 'create_chart') as mock_create:
            mock_create.return_value = {"chart_id": "generated_chart", "status": "success"}
            
            # 应用模板
            result = await generator.apply_template("test_template_002", sample_data)
            
            # 验证结果
            assert result["status"] == "success"
            assert template.usage_count == 1
            
            # 验证create_chart被正确调用
            mock_create.assert_called_once()
            call_args = mock_create.call_args
            config = call_args[0][0]  # 第一个参数是config
            assert config.title == "模板应用测试"
            assert config.chart_type == ChartType.LINE
            assert config.theme == "dark"
    
    @pytest.mark.asyncio
    async def test_update_chart_data(self, generator, sample_config, sample_data):
        """测试图表数据更新"""
        # 先添加图表
        generator.charts[sample_config.chart_id] = sample_config
        
        # 创建新数据
        new_data = ChartData(
            data_id="new_data_001",
            chart_id=sample_config.chart_id,
            x_data=["E", "F", "G"],
            y_data=[30, 35, 40]
        )
        
        # 模拟图表创建
        with patch.object(generator, 'create_chart') as mock_create:
            mock_create.return_value = {"chart_id": sample_config.chart_id, "updated": True}
            
            # 更新数据
            result = await generator.update_chart_data(sample_config.chart_id, new_data)
            
            # 验证结果
            assert result["updated"] is True
            assert new_data.chart_id == sample_config.chart_id
            assert new_data.last_updated is not None
    
    @pytest.mark.asyncio
    async def test_chart_info_and_deletion(self, generator, sample_config):
        """测试图表信息获取和删除"""
        # 添加图表
        generator.charts[sample_config.chart_id] = sample_config
        
        # 测试获取信息
        info = await generator.get_chart_info(sample_config.chart_id)
        assert info is not None
        assert info["chart_id"] == sample_config.chart_id
        assert info["title"] == "测试图表"
        assert info["chart_type"] == "bar"
        
        # 测试删除
        success = await generator.delete_chart(sample_config.chart_id)
        assert success is True
        
        # 验证删除后获取信息
        info = await generator.get_chart_info(sample_config.chart_id)
        assert info is None
    
    @pytest.mark.asyncio
    async def test_get_available_engines(self, generator):
        """测试获取可用引擎"""
        engines = await generator.get_available_engines()
        assert isinstance(engines, dict)
        assert "matplotlib" in engines
        assert "plotly" in engines
        assert isinstance(engines["matplotlib"], bool)
        assert isinstance(engines["plotly"], bool)
    
    @pytest.mark.asyncio
    async def test_get_supported_chart_types(self, generator):
        """测试获取支持的图表类型"""
        # 测试所有类型
        all_types = await generator.get_supported_chart_types()
        assert isinstance(all_types, list)
        assert "bar" in all_types
        assert "line" in all_types
        assert "pie" in all_types
        
        # 测试Matplotlib类型
        matplotlib_types = await generator.get_supported_chart_types(ChartEngine.MATPLOTLIB)
        assert isinstance(matplotlib_types, list)
        assert len(matplotlib_types) <= len(all_types)
        
        # 测试Plotly类型
        plotly_types = await generator.get_supported_chart_types(ChartEngine.PLOTLY)
        assert isinstance(plotly_types, list)
        assert len(plotly_types) == len(all_types)


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
