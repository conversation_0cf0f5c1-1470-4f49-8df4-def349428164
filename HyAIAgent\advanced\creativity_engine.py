"""
创意生成引擎模块

提供多种创意生成功能，包括创意写作、方案设计、创意评估和优化等核心能力。
支持多种创意类型和模板，具备智能评分和历史管理功能。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import asyncio
import json
import logging
import uuid
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union
from collections import defaultdict, deque

from core.ai_client import SimpleAIClient
from core.prompt_manager import PromptManager
from core.kv_store import KVStore


class CreativityType(Enum):
    """创意类型枚举"""
    WRITING = "writing"  # 创意写作
    DESIGN = "design"  # 方案设计
    BRAINSTORM = "brainstorm"  # 头脑风暴
    INNOVATION = "innovation"  # 创新方案
    STORYTELLING = "storytelling"  # 故事创作
    MARKETING = "marketing"  # 营销创意
    PROBLEM_SOLVING = "problem_solving"  # 问题解决
    ARTISTIC = "artistic"  # 艺术创作


class CreativityLevel(Enum):
    """创意水平枚举"""
    BASIC = "basic"  # 基础创意
    INTERMEDIATE = "intermediate"  # 中级创意
    ADVANCED = "advanced"  # 高级创意
    EXPERT = "expert"  # 专家级创意


class EvaluationCriteria(Enum):
    """评估标准枚举"""
    ORIGINALITY = "originality"  # 原创性
    FEASIBILITY = "feasibility"  # 可行性
    CREATIVITY = "creativity"  # 创造性
    RELEVANCE = "relevance"  # 相关性
    IMPACT = "impact"  # 影响力
    CLARITY = "clarity"  # 清晰度
    INNOVATION = "innovation"  # 创新性
    PRACTICALITY = "practicality"  # 实用性


@dataclass
class CreativityRequest:
    """创意请求数据模型"""
    request_id: str
    user_id: str
    creativity_type: CreativityType
    title: str
    description: str
    requirements: Dict[str, Any] = field(default_factory=dict)
    constraints: List[str] = field(default_factory=list)
    target_audience: Optional[str] = None
    creativity_level: CreativityLevel = CreativityLevel.INTERMEDIATE
    max_results: int = 3
    timestamp: datetime = field(default_factory=datetime.now)
    context: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'request_id': self.request_id,
            'user_id': self.user_id,
            'creativity_type': self.creativity_type.value,
            'title': self.title,
            'description': self.description,
            'requirements': self.requirements,
            'constraints': self.constraints,
            'target_audience': self.target_audience,
            'creativity_level': self.creativity_level.value,
            'max_results': self.max_results,
            'timestamp': self.timestamp.isoformat(),
            'context': self.context
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CreativityRequest':
        """从字典创建实例"""
        return cls(
            request_id=data['request_id'],
            user_id=data['user_id'],
            creativity_type=CreativityType(data['creativity_type']),
            title=data['title'],
            description=data['description'],
            requirements=data.get('requirements', {}),
            constraints=data.get('constraints', []),
            target_audience=data.get('target_audience'),
            creativity_level=CreativityLevel(data.get('creativity_level', 'intermediate')),
            max_results=data.get('max_results', 3),
            timestamp=datetime.fromisoformat(data['timestamp']),
            context=data.get('context', {})
        )


@dataclass
class CreativityResult:
    """创意结果数据模型"""
    result_id: str
    request_id: str
    title: str
    content: str
    creativity_type: CreativityType
    generated_at: datetime = field(default_factory=datetime.now)
    evaluation_scores: Dict[str, float] = field(default_factory=dict)
    overall_score: float = 0.0
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'result_id': self.result_id,
            'request_id': self.request_id,
            'title': self.title,
            'content': self.content,
            'creativity_type': self.creativity_type.value,
            'generated_at': self.generated_at.isoformat(),
            'evaluation_scores': self.evaluation_scores,
            'overall_score': self.overall_score,
            'tags': self.tags,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CreativityResult':
        """从字典创建实例"""
        return cls(
            result_id=data['result_id'],
            request_id=data['request_id'],
            title=data['title'],
            content=data['content'],
            creativity_type=CreativityType(data['creativity_type']),
            generated_at=datetime.fromisoformat(data['generated_at']),
            evaluation_scores=data.get('evaluation_scores', {}),
            overall_score=data.get('overall_score', 0.0),
            tags=data.get('tags', []),
            metadata=data.get('metadata', {})
        )


@dataclass
class CreativityTemplate:
    """创意模板数据模型"""
    template_id: str
    name: str
    creativity_type: CreativityType
    description: str
    prompt_template: str
    parameters: List[str] = field(default_factory=list)
    examples: List[str] = field(default_factory=list)
    difficulty_level: CreativityLevel = CreativityLevel.INTERMEDIATE
    tags: List[str] = field(default_factory=list)
    usage_count: int = 0
    success_rate: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'template_id': self.template_id,
            'name': self.name,
            'creativity_type': self.creativity_type.value,
            'description': self.description,
            'prompt_template': self.prompt_template,
            'parameters': self.parameters,
            'examples': self.examples,
            'difficulty_level': self.difficulty_level.value,
            'tags': self.tags,
            'usage_count': self.usage_count,
            'success_rate': self.success_rate
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CreativityTemplate':
        """从字典创建实例"""
        return cls(
            template_id=data['template_id'],
            name=data['name'],
            creativity_type=CreativityType(data['creativity_type']),
            description=data['description'],
            prompt_template=data['prompt_template'],
            parameters=data.get('parameters', []),
            examples=data.get('examples', []),
            difficulty_level=CreativityLevel(data.get('difficulty_level', 'intermediate')),
            tags=data.get('tags', []),
            usage_count=data.get('usage_count', 0),
            success_rate=data.get('success_rate', 0.0)
        )


@dataclass
class CreativityStats:
    """创意统计数据模型"""
    total_requests: int = 0
    successful_generations: int = 0
    failed_generations: int = 0
    average_score: float = 0.0
    type_distribution: Dict[str, int] = field(default_factory=dict)
    level_distribution: Dict[str, int] = field(default_factory=dict)
    template_usage: Dict[str, int] = field(default_factory=dict)
    user_satisfaction: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'total_requests': self.total_requests,
            'successful_generations': self.successful_generations,
            'failed_generations': self.failed_generations,
            'average_score': self.average_score,
            'type_distribution': self.type_distribution,
            'level_distribution': self.level_distribution,
            'template_usage': self.template_usage,
            'user_satisfaction': self.user_satisfaction,
            'last_updated': self.last_updated.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CreativityStats':
        """从字典创建实例"""
        return cls(
            total_requests=data.get('total_requests', 0),
            successful_generations=data.get('successful_generations', 0),
            failed_generations=data.get('failed_generations', 0),
            average_score=data.get('average_score', 0.0),
            type_distribution=data.get('type_distribution', {}),
            level_distribution=data.get('level_distribution', {}),
            template_usage=data.get('template_usage', {}),
            user_satisfaction=data.get('user_satisfaction', 0.0),
            last_updated=datetime.fromisoformat(data.get('last_updated', datetime.now().isoformat()))
        )


class CreativityEngine:
    """
    创意生成引擎
    
    提供多种创意生成功能，包括：
    - 创意写作和故事创作
    - 方案设计和问题解决
    - 创意评估和优化
    - 创意历史管理
    """
    
    # 存储键前缀
    REQUEST_PREFIX = "creativity:request:"
    RESULT_PREFIX = "creativity:result:"
    TEMPLATE_PREFIX = "creativity:template:"
    STATS_KEY = "creativity:stats"
    
    def __init__(self, ai_client: SimpleAIClient, prompt_manager: PromptManager,
                 kv_store: KVStore, max_history_size: int = 1000,
                 default_temperature: float = 0.8, evaluation_threshold: float = 0.6):
        """
        初始化创意生成引擎
        
        Args:
            ai_client: AI客户端
            prompt_manager: 提示词管理器
            kv_store: 键值存储
            max_history_size: 最大历史记录数
            default_temperature: 默认温度参数（创意生成通常需要较高温度）
            evaluation_threshold: 评估阈值
        """
        self.ai_client = ai_client
        self.prompt_manager = prompt_manager
        self.kv_store = kv_store
        self.max_history_size = max_history_size
        self.default_temperature = default_temperature
        self.evaluation_threshold = evaluation_threshold
        
        # 缓存
        self.request_cache = deque(maxlen=max_history_size)
        self.result_cache = deque(maxlen=max_history_size)
        self.template_cache: Dict[str, CreativityTemplate] = {}
        
        # 统计信息
        self.stats = CreativityStats()
        
        # 日志记录器
        self.logger = logging.getLogger(__name__)
        
        # 初始化默认模板
        asyncio.create_task(self._initialize_default_templates())
        
        self.logger.info("创意生成引擎初始化完成")

    # 核心方法
    async def generate_creativity(self, request: CreativityRequest) -> Dict[str, Any]:
        """
        生成创意内容

        Args:
            request: 创意请求

        Returns:
            Dict[str, Any]: 生成结果
        """
        try:
            self.logger.info(f"开始生成创意 - 请求ID: {request.request_id}, 类型: {request.creativity_type.value}")

            # 存储请求
            await self._store_request(request)

            # 选择合适的模板
            template = await self._select_template(request)
            if not template:
                return {
                    "success": False,
                    "error": "未找到合适的创意模板",
                    "request_id": request.request_id
                }

            # 生成创意内容
            results = []
            for i in range(request.max_results):
                try:
                    result = await self._generate_single_creativity(request, template, i + 1)
                    if result:
                        results.append(result)
                except Exception as e:
                    self.logger.error(f"生成第{i+1}个创意失败: {str(e)}")
                    continue

            if not results:
                self.stats.failed_generations += 1
                await self._save_stats()
                return {
                    "success": False,
                    "error": "创意生成失败",
                    "request_id": request.request_id
                }

            # 评估创意质量
            for result in results:
                await self._evaluate_creativity(result)
                await self._store_result(result)

            # 更新统计信息
            self.stats.successful_generations += 1
            self.stats.total_requests += 1
            self.stats.type_distribution[request.creativity_type.value] = \
                self.stats.type_distribution.get(request.creativity_type.value, 0) + 1
            self.stats.level_distribution[request.creativity_level.value] = \
                self.stats.level_distribution.get(request.creativity_level.value, 0) + 1

            # 计算平均分数
            total_score = sum(result.overall_score for result in results)
            avg_score = total_score / len(results) if results else 0.0
            self.stats.average_score = (self.stats.average_score * (self.stats.successful_generations - 1) + avg_score) / self.stats.successful_generations

            await self._save_stats()

            self.logger.info(f"创意生成完成 - 请求ID: {request.request_id}, 生成数量: {len(results)}")

            return {
                "success": True,
                "request_id": request.request_id,
                "results": [result.to_dict() for result in results],
                "template_used": template.name,
                "generation_count": len(results),
                "average_score": avg_score
            }

        except Exception as e:
            self.logger.error(f"创意生成出错: {str(e)}")
            self.stats.failed_generations += 1
            await self._save_stats()
            return {
                "success": False,
                "error": str(e),
                "request_id": request.request_id
            }

    async def evaluate_creativity(self, result: CreativityResult,
                                criteria: List[EvaluationCriteria] = None) -> Dict[str, Any]:
        """
        评估创意质量

        Args:
            result: 创意结果
            criteria: 评估标准列表

        Returns:
            Dict[str, Any]: 评估结果
        """
        try:
            self.logger.info(f"开始评估创意 - 结果ID: {result.result_id}")

            if criteria is None:
                criteria = [
                    EvaluationCriteria.ORIGINALITY,
                    EvaluationCriteria.CREATIVITY,
                    EvaluationCriteria.RELEVANCE,
                    EvaluationCriteria.CLARITY,
                    EvaluationCriteria.FEASIBILITY
                ]

            # 构建评估提示词
            evaluation_prompt = self._build_evaluation_prompt(result, criteria)

            # 调用AI进行评估
            evaluation_response = self.ai_client.chat(
                evaluation_prompt,
                system_prompt="你是一个专业的创意评估专家，请客观公正地评估创意作品的质量。"
            )

            # 解析评估结果
            scores = await self._parse_evaluation_response(evaluation_response, criteria)

            # 更新结果
            result.evaluation_scores = scores
            result.overall_score = sum(scores.values()) / len(scores) if scores else 0.0

            # 存储更新后的结果
            await self._store_result(result)

            self.logger.info(f"创意评估完成 - 结果ID: {result.result_id}, 总分: {result.overall_score:.2f}")

            return {
                "success": True,
                "result_id": result.result_id,
                "evaluation_scores": scores,
                "overall_score": result.overall_score,
                "criteria_used": [c.value for c in criteria]
            }

        except Exception as e:
            self.logger.error(f"创意评估出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "result_id": result.result_id
            }

    async def optimize_creativity(self, result: CreativityResult,
                                optimization_goals: List[str] = None) -> Dict[str, Any]:
        """
        优化创意内容

        Args:
            result: 原始创意结果
            optimization_goals: 优化目标列表

        Returns:
            Dict[str, Any]: 优化结果
        """
        try:
            self.logger.info(f"开始优化创意 - 结果ID: {result.result_id}")

            if optimization_goals is None:
                optimization_goals = ["提高原创性", "增强可行性", "改善表达清晰度"]

            # 构建优化提示词
            optimization_prompt = self._build_optimization_prompt(result, optimization_goals)

            # 调用AI进行优化
            optimized_content = self.ai_client.chat(
                optimization_prompt,
                system_prompt="你是一个创意优化专家，请根据指定目标改进创意内容，保持原有创意的核心价值。"
            )

            # 创建优化后的结果
            optimized_result = CreativityResult(
                result_id=f"{result.result_id}_optimized_{uuid.uuid4().hex[:8]}",
                request_id=result.request_id,
                title=f"{result.title} (优化版)",
                content=optimized_content,
                creativity_type=result.creativity_type,
                metadata={
                    **result.metadata,
                    "original_result_id": result.result_id,
                    "optimization_goals": optimization_goals,
                    "optimization_timestamp": datetime.now().isoformat()
                }
            )

            # 评估优化后的创意
            await self._evaluate_creativity(optimized_result)
            await self._store_result(optimized_result)

            # 比较优化效果
            improvement = optimized_result.overall_score - result.overall_score

            self.logger.info(f"创意优化完成 - 原始ID: {result.result_id}, 优化ID: {optimized_result.result_id}, 改进: {improvement:.2f}")

            return {
                "success": True,
                "original_result_id": result.result_id,
                "optimized_result": optimized_result.to_dict(),
                "improvement_score": improvement,
                "optimization_goals": optimization_goals
            }

        except Exception as e:
            self.logger.error(f"创意优化出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "result_id": result.result_id
            }

    async def add_template(self, template: CreativityTemplate) -> Dict[str, Any]:
        """
        添加创意模板

        Args:
            template: 创意模板

        Returns:
            Dict[str, Any]: 添加结果
        """
        try:
            self.logger.info(f"添加创意模板 - ID: {template.template_id}, 名称: {template.name}")

            # 存储模板
            await self._store_template(template)

            # 更新缓存
            self.template_cache[template.template_id] = template

            self.logger.info(f"创意模板添加成功 - ID: {template.template_id}")

            return {
                "success": True,
                "template_id": template.template_id,
                "message": "模板添加成功"
            }

        except Exception as e:
            self.logger.error(f"添加创意模板出错: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "template_id": template.template_id
            }

    async def get_creativity_history(self, user_id: Optional[str] = None,
                                   creativity_type: Optional[CreativityType] = None,
                                   limit: int = 50) -> Dict[str, Any]:
        """
        获取创意历史记录

        Args:
            user_id: 用户ID（可选）
            creativity_type: 创意类型（可选）
            limit: 返回数量限制

        Returns:
            Dict[str, Any]: 历史记录
        """
        try:
            self.logger.info(f"获取创意历史 - 用户: {user_id or 'all'}, 类型: {creativity_type.value if creativity_type else 'all'}")

            # 构建查询模式
            patterns = []
            if user_id and creativity_type:
                patterns.append(f"{self.RESULT_PREFIX}{user_id}:*")
            elif user_id:
                patterns.append(f"{self.RESULT_PREFIX}{user_id}:*")
            elif creativity_type:
                patterns.append(f"{self.RESULT_PREFIX}*")
            else:
                patterns.append(f"{self.RESULT_PREFIX}*")

            # 获取历史记录
            history_results = []
            for pattern in patterns:
                keys = self.kv_store.keys()
                matching_keys = [key for key in keys if key.startswith(pattern.replace("*", ""))]

                for key in matching_keys[:limit]:
                    result_data = self.kv_store.get(key)
                    if result_data:
                        try:
                            result = CreativityResult.from_dict(result_data)
                            # 过滤类型
                            if creativity_type and result.creativity_type != creativity_type:
                                continue
                            history_results.append(result)
                        except Exception as e:
                            self.logger.error(f"解析历史记录失败: {key}, 错误: {e}")
                            continue

            # 按时间排序
            history_results.sort(key=lambda x: x.generated_at, reverse=True)
            history_results = history_results[:limit]

            self.logger.info(f"创意历史获取完成 - 用户: {user_id or 'all'}, 记录数: {len(history_results)}")

            return {
                "success": True,
                "user_id": user_id,
                "creativity_type": creativity_type.value if creativity_type else None,
                "total_count": len(history_results),
                "results": [result.to_dict() for result in history_results]
            }

        except Exception as e:
            self.logger.error(f"获取创意历史出错: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_creativity_stats(self) -> Dict[str, Any]:
        """
        获取创意统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            self.logger.info("获取创意统计信息")

            # 从存储加载最新统计
            stats_data = self.kv_store.get(self.STATS_KEY)
            if stats_data:
                self.stats = CreativityStats.from_dict(stats_data)

            # 计算成功率
            success_rate = 0.0
            if self.stats.total_requests > 0:
                success_rate = self.stats.successful_generations / self.stats.total_requests

            # 获取模板使用统计
            template_stats = {}
            for template_id, template in self.template_cache.items():
                template_stats[template.name] = {
                    "usage_count": template.usage_count,
                    "success_rate": template.success_rate
                }

            return {
                "success": True,
                "stats": {
                    **self.stats.to_dict(),
                    "success_rate": success_rate,
                    "template_stats": template_stats
                }
            }

        except Exception as e:
            self.logger.error(f"获取创意统计出错: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def clear_creativity_history(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        清理创意历史记录

        Args:
            user_id: 用户ID（可选，为None时清理所有）

        Returns:
            Dict[str, Any]: 清理结果
        """
        try:
            self.logger.info(f"清理创意历史 - 用户: {user_id or 'all'}")

            cleared_count = 0

            if user_id:
                # 清理特定用户的记录
                patterns = [
                    f"{self.REQUEST_PREFIX}{user_id}:*",
                    f"{self.RESULT_PREFIX}{user_id}:*"
                ]

                for pattern in patterns:
                    keys = self.kv_store.keys()
                    matching_keys = [key for key in keys if key.startswith(pattern.replace("*", ""))]

                    for key in matching_keys:
                        self.kv_store.delete(key)
                        cleared_count += 1

                # 清理缓存
                self.request_cache.clear()
                self.result_cache.clear()

            else:
                # 清理所有记录
                prefixes = [self.REQUEST_PREFIX, self.RESULT_PREFIX]

                for prefix in prefixes:
                    keys = self.kv_store.keys()
                    matching_keys = [key for key in keys if key.startswith(prefix)]

                    for key in matching_keys:
                        self.kv_store.delete(key)
                        cleared_count += 1

                # 清理所有缓存
                self.request_cache.clear()
                self.result_cache.clear()

                # 重置统计信息
                self.stats = CreativityStats()
                await self._save_stats()

            self.logger.info(f"创意历史清理完成 - 用户: {user_id or 'all'}, 删除记录: {cleared_count}")

            return {
                "success": True,
                "user_id": user_id,
                "cleared_count": cleared_count
            }

        except Exception as e:
            self.logger.error(f"清理创意历史出错: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    # 私有辅助方法
    async def _initialize_default_templates(self) -> None:
        """初始化默认创意模板"""
        try:
            default_templates = [
                CreativityTemplate(
                    template_id="writing_story_basic",
                    name="基础故事创作",
                    creativity_type=CreativityType.STORYTELLING,
                    description="用于创作基础故事的模板",
                    prompt_template="""请创作一个{genre}类型的故事，要求如下：

标题：{title}
主题：{theme}
字数：{word_count}字左右
目标读者：{target_audience}

故事要求：
1. 情节引人入胜，有明确的开头、发展、高潮和结尾
2. 人物形象鲜明，性格突出
3. 语言生动，符合{genre}类型的特点
4. 传达积极正面的价值观

请开始创作：""",
                    parameters=["genre", "title", "theme", "word_count", "target_audience"],
                    examples=["科幻小说", "悬疑故事", "童话故事"],
                    difficulty_level=CreativityLevel.BASIC
                ),

                CreativityTemplate(
                    template_id="design_solution_intermediate",
                    name="方案设计模板",
                    creativity_type=CreativityType.DESIGN,
                    description="用于设计解决方案的中级模板",
                    prompt_template="""请为以下问题设计一个创新解决方案：

问题描述：{problem_description}
目标用户：{target_users}
约束条件：{constraints}
预算范围：{budget_range}
时间限制：{time_limit}

方案要求：
1. 创新性强，具有独特的解决思路
2. 可行性高，能够实际实施
3. 成本效益好，符合预算要求
4. 用户体验佳，满足目标用户需求

请提供详细的解决方案：""",
                    parameters=["problem_description", "target_users", "constraints", "budget_range", "time_limit"],
                    examples=["产品设计", "服务优化", "流程改进"],
                    difficulty_level=CreativityLevel.INTERMEDIATE
                ),

                CreativityTemplate(
                    template_id="brainstorm_ideas_advanced",
                    name="高级头脑风暴",
                    creativity_type=CreativityType.BRAINSTORM,
                    description="用于高级头脑风暴的模板",
                    prompt_template="""请围绕以下主题进行深度头脑风暴：

主题：{topic}
背景信息：{background}
目标：{objectives}
参与者：{participants}
时间框架：{timeframe}

头脑风暴要求：
1. 生成至少{idea_count}个创新想法
2. 每个想法都要有独特性和可行性
3. 从多个角度思考问题
4. 鼓励跨界思维和联想
5. 对每个想法进行简要评估

请开始头脑风暴：""",
                    parameters=["topic", "background", "objectives", "participants", "timeframe", "idea_count"],
                    examples=["产品创新", "营销策略", "技术突破"],
                    difficulty_level=CreativityLevel.ADVANCED
                )
            ]

            # 存储默认模板
            for template in default_templates:
                await self._store_template(template)
                self.template_cache[template.template_id] = template

            self.logger.info(f"默认创意模板初始化完成，共{len(default_templates)}个模板")

        except Exception as e:
            self.logger.error(f"初始化默认模板失败: {str(e)}")

    async def _store_request(self, request: CreativityRequest) -> None:
        """存储创意请求"""
        key = f"{self.REQUEST_PREFIX}{request.user_id}:{request.request_id}"
        self.kv_store.set(key, request.to_dict(), ttl=86400 * 30)  # 30天TTL
        self.request_cache.append(request)

    async def _store_result(self, result: CreativityResult) -> None:
        """存储创意结果"""
        # 从请求ID中提取用户ID
        request_data = self.kv_store.get(f"{self.REQUEST_PREFIX}*{result.request_id}")
        user_id = "unknown"
        if request_data:
            user_id = request_data.get("user_id", "unknown")

        key = f"{self.RESULT_PREFIX}{user_id}:{result.result_id}"
        self.kv_store.set(key, result.to_dict(), ttl=86400 * 90)  # 90天TTL
        self.result_cache.append(result)

    async def _store_template(self, template: CreativityTemplate) -> None:
        """存储创意模板"""
        key = f"{self.TEMPLATE_PREFIX}{template.template_id}"
        self.kv_store.set(key, template.to_dict())

    async def _save_stats(self) -> None:
        """保存统计信息"""
        self.stats.last_updated = datetime.now()
        self.kv_store.set(self.STATS_KEY, self.stats.to_dict())

    async def _select_template(self, request: CreativityRequest) -> Optional[CreativityTemplate]:
        """选择合适的创意模板"""
        try:
            # 首先尝试从缓存中查找
            suitable_templates = []
            for template in self.template_cache.values():
                if template.creativity_type == request.creativity_type:
                    suitable_templates.append(template)

            # 如果缓存中没有，从存储中加载
            if not suitable_templates:
                keys = self.kv_store.keys()
                template_keys = [key for key in keys if key.startswith(self.TEMPLATE_PREFIX)]

                for key in template_keys:
                    template_data = self.kv_store.get(key)
                    if template_data:
                        try:
                            template = CreativityTemplate.from_dict(template_data)
                            if template.creativity_type == request.creativity_type:
                                suitable_templates.append(template)
                                self.template_cache[template.template_id] = template
                        except Exception as e:
                            self.logger.error(f"加载模板失败: {key}, 错误: {e}")
                            continue

            if not suitable_templates:
                return None

            # 根据难度级别和成功率选择最佳模板
            best_template = None
            best_score = -1

            for template in suitable_templates:
                # 计算模板评分（考虑难度匹配度和成功率）
                difficulty_match = 1.0 if template.difficulty_level == request.creativity_level else 0.5
                success_weight = template.success_rate if template.usage_count > 0 else 0.5
                score = difficulty_match * 0.6 + success_weight * 0.4

                if score > best_score:
                    best_score = score
                    best_template = template

            return best_template

        except Exception as e:
            self.logger.error(f"选择模板失败: {str(e)}")
            return None

    async def _generate_single_creativity(self, request: CreativityRequest,
                                        template: CreativityTemplate,
                                        iteration: int) -> Optional[CreativityResult]:
        """生成单个创意内容"""
        try:
            # 构建提示词
            prompt = self._build_generation_prompt(request, template)

            # 设置创意生成的温度参数
            original_temp = self.ai_client.temperature
            self.ai_client.temperature = self.default_temperature

            # 生成创意内容
            content = self.ai_client.chat(
                prompt,
                system_prompt=f"你是一个专业的{request.creativity_type.value}创意专家，请根据要求生成高质量的创意内容。"
            )

            # 恢复原始温度
            self.ai_client.temperature = original_temp

            # 创建结果对象
            result = CreativityResult(
                result_id=f"{request.request_id}_result_{iteration}_{uuid.uuid4().hex[:8]}",
                request_id=request.request_id,
                title=f"{request.title} - 方案{iteration}",
                content=content,
                creativity_type=request.creativity_type,
                metadata={
                    "template_id": template.template_id,
                    "template_name": template.name,
                    "iteration": iteration,
                    "generation_timestamp": datetime.now().isoformat()
                }
            )

            # 更新模板使用统计
            template.usage_count += 1
            await self._store_template(template)

            return result

        except Exception as e:
            self.logger.error(f"生成单个创意失败: {str(e)}")
            return None

    def _build_generation_prompt(self, request: CreativityRequest, template: CreativityTemplate) -> str:
        """构建生成提示词"""
        try:
            # 基础提示词
            prompt = template.prompt_template

            # 替换基本参数
            replacements = {
                "title": request.title,
                "description": request.description,
                "target_audience": request.target_audience or "一般用户",
                "creativity_level": request.creativity_level.value
            }

            # 添加需求参数
            for key, value in request.requirements.items():
                replacements[key] = str(value)

            # 执行参数替换
            for param, value in replacements.items():
                placeholder = f"{{{param}}}"
                if placeholder in prompt:
                    prompt = prompt.replace(placeholder, value)

            # 添加约束条件
            if request.constraints:
                constraints_text = "\n".join([f"- {constraint}" for constraint in request.constraints])
                prompt += f"\n\n约束条件：\n{constraints_text}"

            # 添加上下文信息
            if request.context:
                context_text = "\n".join([f"{k}: {v}" for k, v in request.context.items()])
                prompt += f"\n\n上下文信息：\n{context_text}"

            return prompt

        except Exception as e:
            self.logger.error(f"构建生成提示词失败: {str(e)}")
            return template.prompt_template

    async def _evaluate_creativity(self, result: CreativityResult) -> None:
        """评估创意质量（内部方法）"""
        try:
            criteria = [
                EvaluationCriteria.ORIGINALITY,
                EvaluationCriteria.CREATIVITY,
                EvaluationCriteria.RELEVANCE,
                EvaluationCriteria.CLARITY
            ]

            evaluation_result = await self.evaluate_creativity(result, criteria)
            if evaluation_result["success"]:
                # 评估已在evaluate_creativity方法中完成
                pass

        except Exception as e:
            self.logger.error(f"内部评估创意失败: {str(e)}")
            # 设置默认分数
            result.evaluation_scores = {criterion.value: 0.5 for criterion in EvaluationCriteria}
            result.overall_score = 0.5

    def _build_evaluation_prompt(self, result: CreativityResult,
                               criteria: List[EvaluationCriteria]) -> str:
        """构建评估提示词"""
        criteria_descriptions = {
            EvaluationCriteria.ORIGINALITY: "原创性 - 内容的独特性和新颖性",
            EvaluationCriteria.CREATIVITY: "创造性 - 想象力和创新思维的体现",
            EvaluationCriteria.RELEVANCE: "相关性 - 与主题和要求的匹配度",
            EvaluationCriteria.CLARITY: "清晰度 - 表达的清楚程度和易理解性",
            EvaluationCriteria.FEASIBILITY: "可行性 - 实际执行的可能性",
            EvaluationCriteria.IMPACT: "影响力 - 潜在的影响和价值",
            EvaluationCriteria.INNOVATION: "创新性 - 突破性和前瞻性",
            EvaluationCriteria.PRACTICALITY: "实用性 - 实际应用的价值"
        }

        criteria_text = "\n".join([
            f"{i+1}. {criteria_descriptions[criterion]} (1-10分)"
            for i, criterion in enumerate(criteria)
        ])

        return f"""请评估以下创意内容的质量：

标题：{result.title}
类型：{result.creativity_type.value}
内容：
{result.content}

评估标准：
{criteria_text}

请为每个标准打分（1-10分，1分最低，10分最高），并简要说明理由。
请按以下格式返回评分：

评分结果：
{chr(10).join([f'{criterion.value}: X分 - 理由' for criterion in criteria])}

总体评价：[简要总结]"""

    async def _parse_evaluation_response(self, response: str,
                                       criteria: List[EvaluationCriteria]) -> Dict[str, float]:
        """解析评估响应"""
        scores = {}

        try:
            # 简单的分数提取逻辑
            for criterion in criteria:
                criterion_name = criterion.value
                # 查找形如 "criterion_name: X分" 的模式
                import re
                pattern = rf"{criterion_name}:\s*(\d+(?:\.\d+)?)"
                match = re.search(pattern, response, re.IGNORECASE)

                if match:
                    score = float(match.group(1))
                    # 将分数标准化到0-1范围
                    scores[criterion_name] = min(max(score / 10.0, 0.0), 1.0)
                else:
                    # 如果没有找到分数，设置默认值
                    scores[criterion_name] = 0.5

        except Exception as e:
            self.logger.error(f"解析评估响应失败: {str(e)}")
            # 设置默认分数
            for criterion in criteria:
                scores[criterion.value] = 0.5

        return scores

    def _build_optimization_prompt(self, result: CreativityResult,
                                 optimization_goals: List[str]) -> str:
        """构建优化提示词"""
        goals_text = "\n".join([f"- {goal}" for goal in optimization_goals])

        return f"""请优化以下创意内容：

原始标题：{result.title}
原始内容：
{result.content}

优化目标：
{goals_text}

当前评分：{result.overall_score:.2f}/1.0

请根据优化目标改进创意内容，保持原有创意的核心价值和主要思路，但在指定方面进行提升。

优化后的内容："""
