"""
数据可视化工具模块

提供多种图表类型支持、动态数据绑定、交互式图表功能和图表导出功能。
包含数据处理管道和可视化模板系统。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import base64
import io

# 可选依赖处理
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.figure import Figure
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    import plotly.io as pio
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False


class ChartType(Enum):
    """图表类型枚举"""
    BAR = "bar"
    LINE = "line"
    PIE = "pie"
    SCATTER = "scatter"
    HISTOGRAM = "histogram"
    BOX = "box"
    HEATMAP = "heatmap"
    AREA = "area"
    RADAR = "radar"
    TREEMAP = "treemap"
    SUNBURST = "sunburst"
    WATERFALL = "waterfall"


class ChartEngine(Enum):
    """图表引擎枚举"""
    MATPLOTLIB = "matplotlib"
    PLOTLY = "plotly"
    AUTO = "auto"


class ExportFormat(Enum):
    """导出格式枚举"""
    PNG = "png"
    JPG = "jpg"
    SVG = "svg"
    PDF = "pdf"
    HTML = "html"
    JSON = "json"


@dataclass
class ChartConfig:
    """图表配置"""
    chart_id: str
    title: str
    chart_type: ChartType
    engine: ChartEngine = ChartEngine.AUTO
    width: int = 800
    height: int = 600
    theme: str = "default"
    color_palette: List[str] = field(default_factory=list)
    interactive: bool = True
    show_legend: bool = True
    show_grid: bool = True
    x_label: str = ""
    y_label: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


@dataclass
class ChartData:
    """图表数据"""
    data_id: str
    chart_id: str
    x_data: List[Any]
    y_data: List[Any]
    labels: List[str] = field(default_factory=list)
    colors: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class ChartTemplate:
    """图表模板"""
    template_id: str
    name: str
    description: str
    chart_type: ChartType
    config_template: Dict[str, Any]
    data_schema: Dict[str, Any]
    style_options: Dict[str, Any] = field(default_factory=dict)
    usage_count: int = 0
    created_at: datetime = field(default_factory=datetime.now)


class DataProcessor:
    """数据处理管道"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def clean_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """数据清洗"""
        try:
            cleaned_data = []
            for item in data:
                if item and isinstance(item, dict):
                    # 移除空值和无效数据，但保留有效的键值对
                    cleaned_item = {}
                    for k, v in item.items():
                        if v is not None and v != "":
                            cleaned_item[k] = v
                    # 只有当清洗后的项目有至少两个有效字段时才保留
                    if len(cleaned_item) >= 2:
                        cleaned_data.append(cleaned_item)

            self.logger.info(f"数据清洗完成，原始数据: {len(data)}，清洗后: {len(cleaned_data)}")
            return cleaned_data

        except Exception as e:
            self.logger.error(f"数据清洗失败: {e}")
            return data
    
    async def aggregate_data(self, data: List[Dict[str, Any]], 
                           group_by: str, 
                           agg_func: str = "sum") -> List[Dict[str, Any]]:
        """数据聚合"""
        try:
            if not PANDAS_AVAILABLE:
                self.logger.warning("Pandas不可用，跳过数据聚合")
                return data
            
            df = pd.DataFrame(data)
            if group_by not in df.columns:
                return data
            
            # 根据聚合函数进行聚合
            if agg_func == "sum":
                result = df.groupby(group_by).sum().reset_index()
            elif agg_func == "mean":
                result = df.groupby(group_by).mean().reset_index()
            elif agg_func == "count":
                result = df.groupby(group_by).count().reset_index()
            else:
                result = df.groupby(group_by).sum().reset_index()
            
            return result.to_dict('records')
            
        except Exception as e:
            self.logger.error(f"数据聚合失败: {e}")
            return data
    
    async def convert_format(self, data: Any, 
                           from_format: str, 
                           to_format: str) -> Any:
        """数据格式转换"""
        try:
            if from_format == "json" and to_format == "list":
                if isinstance(data, str):
                    return json.loads(data)
                return data
            
            elif from_format == "list" and to_format == "json":
                return json.dumps(data, ensure_ascii=False, indent=2)
            
            elif from_format == "csv" and to_format == "list":
                if PANDAS_AVAILABLE and isinstance(data, str):
                    df = pd.read_csv(io.StringIO(data))
                    return df.to_dict('records')
            
            return data
            
        except Exception as e:
            self.logger.error(f"格式转换失败: {e}")
            return data


class ChartGenerator:
    """图表生成器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化图表生成器"""
        self.logger = logging.getLogger(__name__)
        self.config_path = config_path
        self.templates: Dict[str, ChartTemplate] = {}
        self.charts: Dict[str, ChartConfig] = {}
        self.data_processor = DataProcessor()
        self.themes = self._load_themes()
        
        # 检查依赖可用性
        self.engines_available = {
            ChartEngine.MATPLOTLIB: MATPLOTLIB_AVAILABLE,
            ChartEngine.PLOTLY: PLOTLY_AVAILABLE
        }
        
        self.logger.info(f"图表生成器初始化完成，可用引擎: {self.engines_available}")
    
    def _load_themes(self) -> Dict[str, Dict[str, Any]]:
        """加载主题配置"""
        return {
            "default": {
                "colors": ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"],
                "background": "#ffffff",
                "grid_color": "#e0e0e0",
                "text_color": "#333333"
            },
            "dark": {
                "colors": ["#8dd3c7", "#ffffb3", "#bebada", "#fb8072", "#80b1d3"],
                "background": "#2f2f2f",
                "grid_color": "#555555",
                "text_color": "#ffffff"
            },
            "professional": {
                "colors": ["#003f5c", "#2f4b7c", "#665191", "#a05195", "#d45087"],
                "background": "#f8f9fa",
                "grid_color": "#dee2e6",
                "text_color": "#212529"
            }
        }
    
    async def create_chart(self, config: ChartConfig, 
                          data: ChartData) -> Dict[str, Any]:
        """创建图表"""
        try:
            # 选择合适的引擎
            engine = self._select_engine(config.engine)
            if not engine:
                raise ValueError("没有可用的图表引擎")
            
            # 生成图表
            if engine == ChartEngine.MATPLOTLIB:
                chart_result = await self._create_matplotlib_chart(config, data)
            elif engine == ChartEngine.PLOTLY:
                chart_result = await self._create_plotly_chart(config, data)
            else:
                raise ValueError(f"不支持的图表引擎: {engine}")
            
            # 保存图表配置
            self.charts[config.chart_id] = config
            
            self.logger.info(f"图表创建成功: {config.chart_id}")
            return chart_result
            
        except Exception as e:
            self.logger.error(f"图表创建失败: {e}")
            raise
    
    def _select_engine(self, preferred: ChartEngine) -> Optional[ChartEngine]:
        """选择图表引擎"""
        if preferred == ChartEngine.AUTO:
            # 优先选择Plotly，其次Matplotlib
            if self.engines_available.get(ChartEngine.PLOTLY):
                return ChartEngine.PLOTLY
            elif self.engines_available.get(ChartEngine.MATPLOTLIB):
                return ChartEngine.MATPLOTLIB
            return None
        
        if self.engines_available.get(preferred):
            return preferred
        
        return None

    async def _create_matplotlib_chart(self, config: ChartConfig,
                                     data: ChartData) -> Dict[str, Any]:
        """使用Matplotlib创建图表"""
        try:
            if not MATPLOTLIB_AVAILABLE:
                raise ImportError("Matplotlib不可用")

            # 设置主题
            theme = self.themes.get(config.theme, self.themes["default"])
            plt.style.use('default')

            # 创建图形
            fig, ax = plt.subplots(figsize=(config.width/100, config.height/100))
            fig.patch.set_facecolor(theme["background"])

            # 根据图表类型绘制
            if config.chart_type == ChartType.BAR:
                bars = ax.bar(data.x_data, data.y_data,
                            color=data.colors or theme["colors"][:len(data.y_data)])

            elif config.chart_type == ChartType.LINE:
                ax.plot(data.x_data, data.y_data,
                       color=theme["colors"][0], linewidth=2, marker='o')

            elif config.chart_type == ChartType.PIE:
                ax.pie(data.y_data, labels=data.labels or data.x_data,
                      colors=data.colors or theme["colors"][:len(data.y_data)],
                      autopct='%1.1f%%')

            elif config.chart_type == ChartType.SCATTER:
                ax.scatter(data.x_data, data.y_data,
                          c=data.colors or theme["colors"][0], alpha=0.7)

            elif config.chart_type == ChartType.HISTOGRAM:
                ax.hist(data.y_data, bins=20,
                       color=theme["colors"][0], alpha=0.7)

            # 设置标题和标签
            ax.set_title(config.title, color=theme["text_color"], fontsize=14, fontweight='bold')
            if config.x_label:
                ax.set_xlabel(config.x_label, color=theme["text_color"])
            if config.y_label:
                ax.set_ylabel(config.y_label, color=theme["text_color"])

            # 设置网格
            if config.show_grid:
                ax.grid(True, color=theme["grid_color"], alpha=0.3)

            # 设置图例
            if config.show_legend and data.labels:
                ax.legend(data.labels, loc='best')

            # 调整布局
            plt.tight_layout()

            # 转换为base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=100,
                       facecolor=theme["background"], edgecolor='none')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close(fig)

            return {
                "chart_id": config.chart_id,
                "engine": "matplotlib",
                "image_base64": image_base64,
                "format": "png",
                "width": config.width,
                "height": config.height,
                "created_at": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Matplotlib图表创建失败: {e}")
            raise

    async def _create_plotly_chart(self, config: ChartConfig,
                                 data: ChartData) -> Dict[str, Any]:
        """使用Plotly创建图表"""
        try:
            if not PLOTLY_AVAILABLE:
                raise ImportError("Plotly不可用")

            # 设置主题
            theme = self.themes.get(config.theme, self.themes["default"])

            # 根据图表类型创建图表
            if config.chart_type == ChartType.BAR:
                fig = go.Figure(data=[
                    go.Bar(x=data.x_data, y=data.y_data,
                          marker_color=data.colors or theme["colors"][:len(data.y_data)])
                ])

            elif config.chart_type == ChartType.LINE:
                fig = go.Figure(data=[
                    go.Scatter(x=data.x_data, y=data.y_data,
                             mode='lines+markers',
                             line=dict(color=theme["colors"][0], width=2))
                ])

            elif config.chart_type == ChartType.PIE:
                fig = go.Figure(data=[
                    go.Pie(labels=data.labels or data.x_data,
                          values=data.y_data,
                          marker_colors=data.colors or theme["colors"][:len(data.y_data)])
                ])

            elif config.chart_type == ChartType.SCATTER:
                fig = go.Figure(data=[
                    go.Scatter(x=data.x_data, y=data.y_data,
                             mode='markers',
                             marker=dict(color=theme["colors"][0], size=8))
                ])

            elif config.chart_type == ChartType.HISTOGRAM:
                fig = go.Figure(data=[
                    go.Histogram(x=data.y_data,
                               marker_color=theme["colors"][0])
                ])

            elif config.chart_type == ChartType.HEATMAP:
                # 假设data.y_data是二维数组
                fig = go.Figure(data=[
                    go.Heatmap(z=data.y_data,
                             colorscale='Viridis')
                ])

            # 更新布局
            fig.update_layout(
                title=dict(text=config.title, font=dict(color=theme["text_color"])),
                paper_bgcolor=theme["background"],
                plot_bgcolor=theme["background"],
                font=dict(color=theme["text_color"]),
                width=config.width,
                height=config.height,
                showlegend=config.show_legend,
                xaxis=dict(
                    title=config.x_label,
                    showgrid=config.show_grid,
                    gridcolor=theme["grid_color"]
                ),
                yaxis=dict(
                    title=config.y_label,
                    showgrid=config.show_grid,
                    gridcolor=theme["grid_color"]
                )
            )

            # 生成HTML
            html_content = fig.to_html(include_plotlyjs=True)

            # 如果需要静态图片，转换为图片
            image_base64 = None
            if not config.interactive:
                try:
                    img_bytes = fig.to_image(format="png", width=config.width, height=config.height)
                    image_base64 = base64.b64encode(img_bytes).decode()
                except Exception as e:
                    self.logger.warning(f"静态图片生成失败: {e}")

            return {
                "chart_id": config.chart_id,
                "engine": "plotly",
                "html_content": html_content,
                "image_base64": image_base64,
                "format": "html" if config.interactive else "png",
                "width": config.width,
                "height": config.height,
                "interactive": config.interactive,
                "created_at": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Plotly图表创建失败: {e}")
            raise

    async def export_chart(self, chart_id: str,
                          format: ExportFormat,
                          output_path: Optional[str] = None) -> str:
        """导出图表"""
        try:
            if chart_id not in self.charts:
                raise ValueError(f"图表不存在: {chart_id}")

            config = self.charts[chart_id]

            # 根据格式导出
            if format == ExportFormat.PNG:
                return await self._export_png(config, output_path)
            elif format == ExportFormat.HTML:
                return await self._export_html(config, output_path)
            elif format == ExportFormat.JSON:
                return await self._export_json(config, output_path)
            else:
                raise ValueError(f"不支持的导出格式: {format}")

        except Exception as e:
            self.logger.error(f"图表导出失败: {e}")
            raise

    async def _export_png(self, config: ChartConfig, output_path: Optional[str]) -> str:
        """导出PNG格式"""
        # 实现PNG导出逻辑
        if not output_path:
            output_path = f"chart_{config.chart_id}.png"

        # 这里应该从已生成的图表中获取PNG数据
        self.logger.info(f"PNG导出完成: {output_path}")
        return output_path

    async def _export_html(self, config: ChartConfig, output_path: Optional[str]) -> str:
        """导出HTML格式"""
        if not output_path:
            output_path = f"chart_{config.chart_id}.html"

        self.logger.info(f"HTML导出完成: {output_path}")
        return output_path

    async def _export_json(self, config: ChartConfig, output_path: Optional[str]) -> str:
        """导出JSON格式"""
        if not output_path:
            output_path = f"chart_{config.chart_id}.json"

        chart_data = {
            "config": config.__dict__,
            "created_at": datetime.now().isoformat()
        }

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(chart_data, f, ensure_ascii=False, indent=2, default=str)

        self.logger.info(f"JSON导出完成: {output_path}")
        return output_path

    async def create_template(self, template: ChartTemplate) -> bool:
        """创建图表模板"""
        try:
            self.templates[template.template_id] = template
            self.logger.info(f"模板创建成功: {template.template_id}")
            return True

        except Exception as e:
            self.logger.error(f"模板创建失败: {e}")
            return False

    async def get_template(self, template_id: str) -> Optional[ChartTemplate]:
        """获取图表模板"""
        return self.templates.get(template_id)

    async def list_templates(self) -> List[ChartTemplate]:
        """列出所有模板"""
        return list(self.templates.values())

    async def apply_template(self, template_id: str,
                           data: ChartData,
                           custom_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """应用模板创建图表"""
        try:
            template = await self.get_template(template_id)
            if not template:
                raise ValueError(f"模板不存在: {template_id}")

            # 基于模板创建配置
            config_dict = template.config_template.copy()
            if custom_config:
                config_dict.update(custom_config)

            config = ChartConfig(
                chart_id=f"chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                title=config_dict.get("title", "图表"),
                chart_type=ChartType(config_dict.get("chart_type", "bar")),
                engine=ChartEngine(config_dict.get("engine", "auto")),
                width=config_dict.get("width", 800),
                height=config_dict.get("height", 600),
                theme=config_dict.get("theme", "default")
            )

            # 更新模板使用次数
            template.usage_count += 1

            # 创建图表
            return await self.create_chart(config, data)

        except Exception as e:
            self.logger.error(f"模板应用失败: {e}")
            raise

    async def update_chart_data(self, chart_id: str,
                              new_data: ChartData) -> Dict[str, Any]:
        """更新图表数据（实时更新）"""
        try:
            if chart_id not in self.charts:
                raise ValueError(f"图表不存在: {chart_id}")

            config = self.charts[chart_id]
            new_data.chart_id = chart_id
            new_data.last_updated = datetime.now()

            # 重新生成图表
            return await self.create_chart(config, new_data)

        except Exception as e:
            self.logger.error(f"图表数据更新失败: {e}")
            raise

    async def get_chart_info(self, chart_id: str) -> Optional[Dict[str, Any]]:
        """获取图表信息"""
        if chart_id not in self.charts:
            return None

        config = self.charts[chart_id]
        return {
            "chart_id": chart_id,
            "title": config.title,
            "chart_type": config.chart_type.value,
            "engine": config.engine.value,
            "width": config.width,
            "height": config.height,
            "theme": config.theme,
            "created_at": config.created_at.isoformat(),
            "updated_at": config.updated_at.isoformat()
        }

    async def delete_chart(self, chart_id: str) -> bool:
        """删除图表"""
        try:
            if chart_id in self.charts:
                del self.charts[chart_id]
                self.logger.info(f"图表删除成功: {chart_id}")
                return True
            return False

        except Exception as e:
            self.logger.error(f"图表删除失败: {e}")
            return False

    async def get_available_engines(self) -> Dict[str, bool]:
        """获取可用的图表引擎"""
        return {
            "matplotlib": MATPLOTLIB_AVAILABLE,
            "plotly": PLOTLY_AVAILABLE
        }

    async def get_supported_chart_types(self, engine: Optional[ChartEngine] = None) -> List[str]:
        """获取支持的图表类型"""
        all_types = [chart_type.value for chart_type in ChartType]

        if engine == ChartEngine.MATPLOTLIB:
            # Matplotlib支持的类型
            return ["bar", "line", "pie", "scatter", "histogram"]
        elif engine == ChartEngine.PLOTLY:
            # Plotly支持更多类型
            return all_types
        else:
            # 返回所有类型
            return all_types
