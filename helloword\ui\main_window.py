#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面模块
负责界面布局、控件创建和样式设置
遵循单一职责原则，只处理UI相关逻辑
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QGridLayout, QLabel, QLineEdit, 
                             QComboBox, QPushButton, QTextEdit, QFrame,
                             QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont


class MainWindowUI(QMainWindow):
    """
    主窗口界面类
    只负责界面的创建、布局和样式，不包含业务逻辑
    """
    
    # 定义界面信号，供业务逻辑层连接
    greeting_requested = pyqtSignal(str, str)  # 问候请求信号
    time_requested = pyqtSignal()              # 时间请求信号
    clear_requested = pyqtSignal()             # 清除请求信号
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.apply_styles()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("🐍 Python PyQt6 GUI Hello World")
        self.setGeometry(100, 100, 600, 500)
        self.setMinimumSize(500, 400)
        
        # 设置窗口居中
        self.center_window()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        self.create_main_layout(central_widget)
        
    def center_window(self):
        """将窗口居中显示"""
        from PyQt6.QtWidgets import QApplication
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def create_main_layout(self, parent):
        """创建主要布局和控件"""
        # 主垂直布局
        main_layout = QVBoxLayout(parent)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # 创建各个区域
        self.create_title_section(main_layout)
        self.create_input_section(main_layout)
        self.create_button_section(main_layout)
        self.create_result_section(main_layout)
        
    def create_title_section(self, parent_layout):
        """创建标题区域"""
        title_label = QLabel("🐍 Python PyQt6 GUI 程序")
        title_font = QFont("Arial", 18, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setObjectName("titleLabel")
        parent_layout.addWidget(title_label)
        
    def create_input_section(self, parent_layout):
        """创建输入区域"""
        # 输入区域框架
        input_frame = QFrame()
        input_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        input_frame.setObjectName("inputFrame")
        input_layout = QGridLayout(input_frame)
        input_layout.setSpacing(15)
        
        # 姓名输入
        name_label = QLabel("请输入您的姓名:")
        name_label.setFont(QFont("Arial", 10))
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("请输入您的姓名...")
        self.name_input.setMinimumHeight(30)
        self.name_input.setObjectName("nameInput")
        
        input_layout.addWidget(name_label, 0, 0)
        input_layout.addWidget(self.name_input, 0, 1)
        
        # 问候语选择
        greeting_label = QLabel("选择问候语:")
        greeting_label.setFont(QFont("Arial", 10))
        self.greeting_combo = QComboBox()
        self.greeting_combo.addItems(["你好", "Hello", "Bonjour", "Hola", "こんにちは"])
        self.greeting_combo.setMinimumHeight(30)
        self.greeting_combo.setObjectName("greetingCombo")
        
        input_layout.addWidget(greeting_label, 1, 0)
        input_layout.addWidget(self.greeting_combo, 1, 1)
        
        # 设置列拉伸
        input_layout.setColumnStretch(1, 1)
        
        parent_layout.addWidget(input_frame)
        
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        # 创建按钮
        self.greet_btn = QPushButton("🎉 问候")
        self.time_btn = QPushButton("⏰ 显示时间")
        self.clear_btn = QPushButton("🗑️ 清除")
        self.exit_btn = QPushButton("❌ 退出")
        
        # 设置按钮属性
        buttons = [self.greet_btn, self.time_btn, self.clear_btn, self.exit_btn]
        for btn in buttons:
            btn.setMinimumHeight(35)
            btn.setObjectName("actionButton")
        
        # 连接按钮信号到界面信号（信号转发）
        self.greet_btn.clicked.connect(self._on_greet_clicked)
        self.time_btn.clicked.connect(self.time_requested.emit)
        self.clear_btn.clicked.connect(self.clear_requested.emit)
        self.exit_btn.clicked.connect(self.close)
        
        # 添加按钮到布局
        for btn in buttons:
            button_layout.addWidget(btn)
        
        parent_layout.addLayout(button_layout)
        
    def create_result_section(self, parent_layout):
        """创建结果显示区域"""
        result_label = QLabel("输出结果:")
        result_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        parent_layout.addWidget(result_label)
        
        # 结果文本框
        self.result_text = QTextEdit()
        self.result_text.setMinimumHeight(150)
        self.result_text.setFont(QFont("Consolas", 10))
        self.result_text.setPlaceholderText("程序输出将显示在这里...")
        self.result_text.setObjectName("resultText")
        
        # 设置文本框可扩展
        self.result_text.setSizePolicy(QSizePolicy.Policy.Expanding, 
                                       QSizePolicy.Policy.Expanding)
        
        parent_layout.addWidget(self.result_text)
        
    def _on_greet_clicked(self):
        """处理问候按钮点击（内部方法，转发信号）"""
        name = self.name_input.text().strip()
        greeting = self.greeting_combo.currentText()
        self.greeting_requested.emit(greeting, name)
        
    def apply_styles(self):
        """应用样式表"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            
            #titleLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
            
            #actionButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 10pt;
            }
            
            #actionButton:hover {
                background-color: #2980b9;
            }
            
            #actionButton:pressed {
                background-color: #21618c;
            }
            
            #nameInput, #greetingCombo {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 5px;
                font-size: 10pt;
            }
            
            #nameInput:focus, #greetingCombo:focus {
                border-color: #3498db;
            }
            
            #resultText {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px;
                background-color: white;
            }
            
            #inputFrame {
                background-color: white;
                border-radius: 8px;
                padding: 10px;
            }
            
            QLabel {
                color: #2c3e50;
            }
        """)
        
    # 公共接口方法，供业务逻辑层调用
    def get_user_input(self):
        """获取用户输入"""
        return {
            'name': self.name_input.text().strip(),
            'greeting': self.greeting_combo.currentText()
        }
        
    def append_result(self, text):
        """向结果区域追加文本"""
        self.result_text.append(text.rstrip())
        # 滚动到底部
        cursor = self.result_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.result_text.setTextCursor(cursor)
        
    def clear_result(self):
        """清除结果区域"""
        self.result_text.clear()
        
    def focus_name_input(self):
        """将焦点设置到姓名输入框"""
        self.name_input.setFocus()
