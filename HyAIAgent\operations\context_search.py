"""
上下文智能搜索模块

该模块提供基于对话上下文和用户意图的智能搜索功能，
能够理解用户的搜索意图并提供更精准的搜索结果。

主要功能：
1. 对话上下文分析和理解
2. 用户意图识别和分类
3. 上下文感知的查询优化
4. 智能搜索结果排序和过滤
5. 搜索历史和偏好学习

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import asyncio
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from collections import defaultdict, deque

# 导入依赖模块
from operations.search_operations import SearchOperations, SearchResponse, SearchResult
from operations.multi_round_search import MultiRoundSearchManager, SearchStrategy
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager
from core.kv_store import KVStore

# 配置日志
logger = logging.getLogger(__name__)

class UserIntent(Enum):
    """用户意图枚举"""
    INFORMATION_SEEKING = "information_seeking"  # 信息查找
    PROBLEM_SOLVING = "problem_solving"  # 问题解决
    COMPARISON = "comparison"  # 比较分析
    TUTORIAL = "tutorial"  # 教程学习
    NEWS_UPDATE = "news_update"  # 新闻更新
    RESEARCH = "research"  # 研究调查
    SHOPPING = "shopping"  # 购物查询
    ENTERTAINMENT = "entertainment"  # 娱乐内容
    TECHNICAL_SUPPORT = "technical_support"  # 技术支持
    GENERAL_INQUIRY = "general_inquiry"  # 一般询问

class ContextType(Enum):
    """上下文类型枚举"""
    CONVERSATION = "conversation"  # 对话上下文
    SEARCH_HISTORY = "search_history"  # 搜索历史
    USER_PROFILE = "user_profile"  # 用户画像
    DOMAIN_KNOWLEDGE = "domain_knowledge"  # 领域知识
    TEMPORAL = "temporal"  # 时间上下文
    LOCATION = "location"  # 位置上下文

@dataclass
class ConversationContext:
    """对话上下文数据结构"""
    conversation_id: str
    messages: List[Dict[str, Any]] = field(default_factory=list)
    topics: List[str] = field(default_factory=list)
    entities: List[str] = field(default_factory=list)
    keywords: List[str] = field(default_factory=list)
    intent_history: List[UserIntent] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class SearchContext:
    """搜索上下文数据结构"""
    session_id: str
    user_id: Optional[str] = None
    conversation_context: Optional[ConversationContext] = None
    search_history: List[Dict[str, Any]] = field(default_factory=list)
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    domain_context: Dict[str, Any] = field(default_factory=dict)
    temporal_context: Dict[str, Any] = field(default_factory=dict)
    location_context: Dict[str, Any] = field(default_factory=dict)
    intent: Optional[UserIntent] = None
    confidence_score: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ContextualSearchResult:
    """上下文搜索结果数据结构"""
    session_id: str
    original_query: str
    optimized_query: str
    detected_intent: UserIntent
    context_factors: Dict[str, float]
    search_results: List[SearchResult]
    relevance_scores: Dict[str, float]
    context_relevance: float
    total_results: int
    execution_time: float
    success: bool
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

class IntentClassifier:
    """用户意图分类器"""
    
    def __init__(self):
        """初始化意图分类器"""
        self.intent_patterns = self._load_intent_patterns()
        self.intent_keywords = self._load_intent_keywords()
        
    def _load_intent_patterns(self) -> Dict[UserIntent, List[str]]:
        """加载意图识别模式"""
        return {
            UserIntent.INFORMATION_SEEKING: [
                r"什么是|what is|如何|how to|为什么|why|哪里|where",
                r"介绍|explain|definition|定义|概念"
            ],
            UserIntent.PROBLEM_SOLVING: [
                r"解决.*问题|solve.*problem|修复|fix|故障|trouble|bug|异常|exception",
                r"如何解决|how to fix|如何修复|troubleshoot|排除故障"
            ],
            UserIntent.COMPARISON: [
                r"比较|compare|对比|vs|versus|区别|difference",
                r"哪个更好|which is better|优缺点|pros and cons"
            ],
            UserIntent.TUTORIAL: [
                r"教程|tutorial|学习|learn|指南|guide",
                r"步骤|steps|如何做|how to do"
            ],
            UserIntent.NEWS_UPDATE: [
                r"最新|latest|新闻|news|更新|update",
                r"今天|today|最近|recent|现在|now"
            ],
            UserIntent.RESEARCH: [
                r"研究|research|调查|survey|分析|analysis",
                r"报告|report|数据|data|统计|statistics"
            ],
            UserIntent.SHOPPING: [
                r"购买|buy|价格|price|商品|product",
                r"推荐|recommend|评价|review|便宜|cheap"
            ],
            UserIntent.ENTERTAINMENT: [
                r"电影|movie|音乐|music|游戏|game",
                r"娱乐|entertainment|有趣|funny|好玩|fun"
            ],
            UserIntent.TECHNICAL_SUPPORT: [
                r"技术|technical|支持|support|帮助|help",
                r"配置|config|设置|setting|安装|install"
            ]
        }
    
    def _load_intent_keywords(self) -> Dict[UserIntent, Set[str]]:
        """加载意图关键词"""
        return {
            UserIntent.INFORMATION_SEEKING: {
                "什么", "如何", "为什么", "哪里", "介绍", "定义", "概念",
                "what", "how", "why", "where", "explain", "definition"
            },
            UserIntent.PROBLEM_SOLVING: {
                "解决", "修复", "问题", "错误", "故障", "bug", "异常",
                "solve", "fix", "problem", "error", "trouble", "exception"
            },
            UserIntent.COMPARISON: {
                "比较", "对比", "区别", "哪个更好", "优缺点",
                "compare", "vs", "versus", "difference", "better", "pros", "cons"
            },
            UserIntent.TUTORIAL: {
                "教程", "学习", "指南", "步骤", "如何做",
                "tutorial", "learn", "guide", "steps", "how to"
            },
            UserIntent.NEWS_UPDATE: {
                "最新", "新闻", "更新", "今天", "最近", "现在",
                "latest", "news", "update", "today", "recent", "now"
            },
            UserIntent.RESEARCH: {
                "研究", "调查", "分析", "报告", "数据", "统计",
                "research", "survey", "analysis", "report", "data", "statistics"
            },
            UserIntent.SHOPPING: {
                "购买", "价格", "商品", "推荐", "评价", "便宜",
                "buy", "price", "product", "recommend", "review", "cheap"
            },
            UserIntent.ENTERTAINMENT: {
                "电影", "音乐", "游戏", "娱乐", "有趣", "好玩",
                "movie", "music", "game", "entertainment", "funny", "fun"
            },
            UserIntent.TECHNICAL_SUPPORT: {
                "技术", "支持", "帮助", "配置", "设置", "安装",
                "technical", "support", "help", "config", "setting", "install"
            }
        }
    
    def classify_intent(self, query: str, context: Optional[ConversationContext] = None) -> Tuple[UserIntent, float]:
        """分类用户意图"""
        try:
            query_lower = query.lower()
            intent_scores = defaultdict(float)
            
            # 基于模式匹配计算分数
            for intent, patterns in self.intent_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, query_lower):
                        # 问题解决意图给更高权重
                        weight = 0.5 if intent == UserIntent.PROBLEM_SOLVING else 0.3
                        intent_scores[intent] += weight
            
            # 基于关键词匹配计算分数
            query_words = set(re.findall(r'\w+', query_lower))
            for intent, keywords in self.intent_keywords.items():
                common_words = query_words.intersection(keywords)
                if common_words:
                    intent_scores[intent] += len(common_words) * 0.2
            
            # 考虑上下文历史
            if context and context.intent_history:
                recent_intents = context.intent_history[-3:]  # 最近3个意图
                for intent in recent_intents:
                    intent_scores[intent] += 0.1
            
            # 选择最高分数的意图
            if intent_scores:
                best_intent = max(intent_scores.items(), key=lambda x: x[1])
                return best_intent[0], min(best_intent[1], 1.0)
            else:
                return UserIntent.GENERAL_INQUIRY, 0.5
                
        except Exception as e:
            logger.error(f"意图分类失败: {e}")
            return UserIntent.GENERAL_INQUIRY, 0.0

class ContextAnalyzer:
    """上下文分析器"""

    def __init__(self, kv_store: Optional[KVStore] = None):
        """初始化上下文分析器"""
        self.kv_store = kv_store
        self.entity_patterns = self._load_entity_patterns()
        self.topic_keywords = self._load_topic_keywords()

    def _load_entity_patterns(self) -> Dict[str, str]:
        """加载实体识别模式"""
        return {
            "person": r"[A-Z][a-z]+ [A-Z][a-z]+|[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*",
            "organization": r"[A-Z][a-zA-Z\s]+(?:公司|Company|Corp|Inc|Ltd|LLC)",
            "location": r"[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:市|省|国|City|State|Country)",
            "date": r"\d{4}[-/]\d{1,2}[-/]\d{1,2}|\d{1,2}[-/]\d{1,2}[-/]\d{4}",
            "time": r"\d{1,2}:\d{2}(?::\d{2})?(?:\s*[AaPp][Mm])?",
            "number": r"\d+(?:\.\d+)?",
            "email": r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}",
            "url": r"https?://[^\s]+",
            "phone": r"\d{3}-\d{3}-\d{4}|\(\d{3}\)\s*\d{3}-\d{4}"
        }

    def _load_topic_keywords(self) -> Dict[str, Set[str]]:
        """加载主题关键词"""
        return {
            "technology": {
                "python", "java", "javascript", "ai", "machine", "learning", "deep",
                "programming", "software", "development", "algorithm", "data", "science",
                "机器学习", "人工智能", "编程", "软件", "开发", "算法", "数据科学"
            },
            "business": {
                "marketing", "sales", "finance", "management", "strategy", "investment",
                "startup", "entrepreneur", "business", "company", "market"
            },
            "science": {
                "research", "experiment", "theory", "hypothesis", "analysis", "study",
                "biology", "chemistry", "physics", "mathematics", "statistics"
            },
            "health": {
                "medicine", "doctor", "hospital", "treatment", "disease", "health",
                "fitness", "nutrition", "exercise", "wellness", "medical"
            },
            "education": {
                "school", "university", "student", "teacher", "course", "learning",
                "education", "study", "academic", "degree", "knowledge", "tutorial",
                "学习", "教程", "课程", "学校", "大学", "学生", "老师", "教育", "知识"
            },
            "entertainment": {
                "movie", "music", "game", "sport", "art", "culture", "entertainment",
                "celebrity", "show", "performance", "festival"
            }
        }

    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """提取文本实体"""
        try:
            entities = defaultdict(list)

            for entity_type, pattern in self.entity_patterns.items():
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    entities[entity_type].extend(matches)

            return dict(entities)

        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            return {}

    def identify_topics(self, text: str) -> List[Tuple[str, float]]:
        """识别文本主题"""
        try:
            text_lower = text.lower()
            # 提取英文单词和中文字符
            english_words = set(re.findall(r'[a-zA-Z]+', text_lower))
            chinese_chars = re.findall(r'[\u4e00-\u9fff]+', text_lower)

            # 合并所有词汇
            all_words = english_words.copy()
            for chinese_text in chinese_chars:
                # 简单的中文词汇提取
                for i in range(len(chinese_text)):
                    for j in range(i+1, min(i+5, len(chinese_text)+1)):
                        word = chinese_text[i:j]
                        if len(word) >= 2:
                            all_words.add(word)

            topic_scores = []
            for topic, keywords in self.topic_keywords.items():
                # 检查关键词是否在文本中
                matched_keywords = []
                for keyword in keywords:
                    if keyword in text_lower or any(keyword in word for word in all_words):
                        matched_keywords.append(keyword)

                if matched_keywords:
                    score = len(matched_keywords) / len(keywords)
                    topic_scores.append((topic, score))

            # 按分数排序
            topic_scores.sort(key=lambda x: x[1], reverse=True)
            return topic_scores[:5]  # 返回前5个主题

        except Exception as e:
            logger.error(f"主题识别失败: {e}")
            return []

    def analyze_conversation_context(self, messages: List[Dict[str, Any]]) -> ConversationContext:
        """分析对话上下文"""
        try:
            conversation_id = f"conv_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 合并所有消息文本
            all_text = " ".join([msg.get("content", "") for msg in messages])

            # 提取实体
            entities = self.extract_entities(all_text)
            all_entities = []
            for entity_list in entities.values():
                all_entities.extend(entity_list)

            # 识别主题
            topics = [topic for topic, score in self.identify_topics(all_text)]

            # 提取关键词
            keywords = self._extract_keywords(all_text)

            return ConversationContext(
                conversation_id=conversation_id,
                messages=messages,
                topics=topics,
                entities=all_entities,
                keywords=keywords,
                metadata={
                    "message_count": len(messages),
                    "total_length": len(all_text),
                    "entity_types": list(entities.keys())
                }
            )

        except Exception as e:
            logger.error(f"对话上下文分析失败: {e}")
            return ConversationContext(conversation_id="error_conv")

    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        try:
            # 简单的关键词提取
            words = re.findall(r'\w+', text.lower())

            # 过滤停用词
            stop_words = {
                "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for",
                "of", "with", "by", "is", "are", "was", "were", "be", "been", "have",
                "has", "had", "do", "does", "did", "will", "would", "could", "should",
                "这", "那", "的", "了", "在", "是", "有", "和", "与", "或", "但", "如果"
            }

            keywords = [word for word in words if len(word) > 2 and word not in stop_words]

            # 统计词频并返回前20个
            from collections import Counter
            word_counts = Counter(keywords)
            return [word for word, count in word_counts.most_common(20)]

        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return []

class ContextualSearchManager:
    """上下文智能搜索管理器"""

    def __init__(self, config_manager: ConfigManager, search_operations: SearchOperations,
                 multi_round_manager: MultiRoundSearchManager, security_manager: SecurityManager = None,
                 kv_store: KVStore = None):
        """初始化上下文搜索管理器"""
        self.config_manager = config_manager
        self.search_operations = search_operations
        self.multi_round_manager = multi_round_manager
        self.security_manager = security_manager
        self.kv_store = kv_store

        # 初始化组件
        self.intent_classifier = IntentClassifier()
        self.context_analyzer = ContextAnalyzer(kv_store)

        # 搜索会话缓存
        self.search_sessions = {}
        self.max_session_age = timedelta(hours=24)

        logger.info("上下文智能搜索管理器初始化完成")

    async def execute_contextual_search(self, query: str, conversation_messages: List[Dict[str, Any]] = None,
                                      user_id: str = None, search_preferences: Dict[str, Any] = None) -> ContextualSearchResult:
        """执行上下文智能搜索"""
        start_time = datetime.now()
        session_id = self._generate_session_id()

        try:
            # 1. 分析对话上下文
            conversation_context = None
            if conversation_messages:
                conversation_context = self.context_analyzer.analyze_conversation_context(conversation_messages)

            # 2. 分类用户意图
            intent, confidence = self.intent_classifier.classify_intent(query, conversation_context)

            # 3. 构建搜索上下文
            search_context = SearchContext(
                session_id=session_id,
                user_id=user_id,
                conversation_context=conversation_context,
                user_preferences=search_preferences or {},
                intent=intent,
                confidence_score=confidence
            )

            # 4. 基于上下文优化查询
            optimized_query = await self._optimize_query_with_context(query, search_context)

            # 5. 选择搜索策略
            search_strategy = self._select_search_strategy(intent, search_context)

            # 6. 执行多轮搜索
            multi_round_result = await self.multi_round_manager.execute_multi_round_search(
                query=optimized_query,
                strategy=search_strategy,
                max_rounds=self._get_max_rounds_for_intent(intent),
                quality_threshold=self._get_quality_threshold_for_intent(intent)
            )

            # 7. 基于上下文重新排序结果
            reranked_results = await self._rerank_results_by_context(
                multi_round_result.final_results, search_context
            )

            # 8. 计算上下文相关性
            context_relevance = self._calculate_context_relevance(reranked_results, search_context)

            # 9. 保存搜索会话
            await self._save_search_session(session_id, search_context, multi_round_result)

            # 10. 构建结果
            execution_time = (datetime.now() - start_time).total_seconds()

            result = ContextualSearchResult(
                session_id=session_id,
                original_query=query,
                optimized_query=optimized_query,
                detected_intent=intent,
                context_factors=self._extract_context_factors(search_context),
                search_results=reranked_results,
                relevance_scores=self._calculate_relevance_scores(reranked_results, search_context),
                context_relevance=context_relevance,
                total_results=len(reranked_results),
                execution_time=execution_time,
                success=multi_round_result.success,
                metadata={
                    "intent_confidence": confidence,
                    "search_strategy": search_strategy.value,
                    "multi_round_stats": {
                        "total_rounds": multi_round_result.total_rounds,
                        "overall_quality": multi_round_result.overall_quality
                    },
                    "context_types": self._get_active_context_types(search_context)
                }
            )

            logger.info(f"上下文搜索完成 - 会话ID: {session_id}, 结果数: {len(reranked_results)}")
            return result

        except Exception as e:
            logger.error(f"上下文搜索执行失败: {e}")
            execution_time = (datetime.now() - start_time).total_seconds()

            return ContextualSearchResult(
                session_id=session_id,
                original_query=query,
                optimized_query=query,
                detected_intent=UserIntent.GENERAL_INQUIRY,
                context_factors={},
                search_results=[],
                relevance_scores={},
                context_relevance=0.0,
                total_results=0,
                execution_time=execution_time,
                success=False,
                error_message=str(e)
            )

    async def _optimize_query_with_context(self, query: str, search_context: SearchContext) -> str:
        """基于上下文优化查询"""
        try:
            optimized_query = query

            # 基于对话上下文添加相关词汇
            if search_context.conversation_context:
                context = search_context.conversation_context

                # 添加主题相关词汇
                if context.topics:
                    main_topic = context.topics[0]
                    optimized_query += f" {main_topic}"

                # 添加重要实体
                if context.entities:
                    important_entities = context.entities[:2]  # 最多添加2个实体
                    for entity in important_entities:
                        if entity.lower() not in query.lower():
                            optimized_query += f" {entity}"

            # 基于用户意图优化
            intent_modifiers = {
                UserIntent.NEWS_UPDATE: " latest recent news",
                UserIntent.TUTORIAL: " tutorial guide how-to",
                UserIntent.COMPARISON: " comparison vs difference",
                UserIntent.RESEARCH: " research study analysis",
                UserIntent.TECHNICAL_SUPPORT: " help support solution"
            }

            if search_context.intent in intent_modifiers:
                modifier = intent_modifiers[search_context.intent]
                if not any(word in query.lower() for word in modifier.split()):
                    optimized_query += modifier

            # 基于用户偏好优化
            if search_context.user_preferences:
                preferred_sources = search_context.user_preferences.get("preferred_sources", [])
                if preferred_sources:
                    source_modifier = f" site:{preferred_sources[0]}"
                    optimized_query += source_modifier

            logger.debug(f"查询优化: '{query}' -> '{optimized_query}'")
            return optimized_query.strip()

        except Exception as e:
            logger.error(f"查询优化失败: {e}")
            return query

    def _select_search_strategy(self, intent: UserIntent, search_context: SearchContext) -> SearchStrategy:
        """根据意图选择搜索策略"""
        strategy_mapping = {
            UserIntent.INFORMATION_SEEKING: SearchStrategy.PROGRESSIVE,
            UserIntent.PROBLEM_SOLVING: SearchStrategy.FOCUSED,
            UserIntent.COMPARISON: SearchStrategy.PARALLEL,
            UserIntent.TUTORIAL: SearchStrategy.PROGRESSIVE,
            UserIntent.NEWS_UPDATE: SearchStrategy.ADAPTIVE,
            UserIntent.RESEARCH: SearchStrategy.EXPLORATORY,
            UserIntent.SHOPPING: SearchStrategy.PARALLEL,
            UserIntent.ENTERTAINMENT: SearchStrategy.ADAPTIVE,
            UserIntent.TECHNICAL_SUPPORT: SearchStrategy.FOCUSED,
            UserIntent.GENERAL_INQUIRY: SearchStrategy.PROGRESSIVE
        }

        return strategy_mapping.get(intent, SearchStrategy.PROGRESSIVE)

    def _get_max_rounds_for_intent(self, intent: UserIntent) -> int:
        """根据意图获取最大搜索轮数"""
        rounds_mapping = {
            UserIntent.INFORMATION_SEEKING: 2,
            UserIntent.PROBLEM_SOLVING: 3,
            UserIntent.COMPARISON: 3,
            UserIntent.TUTORIAL: 2,
            UserIntent.NEWS_UPDATE: 2,
            UserIntent.RESEARCH: 4,
            UserIntent.SHOPPING: 3,
            UserIntent.ENTERTAINMENT: 2,
            UserIntent.TECHNICAL_SUPPORT: 3,
            UserIntent.GENERAL_INQUIRY: 2
        }

        return rounds_mapping.get(intent, 2)

    def _get_quality_threshold_for_intent(self, intent: UserIntent) -> float:
        """根据意图获取质量阈值"""
        threshold_mapping = {
            UserIntent.INFORMATION_SEEKING: 0.7,
            UserIntent.PROBLEM_SOLVING: 0.8,
            UserIntent.COMPARISON: 0.7,
            UserIntent.TUTORIAL: 0.6,
            UserIntent.NEWS_UPDATE: 0.6,
            UserIntent.RESEARCH: 0.8,
            UserIntent.SHOPPING: 0.6,
            UserIntent.ENTERTAINMENT: 0.5,
            UserIntent.TECHNICAL_SUPPORT: 0.8,
            UserIntent.GENERAL_INQUIRY: 0.6
        }

        return threshold_mapping.get(intent, 0.7)

    async def _rerank_results_by_context(self, results: List[SearchResult],
                                       search_context: SearchContext) -> List[SearchResult]:
        """基于上下文重新排序搜索结果"""
        try:
            if not results:
                return results

            # 计算每个结果的上下文相关性分数
            scored_results = []
            for result in results:
                context_score = self._calculate_result_context_score(result, search_context)
                # 结合原始分数和上下文分数
                combined_score = (result.score * 0.6) + (context_score * 0.4)
                scored_results.append((result, combined_score))

            # 按综合分数排序
            scored_results.sort(key=lambda x: x[1], reverse=True)

            # 返回重新排序的结果
            reranked = [result for result, score in scored_results]
            logger.debug(f"结果重新排序完成，共{len(reranked)}个结果")

            return reranked

        except Exception as e:
            logger.error(f"结果重新排序失败: {e}")
            return results

    def _calculate_result_context_score(self, result: SearchResult, search_context: SearchContext) -> float:
        """计算单个结果的上下文相关性分数"""
        try:
            score = 0.0

            # 基于对话上下文评分
            if search_context.conversation_context:
                context = search_context.conversation_context

                # 主题匹配
                result_text = f"{result.title} {result.content}".lower()
                for topic in context.topics:
                    if topic.lower() in result_text:
                        score += 0.2

                # 实体匹配
                for entity in context.entities:
                    if entity.lower() in result_text:
                        score += 0.15

                # 关键词匹配
                for keyword in context.keywords:
                    if keyword.lower() in result_text:
                        score += 0.1

            # 基于用户意图评分
            intent_keywords = {
                UserIntent.NEWS_UPDATE: ["news", "latest", "recent", "today", "update"],
                UserIntent.TUTORIAL: ["tutorial", "guide", "how-to", "step", "learn"],
                UserIntent.COMPARISON: ["vs", "compare", "difference", "better", "review"],
                UserIntent.RESEARCH: ["research", "study", "analysis", "report", "data"],
                UserIntent.TECHNICAL_SUPPORT: ["help", "support", "solution", "fix", "troubleshoot"]
            }

            if search_context.intent in intent_keywords:
                keywords = intent_keywords[search_context.intent]
                result_text = f"{result.title} {result.content}".lower()
                for keyword in keywords:
                    if keyword in result_text:
                        score += 0.1

            # 基于时间相关性（对新闻类查询）
            if search_context.intent == UserIntent.NEWS_UPDATE and result.published_date:
                try:
                    pub_date = datetime.fromisoformat(result.published_date.replace('Z', '+00:00'))
                    days_old = (datetime.now() - pub_date.replace(tzinfo=None)).days
                    if days_old <= 1:
                        score += 0.3
                    elif days_old <= 7:
                        score += 0.2
                    elif days_old <= 30:
                        score += 0.1
                except:
                    pass

            return min(score, 1.0)

        except Exception as e:
            logger.error(f"上下文分数计算失败: {e}")
            return 0.0

    def _calculate_context_relevance(self, results: List[SearchResult], search_context: SearchContext) -> float:
        """计算整体上下文相关性"""
        try:
            if not results:
                return 0.0

            total_score = sum(self._calculate_result_context_score(result, search_context) for result in results)
            return total_score / len(results)

        except Exception as e:
            logger.error(f"上下文相关性计算失败: {e}")
            return 0.0

    def _extract_context_factors(self, search_context: SearchContext) -> Dict[str, float]:
        """提取上下文因子"""
        factors = {}

        # 对话上下文因子
        if search_context.conversation_context:
            context = search_context.conversation_context
            factors["conversation_length"] = len(context.messages) / 10.0  # 归一化
            factors["topic_diversity"] = len(context.topics) / 5.0  # 归一化
            factors["entity_richness"] = len(context.entities) / 10.0  # 归一化

        # 意图置信度
        factors["intent_confidence"] = search_context.confidence_score

        # 用户偏好因子
        if search_context.user_preferences:
            factors["has_preferences"] = 1.0
        else:
            factors["has_preferences"] = 0.0

        return factors

    def _calculate_relevance_scores(self, results: List[SearchResult],
                                  search_context: SearchContext) -> Dict[str, float]:
        """计算相关性分数"""
        scores = {}
        for i, result in enumerate(results):
            scores[f"result_{i}"] = self._calculate_result_context_score(result, search_context)
        return scores

    def _get_active_context_types(self, search_context: SearchContext) -> List[str]:
        """获取活跃的上下文类型"""
        active_types = []

        if search_context.conversation_context:
            active_types.append(ContextType.CONVERSATION.value)

        if search_context.search_history:
            active_types.append(ContextType.SEARCH_HISTORY.value)

        if search_context.user_preferences:
            active_types.append(ContextType.USER_PROFILE.value)

        if search_context.domain_context:
            active_types.append(ContextType.DOMAIN_KNOWLEDGE.value)

        if search_context.temporal_context:
            active_types.append(ContextType.TEMPORAL.value)

        if search_context.location_context:
            active_types.append(ContextType.LOCATION.value)

        return active_types

    def _generate_session_id(self) -> str:
        """生成搜索会话ID"""
        import uuid
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"context_search_{uuid.uuid4().hex[:8]}_{timestamp}"

    async def _save_search_session(self, session_id: str, search_context: SearchContext,
                                 multi_round_result) -> None:
        """保存搜索会话"""
        try:
            session_data = {
                "session_id": session_id,
                "search_context": {
                    "user_id": search_context.user_id,
                    "intent": search_context.intent.value,
                    "confidence_score": search_context.confidence_score,
                    "created_at": search_context.created_at.isoformat()
                },
                "multi_round_result": {
                    "total_rounds": multi_round_result.total_rounds,
                    "overall_quality": multi_round_result.overall_quality,
                    "success": multi_round_result.success
                },
                "created_at": datetime.now().isoformat()
            }

            # 保存到内存缓存
            self.search_sessions[session_id] = session_data

            # 如果有KV存储，也保存到持久化存储
            if self.kv_store:
                await self.kv_store.set(f"search_session:{session_id}", session_data)

            logger.debug(f"搜索会话已保存: {session_id}")

        except Exception as e:
            logger.error(f"搜索会话保存失败: {e}")

    async def get_search_history(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取用户搜索历史"""
        try:
            history = []

            # 从内存缓存获取
            for session_id, session_data in self.search_sessions.items():
                if session_data.get("search_context", {}).get("user_id") == user_id:
                    history.append(session_data)

            # 如果有KV存储，从持久化存储获取更多历史
            if self.kv_store and len(history) < limit:
                # 这里可以实现更复杂的历史查询逻辑
                pass

            # 按时间排序并限制数量
            history.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            return history[:limit]

        except Exception as e:
            logger.error(f"获取搜索历史失败: {e}")
            return []

    async def cleanup_old_sessions(self, max_age_hours: int = 24) -> int:
        """清理旧的搜索会话"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            cleaned_count = 0

            # 清理内存缓存
            sessions_to_remove = []
            for session_id, session_data in self.search_sessions.items():
                created_at = datetime.fromisoformat(session_data.get("created_at", ""))
                if created_at < cutoff_time:
                    sessions_to_remove.append(session_id)

            for session_id in sessions_to_remove:
                del self.search_sessions[session_id]
                cleaned_count += 1

            logger.info(f"清理了{cleaned_count}个旧搜索会话")
            return cleaned_count

        except Exception as e:
            logger.error(f"清理旧会话失败: {e}")
            return 0
