#!/usr/bin/env python3
"""
数据可视化和工作流系统演示脚本

演示ChartGenerator、ReportBuilder和WorkflowEngine的核心功能。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import asyncio
import json
import tempfile
import os
from datetime import datetime

from tools.chart_generator import (
    ChartGenerator, ChartConfig, ChartData, ChartTemplate,
    ChartType, ChartEngine
)
from tools.report_builder import (
    ReportBuilder, ReportTemplate, DataSource, ReportConfig,
    ReportFormat, DataSourceType
)
from tools.workflow_engine import (
    WorkflowEngine, Workflow, Task, TaskType
)


async def demo_chart_generator():
    """演示图表生成器功能"""
    print("\n🎨 === 图表生成器演示 ===")
    
    # 创建图表生成器
    generator = ChartGenerator()
    
    # 准备示例数据
    chart_data = ChartData(
        data_id="demo_data_001",
        chart_id="demo_chart_001",
        x_data=["一月", "二月", "三月", "四月", "五月"],
        y_data=[120, 190, 300, 500, 200],
        labels=["销售额"],
        metadata={"title": "月度销售数据", "unit": "万元"}
    )
    
    # 创建图表配置
    chart_config = ChartConfig(
        chart_id="demo_chart_001",
        title="月度销售趋势图",
        chart_type=ChartType.LINE,
        engine=ChartEngine.MATPLOTLIB,
        width=800,
        height=600,
        theme="default"
    )
    
    try:
        # 生成图表
        print("📊 正在生成折线图...")
        result = await generator.create_chart(chart_config, chart_data)
        print(f"✅ 图表生成成功: {result['chart_id']}")
        print(f"   引擎: {result['engine']}")
        print(f"   格式: {result['format']}")
        
        # 导出图表
        print("💾 正在导出图表...")
        export_result = await generator.export_chart(result, "png")
        print(f"✅ 图表导出成功: {export_result['file_path']}")
        
        # 创建图表模板
        template = ChartTemplate(
            template_id="sales_template",
            name="销售报表模板",
            description="用于销售数据的标准图表模板",
            chart_type=ChartType.BAR,
            default_config={
                "width": 1000,
                "height": 600,
                "theme": "business"
            }
        )
        
        await generator.create_template(template)
        print("✅ 图表模板创建成功")
        
        # 获取可用引擎
        engines = await generator.get_available_engines()
        print(f"🔧 可用引擎: {engines}")
        
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")


async def demo_report_builder():
    """演示报告生成器功能"""
    print("\n📄 === 报告生成器演示 ===")
    
    # 创建报告生成器
    builder = ReportBuilder()
    
    # 创建报告模板
    template = ReportTemplate(
        template_id="monthly_report",
        name="月度报告模板",
        description="标准月度业务报告模板",
        template_content="""
        <html>
        <head><title>{{title}}</title></head>
        <body>
            <h1>{{title}}</h1>
            <p>报告日期: {{date}}</p>
            <h2>业务数据</h2>
            <p>总销售额: {{total_sales}}</p>
            <p>客户数量: {{customer_count}}</p>
            <h2>分析结论</h2>
            <p>{{conclusion}}</p>
        </body>
        </html>
        """,
        format=ReportFormat.HTML,
        variables=["title", "date", "total_sales", "customer_count", "conclusion"]
    )
    
    try:
        # 创建模板
        await builder.create_template(template)
        print("✅ 报告模板创建成功")
        
        # 创建数据源
        sample_data = {
            "total_sales": "1,250万元",
            "customer_count": "2,350",
            "growth_rate": "15.2%"
        }
        
        # 创建临时JSON文件作为数据源
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(sample_data, f, ensure_ascii=False)
            temp_file = f.name
        
        data_source = DataSource(
            source_id="business_data",
            name="业务数据源",
            source_type=DataSourceType.JSON,
            connection_config={"file_path": temp_file}
        )
        
        await builder.add_data_source(data_source)
        print("✅ 数据源添加成功")
        
        # 创建报告配置
        report_config = ReportConfig(
            report_id="monthly_report_202507",
            title="2025年7月月度报告",
            template_id="monthly_report",
            data_sources=["business_data"],
            format=ReportFormat.HTML,
            variables={
                "title": "2025年7月业务月报",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "conclusion": "本月业务表现良好，各项指标稳步增长。"
            }
        )
        
        # 生成报告
        print("📝 正在生成报告...")
        result = await builder.generate_report(report_config)
        print(f"✅ 报告生成成功: {result.file_path}")
        print(f"   格式: {result.format}")
        print(f"   状态: {result.status}")
        
        # 获取可用格式
        formats = await builder.get_available_formats()
        print(f"📋 支持的报告格式: {formats}")
        
        # 清理临时文件
        os.unlink(temp_file)
        if os.path.exists(result.file_path):
            os.unlink(result.file_path)
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")


async def demo_workflow_engine():
    """演示工作流引擎功能"""
    print("\n⚙️ === 工作流引擎演示 ===")
    
    # 创建工作流引擎
    engine = WorkflowEngine()
    
    try:
        # 创建工作流
        workflow = Workflow(
            workflow_id="demo_workflow",
            name="演示工作流",
            description="展示工作流引擎功能的示例工作流",
            start_task_id="task_1"
        )
        
        # 添加任务1：函数调用
        task1 = Task(
            task_id="task_1",
            name="数据准备任务",
            task_type=TaskType.FUNCTION,
            config={
                "function_name": "prepare_data",
                "parameters": {"source": "database"},
                "output_variable": "prepared_data"
            }
        )
        
        # 添加任务2：HTTP请求
        task2 = Task(
            task_id="task_2",
            name="API调用任务",
            task_type=TaskType.HTTP_REQUEST,
            config={
                "url": "https://api.example.com/process",
                "method": "POST",
                "output_variable": "api_result"
            },
            dependencies=["task_1"]
        )
        
        # 添加任务3：延迟任务
        task3 = Task(
            task_id="task_3",
            name="等待任务",
            task_type=TaskType.DELAY,
            config={"delay_seconds": 1},
            dependencies=["task_2"]
        )
        
        # 添加任务4：通知任务
        task4 = Task(
            task_id="task_4",
            name="完成通知",
            task_type=TaskType.NOTIFICATION,
            config={
                "message": "工作流执行完成",
                "recipients": ["<EMAIL>"]
            },
            dependencies=["task_3"]
        )
        
        # 将任务添加到工作流
        workflow.tasks = {
            "task_1": task1,
            "task_2": task2,
            "task_3": task3,
            "task_4": task4
        }
        
        # 创建工作流
        await engine.create_workflow(workflow)
        print("✅ 工作流创建成功")
        
        # 验证工作流
        validation = await engine.validate_workflow(workflow)
        if validation["valid"]:
            print("✅ 工作流验证通过")
        else:
            print(f"❌ 工作流验证失败: {validation['errors']}")
            return
        
        # 执行工作流
        print("🚀 正在执行工作流...")
        execution_id = await engine.execute_workflow(workflow.workflow_id)
        print(f"✅ 工作流开始执行，执行ID: {execution_id}")
        
        # 等待一段时间让工作流执行
        await asyncio.sleep(2)
        
        # 获取执行状态
        status = await engine.get_execution_status(execution_id)
        if status:
            print(f"📊 执行状态: {status['status']}")
            print(f"   已完成任务: {status['completed_tasks']}")
            print(f"   总任务数: {status['total_tasks']}")
        
        # 获取工作流统计
        stats = await engine.get_workflow_statistics(workflow.workflow_id)
        print(f"📈 工作流统计:")
        print(f"   总执行次数: {stats['total_executions']}")
        print(f"   成功率: {stats['success_rate']:.1%}")
        
    except Exception as e:
        print(f"❌ 工作流执行失败: {e}")


async def main():
    """主演示函数"""
    print("🎉 HyAIAgent 数据可视化和工作流系统演示")
    print("=" * 50)
    
    # 演示各个组件
    await demo_chart_generator()
    await demo_report_builder()
    await demo_workflow_engine()
    
    print("\n🎊 演示完成！")
    print("✨ 所有功能模块都已成功演示")


if __name__ == "__main__":
    asyncio.run(main())
