"""
HyAIAgent 第四阶段 - 搜索操作模块测试
测试Tavily API集成和搜索功能
"""

import asyncio
import pytest
import json
import os
import tempfile
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

# 导入被测试的模块
from operations.search_operations import (
    TavilySearchClient, 
    SearchOperations, 
    SearchResult, 
    SearchResponse
)
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager
from core.kv_store import KVStore


class TestTavilySearchClient:
    """Tavily搜索客户端测试"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TavilySearchClient(api_key="test_api_key")
    
    def test_client_initialization(self, client):
        """测试客户端初始化"""
        assert client.api_key == "test_api_key"
        assert client.base_url == "https://api.tavily.com"
        assert client.request_count == 0
        assert client.rate_limit_delay == 1.0
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, client):
        """测试速率限制"""
        import time
        
        # 记录开始时间
        start_time = time.time()
        
        # 连续调用两次_rate_limit
        await client._rate_limit()
        await client._rate_limit()
        
        # 检查是否有延迟
        elapsed_time = time.time() - start_time
        assert elapsed_time >= client.rate_limit_delay
    
    @pytest.mark.asyncio
    async def test_search_success(self, client):
        """测试成功的搜索请求"""
        # 模拟API响应
        mock_response_data = {
            "results": [
                {
                    "title": "测试标题1",
                    "url": "https://example.com/1",
                    "content": "测试内容1",
                    "score": 0.95,
                    "published_date": "2025-01-01"
                },
                {
                    "title": "测试标题2", 
                    "url": "https://example.com/2",
                    "content": "测试内容2",
                    "score": 0.85
                }
            ]
        }
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            # 配置mock响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value=mock_response_data)
            mock_post.return_value.__aenter__.return_value = mock_response
            
            # 执行搜索
            result = await client.search("测试查询")
            
            # 验证结果
            assert isinstance(result, SearchResponse)
            assert result.query == "测试查询"
            assert len(result.results) == 2
            assert result.total_results == 2
            assert result.search_depth == "basic"
            
            # 验证第一个结果
            first_result = result.results[0]
            assert first_result.title == "测试标题1"
            assert first_result.url == "https://example.com/1"
            assert first_result.content == "测试内容1"
            assert first_result.score == 0.95
            assert first_result.published_date == "2025-01-01"
            assert first_result.source == "tavily"
    
    @pytest.mark.asyncio
    async def test_search_api_error(self, client):
        """测试API错误处理"""
        with patch('aiohttp.ClientSession.post') as mock_post:
            # 配置mock错误响应
            mock_response = AsyncMock()
            mock_response.status = 400
            mock_response.text = AsyncMock(return_value="Bad Request")
            mock_post.return_value.__aenter__.return_value = mock_response
            
            # 执行搜索并期望异常
            with pytest.raises(Exception) as exc_info:
                await client.search("测试查询")
            
            assert "Tavily API请求失败: 400" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_search_timeout(self, client):
        """测试搜索超时"""
        with patch('aiohttp.ClientSession.post') as mock_post:
            # 配置mock超时
            mock_post.side_effect = asyncio.TimeoutError()
            
            # 执行搜索并期望超时异常
            with pytest.raises(Exception) as exc_info:
                await client.search("测试查询", timeout=1)
            
            assert "Tavily API请求超时" in str(exc_info.value)


class TestSearchOperations:
    """搜索操作管理器测试"""
    
    @pytest.fixture
    def temp_config_file(self):
        """创建临时配置文件"""
        config_data = {
            "search": {
                "provider": "tavily",
                "tavily": {
                    "api_key": "test_api_key",
                    "base_url": "https://api.tavily.com",
                    "max_results": 5,
                    "search_depth": "basic",
                    "timeout": 30
                },
                "cache": {
                    "enabled": True,
                    "ttl": 3600,
                    "max_size": 1000
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            temp_file = f.name
        
        yield temp_file
        
        # 清理临时文件
        os.unlink(temp_file)
    
    @pytest.fixture
    def config_manager(self, temp_config_file):
        """创建配置管理器"""
        return ConfigManager(config_path=temp_config_file)
    
    @pytest.fixture
    def kv_store(self):
        """创建KV存储"""
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as f:
            temp_file = f.name

        store = KVStore(db_path=temp_file)
        yield store

        # 清理临时文件
        try:
            store.close()  # 确保数据库连接关闭
            os.unlink(temp_file)
        except (PermissionError, FileNotFoundError):
            pass  # 忽略文件删除错误
    
    @pytest.fixture
    def search_ops(self, config_manager, kv_store):
        """创建搜索操作管理器"""
        return SearchOperations(
            config_manager=config_manager,
            kv_store=kv_store
        )
    
    def test_initialization(self, search_ops):
        """测试初始化"""
        assert search_ops.config_manager is not None
        assert search_ops.kv_store is not None
        assert search_ops.tavily_client is not None
        assert "total_searches" in search_ops.search_stats
    
    def test_initialization_without_api_key(self, temp_config_file):
        """测试没有API密钥时的初始化"""
        # 创建没有API密钥的配置
        config_data = {
            "search": {
                "tavily": {
                    "api_key": "${TAVILY_API_KEY}"
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            bad_config_file = f.name
        
        try:
            config_manager = ConfigManager(config_path=bad_config_file)
            
            with pytest.raises(ValueError) as exc_info:
                SearchOperations(config_manager=config_manager)
            
            assert "Tavily API密钥未配置" in str(exc_info.value)
        finally:
            os.unlink(bad_config_file)
    
    def test_cache_key_generation(self, search_ops):
        """测试缓存键生成"""
        key1 = search_ops._generate_cache_key("测试查询", search_depth="basic")
        key2 = search_ops._generate_cache_key("测试查询", search_depth="basic")
        key3 = search_ops._generate_cache_key("测试查询", search_depth="advanced")
        
        # 相同参数应该生成相同的键
        assert key1 == key2
        # 不同参数应该生成不同的键
        assert key1 != key3
        # 键应该以前缀开始
        assert key1.startswith("search_cache_")
    
    @pytest.mark.asyncio
    async def test_cache_operations(self, search_ops):
        """测试缓存操作"""
        # 创建测试搜索响应
        test_results = [
            SearchResult(
                title="测试标题",
                url="https://example.com",
                content="测试内容",
                score=0.9
            )
        ]
        
        test_response = SearchResponse(
            query="测试查询",
            results=test_results,
            total_results=1,
            search_time=1.5,
            timestamp=datetime.now(),
            search_depth="basic"
        )
        
        # 测试缓存存储
        cache_key = search_ops._generate_cache_key("测试查询")
        await search_ops._cache_result(cache_key, test_response)
        
        # 测试缓存获取
        cached_result = await search_ops._get_cached_result(cache_key)
        assert cached_result is not None
        assert cached_result.query == "测试查询"
        assert len(cached_result.results) == 1
        assert cached_result.results[0].title == "测试标题"
    
    @pytest.mark.asyncio
    async def test_cache_expiration(self, search_ops):
        """测试缓存过期"""
        # 创建过期的缓存数据
        expired_time = datetime.now() - timedelta(hours=2)
        test_results = [SearchResult(title="测试", url="https://example.com", content="内容", score=0.9)]
        
        expired_response = SearchResponse(
            query="测试查询",
            results=test_results,
            total_results=1,
            search_time=1.0,
            timestamp=expired_time,
            search_depth="basic"
        )
        
        # 手动设置过期缓存
        cache_key = search_ops._generate_cache_key("测试查询")
        search_ops.kv_store.set(cache_key, expired_response.to_dict())
        
        # 尝试获取缓存，应该返回None（已过期）
        cached_result = await search_ops._get_cached_result(cache_key)
        assert cached_result is None
    
    @pytest.mark.asyncio
    async def test_search_web_with_mock(self, search_ops):
        """测试网络搜索（使用mock）"""
        # 创建mock搜索响应
        mock_results = [
            SearchResult(
                title="Mock标题",
                url="https://mock.com",
                content="Mock内容",
                score=0.95
            )
        ]
        
        mock_response = SearchResponse(
            query="mock查询",
            results=mock_results,
            total_results=1,
            search_time=0.5,
            timestamp=datetime.now(),
            search_depth="basic"
        )
        
        # Mock Tavily客户端的搜索方法
        with patch.object(search_ops.tavily_client, 'search', return_value=mock_response):
            result = await search_ops.search_web("mock查询")
            
            assert isinstance(result, SearchResponse)
            assert result.query == "mock查询"
            assert len(result.results) == 1
            assert result.results[0].title == "Mock标题"
            
            # 检查统计信息
            stats = await search_ops.get_search_stats()
            assert stats["total_searches"] == 1
            assert stats["successful_searches"] == 1
            assert stats["failed_searches"] == 0
    
    @pytest.mark.asyncio
    async def test_search_web_with_cache(self, search_ops):
        """测试带缓存的网络搜索"""
        mock_results = [SearchResult(title="缓存测试", url="https://cache.com", content="缓存内容", score=0.9)]
        mock_response = SearchResponse(
            query="缓存查询",
            results=mock_results,
            total_results=1,
            search_time=0.3,
            timestamp=datetime.now(),
            search_depth="basic"
        )
        
        with patch.object(search_ops.tavily_client, 'search', return_value=mock_response) as mock_search:
            # 第一次搜索
            result1 = await search_ops.search_web("缓存查询")
            assert mock_search.call_count == 1
            
            # 第二次搜索（应该使用缓存）
            result2 = await search_ops.search_web("缓存查询")
            assert mock_search.call_count == 1  # 没有增加，说明使用了缓存
            
            # 验证结果一致
            assert result1.query == result2.query
            assert len(result1.results) == len(result2.results)
            
            # 检查缓存命中统计
            stats = await search_ops.get_search_stats()
            assert stats["cache_hits"] == 1
    
    @pytest.mark.asyncio
    async def test_search_stats(self, search_ops):
        """测试搜索统计"""
        # 初始统计
        stats = await search_ops.get_search_stats()
        assert stats["total_searches"] == 0
        assert stats["success_rate"] == 0.0
        assert stats["cache_hit_rate"] == 0.0
        assert stats["average_search_time"] == 0.0
        
        # 模拟一些搜索操作
        search_ops.search_stats["total_searches"] = 10
        search_ops.search_stats["successful_searches"] = 8
        search_ops.search_stats["failed_searches"] = 2
        search_ops.search_stats["cache_hits"] = 3
        search_ops.search_stats["total_search_time"] = 16.0
        
        stats = await search_ops.get_search_stats()
        assert stats["total_searches"] == 10
        assert stats["success_rate"] == 0.8
        assert stats["cache_hit_rate"] == 0.3
        assert stats["average_search_time"] == 2.0
    
    @pytest.mark.asyncio
    async def test_close(self, search_ops):
        """测试关闭操作"""
        # 确保可以正常关闭
        await search_ops.close()
        
        # 验证Tavily客户端会话已关闭
        assert search_ops.tavily_client.session is None


# 运行测试的主函数
if __name__ == "__main__":
    # 设置环境变量（如果需要）
    os.environ["TAVILY_API_KEY"] = "test_api_key"
    
    # 运行测试
    pytest.main([__file__, "-v", "--tb=short"])
