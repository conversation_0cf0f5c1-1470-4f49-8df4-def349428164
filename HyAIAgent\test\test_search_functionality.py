"""
HyAIAgent 第四阶段 - 搜索功能集成测试
步骤4.5: 搜索功能测试
"""

import asyncio
import pytest
import json
import os
import sys
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager
from core.kv_store import KVStore
from operations.search_operations import (
    SearchOperations, BasicSearchOperation, SearchToolkit,
    SearchResult, SearchResponse, TavilySearchClient
)


class TestSearchFunctionality:
    """搜索功能集成测试类"""
    
    @pytest.fixture
    async def setup_real_environment(self):
        """设置真实环境测试（使用模拟API）"""
        # 创建真实配置
        test_config = {
            "search": {
                "tavily": {
                    "api_key": "test_api_key_12345",
                    "base_url": "https://api.tavily.com",
                    "timeout": 30,
                    "max_results": 10
                },
                "cache": {
                    "enabled": True,
                    "ttl": 3600,
                    "max_size": 1000
                }
            }
        }
        
        # 创建配置管理器
        config_manager = Mock(spec=ConfigManager)
        config_manager.get.side_effect = lambda key, default=None: test_config.get(key, default)
        
        # 创建安全管理器
        security_manager = Mock(spec=SecurityManager)
        
        # 创建KV存储
        kv_store = Mock(spec=KVStore)
        kv_store.get.return_value = None
        kv_store.set.return_value = True
        
        return {
            "config_manager": config_manager,
            "security_manager": security_manager,
            "kv_store": kv_store,
            "test_config": test_config
        }
    
    @pytest.mark.asyncio
    async def test_complete_search_workflow(self, setup_real_environment):
        """测试完整的搜索工作流程"""
        env = await setup_real_environment
        
        # 模拟API响应
        mock_api_response = {
            "results": [
                {
                    "title": "Python编程入门教程",
                    "url": "https://example.com/python-tutorial",
                    "content": "Python是一种高级编程语言，易于学习和使用...",
                    "score": 0.95,
                    "published_date": "2025-07-29"
                },
                {
                    "title": "Python最佳实践指南",
                    "url": "https://example.com/python-best-practices",
                    "content": "本文介绍Python编程的最佳实践和代码规范...",
                    "score": 0.88,
                    "published_date": "2025-07-28"
                }
            ]
        }
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            # 配置模拟响应
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = mock_api_response
            mock_post.return_value.__aenter__.return_value = mock_response
            
            # 创建搜索工具包
            toolkit = SearchToolkit(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            
            # 执行搜索
            result = await toolkit.quick_search("Python编程教程", max_results=5)
            
            # 验证结果
            assert result["success"] is True
            assert result["data"] is not None
            assert result["data"]["total_results"] == 2
            assert result["data"]["query"] == "Python编程教程"
            assert len(result["data"]["search_response"]["results"]) == 2
            
            # 验证搜索结果内容
            search_results = result["data"]["search_response"]["results"]
            assert search_results[0]["title"] == "Python编程入门教程"
            assert search_results[0]["score"] == 0.95
            assert search_results[1]["title"] == "Python最佳实践指南"
            assert search_results[1]["score"] == 0.88
            
            await toolkit.close()
    
    @pytest.mark.asyncio
    async def test_advanced_search_with_domain_filtering(self, setup_real_environment):
        """测试带域名过滤的高级搜索"""
        env = await setup_real_environment
        
        mock_api_response = {
            "results": [
                {
                    "title": "GitHub上的Python项目",
                    "url": "https://github.com/python/cpython",
                    "content": "CPython是Python的官方实现...",
                    "score": 0.92,
                    "published_date": "2025-07-29"
                }
            ]
        }
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = mock_api_response
            mock_post.return_value.__aenter__.return_value = mock_response
            
            toolkit = SearchToolkit(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            
            # 执行高级搜索，只搜索GitHub域名
            result = await toolkit.advanced_search(
                "Python项目",
                max_results=10,
                include_domains=["github.com"]
            )
            
            assert result["success"] is True
            assert result["data"]["total_results"] == 1
            assert "github.com" in result["data"]["search_response"]["results"][0]["url"]
            
            await toolkit.close()
    
    @pytest.mark.asyncio
    async def test_batch_search_functionality(self, setup_real_environment):
        """测试批量搜索功能"""
        env = await setup_real_environment
        
        # 为不同查询准备不同的响应
        def mock_response_generator(query):
            if "Python" in query:
                return {
                    "results": [{
                        "title": f"{query} - Python相关结果",
                        "url": "https://python.org",
                        "content": f"关于{query}的Python内容",
                        "score": 0.9
                    }]
                }
            elif "JavaScript" in query:
                return {
                    "results": [{
                        "title": f"{query} - JavaScript相关结果",
                        "url": "https://javascript.info",
                        "content": f"关于{query}的JavaScript内容",
                        "score": 0.85
                    }]
                }
            else:
                return {"results": []}
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            async def mock_post_side_effect(*args, **kwargs):
                # 从请求中提取查询
                json_data = kwargs.get('json', {})
                query = json_data.get('query', '')
                
                mock_response = AsyncMock()
                mock_response.status = 200
                mock_response.json.return_value = mock_response_generator(query)
                return mock_response
            
            mock_post.return_value.__aenter__ = mock_post_side_effect
            
            toolkit = SearchToolkit(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            
            # 执行批量搜索
            queries = ["Python编程", "JavaScript开发", "Java教程"]
            result = await toolkit.batch_search(queries, max_results_per_query=3)
            
            assert result["success"] is True
            assert result["total_queries"] == 3
            assert result["successful_count"] >= 2  # 至少Python和JavaScript应该成功
            assert len(result["results"]) >= 2
            
            await toolkit.close()
    
    @pytest.mark.asyncio
    async def test_search_caching_mechanism(self, setup_real_environment):
        """测试搜索缓存机制"""
        env = await setup_real_environment
        
        # 设置缓存返回值
        cached_data = {
            "query": "Python缓存测试",
            "results": [{
                "title": "缓存的Python结果",
                "url": "https://cached.example.com",
                "content": "这是来自缓存的结果",
                "score": 0.9,
                "published_date": "2025-07-29",
                "source": "tavily"
            }],
            "total_results": 1,
            "search_time": 0.1,
            "timestamp": datetime.now().isoformat(),
            "search_depth": "basic"
        }
        
        # 模拟缓存命中
        env["kv_store"].get.return_value = cached_data
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            toolkit = SearchToolkit(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            
            # 第一次搜索（应该使用缓存）
            result = await toolkit.quick_search("Python缓存测试")
            
            assert result["success"] is True
            assert result["data"]["search_response"]["results"][0]["title"] == "缓存的Python结果"
            
            # 验证没有调用API（因为使用了缓存）
            mock_post.assert_not_called()
            
            await toolkit.close()
    
    @pytest.mark.asyncio
    async def test_search_error_handling(self, setup_real_environment):
        """测试搜索错误处理"""
        env = await setup_real_environment
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            # 模拟API错误
            mock_response = AsyncMock()
            mock_response.status = 429  # Rate limit exceeded
            mock_response.text.return_value = "Rate limit exceeded"
            mock_post.return_value.__aenter__.return_value = mock_response
            
            toolkit = SearchToolkit(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )
            
            # 执行搜索（应该失败）
            result = await toolkit.quick_search("测试错误处理")
            
            assert result["success"] is False
            assert "Tavily API请求失败" in result["error"]
            
            await toolkit.close()
    
    @pytest.mark.asyncio
    async def test_search_parameter_validation(self, setup_real_environment):
        """测试搜索参数验证"""
        env = await setup_real_environment
        
        with patch('operations.search_operations.TavilySearchClient') as mock_client_class:
            # 创建模拟的客户端实例
            mock_client = AsyncMock()
            mock_client.close = AsyncMock()
            mock_client_class.return_value = mock_client

            toolkit = SearchToolkit(
                env["config_manager"],
                env["security_manager"],
                env["kv_store"]
            )

            # 测试无效参数（这个不需要实际执行搜索）
            invalid_params = {
                "query": "",  # 空查询
                "max_results": 25  # 超出范围
            }

            result = await toolkit.search_with_validation(invalid_params)
            assert result["success"] is False
            assert "参数验证失败" in result["error"]
            assert "validation" in result
            assert result["validation"]["valid"] is False

            await toolkit.close()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
