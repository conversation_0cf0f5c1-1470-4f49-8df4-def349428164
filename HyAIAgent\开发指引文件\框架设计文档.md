# 🤖 HyAIAgent 框架设计文档

## 📖 项目概述

HyAIAgent是一个自主思考、自主决策的AI助手框架，能够根据用户输入自动拆分任务、调用AI API、执行本地操作，并持续迭代优化直到完成目标。

### 🎯 V1版本核心特性
- **自主任务分解** - AI自动将复杂任务拆分为可执行的子任务
- **OpenAI兼容API支持** - 支持OpenAI API格式的多种AI服务商
- **智能提示词系统** - 全局执行提示词 + 任务专用提示词文件
- **基础操作能力** - 支持文件操作、数据库操作、网络搜索(Tavily)
- **双重存储架构** - SQLite关系数据库 + 轻量级KV数据库
- **简洁架构** - 专注核心功能，避免过度设计

### 🚫 V1版本不包含的功能
- Office文档处理（预留扩展接口）
- 自动化脚本执行
- 插件系统和生态建设
- Web界面和移动适配
- 主题系统和国际化
- 自适应学习和知识图谱
- 智能调度（由AI端点决定）

## 🏗️ 系统架构

### V1版本精简架构
```
┌─────────────────────────────────────────────────────────────┐
│                 HyAIAgent V1 Framework                      │
├─────────────────────────────────────────────────────────────┤
│  🧠 AI Client Manager (AI客户端管理)                        │
│  ├── OpenAI Compatible API (OpenAI兼容API)                  │
│  ├── Multi-Provider Support (多服务商支持)                  │
│  └── Request Router (请求路由器)                            │
├─────────────────────────────────────────────────────────────┤
│  📝 Prompt System (提示词系统)                              │
│  ├── Global Execution Prompt (全局执行提示词)               │
│  ├── Task-Specific Prompts (任务专用提示词)                 │
│  ├── Prompt Template Engine (提示词模板引擎)                │
│  └── Dynamic Prompt Builder (动态提示词构建器)              │
├─────────────────────────────────────────────────────────────┤
│  📋 Task Management (任务管理)                              │
│  ├── Task Queue (任务队列)                                  │
│  ├── Task Executor (任务执行器)                             │
│  ├── Task Orchestrator (任务编排器)                         │
│  └── Progress Monitor (进度监控)                            │
├─────────────────────────────────────────────────────────────┤
│  🔧 Core Operations (核心操作)                              │
│  ├── File Operations (文件操作)                             │
│  ├── Database Operations (数据库操作)                       │
│  ├── Tavily Search (Tavily网络搜索)                         │
│  └── KV Operations (KV数据库操作)                           │
├─────────────────────────────────────────────────────────────┤
│  💾 Dual Storage (双重存储)                                 │
│  ├── SQLite Database (SQLite关系数据库)                     │
│  │   ├── Task Records (任务记录)                            │
│  │   ├── Conversation History (对话历史)                    │
│  │   └── System Logs (系统日志)                             │
│  └── KV Database (轻量级KV数据库)                           │
│      ├── Global Variables (全局变量)                        │
│      ├── Session Data (会话数据)                            │
│      └── Cache Data (缓存数据)                              │
├─────────────────────────────────────────────────────────────┤
│  🌐 Communication (通信层)                                  │
│  ├── JSON Protocol (JSON协议)                               │
│  ├── Response Parser (响应解析)                             │
│  └── Error Handler (错误处理)                               │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 工作流程

### 基础工作流程
```mermaid
graph TD
    A[用户输入] --> B[AI决策引擎]
    B --> C{任务类型判断}
    C -->|简单对话| D[直接AI回复]
    C -->|文件操作| E[文件操作模块]
    C -->|数据查询| F[数据库操作模块]
    C -->|网络搜索| G[搜索操作模块]
    C -->|复杂任务| H[任务分解]
    
    E --> I[执行结果]
    F --> I
    G --> I
    H --> J[任务队列]
    J --> K[任务执行器]
    K --> I
    
    I --> L[结果整合]
    L --> M[AI总结回复]
    M --> N[返回用户]
    
    D --> N
```

## 📡 通信协议

### AI交互JSON协议
```json
{
  "request": {
    "type": "task_analysis",
    "user_input": "用户的原始输入",
    "context": {
      "conversation_history": [],
      "available_operations": ["file", "database", "search"],
      "current_workspace": "/path/to/workspace"
    }
  },
  "response": {
    "analysis": {
      "task_type": "complex_task",
      "confidence": 0.9,
      "reasoning": "任务分析的推理过程"
    },
    "execution_plan": {
      "tasks": [
        {
          "id": "task_001",
          "type": "file_operation",
          "operation": "read_file",
          "parameters": {
            "file_path": "example.txt"
          },
          "priority": 1,
          "dependencies": []
        }
      ]
    },
    "expected_outcome": "预期的执行结果描述"
  }
}
```

### 操作执行协议
```json
{
  "operation": {
    "type": "file_operation",
    "action": "read_file",
    "parameters": {
      "file_path": "example.txt",
      "encoding": "utf-8"
    }
  },
  "result": {
    "status": "success",
    "data": {
      "content": "文件内容",
      "file_size": 1024,
      "last_modified": "2024-01-01T12:00:00Z"
    },
    "execution_time": 0.05
  }
}
```

## 🔧 核心模块设计

### 1. AI客户端管理器
```python
class AIClientManager:
    """多AI模型客户端管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.clients = {}
        self.default_provider = AIProvider.OPENAI
    
    async def chat_completion(self, messages: list, provider: Optional[AIProvider] = None) -> str:
        """统一的聊天完成接口"""
        pass
    
    def get_available_providers(self) -> list:
        """获取可用的AI提供商"""
        pass
```

### 2. 任务管理系统
```python
class TaskManager:
    """任务管理器"""
    
    def __init__(self, database: HyAIDatabase):
        self.database = database
        self.task_queue = asyncio.Queue()
        self.running_tasks = {}
    
    async def create_task(self, task_data: Dict[str, Any]) -> str:
        """创建新任务"""
        pass
    
    async def execute_task(self, task_id: str) -> Dict[str, Any]:
        """执行任务"""
        pass
```

### 3. 操作模块基类
```python
class BaseOperation:
    """操作模块基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    async def execute(self, operation: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行操作"""
        raise NotImplementedError
    
    def get_supported_operations(self) -> List[str]:
        """获取支持的操作列表"""
        raise NotImplementedError
```

## 💾 数据存储设计

### SQLite数据库表结构
```sql
-- 任务表
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT UNIQUE NOT NULL,
    type TEXT NOT NULL,
    description TEXT,
    parameters TEXT, -- JSON格式
    status TEXT DEFAULT 'pending',
    priority INTEGER DEFAULT 5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    result TEXT -- JSON格式
);

-- 对话历史表
CREATE TABLE conversations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    role TEXT NOT NULL, -- user/assistant
    content TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ai_provider TEXT,
    tokens_used INTEGER
);

-- AI缓存表
CREATE TABLE ai_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cache_key TEXT UNIQUE NOT NULL,
    content TEXT NOT NULL,
    tags TEXT, -- JSON数组
    importance REAL DEFAULT 0.5,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    use_count INTEGER DEFAULT 0
);
```

## 📝 智能提示词系统

### 提示词文件结构
```
prompts/
├── global/
│   ├── execution_orchestrator.md      # 全局执行编排提示词
│   ├── task_analyzer.md              # 任务分析提示词
│   └── response_formatter.md         # 响应格式化提示词
├── tasks/
│   ├── file_operations.md            # 文件操作任务提示词
│   ├── data_analysis.md              # 数据分析任务提示词
│   ├── web_search.md                 # 网络搜索任务提示词
│   ├── code_generation.md            # 代码生成任务提示词
│   └── content_creation.md           # 内容创建任务提示词
└── templates/
    ├── task_template.md              # 任务提示词模板
    └── response_template.json        # 响应格式模板
```

### 全局执行编排提示词 (global/execution_orchestrator.md)
```markdown
# HyAIAgent 全局执行编排器

你是HyAIAgent框架的核心智能编排器，负责：

## 🎯 核心职责
1. **任务分析** - 深度理解用户需求，识别任务类型和复杂度
2. **任务分解** - 将复杂任务拆分为可执行的子任务序列
3. **执行编排** - 确定任务执行顺序、依赖关系和并发策略
4. **循环控制** - 管理任务执行循环，处理反馈和迭代优化
5. **提示词路由** - 根据任务类型选择对应的专用提示词文件

## 🔧 可用操作模块
- **file_operations**: 文件读写、删除、列表、搜索等操作
- **kv_operations**: 轻量级KV数据库存储和查询
- **database_operations**: SQLite数据库查询、插入、更新等操作
- **search_operations**: Tavily网络搜索和信息获取
- **code_operations**: 代码分析、生成、执行等操作

## 🗂️ 任务类型映射
根据用户输入的任务类型，选择对应的专用提示词：
- 文件相关操作 → `tasks/file_operations.md`
- 数据分析处理 → `tasks/data_analysis.md`
- 网络信息搜索 → `tasks/web_search.md`
- 代码编写调试 → `tasks/code_generation.md`
- 内容创作编辑 → `tasks/content_creation.md`

## 📊 执行流程
1. **接收输入** - 获取用户输入和上下文信息
2. **任务识别** - 分析任务类型，评估复杂度
3. **提示词选择** - 根据任务类型选择专用提示词文件
4. **执行计划** - 制定详细的执行步骤和参数
5. **循环执行** - 执行任务，收集结果，判断是否需要继续
6. **结果整合** - 汇总所有执行结果，生成最终回复

## 🔄 循环控制逻辑
- **继续条件**: 任务未完成、有新信息需要处理、用户有后续要求
- **终止条件**: 任务完成、达到最大循环次数、出现不可恢复错误
- **反馈机制**: 每次循环后评估结果质量，调整后续策略

## 📋 响应格式
始终返回标准JSON格式：
{
  "task_analysis": {
    "type": "任务类型",
    "complexity": "简单/中等/复杂",
    "estimated_steps": 数字
  },
  "execution_plan": {
    "steps": [
      {
        "step_id": "步骤ID",
        "action": "操作类型",
        "prompt_file": "使用的提示词文件",
        "parameters": {},
        "expected_output": "预期输出描述"
      }
    ]
  },
  "continue_execution": true/false,
  "next_action": "下一步行动描述"
}
```

### 任务专用提示词示例 (tasks/file_operations.md)
```markdown
# 文件操作任务专用提示词

你是文件操作专家，专门处理各种文件相关的任务。

## 🎯 专业领域
- 文件读写操作
- 目录管理
- 文件搜索和过滤
- 批量文件处理
- 文件格式转换

## 📊 输入信息分析
分析以下信息来制定执行计划：
- **用户输入**: {user_input}
- **当前工作目录**: {workspace}
- **全局变量**: {global_vars}
- **上阶段结果**: {previous_results}

## 🔧 可用操作
- `read_file(path)` - 读取文件内容
- `write_file(path, content)` - 写入文件
- `delete_file(path)` - 删除文件
- `list_files(directory)` - 列出目录文件
- `search_files(pattern)` - 搜索文件
- `kv_set(key, value)` - 存储KV数据
- `kv_get(key)` - 获取KV数据

## 🛡️ 安全检查
- 确保文件路径在允许的工作目录内
- 检查文件扩展名是否在白名单中
- 验证文件大小不超过限制
- 避免覆盖重要系统文件

## 📋 执行步骤模板
1. **参数验证** - 检查输入参数的有效性
2. **安全检查** - 验证操作的安全性
3. **执行操作** - 执行具体的文件操作
4. **结果验证** - 确认操作是否成功
5. **状态更新** - 更新全局状态和KV存储

## 📊 响应格式
{
  "operation_type": "文件操作类型",
  "safety_check": "安全检查结果",
  "execution_steps": [
    {
      "action": "具体操作",
      "parameters": {},
      "result": "执行结果"
    }
  ],
  "global_updates": {
    "kv_updates": {},
    "status": "操作状态"
  },
  "next_recommendations": "后续建议操作"
}
```

## 🛡️ 安全机制

### 文件操作安全
- 限制操作范围在指定工作目录内
- 检查文件扩展名白名单
- 限制文件大小和操作频率
- 记录所有文件操作日志

### API调用安全
- 实施速率限制
- 验证API响应格式
- 处理API错误和超时
- 保护API密钥安全

### 数据安全
- 敏感数据加密存储
- 定期清理过期数据
- 备份重要数据
- 访问权限控制

## 📈 性能优化

### 缓存策略
- AI响应结果缓存
- 文件内容缓存
- 搜索结果缓存
- 智能缓存清理

### 并发处理
- 异步任务执行
- 连接池管理
- 资源限制控制
- 负载均衡

## 🔮 扩展性设计

### 预留扩展接口
```python
class ExtensionInterface:
    """扩展接口"""
    
    def get_name(self) -> str:
        """获取扩展名称"""
        pass
    
    def get_operations(self) -> List[str]:
        """获取支持的操作"""
        pass
    
    async def execute_operation(self, operation: str, parameters: Dict) -> Dict:
        """执行操作"""
        pass
```

### 未来扩展方向
- Office文档处理模块
- 邮件集成模块
- 日历管理模块
- 自动化脚本执行
- 机器学习模型集成

---

**🎯 设计目标**: 构建一个简洁、高效、可扩展的AI助手框架，为用户提供智能化的自动化解决方案。
