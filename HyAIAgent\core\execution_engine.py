"""
执行引擎模块

本模块实现了任务执行引擎，负责单任务执行、错误处理和结果评估。
"""

import asyncio
import logging
import traceback
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
import time

from .task_models import Task, ExecutionResult, TaskStatus, TaskType
from .ai_client import SimpleAIClient
from .prompt_manager import PromptManager

logger = logging.getLogger(__name__)


class ExecutionEngine:
    """任务执行引擎
    
    负责单任务执行、错误处理、结果评估和重试机制。
    支持不同类型任务的专门处理逻辑。
    """
    
    def __init__(self, ai_client: SimpleAIClient, prompt_manager: PromptManager):
        """初始化执行引擎
        
        Args:
            ai_client: AI客户端实例
            prompt_manager: 提示词管理器实例
        """
        self.ai_client = ai_client
        self.prompt_manager = prompt_manager
        
        # 任务执行器注册表
        self.task_executors: Dict[TaskType, Callable] = {
            TaskType.ANALYSIS: self._execute_analysis_task,
            TaskType.PLANNING: self._execute_planning_task,
            TaskType.EXECUTION: self._execute_execution_task,
            TaskType.COMMUNICATION: self._execute_communication_task,
            TaskType.RESEARCH: self._execute_research_task,
            TaskType.GENERAL: self._execute_general_task
        }
        
        # 执行统计
        self.execution_stats = {
            'total_executed': 0,
            'successful': 0,
            'failed': 0,
            'retried': 0
        }
        
        logger.info("ExecutionEngine initialized successfully")
    
    async def execute_task(self, task: Task) -> ExecutionResult:
        """执行单个任务
        
        Args:
            task: 要执行的任务
            
        Returns:
            ExecutionResult: 执行结果
        """
        start_time = time.time()
        result = ExecutionResult(
            task_id=task.id,
            started_at=datetime.now()
        )
        
        try:
            logger.info(f"开始执行任务: {task.id} - {task.title}")
            
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            
            # 获取对应的执行器
            executor = self.task_executors.get(task.task_type, self._execute_general_task)
            
            # 执行任务
            execution_data = await executor(task)
            
            # 设置执行结果
            result.status = TaskStatus.COMPLETED
            result.success = True
            result.result_data = execution_data
            result.execution_time = time.time() - start_time
            
            # 更新任务状态
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.output_data = execution_data
            task.progress = 100.0
            
            # 更新统计
            self.execution_stats['total_executed'] += 1
            self.execution_stats['successful'] += 1
            
            logger.info(f"任务执行成功: {task.id}")
            
        except Exception as e:
            logger.error(f"任务执行失败: {task.id} - {str(e)}")
            
            # 设置错误结果
            result.status = TaskStatus.FAILED
            result.success = False
            result.error_message = str(e)
            result.stack_trace = traceback.format_exc()
            result.execution_time = time.time() - start_time
            
            # 更新任务状态
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
            
            # 更新统计
            self.execution_stats['total_executed'] += 1
            self.execution_stats['failed'] += 1
            
            # 尝试错误处理
            await self.handle_task_failure(task, e)
        
        finally:
            result.completed_at = datetime.now()
        
        return result
    
    async def handle_task_failure(self, task: Task, error: Exception) -> bool:
        """处理任务执行失败
        
        Args:
            task: 失败的任务
            error: 错误信息
            
        Returns:
            bool: 是否应该重试
        """
        try:
            logger.info(f"处理任务失败: {task.id} - {str(error)}")
            
            # 增加重试计数
            task.retry_count += 1
            
            # 判断是否应该重试
            should_retry = (
                task.retry_count < task.max_retries and
                self._is_retryable_error(error)
            )
            
            if should_retry:
                logger.info(f"任务将重试: {task.id} (第{task.retry_count}次重试)")
                task.status = TaskStatus.PENDING
                task.error_message = None
                self.execution_stats['retried'] += 1
                return True
            else:
                logger.warning(f"任务重试次数已达上限或错误不可重试: {task.id}")
                return False
                
        except Exception as e:
            logger.error(f"处理任务失败时出错: {str(e)}")
            return False
    
    async def evaluate_result(self, result: ExecutionResult) -> Dict[str, Any]:
        """评估执行结果
        
        Args:
            result: 执行结果
            
        Returns:
            Dict[str, Any]: 评估报告
        """
        try:
            evaluation = {
                'task_id': result.task_id,
                'success': result.success,
                'execution_time': result.execution_time,
                'quality_score': 0.0,
                'completeness_score': 0.0,
                'efficiency_score': 0.0,
                'overall_score': 0.0,
                'recommendations': []
            }
            
            if result.success:
                # 评估执行质量
                evaluation['quality_score'] = await self._evaluate_quality(result)
                evaluation['completeness_score'] = await self._evaluate_completeness(result)
                evaluation['efficiency_score'] = await self._evaluate_efficiency(result)
                
                # 计算总体评分
                evaluation['overall_score'] = (
                    evaluation['quality_score'] * 0.4 +
                    evaluation['completeness_score'] * 0.4 +
                    evaluation['efficiency_score'] * 0.2
                )
                
                # 生成建议
                evaluation['recommendations'] = await self._generate_recommendations(result, evaluation)
            
            logger.info(f"结果评估完成: {result.task_id} - 总分: {evaluation['overall_score']:.2f}")
            return evaluation
            
        except Exception as e:
            logger.error(f"评估执行结果失败: {str(e)}")
            return {
                'task_id': result.task_id,
                'success': False,
                'error': str(e)
            }
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        stats = self.execution_stats.copy()
        if stats['total_executed'] > 0:
            stats['success_rate'] = stats['successful'] / stats['total_executed']
            stats['failure_rate'] = stats['failed'] / stats['total_executed']
            stats['retry_rate'] = stats['retried'] / stats['total_executed']
        else:
            stats['success_rate'] = 0.0
            stats['failure_rate'] = 0.0
            stats['retry_rate'] = 0.0
        
        return stats
    
    # 任务类型专门执行器
    
    async def _execute_analysis_task(self, task: Task) -> Dict[str, Any]:
        """执行分析任务"""
        prompt = self.prompt_manager.get_template_prompt("analysis_task", {
            'task_description': task.description,
            'context': task.context
        })
        
        if not prompt:
            prompt = f"请分析以下内容：\n{task.description}\n\n上下文：{task.context}"
        
        response = await asyncio.to_thread(self.ai_client.chat, task.description, prompt)
        
        return {
            'analysis_result': response,
            'task_type': 'analysis',
            'timestamp': datetime.now().isoformat()
        }
    
    async def _execute_planning_task(self, task: Task) -> Dict[str, Any]:
        """执行规划任务"""
        prompt = self.prompt_manager.get_template_prompt("planning_task", {
            'task_description': task.description,
            'context': task.context
        })
        
        if not prompt:
            prompt = f"请制定以下计划：\n{task.description}\n\n上下文：{task.context}"
        
        response = await asyncio.to_thread(self.ai_client.chat, task.description, prompt)
        
        return {
            'planning_result': response,
            'task_type': 'planning',
            'timestamp': datetime.now().isoformat()
        }
    
    async def _execute_execution_task(self, task: Task) -> Dict[str, Any]:
        """执行执行任务"""
        # 这里可以集成具体的操作模块
        return {
            'execution_result': f"执行任务: {task.description}",
            'task_type': 'execution',
            'timestamp': datetime.now().isoformat(),
            'status': 'completed'
        }
    
    async def _execute_communication_task(self, task: Task) -> Dict[str, Any]:
        """执行沟通任务"""
        response = await asyncio.to_thread(self.ai_client.chat, task.description)
        
        return {
            'communication_result': response,
            'task_type': 'communication',
            'timestamp': datetime.now().isoformat()
        }
    
    async def _execute_research_task(self, task: Task) -> Dict[str, Any]:
        """执行研究任务"""
        prompt = f"请研究以下主题：\n{task.description}\n\n请提供详细的研究结果。"
        response = await asyncio.to_thread(self.ai_client.chat, task.description, prompt)
        
        return {
            'research_result': response,
            'task_type': 'research',
            'timestamp': datetime.now().isoformat()
        }
    
    async def _execute_general_task(self, task: Task) -> Dict[str, Any]:
        """执行通用任务"""
        response = await asyncio.to_thread(self.ai_client.chat, task.description)
        
        return {
            'general_result': response,
            'task_type': 'general',
            'timestamp': datetime.now().isoformat()
        }
    
    # 辅助方法
    
    def _is_retryable_error(self, error: Exception) -> bool:
        """判断错误是否可重试"""
        retryable_errors = [
            'timeout',
            'connection',
            'network',
            'temporary',
            'rate limit'
        ]
        
        error_str = str(error).lower()
        return any(keyword in error_str for keyword in retryable_errors)
    
    async def _evaluate_quality(self, result: ExecutionResult) -> float:
        """评估结果质量"""
        # 简单的质量评估逻辑
        if result.success and result.result_data:
            return 0.8  # 基础质量分
        return 0.0
    
    async def _evaluate_completeness(self, result: ExecutionResult) -> float:
        """评估结果完整性"""
        # 简单的完整性评估逻辑
        if result.success and result.result_data:
            return 0.9  # 基础完整性分
        return 0.0
    
    async def _evaluate_efficiency(self, result: ExecutionResult) -> float:
        """评估执行效率"""
        # 基于执行时间的效率评估
        if result.execution_time:
            if result.execution_time < 5:  # 5秒内完成
                return 1.0
            elif result.execution_time < 30:  # 30秒内完成
                return 0.8
            elif result.execution_time < 60:  # 1分钟内完成
                return 0.6
            else:
                return 0.4
        return 0.5
    
    async def _generate_recommendations(self, result: ExecutionResult, evaluation: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if evaluation['efficiency_score'] < 0.6:
            recommendations.append("建议优化执行效率，减少执行时间")
        
        if evaluation['quality_score'] < 0.7:
            recommendations.append("建议提高结果质量，增加详细程度")
        
        if evaluation['completeness_score'] < 0.8:
            recommendations.append("建议完善结果内容，确保信息完整")
        
        return recommendations
