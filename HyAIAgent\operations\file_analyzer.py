"""
文件分析器模块

提供综合的文件分析功能，集成文档处理、文本分析、配置分析等多种分析能力，
为AI提供深度的文件理解和分析能力。

Author: HyAIAgent
Date: 2025-07-29
"""

import asyncio
import hashlib
import json
import mimetypes
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from .security_manager import SecurityManager
from .file_operations import FileOperations
from .document_processor import DocumentProcessor
from .text_analyzer import TextAnalyzer
from .config_processor import ConfigProcessor
from .file_utils import FileUtils
from .path_utils import PathUtils


class FileAnalyzer:
    """
    文件分析器类
    
    提供综合的文件分析功能，包括：
    - 文件基础信息分析
    - 内容深度分析
    - 结构化数据分析
    - 安全性分析
    - 相似性分析
    - 批量文件分析
    """
    
    def __init__(self, workspace_path: str = "./workspace",
                 security_manager: Optional[SecurityManager] = None,
                 file_operations: Optional[FileOperations] = None,
                 document_processor: Optional[DocumentProcessor] = None,
                 text_analyzer: Optional[TextAnalyzer] = None,
                 config_processor: Optional[ConfigProcessor] = None) -> None:
        """
        初始化文件分析器
        
        Args:
            workspace_path (str): 工作空间路径
            security_manager (SecurityManager, optional): 安全管理器实例
            file_operations (FileOperations, optional): 文件操作实例
            document_processor (DocumentProcessor, optional): 文档处理器实例
            text_analyzer (TextAnalyzer, optional): 文本分析器实例
            config_processor (ConfigProcessor, optional): 配置处理器实例
        """
        self.workspace_path = Path(workspace_path)
        
        # 初始化依赖组件
        self.security_manager = security_manager or SecurityManager(str(self.workspace_path))
        self.file_operations = file_operations or FileOperations(str(self.workspace_path), self.security_manager)
        self.document_processor = document_processor or DocumentProcessor(str(self.workspace_path), self.security_manager, self.file_operations)
        self.text_analyzer = text_analyzer or TextAnalyzer(str(self.workspace_path), self.security_manager, self.file_operations)
        self.config_processor = config_processor or ConfigProcessor(str(self.workspace_path))
        
        # 分析统计信息
        self.analysis_stats = {
            'total_analyzed': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'analysis_types': {
                'basic': 0,
                'content': 0,
                'structure': 0,
                'security': 0,
                'similarity': 0
            },
            'file_types_analyzed': {},
            'total_analysis_time': 0.0,
            'average_analysis_time': 0.0
        }
        
        # 支持的分析类型
        self.supported_analysis_types = {
            'basic': '基础文件信息分析',
            'content': '文件内容深度分析',
            'structure': '结构化数据分析',
            'security': '安全性分析',
            'similarity': '文件相似性分析',
            'comprehensive': '综合分析（包含所有类型）'
        }
    
    async def analyze_file(self, file_path: str, 
                          analysis_types: List[str] = None,
                          analysis_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        分析单个文件
        
        Args:
            file_path (str): 文件路径
            analysis_types (List[str], optional): 分析类型列表，默认为['basic', 'content']
            analysis_options (Dict[str, Any], optional): 分析选项配置
            
        Returns:
            Dict[str, Any]: 文件分析结果
        """
        start_time = datetime.now()
        
        try:
            # 默认分析类型
            if analysis_types is None:
                analysis_types = ['basic', 'content']
            
            # 默认分析选项
            if analysis_options is None:
                analysis_options = {}
            
            # 安全验证
            validation_result = self.security_manager.validate_operation(file_path, "read")
            if not validation_result['valid']:
                errors = validation_result.get('errors', ['未知安全错误'])
                return {
                    'success': False,
                    'error': f"安全验证失败: {'; '.join(errors)}",
                    'file_path': file_path,
                    'analysis_time': 0
                }
            
            # 检查文件是否存在
            full_path = self.workspace_path / file_path
            if not full_path.exists():
                return {
                    'success': False,
                    'error': f"文件不存在: {file_path}",
                    'file_path': file_path,
                    'analysis_time': 0
                }
            
            # 初始化分析结果
            analysis_result = {
                'success': True,
                'file_path': file_path,
                'analysis_timestamp': start_time.isoformat(),
                'analysis_types': analysis_types,
                'analyses': {}
            }
            
            # 执行各种类型的分析
            for analysis_type in analysis_types:
                if analysis_type == 'basic':
                    analysis_result['analyses']['basic'] = await self._analyze_basic_info(full_path)
                elif analysis_type == 'content':
                    analysis_result['analyses']['content'] = await self._analyze_content(full_path, analysis_options)
                elif analysis_type == 'structure':
                    analysis_result['analyses']['structure'] = await self._analyze_structure(full_path, analysis_options)
                elif analysis_type == 'security':
                    analysis_result['analyses']['security'] = await self._analyze_security(full_path, analysis_options)
                elif analysis_type == 'comprehensive':
                    # 综合分析包含所有类型
                    analysis_result['analyses']['basic'] = await self._analyze_basic_info(full_path)
                    analysis_result['analyses']['content'] = await self._analyze_content(full_path, analysis_options)
                    analysis_result['analyses']['structure'] = await self._analyze_structure(full_path, analysis_options)
                    analysis_result['analyses']['security'] = await self._analyze_security(full_path, analysis_options)
                
                # 更新统计
                self.analysis_stats['analysis_types'][analysis_type] += 1
            
            # 计算分析时间
            end_time = datetime.now()
            analysis_time = (end_time - start_time).total_seconds()
            analysis_result['analysis_time'] = analysis_time
            
            # 更新统计信息
            self._update_stats('success', full_path.suffix.lower(), analysis_time)
            
            return analysis_result
            
        except Exception as e:
            # 更新失败统计
            self._update_stats('failed', '', 0)
            
            return {
                'success': False,
                'error': f"分析失败: {str(e)}",
                'file_path': file_path,
                'analysis_time': (datetime.now() - start_time).total_seconds()
            }
    
    async def batch_analyze_files(self, file_patterns: List[str],
                                 analysis_types: List[str] = None,
                                 analysis_options: Optional[Dict[str, Any]] = None,
                                 max_concurrent: int = 5) -> Dict[str, Any]:
        """
        批量分析多个文件
        
        Args:
            file_patterns (List[str]): 文件模式列表
            analysis_types (List[str], optional): 分析类型列表
            analysis_options (Dict[str, Any], optional): 分析选项配置
            max_concurrent (int): 最大并发数
            
        Returns:
            Dict[str, Any]: 批量分析结果
        """
        start_time = datetime.now()
        
        try:
            # 获取匹配的文件列表
            all_files = []
            for pattern in file_patterns:
                search_result = await self.file_operations.search_files(pattern)
                if search_result['success']:
                    all_files.extend([f['relative_path'] for f in search_result['files']])
            
            if not all_files:
                return {
                    'success': False,
                    'error': "没有找到匹配的文件",
                    'patterns': file_patterns,
                    'total_files': 0,
                    'analysis_time': 0
                }
            
            # 创建信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def analyze_single_file(file_path: str) -> Dict[str, Any]:
                async with semaphore:
                    return await self.analyze_file(file_path, analysis_types, analysis_options)
            
            # 并发执行分析
            tasks = [analyze_single_file(file_path) for file_path in all_files]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            successful_analyses = []
            failed_analyses = []
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    failed_analyses.append({
                        'file_path': all_files[i],
                        'error': str(result)
                    })
                elif result.get('success', False):
                    successful_analyses.append(result)
                else:
                    failed_analyses.append(result)
            
            # 计算总时间
            total_time = (datetime.now() - start_time).total_seconds()
            
            return {
                'success': True,
                'patterns': file_patterns,
                'total_files': len(all_files),
                'successful_analyses': len(successful_analyses),
                'failed_analyses': len(failed_analyses),
                'results': successful_analyses,
                'failures': failed_analyses,
                'analysis_time': total_time,
                'average_time_per_file': total_time / len(all_files) if all_files else 0
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"批量分析失败: {str(e)}",
                'patterns': file_patterns,
                'total_files': 0,
                'analysis_time': (datetime.now() - start_time).total_seconds()
            }

    async def compare_files(self, file_path1: str, file_path2: str,
                           comparison_types: List[str] = None) -> Dict[str, Any]:
        """
        比较两个文件的相似性

        Args:
            file_path1 (str): 第一个文件路径
            file_path2 (str): 第二个文件路径
            comparison_types (List[str], optional): 比较类型列表

        Returns:
            Dict[str, Any]: 文件比较结果
        """
        try:
            if comparison_types is None:
                comparison_types = ['basic', 'content', 'structure']

            # 分析两个文件
            analysis1 = await self.analyze_file(file_path1, ['basic', 'content', 'structure'])
            analysis2 = await self.analyze_file(file_path2, ['basic', 'content', 'structure'])

            if not (analysis1['success'] and analysis2['success']):
                return {
                    'success': False,
                    'error': "文件分析失败",
                    'file1': file_path1,
                    'file2': file_path2
                }

            comparison_result = {
                'success': True,
                'file1': file_path1,
                'file2': file_path2,
                'comparison_types': comparison_types,
                'comparisons': {}
            }

            # 基础信息比较
            if 'basic' in comparison_types:
                comparison_result['comparisons']['basic'] = self._compare_basic_info(
                    analysis1['analyses'].get('basic', {}),
                    analysis2['analyses'].get('basic', {})
                )

            # 内容比较
            if 'content' in comparison_types:
                comparison_result['comparisons']['content'] = await self._compare_content(
                    file_path1, file_path2,
                    analysis1['analyses'].get('content', {}),
                    analysis2['analyses'].get('content', {})
                )

            # 结构比较
            if 'structure' in comparison_types:
                comparison_result['comparisons']['structure'] = self._compare_structure(
                    analysis1['analyses'].get('structure', {}),
                    analysis2['analyses'].get('structure', {})
                )

            return comparison_result

        except Exception as e:
            return {
                'success': False,
                'error': f"文件比较失败: {str(e)}",
                'file1': file_path1,
                'file2': file_path2
            }

    async def find_similar_files(self, target_file: str,
                                search_patterns: List[str] = ["*"],
                                similarity_threshold: float = 0.7,
                                max_results: int = 10) -> Dict[str, Any]:
        """
        查找与目标文件相似的文件

        Args:
            target_file (str): 目标文件路径
            search_patterns (List[str]): 搜索模式列表
            similarity_threshold (float): 相似度阈值
            max_results (int): 最大结果数量

        Returns:
            Dict[str, Any]: 相似文件查找结果
        """
        try:
            # 分析目标文件
            target_analysis = await self.analyze_file(target_file, ['basic', 'content'])
            if not target_analysis['success']:
                return {
                    'success': False,
                    'error': f"目标文件分析失败: {target_analysis.get('error', '')}",
                    'target_file': target_file
                }

            # 批量分析候选文件
            batch_analysis = await self.batch_analyze_files(search_patterns, ['basic', 'content'])
            if not batch_analysis['success']:
                return {
                    'success': False,
                    'error': f"候选文件分析失败: {batch_analysis.get('error', '')}",
                    'target_file': target_file
                }

            # 计算相似度
            similar_files = []
            for candidate in batch_analysis['results']:
                if candidate['file_path'] == target_file:
                    continue  # 跳过目标文件本身

                similarity_score = self._calculate_similarity(
                    target_analysis['analyses'],
                    candidate['analyses']
                )

                if similarity_score >= similarity_threshold:
                    similar_files.append({
                        'file_path': candidate['file_path'],
                        'similarity_score': similarity_score,
                        'analysis': candidate['analyses']
                    })

            # 按相似度排序
            similar_files.sort(key=lambda x: x['similarity_score'], reverse=True)

            return {
                'success': True,
                'target_file': target_file,
                'similarity_threshold': similarity_threshold,
                'total_candidates': batch_analysis['total_files'],
                'similar_files_count': len(similar_files),
                'similar_files': similar_files[:max_results]
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"相似文件查找失败: {str(e)}",
                'target_file': target_file
            }

    def get_analysis_stats(self) -> Dict[str, Any]:
        """
        获取分析统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            'total_analyzed': self.analysis_stats['total_analyzed'],
            'successful_analyses': self.analysis_stats['successful_analyses'],
            'failed_analyses': self.analysis_stats['failed_analyses'],
            'success_rate': (
                self.analysis_stats['successful_analyses'] / self.analysis_stats['total_analyzed']
                if self.analysis_stats['total_analyzed'] > 0 else 0
            ),
            'analysis_types': self.analysis_stats['analysis_types'].copy(),
            'file_types_analyzed': self.analysis_stats['file_types_analyzed'].copy(),
            'total_analysis_time': self.analysis_stats['total_analysis_time'],
            'average_analysis_time': self.analysis_stats['average_analysis_time'],
            'supported_analysis_types': self.supported_analysis_types.copy()
        }

    def reset_stats(self) -> None:
        """重置分析统计"""
        self.analysis_stats = {
            'total_analyzed': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'analysis_types': {
                'basic': 0,
                'content': 0,
                'structure': 0,
                'security': 0,
                'similarity': 0
            },
            'file_types_analyzed': {},
            'total_analysis_time': 0.0,
            'average_analysis_time': 0.0
        }

    # ==================== 私有方法 ====================

    async def _analyze_basic_info(self, file_path: Path) -> Dict[str, Any]:
        """分析文件基础信息"""
        try:
            stat = file_path.stat()
            file_info = FileUtils.get_file_info(file_path)

            return {
                'success': True,
                'file_name': file_path.name,
                'file_size': stat.st_size,
                'file_size_formatted': FileUtils.format_file_size(stat.st_size),
                'file_extension': FileUtils.get_file_extension(file_path),
                'file_category': FileUtils.get_file_category(file_path),
                'mime_type': FileUtils.get_mime_type(file_path),
                'is_text_file': FileUtils.is_text_file(file_path),
                'is_image_file': FileUtils.is_image_file(file_path),
                'is_dangerous_file': FileUtils.is_dangerous_file(file_path),
                'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'accessed_time': datetime.fromtimestamp(stat.st_atime).isoformat(),
                'file_hash_md5': FileUtils.calculate_file_hash(file_path, 'md5'),
                'file_hash_sha256': FileUtils.calculate_file_hash(file_path, 'sha256'),
                'detailed_info': file_info
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"基础信息分析失败: {str(e)}"
            }

    async def _analyze_content(self, file_path: Path, options: Dict[str, Any]) -> Dict[str, Any]:
        """分析文件内容"""
        try:
            # 检查是否为文本文件
            if not FileUtils.is_text_file(file_path):
                return {
                    'success': False,
                    'error': "非文本文件，无法进行内容分析"
                }

            # 读取文件内容
            read_result = await self.file_operations.read_file(str(file_path.relative_to(self.workspace_path)))
            if not read_result['success']:
                return {
                    'success': False,
                    'error': f"文件读取失败: {read_result.get('error', '')}"
                }

            content = read_result['content']

            # 使用文本分析器分析内容
            text_analysis = await self.text_analyzer.analyze_text(content, options.get('text_analysis_options'))

            # 使用文档处理器分析文档结构
            doc_analysis = await self.document_processor.process_document(
                str(file_path.relative_to(self.workspace_path))
            )

            return {
                'success': True,
                'content_length': len(content),
                'text_analysis': text_analysis,
                'document_analysis': doc_analysis,
                'encoding': read_result.get('encoding', 'unknown')
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"内容分析失败: {str(e)}"
            }

    async def _analyze_structure(self, file_path: Path, options: Dict[str, Any]) -> Dict[str, Any]:
        """分析文件结构"""
        try:
            file_extension = FileUtils.get_file_extension(file_path)

            # 配置文件结构分析
            if file_extension in ['.json', '.yaml', '.yml', '.ini', '.cfg', '.conf', '.toml']:
                config_analysis = await self.config_processor.process_config(
                    str(file_path.relative_to(self.workspace_path))
                )
                return {
                    'success': True,
                    'structure_type': 'configuration',
                    'config_analysis': config_analysis
                }

            # 文档结构分析
            elif file_extension in ['.md', '.rst', '.txt', '.html', '.xml']:
                doc_analysis = await self.document_processor.process_document(
                    str(file_path.relative_to(self.workspace_path))
                )
                return {
                    'success': True,
                    'structure_type': 'document',
                    'document_structure': doc_analysis
                }

            # 代码文件结构分析
            elif file_extension in ['.py', '.js', '.java', '.cpp', '.c', '.h']:
                # 简单的代码结构分析
                read_result = await self.file_operations.read_file(str(file_path.relative_to(self.workspace_path)))
                if read_result['success']:
                    content = read_result['content']
                    code_structure = self._analyze_code_structure(content, file_extension)
                    return {
                        'success': True,
                        'structure_type': 'code',
                        'code_structure': code_structure
                    }

            return {
                'success': False,
                'error': f"不支持的文件类型进行结构分析: {file_extension}"
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"结构分析失败: {str(e)}"
            }

    async def _analyze_security(self, file_path: Path, options: Dict[str, Any]) -> Dict[str, Any]:
        """分析文件安全性"""
        try:
            security_analysis = {
                'success': True,
                'file_path': str(file_path),
                'security_checks': {}
            }

            # 路径安全检查
            path_validation = self.security_manager.validate_path(str(file_path))
            security_analysis['security_checks']['path_safe'] = path_validation

            # 文件类型安全检查
            type_validation = self.security_manager.check_file_type(str(file_path))
            security_analysis['security_checks']['type_safe'] = type_validation

            # 文件大小检查
            size_validation = self.security_manager.check_file_size(str(file_path))
            security_analysis['security_checks']['size_safe'] = size_validation

            # 权限检查
            permission_validation = self.security_manager.check_file_permissions(str(file_path), "read")
            security_analysis['security_checks']['permission_safe'] = permission_validation

            # 危险文件检查
            is_dangerous = FileUtils.is_dangerous_file(file_path)
            security_analysis['security_checks']['is_dangerous'] = is_dangerous

            # 综合安全评分
            safe_checks = sum([
                path_validation,
                type_validation,
                size_validation,
                permission_validation,
                not is_dangerous
            ])
            security_analysis['security_score'] = safe_checks / 5.0
            security_analysis['security_level'] = self._get_security_level(security_analysis['security_score'])

            return security_analysis

        except Exception as e:
            return {
                'success': False,
                'error': f"安全分析失败: {str(e)}"
            }

    def _analyze_code_structure(self, content: str, file_extension: str) -> Dict[str, Any]:
        """分析代码文件结构"""
        try:
            structure = {
                'file_type': file_extension,
                'total_lines': len(content.splitlines()),
                'non_empty_lines': len([line for line in content.splitlines() if line.strip()]),
                'functions': [],
                'classes': [],
                'imports': [],
                'comments': []
            }

            lines = content.splitlines()

            if file_extension == '.py':
                # Python代码分析
                for i, line in enumerate(lines, 1):
                    stripped = line.strip()
                    if stripped.startswith('def '):
                        structure['functions'].append({
                            'name': stripped.split('(')[0].replace('def ', ''),
                            'line': i
                        })
                    elif stripped.startswith('class '):
                        structure['classes'].append({
                            'name': stripped.split('(')[0].replace('class ', '').rstrip(':'),
                            'line': i
                        })
                    elif stripped.startswith('import ') or stripped.startswith('from '):
                        structure['imports'].append({
                            'statement': stripped,
                            'line': i
                        })
                    elif stripped.startswith('#'):
                        structure['comments'].append({
                            'content': stripped,
                            'line': i
                        })

            return structure

        except Exception as e:
            return {
                'error': f"代码结构分析失败: {str(e)}"
            }

    def _compare_basic_info(self, info1: Dict[str, Any], info2: Dict[str, Any]) -> Dict[str, Any]:
        """比较基础信息"""
        try:
            comparison = {
                'file_size_diff': abs(info1.get('file_size', 0) - info2.get('file_size', 0)),
                'same_extension': info1.get('file_extension') == info2.get('file_extension'),
                'same_category': info1.get('file_category') == info2.get('file_category'),
                'same_mime_type': info1.get('mime_type') == info2.get('mime_type'),
                'hash_match_md5': info1.get('file_hash_md5') == info2.get('file_hash_md5'),
                'hash_match_sha256': info1.get('file_hash_sha256') == info2.get('file_hash_sha256')
            }

            # 计算基础相似度
            similarity_factors = [
                comparison['same_extension'],
                comparison['same_category'],
                comparison['same_mime_type'],
                comparison['hash_match_md5']
            ]
            comparison['basic_similarity'] = sum(similarity_factors) / len(similarity_factors)

            return comparison

        except Exception as e:
            return {
                'error': f"基础信息比较失败: {str(e)}"
            }

    async def _compare_content(self, file1: str, file2: str,
                              content1: Dict[str, Any], content2: Dict[str, Any]) -> Dict[str, Any]:
        """比较文件内容"""
        try:
            comparison = {
                'content_length_diff': abs(
                    content1.get('content_length', 0) - content2.get('content_length', 0)
                ),
                'encoding_match': content1.get('encoding') == content2.get('encoding')
            }

            # 比较文本分析结果
            text1 = content1.get('text_analysis', {})
            text2 = content2.get('text_analysis', {})

            if text1.get('success') and text2.get('success'):
                comparison['text_similarity'] = self._calculate_text_similarity(text1, text2)

            return comparison

        except Exception as e:
            return {
                'error': f"内容比较失败: {str(e)}"
            }

    def _compare_structure(self, struct1: Dict[str, Any], struct2: Dict[str, Any]) -> Dict[str, Any]:
        """比较文件结构"""
        try:
            comparison = {
                'same_structure_type': struct1.get('structure_type') == struct2.get('structure_type')
            }

            # 根据结构类型进行具体比较
            if comparison['same_structure_type']:
                structure_type = struct1.get('structure_type')

                if structure_type == 'code':
                    code1 = struct1.get('code_structure', {})
                    code2 = struct2.get('code_structure', {})
                    comparison['code_similarity'] = self._calculate_code_similarity(code1, code2)

                elif structure_type == 'configuration':
                    config1 = struct1.get('config_analysis', {})
                    config2 = struct2.get('config_analysis', {})
                    comparison['config_similarity'] = self._calculate_config_similarity(config1, config2)

            return comparison

        except Exception as e:
            return {
                'error': f"结构比较失败: {str(e)}"
            }

    def _calculate_similarity(self, analysis1: Dict[str, Any], analysis2: Dict[str, Any]) -> float:
        """计算两个文件分析结果的整体相似度"""
        try:
            similarity_scores = []

            # 基础信息相似度
            basic1 = analysis1.get('basic', {})
            basic2 = analysis2.get('basic', {})
            if basic1.get('success') and basic2.get('success'):
                basic_comparison = self._compare_basic_info(basic1, basic2)
                similarity_scores.append(basic_comparison.get('basic_similarity', 0))

            # 内容相似度
            content1 = analysis1.get('content', {})
            content2 = analysis2.get('content', {})
            if content1.get('success') and content2.get('success'):
                text1 = content1.get('text_analysis', {})
                text2 = content2.get('text_analysis', {})
                if text1.get('success') and text2.get('success'):
                    text_similarity = self._calculate_text_similarity(text1, text2)
                    similarity_scores.append(text_similarity)

            # 返回平均相似度
            return sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0.0

        except Exception:
            return 0.0

    def _calculate_text_similarity(self, text1: Dict[str, Any], text2: Dict[str, Any]) -> float:
        """计算文本相似度"""
        try:
            similarity_factors = []

            # 词频相似度
            if 'word_frequency' in text1 and 'word_frequency' in text2:
                freq1 = text1['word_frequency']
                freq2 = text2['word_frequency']

                # 计算共同词汇比例
                common_words = set(freq1.keys()) & set(freq2.keys())
                total_words = set(freq1.keys()) | set(freq2.keys())
                if total_words:
                    word_similarity = len(common_words) / len(total_words)
                    similarity_factors.append(word_similarity)

            # 语言相似度
            if 'language' in text1 and 'language' in text2:
                lang_similarity = 1.0 if text1['language'] == text2['language'] else 0.0
                similarity_factors.append(lang_similarity)

            # 文档类型相似度
            if 'document_type' in text1 and 'document_type' in text2:
                type_similarity = 1.0 if text1['document_type'] == text2['document_type'] else 0.0
                similarity_factors.append(type_similarity)

            return sum(similarity_factors) / len(similarity_factors) if similarity_factors else 0.0

        except Exception:
            return 0.0

    def _calculate_code_similarity(self, code1: Dict[str, Any], code2: Dict[str, Any]) -> float:
        """计算代码结构相似度"""
        try:
            similarity_factors = []

            # 函数数量相似度
            func_count1 = len(code1.get('functions', []))
            func_count2 = len(code2.get('functions', []))
            if func_count1 + func_count2 > 0:
                func_similarity = 1 - abs(func_count1 - func_count2) / max(func_count1, func_count2, 1)
                similarity_factors.append(func_similarity)

            # 类数量相似度
            class_count1 = len(code1.get('classes', []))
            class_count2 = len(code2.get('classes', []))
            if class_count1 + class_count2 > 0:
                class_similarity = 1 - abs(class_count1 - class_count2) / max(class_count1, class_count2, 1)
                similarity_factors.append(class_similarity)

            return sum(similarity_factors) / len(similarity_factors) if similarity_factors else 0.0

        except Exception:
            return 0.0

    def _calculate_config_similarity(self, config1: Dict[str, Any], config2: Dict[str, Any]) -> float:
        """计算配置文件相似度"""
        try:
            # 简单的键值对比较
            if config1.get('success') and config2.get('success'):
                data1 = config1.get('parsed_data', {})
                data2 = config2.get('parsed_data', {})

                if isinstance(data1, dict) and isinstance(data2, dict):
                    keys1 = set(data1.keys())
                    keys2 = set(data2.keys())

                    if keys1 or keys2:
                        common_keys = keys1 & keys2
                        total_keys = keys1 | keys2
                        return len(common_keys) / len(total_keys)

            return 0.0

        except Exception:
            return 0.0

    def _get_security_level(self, score: float) -> str:
        """根据安全评分获取安全等级"""
        if score >= 0.9:
            return "高安全"
        elif score >= 0.7:
            return "中等安全"
        elif score >= 0.5:
            return "低安全"
        else:
            return "不安全"

    def _update_stats(self, status: str, file_extension: str, analysis_time: float) -> None:
        """更新分析统计信息"""
        self.analysis_stats['total_analyzed'] += 1

        if status == 'success':
            self.analysis_stats['successful_analyses'] += 1
        else:
            self.analysis_stats['failed_analyses'] += 1

        if file_extension:
            if file_extension not in self.analysis_stats['file_types_analyzed']:
                self.analysis_stats['file_types_analyzed'][file_extension] = 0
            self.analysis_stats['file_types_analyzed'][file_extension] += 1

        self.analysis_stats['total_analysis_time'] += analysis_time
        if self.analysis_stats['total_analyzed'] > 0:
            self.analysis_stats['average_analysis_time'] = (
                self.analysis_stats['total_analysis_time'] / self.analysis_stats['total_analyzed']
            )
