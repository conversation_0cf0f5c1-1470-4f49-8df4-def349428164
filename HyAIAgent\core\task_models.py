"""
任务管理相关的数据模型

本模块定义了任务管理系统中使用的核心数据结构，包括任务、执行计划、执行结果等。
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union
from enum import Enum
from datetime import datetime
import uuid


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"          # 等待执行
    RUNNING = "running"          # 正在执行
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 执行失败
    CANCELLED = "cancelled"     # 已取消
    PAUSED = "paused"          # 已暂停


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


class TaskType(Enum):
    """任务类型枚举"""
    ANALYSIS = "analysis"        # 分析任务
    PLANNING = "planning"        # 规划任务
    EXECUTION = "execution"      # 执行任务
    COMMUNICATION = "communication"  # 沟通任务
    RESEARCH = "research"        # 研究任务
    GENERAL = "general"          # 通用任务
    FILE_OPERATION = "file_operation"  # 文件操作任务


@dataclass
class Task:
    """任务数据模型"""
    
    # 基础信息
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = ""
    description: str = ""
    task_type: TaskType = TaskType.GENERAL
    
    # 状态信息
    status: TaskStatus = TaskStatus.PENDING
    priority: TaskPriority = TaskPriority.NORMAL
    progress: float = 0.0  # 进度百分比 0-100
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_duration: Optional[int] = None  # 预估时长（秒）
    actual_duration: Optional[int] = None     # 实际时长（秒）
    
    # 依赖关系
    dependencies: List[str] = field(default_factory=list)  # 依赖的任务ID列表
    parent_task_id: Optional[str] = None
    subtasks: List[str] = field(default_factory=list)  # 子任务ID列表
    
    # 执行信息
    assigned_to: Optional[str] = None  # 分配给的执行器
    context: Dict[str, Any] = field(default_factory=dict)  # 任务上下文
    input_data: Dict[str, Any] = field(default_factory=dict)  # 输入数据
    output_data: Dict[str, Any] = field(default_factory=dict)  # 输出数据
    
    # 错误信息
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'task_type': self.task_type.value,
            'status': self.status.value,
            'priority': self.priority.value,
            'progress': self.progress,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'estimated_duration': self.estimated_duration,
            'actual_duration': self.actual_duration,
            'dependencies': self.dependencies,
            'parent_task_id': self.parent_task_id,
            'subtasks': self.subtasks,
            'assigned_to': self.assigned_to,
            'context': self.context,
            'input_data': self.input_data,
            'output_data': self.output_data,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        """从字典创建Task实例"""
        task = cls()
        task.id = data.get('id', task.id)
        task.title = data.get('title', '')
        task.description = data.get('description', '')
        task.task_type = TaskType(data.get('task_type', TaskType.GENERAL.value))
        task.status = TaskStatus(data.get('status', TaskStatus.PENDING.value))
        task.priority = TaskPriority(data.get('priority', TaskPriority.NORMAL.value))
        task.progress = data.get('progress', 0.0)
        
        # 时间字段处理
        if data.get('created_at'):
            task.created_at = datetime.fromisoformat(data['created_at'])
        if data.get('started_at'):
            task.started_at = datetime.fromisoformat(data['started_at'])
        if data.get('completed_at'):
            task.completed_at = datetime.fromisoformat(data['completed_at'])
            
        task.estimated_duration = data.get('estimated_duration')
        task.actual_duration = data.get('actual_duration')
        task.dependencies = data.get('dependencies', [])
        task.parent_task_id = data.get('parent_task_id')
        task.subtasks = data.get('subtasks', [])
        task.assigned_to = data.get('assigned_to')
        task.context = data.get('context', {})
        task.input_data = data.get('input_data', {})
        task.output_data = data.get('output_data', {})
        task.error_message = data.get('error_message')
        task.retry_count = data.get('retry_count', 0)
        task.max_retries = data.get('max_retries', 3)
        
        return task


@dataclass
class ExecutionPlan:
    """执行计划数据模型"""
    
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    
    # 任务信息
    tasks: List[Task] = field(default_factory=list)
    task_order: List[str] = field(default_factory=list)  # 任务执行顺序
    
    # 状态信息
    status: TaskStatus = TaskStatus.PENDING
    progress: float = 0.0
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    estimated_duration: Optional[int] = None
    actual_duration: Optional[int] = None
    
    # 执行配置
    parallel_execution: bool = False  # 是否支持并行执行
    max_concurrent_tasks: int = 1     # 最大并发任务数
    auto_retry: bool = True           # 是否自动重试失败任务
    
    # 上下文信息
    global_context: Dict[str, Any] = field(default_factory=dict)
    
    def add_task(self, task: 'Task') -> None:
        """添加任务到执行计划"""
        if task not in self.tasks:
            self.tasks.append(task)
            self.task_order.append(task.id)

    def remove_task(self, task_id: str) -> bool:
        """从执行计划中移除任务"""
        for i, task in enumerate(self.tasks):
            if task.id == task_id:
                self.tasks.pop(i)
                if task_id in self.task_order:
                    self.task_order.remove(task_id)
                return True
        return False

    def get_task(self, task_id: str) -> Optional['Task']:
        """根据ID获取任务"""
        for task in self.tasks:
            if task.id == task_id:
                return task
        return None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'tasks': [task.to_dict() for task in self.tasks],
            'task_order': self.task_order,
            'status': self.status.value,
            'progress': self.progress,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'estimated_duration': self.estimated_duration,
            'actual_duration': self.actual_duration,
            'parallel_execution': self.parallel_execution,
            'max_concurrent_tasks': self.max_concurrent_tasks,
            'auto_retry': self.auto_retry,
            'global_context': self.global_context
        }


@dataclass
class ExecutionResult:
    """执行结果数据模型"""
    
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    plan_id: str = ""
    task_id: str = ""
    
    # 执行状态
    status: TaskStatus = TaskStatus.PENDING
    success: bool = False
    
    # 结果数据
    result_data: Dict[str, Any] = field(default_factory=dict)
    output_files: List[str] = field(default_factory=list)
    logs: List[str] = field(default_factory=list)
    
    # 错误信息
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    stack_trace: Optional[str] = None
    
    # 性能信息
    execution_time: Optional[float] = None  # 执行时间（秒）
    memory_usage: Optional[float] = None    # 内存使用（MB）
    cpu_usage: Optional[float] = None       # CPU使用率
    
    # 时间信息
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'plan_id': self.plan_id,
            'task_id': self.task_id,
            'status': self.status.value,
            'success': self.success,
            'result_data': self.result_data,
            'output_files': self.output_files,
            'logs': self.logs,
            'error_message': self.error_message,
            'error_code': self.error_code,
            'stack_trace': self.stack_trace,
            'execution_time': self.execution_time,
            'memory_usage': self.memory_usage,
            'cpu_usage': self.cpu_usage,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }


@dataclass
class ProgressInfo:
    """进度信息数据模型"""
    
    plan_id: str = ""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    running_tasks: int = 0
    pending_tasks: int = 0
    
    overall_progress: float = 0.0  # 总体进度百分比
    estimated_remaining_time: Optional[int] = None  # 预估剩余时间（秒）
    
    current_task: Optional[Task] = None
    recent_results: List[ExecutionResult] = field(default_factory=list)
    
    # 统计信息
    start_time: Optional[datetime] = None
    last_update_time: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'plan_id': self.plan_id,
            'total_tasks': self.total_tasks,
            'completed_tasks': self.completed_tasks,
            'failed_tasks': self.failed_tasks,
            'running_tasks': self.running_tasks,
            'pending_tasks': self.pending_tasks,
            'overall_progress': self.overall_progress,
            'estimated_remaining_time': self.estimated_remaining_time,
            'current_task': self.current_task.to_dict() if self.current_task else None,
            'recent_results': [result.to_dict() for result in self.recent_results],
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'last_update_time': self.last_update_time.isoformat()
        }
