#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
第五阶段集成测试
测试各模块之间的集成和协作功能
"""

import asyncio
import pytest
import sys
import os
import time
from pathlib import Path
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.config_manager import ConfigManager
from core.ai_client import SimpleAIClient
from core.kv_store import KVStore
from core.task_manager import TaskManager
from core.execution_engine import ExecutionEngine
from core.autonomous_agent import AutonomousAgent

# 导入高级功能模块
from advanced.reasoning_engine import ReasoningEngine
from advanced.knowledge_graph import KnowledgeGraph
from advanced.learning_system import LearningSystem

# 导入监控模块
from monitoring.performance_monitor import PerformanceMonitor
from monitoring.usage_tracker import UsageTracker

# 导入工具模块
from tools.chart_generator import ChartGenerator
from tools.workflow_engine import WorkflowEngine

class TestStage5Integration:
    """第五阶段集成测试类"""
    
    @pytest.fixture(autouse=True)
    async def setup_method(self):
        """测试前置设置"""
        self.config_manager = ConfigManager()
        self.kv_store = KVStore(db_path="data/test_integration.json")
        
        # Mock AI客户端
        self.ai_client = Mock(spec=SimpleAIClient)
        self.ai_client.chat = AsyncMock(return_value="集成测试响应")
        
        # 集成测试结果
        self.integration_results = []
        
        yield
        
        # 清理
        await self.kv_store.close()
        if os.path.exists("data/test_integration.json"):
            os.remove("data/test_integration.json")
    
    def record_integration_result(self, test_name: str, success: bool, details: str = ""):
        """记录集成测试结果"""
        self.integration_results.append({
            'test_name': test_name,
            'success': success,
            'details': details,
            'timestamp': time.time()
        })
    
    @pytest.mark.asyncio
    async def test_ai_reasoning_knowledge_integration(self):
        """测试AI推理与知识图谱集成"""
        try:
            # 创建推理引擎和知识图谱
            reasoning_engine = ReasoningEngine(
                ai_client=self.ai_client,
                kv_store=self.kv_store
            )
            
            knowledge_graph = KnowledgeGraph(
                ai_client=self.ai_client,
                kv_store=self.kv_store
            )
            
            # 添加知识到图谱
            await knowledge_graph.add_knowledge("Python", "是", "编程语言")
            await knowledge_graph.add_knowledge("Python", "用于", "AI开发")
            await knowledge_graph.add_knowledge("AI开发", "需要", "数据科学知识")
            
            # 使用推理引擎分析问题
            problem = "如何使用Python进行AI开发？"
            reasoning_result = await reasoning_engine.multi_step_reasoning(problem)
            
            # 验证集成结果
            assert reasoning_result is not None
            assert reasoning_result.confidence > 0
            
            # 查询相关知识
            knowledge_results = await knowledge_graph.query_knowledge("Python")
            assert len(knowledge_results.triples) > 0
            
            self.record_integration_result(
                "ai_reasoning_knowledge_integration", 
                True, 
                f"推理结果置信度: {reasoning_result.confidence}, 知识条目: {len(knowledge_results.triples)}"
            )
            
            print("✅ AI推理与知识图谱集成测试通过")
            
        except Exception as e:
            self.record_integration_result("ai_reasoning_knowledge_integration", False, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_learning_system_integration(self):
        """测试学习系统与其他模块集成"""
        try:
            # 创建学习系统
            learning_system = LearningSystem(
                ai_client=self.ai_client,
                kv_store=self.kv_store
            )
            
            # 创建性能监控
            performance_monitor = PerformanceMonitor()
            
            # 模拟用户交互学习
            interactions = [
                {
                    'user_id': 'test_user_1',
                    'query': 'Python基础语法',
                    'response': '变量、函数、类等基础概念',
                    'feedback': 'helpful',
                    'timestamp': time.time()
                },
                {
                    'user_id': 'test_user_1',
                    'query': 'Python高级特性',
                    'response': '装饰器、生成器、上下文管理器',
                    'feedback': 'very_helpful',
                    'timestamp': time.time()
                }
            ]
            
            # 学习用户交互
            for interaction in interactions:
                await learning_system.learn_from_interaction(interaction)
            
            # 获取系统性能指标
            metrics = await performance_monitor.collect_system_metrics()
            
            # 验证学习效果
            user_preferences = learning_system.get_user_preferences('test_user_1')
            assert user_preferences is not None
            
            # 验证性能监控
            assert 'cpu_usage' in metrics
            assert 'memory_usage' in metrics
            
            self.record_integration_result(
                "learning_system_integration", 
                True, 
                f"学习交互数: {len(interactions)}, 性能指标数: {len(metrics)}"
            )
            
            print("✅ 学习系统集成测试通过")
            
        except Exception as e:
            self.record_integration_result("learning_system_integration", False, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_task_execution_monitoring_integration(self):
        """测试任务执行与监控集成"""
        try:
            # 创建任务管理器和执行引擎
            task_manager = TaskManager(
                ai_client=self.ai_client,
                prompt_manager=Mock(),
                kv_store=self.kv_store
            )
            
            execution_engine = ExecutionEngine(
                ai_client=self.ai_client,
                prompt_manager=Mock()
            )
            
            # 创建使用跟踪器
            usage_tracker = UsageTracker()
            
            # 创建测试任务
            user_input = "分析系统性能并生成报告"
            tasks = await task_manager.decompose_task(user_input)
            
            # 创建执行计划
            plan = await task_manager.create_execution_plan(tasks, "性能分析计划")
            
            # 跟踪任务执行
            await usage_tracker.track_task_execution(plan.plan_id, "started")
            
            # 模拟任务执行
            for task in tasks:
                result = await execution_engine.execute_task(task)
                await usage_tracker.track_task_execution(task.task_id, "completed")
            
            await usage_tracker.track_task_execution(plan.plan_id, "completed")
            
            # 获取使用统计
            usage_stats = await usage_tracker.get_usage_statistics()
            
            # 验证集成结果
            assert len(tasks) > 0
            assert plan.plan_id is not None
            assert usage_stats is not None
            
            self.record_integration_result(
                "task_execution_monitoring_integration", 
                True, 
                f"任务数: {len(tasks)}, 计划ID: {plan.plan_id}"
            )
            
            print("✅ 任务执行与监控集成测试通过")
            
        except Exception as e:
            self.record_integration_result("task_execution_monitoring_integration", False, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_visualization_workflow_integration(self):
        """测试可视化与工作流集成"""
        try:
            # 创建图表生成器和工作流引擎
            chart_generator = ChartGenerator()
            workflow_engine = WorkflowEngine()
            
            # 定义数据可视化工作流
            workflow_definition = {
                'name': '数据可视化工作流',
                'steps': [
                    {
                        'id': 'data_preparation',
                        'type': 'data_processing',
                        'config': {
                            'data_source': 'test_data',
                            'processing_type': 'aggregation'
                        }
                    },
                    {
                        'id': 'chart_generation',
                        'type': 'visualization',
                        'config': {
                            'chart_type': 'bar',
                            'title': '测试数据图表'
                        }
                    }
                ]
            }
            
            # 创建工作流
            workflow = await workflow_engine.create_workflow(workflow_definition)
            
            # 准备测试数据
            test_data = {
                'labels': ['A', 'B', 'C', 'D'],
                'values': [10, 25, 15, 30]
            }
            
            # 执行工作流
            execution_result = await workflow_engine.execute_workflow(
                workflow.workflow_id, 
                {'test_data': test_data}
            )
            
            # 生成图表
            chart_data = {
                'type': 'bar',
                'data': test_data,
                'title': '工作流生成的图表'
            }
            
            chart_result = await chart_generator.generate_chart(chart_data)
            
            # 验证集成结果
            assert workflow.workflow_id is not None
            assert execution_result.success is True
            assert chart_result.success is True
            
            self.record_integration_result(
                "visualization_workflow_integration", 
                True, 
                f"工作流ID: {workflow.workflow_id}, 图表生成: {chart_result.success}"
            )
            
            print("✅ 可视化与工作流集成测试通过")
            
        except Exception as e:
            self.record_integration_result("visualization_workflow_integration", False, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_autonomous_agent_full_integration(self):
        """测试自主代理完整集成"""
        try:
            # 使用Mock避免实际的AI调用
            with patch('core.autonomous_agent.SimpleAIClient') as mock_ai_client:
                mock_ai_client.return_value.chat = AsyncMock(return_value="自主代理测试响应")
                
                # 创建自主代理
                agent = AutonomousAgent()
                
                # 启动代理
                await agent.start()
                
                # 处理测试请求
                test_request = "分析当前系统状态并提供优化建议"
                response = await agent.process_request(test_request)
                
                # 获取代理状态
                status = agent.get_status()
                
                # 停止代理
                await agent.stop()
                
                # 验证集成结果
                assert response is not None
                assert status['state'] in ['idle', 'stopped']
                assert agent.agent_id is not None
                
                self.record_integration_result(
                    "autonomous_agent_full_integration", 
                    True, 
                    f"代理ID: {agent.agent_id}, 状态: {status['state']}"
                )
                
                print("✅ 自主代理完整集成测试通过")
                
        except Exception as e:
            self.record_integration_result("autonomous_agent_full_integration", False, str(e))
            raise
    
    def test_integration_summary(self):
        """集成测试总结"""
        if not self.integration_results:
            pytest.skip("没有集成测试结果")
        
        print("\n" + "="*60)
        print("🔗 第五阶段集成测试总结")
        print("="*60)
        
        successful_tests = [r for r in self.integration_results if r['success']]
        failed_tests = [r for r in self.integration_results if not r['success']]
        
        print(f"总测试数量: {len(self.integration_results)}")
        print(f"成功测试数量: {len(successful_tests)}")
        print(f"失败测试数量: {len(failed_tests)}")
        print(f"成功率: {len(successful_tests)/len(self.integration_results)*100:.1f}%")
        
        print("\n详细集成测试结果:")
        for result in self.integration_results:
            status = "✅" if result['success'] else "❌"
            print(f"  {status} {result['test_name']}")
            if result['details']:
                print(f"     详情: {result['details']}")
        
        # 集成测试要求
        success_rate = len(successful_tests) / len(self.integration_results)
        assert success_rate >= 0.8, f"集成测试成功率过低: {success_rate*100:.1f}%"
        
        print(f"\n✅ 集成测试成功率达标: {success_rate*100:.1f}% (要求 ≥ 80%)")

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
