"""
提示词管理器模块
负责加载、管理和渲染提示词模板
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from jinja2 import Template, Environment, FileSystemLoader
from loguru import logger


class PromptManager:
    """
    提示词管理器
    负责加载提示词文件、模板渲染和提示词缓存
    """
    
    def __init__(self, prompts_dir: str = "prompts"):
        """
        初始化提示词管理器
        
        Args:
            prompts_dir (str): 提示词目录路径
        """
        self.prompts_dir = Path(prompts_dir)
        self.global_dir = self.prompts_dir / "global"
        self.templates_dir = self.prompts_dir / "templates"
        self.prompt_cache = {}
        
        # 确保目录存在
        self.prompts_dir.mkdir(parents=True, exist_ok=True)
        self.global_dir.mkdir(parents=True, exist_ok=True)
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.prompts_dir)),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        logger.info(f"提示词管理器初始化完成: {self.prompts_dir}")
        self._load_all_prompts()
    
    def _load_all_prompts(self) -> None:
        """加载所有提示词文件到缓存"""
        try:
            # 加载全局提示词
            for prompt_file in self.global_dir.glob("*.md"):
                prompt_name = prompt_file.stem
                self._load_prompt_file(prompt_file, f"global.{prompt_name}")
            
            # 加载模板提示词
            for prompt_file in self.templates_dir.glob("*.md"):
                prompt_name = prompt_file.stem
                self._load_prompt_file(prompt_file, f"templates.{prompt_name}")
            
            logger.info(f"加载提示词文件完成，共{len(self.prompt_cache)}个")
            
        except Exception as e:
            logger.error(f"加载提示词文件失败: {e}")
    
    def _load_prompt_file(self, file_path: Path, cache_key: str) -> None:
        """
        加载单个提示词文件
        
        Args:
            file_path (Path): 文件路径
            cache_key (str): 缓存键名
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            self.prompt_cache[cache_key] = {
                'content': content,
                'file_path': str(file_path),
                'modified_time': file_path.stat().st_mtime
            }
            
            logger.debug(f"加载提示词: {cache_key}")
            
        except Exception as e:
            logger.error(f"加载提示词文件失败: {file_path}, 错误: {e}")
    
    def get_global_prompt(self, name: str) -> Optional[str]:
        """
        获取全局提示词
        
        Args:
            name (str): 提示词名称
            
        Returns:
            Optional[str]: 提示词内容，如果不存在则返回None
        """
        cache_key = f"global.{name}"
        return self._get_prompt_from_cache(cache_key)
    
    def get_template_prompt(self, name: str, variables: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """
        获取模板提示词并渲染
        
        Args:
            name (str): 模板名称
            variables (Dict[str, Any], optional): 模板变量
            
        Returns:
            Optional[str]: 渲染后的提示词内容
        """
        cache_key = f"templates.{name}"
        template_content = self._get_prompt_from_cache(cache_key)
        
        if template_content is None:
            return None
        
        if variables is None:
            variables = {}
        
        try:
            template = Template(template_content)
            rendered_content = template.render(**variables)
            logger.debug(f"渲染模板提示词: {name}")
            return rendered_content
            
        except Exception as e:
            logger.error(f"渲染模板提示词失败: {name}, 错误: {e}")
            return template_content  # 返回原始内容
    
    def _get_prompt_from_cache(self, cache_key: str) -> Optional[str]:
        """
        从缓存获取提示词，如果文件已修改则重新加载
        
        Args:
            cache_key (str): 缓存键名
            
        Returns:
            Optional[str]: 提示词内容
        """
        if cache_key not in self.prompt_cache:
            return None
        
        cached_item = self.prompt_cache[cache_key]
        file_path = Path(cached_item['file_path'])
        
        # 检查文件是否存在
        if not file_path.exists():
            logger.warning(f"提示词文件不存在: {file_path}")
            del self.prompt_cache[cache_key]
            return None
        
        # 检查文件是否已修改
        current_mtime = file_path.stat().st_mtime
        if current_mtime > cached_item['modified_time']:
            logger.debug(f"提示词文件已修改，重新加载: {cache_key}")
            self._load_prompt_file(file_path, cache_key)
        
        return self.prompt_cache[cache_key]['content']
    
    def add_prompt(self, name: str, content: str, prompt_type: str = "global") -> bool:
        """
        添加新的提示词
        
        Args:
            name (str): 提示词名称
            content (str): 提示词内容
            prompt_type (str): 提示词类型 ("global" 或 "templates")
            
        Returns:
            bool: 添加是否成功
        """
        try:
            if prompt_type == "global":
                file_path = self.global_dir / f"{name}.md"
            elif prompt_type == "templates":
                file_path = self.templates_dir / f"{name}.md"
            else:
                logger.error(f"无效的提示词类型: {prompt_type}")
                return False
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 更新缓存
            cache_key = f"{prompt_type}.{name}"
            self._load_prompt_file(file_path, cache_key)
            
            logger.info(f"添加提示词成功: {cache_key}")
            return True
            
        except Exception as e:
            logger.error(f"添加提示词失败: {name}, 错误: {e}")
            return False
    
    def update_prompt(self, name: str, content: str, prompt_type: str = "global") -> bool:
        """
        更新提示词
        
        Args:
            name (str): 提示词名称
            content (str): 新的提示词内容
            prompt_type (str): 提示词类型
            
        Returns:
            bool: 更新是否成功
        """
        return self.add_prompt(name, content, prompt_type)
    
    def delete_prompt(self, name: str, prompt_type: str = "global") -> bool:
        """
        删除提示词
        
        Args:
            name (str): 提示词名称
            prompt_type (str): 提示词类型
            
        Returns:
            bool: 删除是否成功
        """
        try:
            if prompt_type == "global":
                file_path = self.global_dir / f"{name}.md"
            elif prompt_type == "templates":
                file_path = self.templates_dir / f"{name}.md"
            else:
                logger.error(f"无效的提示词类型: {prompt_type}")
                return False
            
            # 删除文件
            if file_path.exists():
                file_path.unlink()
            
            # 从缓存中删除
            cache_key = f"{prompt_type}.{name}"
            if cache_key in self.prompt_cache:
                del self.prompt_cache[cache_key]
            
            logger.info(f"删除提示词成功: {cache_key}")
            return True
            
        except Exception as e:
            logger.error(f"删除提示词失败: {name}, 错误: {e}")
            return False
    
    def list_prompts(self, prompt_type: Optional[str] = None) -> List[str]:
        """
        列出所有提示词
        
        Args:
            prompt_type (str, optional): 提示词类型，None表示列出所有类型
            
        Returns:
            List[str]: 提示词名称列表
        """
        prompts = []
        
        for cache_key in self.prompt_cache.keys():
            if prompt_type is None:
                prompts.append(cache_key)
            elif cache_key.startswith(f"{prompt_type}."):
                prompts.append(cache_key.split('.', 1)[1])  # 移除类型前缀
        
        return sorted(prompts)
    
    def get_prompt_info(self, name: str, prompt_type: str = "global") -> Optional[Dict[str, Any]]:
        """
        获取提示词信息
        
        Args:
            name (str): 提示词名称
            prompt_type (str): 提示词类型
            
        Returns:
            Optional[Dict[str, Any]]: 提示词信息
        """
        cache_key = f"{prompt_type}.{name}"
        
        if cache_key not in self.prompt_cache:
            return None
        
        cached_item = self.prompt_cache[cache_key]
        file_path = Path(cached_item['file_path'])
        
        return {
            'name': name,
            'type': prompt_type,
            'file_path': str(file_path),
            'content_length': len(cached_item['content']),
            'modified_time': cached_item['modified_time'],
            'exists': file_path.exists()
        }
    
    def reload_prompts(self) -> None:
        """重新加载所有提示词"""
        self.prompt_cache.clear()
        self._load_all_prompts()
        logger.info("重新加载所有提示词完成")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取提示词管理器统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        global_count = len([k for k in self.prompt_cache.keys() if k.startswith('global.')])
        template_count = len([k for k in self.prompt_cache.keys() if k.startswith('templates.')])
        
        return {
            'total_prompts': len(self.prompt_cache),
            'global_prompts': global_count,
            'template_prompts': template_count,
            'prompts_dir': str(self.prompts_dir),
            'global_dir': str(self.global_dir),
            'templates_dir': str(self.templates_dir)
        }
