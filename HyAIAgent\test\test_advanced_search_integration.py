"""
高级搜索集成测试模块

综合测试整个搜索系统的集成功能，包括搜索操作、内容处理、信息分析和策略优化的协同工作。
"""

import asyncio
import pytest
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any
from unittest.mock import Mock, AsyncMock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from operations.search_operations import SearchOperations, SearchResult, SearchResponse
from operations.content_processor import ContentProcessor, ProcessedContent
from operations.information_analyzer import InformationAnalyzer, AnalysisResults
from operations.search_strategy_optimizer import (
    SearchStrategyOptimizer, SearchParameters, PerformanceMetrics, OptimizationStrategy
)


class TestAdvancedSearchIntegration:
    """高级搜索集成测试类"""
    
    def setup_method(self):
        """测试前设置"""
        # 创建模拟的配置管理器
        self.mock_config = Mock()
        self.mock_config.get_search_config.return_value = {
            'default_max_results': 5,
            'default_timeout': 30,
            'cache_enabled': True
        }
        
        # 初始化各个组件
        self.search_ops = SearchOperations(self.mock_config)
        self.content_processor = ContentProcessor(self.mock_config)
        self.info_analyzer = InformationAnalyzer(self.mock_config)
        self.strategy_optimizer = SearchStrategyOptimizer(self.mock_config)
        
        # 创建测试数据
        self.test_query = "人工智能发展趋势"
        self.test_search_results = [
            SearchResult(
                title="AI技术发展报告2024",
                url="https://example.com/ai-report-2024",
                content="人工智能技术在2024年呈现快速发展趋势，特别是在大语言模型领域...",
                score=0.95,
                source="tech_news"
            ),
            SearchResult(
                title="机器学习最新进展",
                url="https://example.com/ml-progress",
                content="机器学习算法在各个领域都有重大突破，深度学习技术日趋成熟...",
                score=0.88,
                source="academic"
            ),
            SearchResult(
                title="AI产业化应用案例",
                url="https://example.com/ai-applications",
                content="人工智能在医疗、金融、教育等行业的应用越来越广泛...",
                score=0.82,
                source="industry"
            )
        ]
    
    @pytest.mark.asyncio
    async def test_end_to_end_search_workflow(self):
        """测试端到端搜索工作流"""
        # 模拟搜索操作
        with patch.object(self.search_ops, 'search_web', new_callable=AsyncMock) as mock_search:
            mock_response = SearchResponse(
                query=self.test_query,
                results=self.test_search_results,
                total_results=len(self.test_search_results),
                search_time=1.5,
                timestamp=datetime.now()
            )
            mock_search.return_value = mock_response

            # 执行搜索
            search_response = await self.search_ops.search_web(
                query=self.test_query,
                max_results=5,
                search_depth="advanced"
            )

            search_results = search_response.results
            
            assert len(search_results) == 3
            assert all(isinstance(result, SearchResult) for result in search_results)
            
            # 处理内容
            processed_contents = []
            for result in search_results:
                # 模拟内容处理
                processed = ProcessedContent(
                    original_url=result.url,
                    title=result.title,
                    cleaned_content=result.content,
                    summary=result.content[:100] + "...",
                    keywords=["人工智能", "AI", "技术发展"],
                    entities=[{"text": "AI", "type": "TECH"}, {"text": "机器学习", "type": "TECH"}],
                    content_type="article",
                    language="zh",
                    word_count=200,
                    reading_time=2,
                    quality_score=result.score,
                    timestamp=datetime.now()
                )
                processed_contents.append(processed)
            
            assert len(processed_contents) == 3
            assert all(content.quality_score > 0.8 for content in processed_contents)
            
            # 信息分析
            # 创建分析数据
            from operations.information_analyzer import DataPoint, Dataset
            
            data_points = [
                DataPoint(timestamp=datetime.now() - timedelta(days=i), value=0.8 + i * 0.05, source="test_data")
                for i in range(10)
            ]
            dataset = Dataset(name="AI发展趋势", data_points=data_points)
            
            # 执行趋势分析
            trend_analysis = await self.info_analyzer.analyze_trends(data_points)
            assert trend_analysis is not None
            assert len(trend_analysis.key_insights) > 0
            
            print("✅ 端到端搜索工作流测试通过")
    
    @pytest.mark.asyncio
    async def test_search_strategy_optimization_integration(self):
        """测试搜索策略优化集成"""
        # 设置优化策略
        self.strategy_optimizer.set_optimization_strategy(OptimizationStrategy.BALANCED)
        
        # 创建初始参数
        initial_params = SearchParameters(
            max_results=5,
            search_depth="basic",
            timeout=30
        )
        
        # 模拟多次搜索并记录性能
        for i in range(10):
            # 模拟性能指标
            metrics = PerformanceMetrics(
                response_time=2.0 + i * 0.1,
                result_count=5,
                quality_score=0.8 - i * 0.01,
                cache_hit=i % 3 == 0,
                success=True,
                cost=0.1,
                timestamp=datetime.now() + timedelta(minutes=i)
            )
            
            await self.strategy_optimizer.record_performance(initial_params, metrics)
        
        # 执行参数优化
        optimization_result = await self.strategy_optimizer.optimize_parameters(initial_params)
        
        assert optimization_result.confidence_score > 0.0
        assert len(optimization_result.optimization_reason) > 0
        assert isinstance(optimization_result.optimized_parameters, SearchParameters)
        
        # 分析性能趋势
        trend_analysis = await self.strategy_optimizer.analyze_performance_trends()
        assert trend_analysis["status"] == "success"
        assert "trends" in trend_analysis
        
        print("✅ 搜索策略优化集成测试通过")
    
    @pytest.mark.asyncio
    async def test_content_processing_pipeline(self):
        """测试内容处理管道"""
        # 模拟原始内容
        raw_content = """
        人工智能技术发展报告
        
        近年来，人工智能技术发展迅速，特别是大语言模型的突破性进展。
        ChatGPT、GPT-4等模型的出现，标志着AI技术进入了新的发展阶段。
        
        主要发展趋势包括：
        1. 模型规模不断扩大
        2. 多模态能力增强
        3. 应用场景更加广泛
        4. 计算效率持续优化
        
        未来展望：
        AI技术将在更多领域发挥重要作用，推动社会数字化转型。
        """
        
        # 创建模拟搜索响应
        from operations.search_operations import SearchResponse
        mock_search_result = SearchResult(
            title="人工智能技术发展报告",
            url="https://example.com/ai-report",
            content=raw_content,
            score=0.9,
            source="test"
        )

        mock_response = SearchResponse(
            query="AI发展报告",
            results=[mock_search_result],
            total_results=1,
            search_time=1.0,
            timestamp=datetime.now()
        )

        # 模拟内容处理
        with patch.object(self.content_processor, 'process_search_response', new_callable=AsyncMock) as mock_process:
            from operations.content_processor import ProcessedResponse
            processed_response = ProcessedResponse(
                original_query="AI发展报告",
                processed_results=[ProcessedContent(
                    original_url="https://example.com/ai-report",
                    title="人工智能技术发展报告",
                    cleaned_content=raw_content,
                    summary="人工智能技术发展迅速，大语言模型取得突破性进展...",
                    keywords=["人工智能", "大语言模型", "ChatGPT", "GPT-4", "数字化转型"],
                    entities=[{"text": "ChatGPT", "type": "PRODUCT"}, {"text": "GPT-4", "type": "PRODUCT"}],
                    content_type="article",
                    language="zh",
                    word_count=500,
                    reading_time=3,
                    quality_score=0.9,
                    timestamp=datetime.now()
                )],
                total_processed=1,
                processing_time=1.5,
                summary="AI技术发展报告摘要",
                key_insights=["AI技术快速发展"],
                related_topics=["机器学习", "深度学习"],
                confidence_score=0.9,
                timestamp=datetime.now()
            )
            mock_process.return_value = processed_response

            # 执行处理
            result = await self.content_processor.process_search_response(mock_response)
            
            assert result.confidence_score > 0.8
            assert len(result.processed_results) >= 1
            assert len(result.key_insights) >= 1
            assert result.total_processed >= 1
            
            print("✅ 内容处理管道测试通过")
    
    @pytest.mark.asyncio
    async def test_information_analysis_integration(self):
        """测试信息分析集成"""
        # 创建多个数据集进行关联分析
        from operations.information_analyzer import DataPoint, Dataset
        
        # AI发展趋势数据
        ai_trend_data = [
            DataPoint(timestamp=datetime.now() - timedelta(days=i), value=0.7 + i * 0.03, source="ai_index")
            for i in range(15)
        ]
        ai_dataset = Dataset(name="AI发展指数", data_points=ai_trend_data)

        # 投资热度数据
        investment_data = [
            DataPoint(timestamp=datetime.now() - timedelta(days=i), value=0.6 + i * 0.04, source="investment_data")
            for i in range(15)
        ]
        investment_dataset = Dataset(name="AI投资热度", data_points=investment_data)
        
        # 执行关联分析
        correlation_result = await self.info_analyzer.find_correlations([ai_dataset, investment_dataset])
        
        assert correlation_result is not None
        assert correlation_result.correlation_coefficient != 0
        assert abs(correlation_result.correlation_coefficient) > 0.5  # 应该有较强相关性
        
        # 执行趋势分析
        trend_result = await self.info_analyzer.analyze_trends(ai_trend_data)
        assert trend_result is not None
        assert len(trend_result.key_insights) > 0
        
        # 生成综合洞察
        analysis_results = AnalysisResults(
            trend_analyses=[trend_result],
            correlation_reports=[correlation_result]
        )
        
        insights = await self.info_analyzer.generate_insights(analysis_results)
        assert insights is not None
        assert len(insights.key_findings) > 0
        assert insights.confidence_score > 0.0
        
        print("✅ 信息分析集成测试通过")
    
    @pytest.mark.asyncio
    async def test_error_handling_and_resilience(self):
        """测试错误处理和系统韧性"""
        # 测试搜索失败的处理
        with patch.object(self.search_ops, 'search_web', new_callable=AsyncMock) as mock_search:
            mock_search.side_effect = Exception("网络连接失败")

            try:
                await self.search_ops.search_web(query="测试查询", max_results=5)
                assert False, "应该抛出异常"
            except Exception as e:
                assert "网络连接失败" in str(e)

        # 测试内容处理失败的处理
        mock_response = SearchResponse(
            query="测试查询",
            results=[],
            total_results=0,
            search_time=1.0,
            timestamp=datetime.now()
        )

        with patch.object(self.content_processor, 'process_search_response', new_callable=AsyncMock) as mock_process:
            mock_process.side_effect = Exception("内容解析失败")

            try:
                await self.content_processor.process_search_response(mock_response)
                assert False, "应该抛出异常"
            except Exception as e:
                assert "内容解析失败" in str(e)
        
        # 测试空数据的处理
        try:
            await self.info_analyzer.analyze_trends([])
            assert False, "应该抛出异常"
        except Exception as e:
            assert "数据点列表不能为空" in str(e)
        
        print("✅ 错误处理和系统韧性测试通过")
    
    @pytest.mark.asyncio
    async def test_performance_monitoring(self):
        """测试性能监控"""
        # 记录多次操作的性能数据
        params = SearchParameters(max_results=5, search_depth="basic", timeout=30)
        
        performance_data = []
        for i in range(20):
            metrics = PerformanceMetrics(
                response_time=1.5 + (i % 5) * 0.3,  # 模拟性能波动
                result_count=5,
                quality_score=0.8 + (i % 3) * 0.05,
                cache_hit=i % 4 == 0,
                success=i % 10 != 9,  # 90%成功率
                cost=0.1,
                timestamp=datetime.now() + timedelta(seconds=i)
            )
            performance_data.append(metrics)
            await self.strategy_optimizer.record_performance(params, metrics)
        
        # 获取性能统计
        stats = self.strategy_optimizer.get_performance_statistics()
        
        assert stats["status"] == "success"
        statistics = stats["statistics"]
        
        assert statistics["total_queries"] == 20
        assert 0.8 <= statistics["success_rate"] <= 1.0
        assert 0.0 <= statistics["cache_hit_rate"] <= 1.0
        assert statistics["response_time"]["mean"] > 0
        assert statistics["quality_score"]["mean"] > 0.7
        
        # 分析性能趋势
        trends = await self.strategy_optimizer.analyze_performance_trends()
        assert trends["status"] == "success"
        assert "trends" in trends
        assert "summary" in trends
        
        print("✅ 性能监控测试通过")
    
    @pytest.mark.asyncio
    async def test_adaptive_search_optimization(self):
        """测试自适应搜索优化"""
        # 模拟不同场景下的搜索优化
        scenarios = [
            {"context": {"urgent": True}, "expected_timeout": lambda x: x <= 20},
            {"context": {"comprehensive": True}, "expected_results": lambda x: x >= 8}
        ]
        
        base_params = SearchParameters(max_results=5, search_depth="basic", timeout=30)
        
        for scenario in scenarios:
            # 执行优化
            result = await self.strategy_optimizer.optimize_parameters(
                base_params, 
                query_context=scenario["context"]
            )
            
            optimized_params = result.optimized_parameters
            
            # 验证优化结果
            if "expected_timeout" in scenario:
                assert scenario["expected_timeout"](optimized_params.timeout)
            if "expected_results" in scenario:
                assert scenario["expected_results"](optimized_params.max_results)
            
            assert result.confidence_score > 0.0
            assert len(result.optimization_reason) > 0
        
        print("✅ 自适应搜索优化测试通过")


async def run_all_tests():
    """运行所有测试"""
    test_instance = TestAdvancedSearchIntegration()
    test_methods = [method for method in dir(test_instance) if method.startswith('test_')]
    
    print("🚀 开始高级搜索集成测试...")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for method_name in test_methods:
        try:
            test_instance.setup_method()
            method = getattr(test_instance, method_name)
            
            if asyncio.iscoroutinefunction(method):
                await method()
            else:
                method()
            
            passed += 1
        except Exception as e:
            print(f"❌ {method_name} 失败: {str(e)}")
            import traceback
            traceback.print_exc()
            failed += 1
    
    print("=" * 50)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有高级搜索集成测试通过！")
    else:
        print(f"⚠️  有 {failed} 个测试失败，请检查代码")
    
    return failed == 0


if __name__ == "__main__":
    asyncio.run(run_all_tests())
