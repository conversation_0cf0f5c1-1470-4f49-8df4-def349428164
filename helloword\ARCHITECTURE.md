# PyQt6 项目架构说明

## 🏗️ 项目结构

```
helloword/
├── main.py                 # 程序入口点
├── app/                    # 应用程序层
│   ├── __init__.py
│   └── application.py      # 应用程序主类
├── ui/                     # 界面层 (View)
│   ├── __init__.py
│   └── main_window.py      # 主窗口界面
├── logic/                  # 业务逻辑层 (Controller)
│   ├── __init__.py
│   └── app_controller.py   # 应用控制器
├── README.md               # 项目说明
├── ARCHITECTURE.md         # 架构说明 (本文件)
├── requirements.txt        # 依赖文件
├── run.py                  # 启动器脚本
└── run.bat                 # Windows批处理启动脚本
```

## 🎯 架构设计原则

### 1. 分层架构 (Layered Architecture)
- **应用层 (Application Layer)**: 应用程序生命周期管理
- **界面层 (UI Layer)**: 用户界面和交互
- **逻辑层 (Logic Layer)**: 业务逻辑和数据处理

### 2. MVC模式 (Model-View-Controller)
- **View (界面层)**: `ui/main_window.py` - 负责界面显示
- **Controller (控制器)**: `logic/app_controller.py` - 负责业务逻辑
- **Model (数据模型)**: 可扩展添加数据模型层

### 3. 单一职责原则 (Single Responsibility Principle)
- 每个模块只负责一个特定的功能
- 界面代码与业务逻辑完全分离
- 便于维护和测试

## 📋 各层职责详解

### 应用层 (`app/application.py`)
**职责**:
- 应用程序初始化和配置
- 全局异常处理
- 国际化设置
- 应用程序生命周期管理

**核心类**:
- `HelloWorldApplication`: 应用程序主类
- `create_application()`: 工厂函数
- `run_application()`: 便捷启动函数

### 界面层 (`ui/main_window.py`)
**职责**:
- 界面布局和控件创建
- 样式表应用
- 用户交互事件捕获
- 界面状态管理

**核心类**:
- `MainWindowUI`: 主窗口界面类

**设计特点**:
- 只处理UI相关逻辑
- 通过信号与控制器通信
- 提供公共接口供控制器调用

### 逻辑层 (`logic/app_controller.py`)
**职责**:
- 业务逻辑处理
- 数据验证和处理
- 界面与数据模型协调
- 错误处理和用户反馈

**核心类**:
- `AppController`: 应用控制器类

**设计特点**:
- 连接界面信号到业务方法
- 处理复杂的业务逻辑
- 管理应用状态

## 🔄 信号槽通信机制

### 界面层信号
```python
# 在 MainWindowUI 中定义
greeting_requested = pyqtSignal(str, str)  # 问候请求
time_requested = pyqtSignal()              # 时间请求
clear_requested = pyqtSignal()             # 清除请求
```

### 控制器信号
```python
# 在 AppController 中定义
result_ready = pyqtSignal(str)             # 结果准备就绪
error_occurred = pyqtSignal(str, str)      # 错误发生
```

### 通信流程
1. 用户操作 → 界面层捕获事件
2. 界面层发射信号 → 控制器接收
3. 控制器处理业务逻辑
4. 控制器发射结果信号 → 界面层更新

## 🚀 扩展指南

### 添加新功能
1. **界面扩展**: 在 `ui/` 目录添加新的界面组件
2. **逻辑扩展**: 在 `logic/` 目录添加新的控制器
3. **数据扩展**: 可添加 `models/` 目录存放数据模型

### 添加新窗口
```python
# 1. 创建新的界面类
class SettingsWindowUI(QDialog):
    pass

# 2. 创建对应的控制器
class SettingsController(QObject):
    pass

# 3. 在应用层中管理
class HelloWorldApplication:
    def show_settings(self):
        self.settings_window = SettingsWindowUI()
        self.settings_controller = SettingsController(self.settings_window)
        self.settings_window.show()
```

### 添加数据模型
```python
# models/user_data.py
class UserDataModel(QObject):
    data_changed = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.user_name = ""
        self.preferences = {}
    
    def save_data(self):
        # 保存数据逻辑
        pass
```

## 🔧 最佳实践

### 1. 命名约定
- 界面类以 `UI` 结尾: `MainWindowUI`
- 控制器类以 `Controller` 结尾: `AppController`
- 信号使用动词形式: `greeting_requested`

### 2. 文件组织
- 每个模块一个文件
- 相关功能放在同一目录
- 使用 `__init__.py` 管理导出

### 3. 信号槽设计
- 界面层只发射信号，不处理业务逻辑
- 控制器连接信号并处理逻辑
- 使用自定义信号实现松耦合

### 4. 错误处理
- 在控制器层统一处理异常
- 通过信号向界面层报告错误
- 提供用户友好的错误信息

## 📈 架构优势

### 1. 可维护性
- 代码职责清晰，易于定位问题
- 修改界面不影响业务逻辑
- 修改逻辑不影响界面显示

### 2. 可测试性
- 业务逻辑独立，便于单元测试
- 界面逻辑简单，减少测试复杂度
- 可以模拟界面进行逻辑测试

### 3. 可扩展性
- 新增功能只需添加对应层的代码
- 支持多窗口和复杂界面
- 便于团队协作开发

### 4. 可重用性
- 界面组件可以复用
- 业务逻辑可以独立使用
- 应用框架可以作为模板

---

**这种架构适合中大型PyQt6项目，为项目的长期维护和扩展提供了坚实的基础。** 🎯
