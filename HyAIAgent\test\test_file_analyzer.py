"""
文件分析器测试模块

测试FileAnalyzer类的各种功能，包括：
- 单文件分析
- 批量文件分析
- 文件比较
- 相似文件查找
- 统计信息

Author: HyAIAgent
Date: 2025-07-29
"""

import asyncio
import json
import os
import tempfile
from pathlib import Path

import pytest

from operations.file_analyzer import FileAnalyzer
from operations.security_manager import SecurityManager
from operations.file_operations import FileOperations


class TestFileAnalyzer:
    """文件分析器测试类"""
    
    @pytest.fixture
    def setup_test_environment(self):
        """设置测试环境"""
        # 创建临时工作目录
        temp_dir = tempfile.mkdtemp()
        workspace_path = Path(temp_dir) / "test_workspace"
        workspace_path.mkdir(exist_ok=True)
        
        # 创建测试文件
        test_files = {
            "test.txt": "这是一个测试文本文件。\n包含中文和English内容。\n用于测试文件分析功能。",
            "test.json": '{"name": "test", "version": "1.0", "description": "测试配置文件"}',
            "test.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""测试Python文件"""

import os
import sys

class TestClass:
    """测试类"""
    
    def __init__(self):
        self.name = "test"
    
    def test_method(self):
        """测试方法"""
        return "Hello World"

def test_function():
    """测试函数"""
    print("This is a test function")

if __name__ == "__main__":
    test_function()
''',
            "test.md": '''# 测试Markdown文件

这是一个测试的Markdown文档。

## 功能特性

- 支持中文
- 支持English
- 支持代码块

```python
print("Hello World")
```

## 总结

这是测试文档的总结部分。
''',
            "config.yaml": '''# 测试YAML配置文件
app:
  name: "测试应用"
  version: "1.0.0"
  debug: true

database:
  host: "localhost"
  port: 5432
  name: "test_db"

features:
  - "feature1"
  - "feature2"
  - "feature3"
''',
            "large_file.txt": "这是一个大文件。\n" * 1000,
            "similar1.txt": "这是相似文件1。\n包含相同的关键词：测试、分析、功能。",
            "similar2.txt": "这是相似文件2。\n也包含相同的关键词：测试、分析、功能。",
            "different.txt": "这是完全不同的文件。\n包含不同的内容和主题。"
        }
        
        for filename, content in test_files.items():
            file_path = workspace_path / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
        
        # 创建子目录和文件
        subdir = workspace_path / "subdir"
        subdir.mkdir(exist_ok=True)
        with open(subdir / "sub_test.txt", 'w', encoding='utf-8') as f:
            f.write("子目录中的测试文件")
        
        # 初始化组件
        security_manager = SecurityManager(str(workspace_path))
        file_operations = FileOperations(str(workspace_path), security_manager)
        file_analyzer = FileAnalyzer(str(workspace_path), security_manager, file_operations)
        
        return {
            'workspace_path': workspace_path,
            'temp_dir': temp_dir,
            'security_manager': security_manager,
            'file_operations': file_operations,
            'file_analyzer': file_analyzer,
            'test_files': list(test_files.keys())
        }
    
    @pytest.mark.asyncio
    async def test_analyze_single_file_basic(self, setup_test_environment):
        """测试单文件基础分析"""
        env = setup_test_environment
        analyzer = env['file_analyzer']
        
        # 测试文本文件分析
        result = await analyzer.analyze_file("test.txt", ["basic"])
        
        assert result['success'] is True
        assert result['file_path'] == "test.txt"
        assert 'basic' in result['analyses']
        
        basic_analysis = result['analyses']['basic']
        assert basic_analysis['success'] is True
        assert basic_analysis['file_name'] == "test.txt"
        assert basic_analysis['file_extension'] == ".txt"
        assert basic_analysis['is_text_file'] is True
        assert basic_analysis['file_size'] > 0
        assert 'file_hash_md5' in basic_analysis
        assert 'file_hash_sha256' in basic_analysis
        
        print(f"✅ 基础分析测试通过 - 文件: {result['file_path']}")
    
    @pytest.mark.asyncio
    async def test_analyze_single_file_content(self, setup_test_environment):
        """测试单文件内容分析"""
        env = setup_test_environment
        analyzer = env['file_analyzer']
        
        # 测试文本文件内容分析
        result = await analyzer.analyze_file("test.txt", ["content"])
        
        assert result['success'] is True
        assert 'content' in result['analyses']
        
        content_analysis = result['analyses']['content']
        assert content_analysis['success'] is True
        assert content_analysis['content_length'] > 0
        assert 'text_analysis' in content_analysis
        assert 'document_analysis' in content_analysis
        
        print(f"✅ 内容分析测试通过 - 内容长度: {content_analysis['content_length']}")
    
    @pytest.mark.asyncio
    async def test_analyze_python_file_structure(self, setup_test_environment):
        """测试Python文件结构分析"""
        env = setup_test_environment
        analyzer = env['file_analyzer']
        
        # 测试Python文件结构分析
        result = await analyzer.analyze_file("test.py", ["structure"])
        
        assert result['success'] is True
        assert 'structure' in result['analyses']
        
        structure_analysis = result['analyses']['structure']
        assert structure_analysis['success'] is True
        assert structure_analysis['structure_type'] == 'code'
        
        code_structure = structure_analysis['code_structure']
        assert 'functions' in code_structure
        assert 'classes' in code_structure
        assert 'imports' in code_structure
        assert len(code_structure['functions']) > 0
        assert len(code_structure['classes']) > 0
        
        print(f"✅ Python结构分析测试通过 - 函数数: {len(code_structure['functions'])}, 类数: {len(code_structure['classes'])}")
    
    @pytest.mark.asyncio
    async def test_analyze_config_file_structure(self, setup_test_environment):
        """测试配置文件结构分析"""
        env = setup_test_environment
        analyzer = env['file_analyzer']
        
        # 测试JSON配置文件
        result = await analyzer.analyze_file("test.json", ["structure"])
        
        assert result['success'] is True
        assert 'structure' in result['analyses']
        
        structure_analysis = result['analyses']['structure']
        assert structure_analysis['success'] is True
        assert structure_analysis['structure_type'] == 'configuration'
        assert 'config_analysis' in structure_analysis
        
        print(f"✅ JSON配置文件结构分析测试通过")
        
        # 测试YAML配置文件
        result = await analyzer.analyze_file("config.yaml", ["structure"])
        
        assert result['success'] is True
        structure_analysis = result['analyses']['structure']
        assert structure_analysis['success'] is True
        assert structure_analysis['structure_type'] == 'configuration'
        
        print(f"✅ YAML配置文件结构分析测试通过")
    
    @pytest.mark.asyncio
    async def test_analyze_file_security(self, setup_test_environment):
        """测试文件安全性分析"""
        env = setup_test_environment
        analyzer = env['file_analyzer']
        
        # 测试安全分析
        result = await analyzer.analyze_file("test.txt", ["security"])
        
        assert result['success'] is True
        assert 'security' in result['analyses']
        
        security_analysis = result['analyses']['security']
        assert security_analysis['success'] is True
        assert 'security_checks' in security_analysis
        assert 'security_score' in security_analysis
        assert 'security_level' in security_analysis
        
        security_checks = security_analysis['security_checks']
        assert 'path_safe' in security_checks
        assert 'type_safe' in security_checks
        assert 'size_safe' in security_checks
        assert 'permission_safe' in security_checks
        assert 'is_dangerous' in security_checks
        
        print(f"✅ 安全分析测试通过 - 安全等级: {security_analysis['security_level']}")
    
    @pytest.mark.asyncio
    async def test_comprehensive_analysis(self, setup_test_environment):
        """测试综合分析"""
        env = setup_test_environment
        analyzer = env['file_analyzer']
        
        # 测试综合分析
        result = await analyzer.analyze_file("test.py", ["comprehensive"])
        
        assert result['success'] is True
        assert 'basic' in result['analyses']
        assert 'content' in result['analyses']
        assert 'structure' in result['analyses']
        assert 'security' in result['analyses']
        
        # 验证所有分析都成功
        for analysis_type, analysis_result in result['analyses'].items():
            assert analysis_result['success'] is True, f"{analysis_type} 分析失败"
        
        print(f"✅ 综合分析测试通过 - 包含 {len(result['analyses'])} 种分析类型")
    
    @pytest.mark.asyncio
    async def test_batch_analyze_files(self, setup_test_environment):
        """测试批量文件分析"""
        env = setup_test_environment
        analyzer = env['file_analyzer']
        
        # 测试批量分析
        result = await analyzer.batch_analyze_files(["*.txt", "*.py"], ["basic", "content"])
        
        assert result['success'] is True
        assert result['total_files'] > 0
        assert result['successful_analyses'] > 0
        assert len(result['results']) > 0
        
        # 验证每个结果
        for file_result in result['results']:
            assert file_result['success'] is True
            assert 'basic' in file_result['analyses']
            assert 'content' in file_result['analyses']
        
        print(f"✅ 批量分析测试通过 - 成功分析 {result['successful_analyses']} 个文件")
    
    @pytest.mark.asyncio
    async def test_compare_files(self, setup_test_environment):
        """测试文件比较"""
        env = setup_test_environment
        analyzer = env['file_analyzer']
        
        # 比较相似文件
        result = await analyzer.compare_files("similar1.txt", "similar2.txt")
        
        assert result['success'] is True
        assert result['file1'] == "similar1.txt"
        assert result['file2'] == "similar2.txt"
        assert 'comparisons' in result
        
        comparisons = result['comparisons']
        if 'basic' in comparisons:
            assert 'basic_similarity' in comparisons['basic']
        
        print(f"✅ 文件比较测试通过")
        
        # 比较不同文件
        result2 = await analyzer.compare_files("similar1.txt", "different.txt")
        assert result2['success'] is True
        
        print(f"✅ 不同文件比较测试通过")

    @pytest.mark.asyncio
    async def test_find_similar_files(self, setup_test_environment):
        """测试相似文件查找"""
        env = setup_test_environment
        analyzer = env['file_analyzer']

        # 查找与similar1.txt相似的文件
        result = await analyzer.find_similar_files("similar1.txt", ["*.txt"], 0.3, 5)

        assert result['success'] is True
        assert result['target_file'] == "similar1.txt"
        assert result['total_candidates'] > 0

        # 应该找到similar2.txt作为相似文件
        similar_files = result['similar_files']
        similar_file_paths = [f['file_path'] for f in similar_files]

        print(f"✅ 相似文件查找测试通过 - 找到 {len(similar_files)} 个相似文件")
        print(f"   相似文件: {similar_file_paths}")

    @pytest.mark.asyncio
    async def test_analyze_nonexistent_file(self, setup_test_environment):
        """测试分析不存在的文件"""
        env = setup_test_environment
        analyzer = env['file_analyzer']

        # 测试不存在的文件
        result = await analyzer.analyze_file("nonexistent.txt")

        assert result['success'] is False
        assert "文件不存在" in result['error']

        print(f"✅ 不存在文件处理测试通过")

    @pytest.mark.asyncio
    async def test_analyze_unsafe_file(self, setup_test_environment):
        """测试分析不安全的文件"""
        env = setup_test_environment
        analyzer = env['file_analyzer']

        # 尝试分析工作目录外的文件
        result = await analyzer.analyze_file("../outside.txt")

        assert result['success'] is False
        assert "安全验证失败" in result['error']

        print(f"✅ 不安全文件处理测试通过")

    def test_get_analysis_stats(self, setup_test_environment):
        """测试获取分析统计信息"""
        env = setup_test_environment
        analyzer = env['file_analyzer']

        # 获取初始统计
        stats = analyzer.get_analysis_stats()

        assert 'total_analyzed' in stats
        assert 'successful_analyses' in stats
        assert 'failed_analyses' in stats
        assert 'success_rate' in stats
        assert 'analysis_types' in stats
        assert 'file_types_analyzed' in stats
        assert 'supported_analysis_types' in stats

        print(f"✅ 统计信息获取测试通过")

    def test_reset_stats(self, setup_test_environment):
        """测试重置统计信息"""
        env = setup_test_environment
        analyzer = env['file_analyzer']

        # 重置统计
        analyzer.reset_stats()
        stats = analyzer.get_analysis_stats()

        assert stats['total_analyzed'] == 0
        assert stats['successful_analyses'] == 0
        assert stats['failed_analyses'] == 0
        assert stats['total_analysis_time'] == 0.0

        print(f"✅ 统计信息重置测试通过")

    @pytest.mark.asyncio
    async def test_analyze_large_file(self, setup_test_environment):
        """测试分析大文件"""
        env = setup_test_environment
        analyzer = env['file_analyzer']

        # 分析大文件
        result = await analyzer.analyze_file("large_file.txt", ["basic", "content"])

        assert result['success'] is True
        assert 'basic' in result['analyses']
        assert 'content' in result['analyses']

        basic_analysis = result['analyses']['basic']
        assert basic_analysis['file_size'] > 10000  # 大文件应该超过10KB

        print(f"✅ 大文件分析测试通过 - 文件大小: {basic_analysis['file_size_formatted']}")

    @pytest.mark.asyncio
    async def test_analyze_markdown_file(self, setup_test_environment):
        """测试分析Markdown文件"""
        env = setup_test_environment
        analyzer = env['file_analyzer']

        # 分析Markdown文件
        result = await analyzer.analyze_file("test.md", ["basic", "content", "structure"])

        assert result['success'] is True
        assert 'basic' in result['analyses']
        assert 'content' in result['analyses']
        assert 'structure' in result['analyses']

        # 验证Markdown文件被正确识别
        basic_analysis = result['analyses']['basic']
        assert basic_analysis['file_extension'] == ".md"
        assert basic_analysis['is_text_file'] is True

        # 验证文档结构分析
        structure_analysis = result['analyses']['structure']
        assert structure_analysis['success'] is True
        assert structure_analysis['structure_type'] == 'document'

        print(f"✅ Markdown文件分析测试通过")

    @pytest.mark.asyncio
    async def test_concurrent_analysis(self, setup_test_environment):
        """测试并发分析性能"""
        env = setup_test_environment
        analyzer = env['file_analyzer']

        # 并发分析多个文件
        tasks = []
        test_files = ["test.txt", "test.py", "test.json", "test.md"]

        for file_path in test_files:
            task = analyzer.analyze_file(file_path, ["basic", "content"])
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        # 验证所有分析都成功
        for i, result in enumerate(results):
            assert result['success'] is True, f"文件 {test_files[i]} 分析失败"

        print(f"✅ 并发分析测试通过 - 同时分析 {len(results)} 个文件")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
