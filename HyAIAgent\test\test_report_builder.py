"""
报告生成器测试模块

测试报告生成器的各项功能，包括模板管理、数据源集成、报告生成等。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import pytest
import asyncio
import tempfile
import os
import json
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

from tools.report_builder import (
    ReportBuilder, ReportTemplate, DataSource, ReportConfig, ReportResult,
    ReportFormat, DataSourceType
)


class TestReportBuilder:
    """报告生成器测试类"""
    
    @pytest.fixture
    def builder(self):
        """创建报告生成器实例"""
        return ReportBuilder()
    
    @pytest.fixture
    def sample_template(self):
        """创建示例模板"""
        return ReportTemplate(
            template_id="template_001",
            name="测试报告模板",
            description="用于测试的报告模板",
            template_content="<h1>{{title}}</h1><p>{{content}}</p>",
            format=ReportFormat.HTML,
            variables=["title", "content"],
            sections=["header", "body"]
        )
    
    @pytest.fixture
    def sample_data_source(self):
        """创建示例数据源"""
        return DataSource(
            source_id="source_001",
            name="测试数据源",
            source_type=DataSourceType.JSON,
            connection_config={"file_path": "test_data.json"}
        )
    
    @pytest.fixture
    def sample_report_config(self):
        """创建示例报告配置"""
        return ReportConfig(
            report_id="report_001",
            title="测试报告",
            template_id="template_001",
            data_sources=["source_001"],
            format=ReportFormat.HTML,
            variables={"title": "测试标题", "content": "测试内容"}
        )
    
    @pytest.mark.asyncio
    async def test_init(self, builder):
        """测试初始化"""
        assert builder is not None
        assert builder.templates == {}
        assert builder.data_sources == {}
        assert builder.reports == {}
    
    @pytest.mark.asyncio
    async def test_create_template(self, builder, sample_template):
        """测试创建模板"""
        result = await builder.create_template(sample_template)
        assert result is True
        assert sample_template.template_id in builder.templates
        
        retrieved_template = await builder.get_template(sample_template.template_id)
        assert retrieved_template is not None
        assert retrieved_template.name == sample_template.name
    
    @pytest.mark.asyncio
    async def test_list_templates(self, builder, sample_template):
        """测试列出模板"""
        await builder.create_template(sample_template)
        
        templates = await builder.list_templates()
        assert len(templates) == 1
        assert templates[0].template_id == sample_template.template_id
    
    @pytest.mark.asyncio
    async def test_add_data_source(self, builder, sample_data_source):
        """测试添加数据源"""
        result = await builder.add_data_source(sample_data_source)
        assert result is True
        assert sample_data_source.source_id in builder.data_sources
        
        retrieved_source = await builder.get_data_source(sample_data_source.source_id)
        assert retrieved_source is not None
        assert retrieved_source.name == sample_data_source.name
    
    @pytest.mark.asyncio
    async def test_fetch_json_data(self, builder, sample_data_source):
        """测试获取JSON数据"""
        # 创建临时JSON文件
        test_data = {"key": "value", "number": 123}
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_file = f.name
        
        try:
            # 更新数据源配置
            sample_data_source.connection_config["file_path"] = temp_file
            await builder.add_data_source(sample_data_source)
            
            # 获取数据
            data = await builder.fetch_data_from_source(sample_data_source.source_id)
            assert data == test_data
            
        finally:
            os.unlink(temp_file)
    
    @pytest.mark.asyncio
    async def test_fetch_csv_data(self, builder):
        """测试获取CSV数据"""
        # 创建临时CSV文件
        csv_content = "name,age,city\nAlice,25,New York\nBob,30,London"
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(csv_content)
            temp_file = f.name
        
        try:
            # 创建CSV数据源
            csv_source = DataSource(
                source_id="csv_source",
                name="CSV数据源",
                source_type=DataSourceType.CSV,
                connection_config={"file_path": temp_file}
            )
            await builder.add_data_source(csv_source)
            
            # 获取数据
            data = await builder.fetch_data_from_source(csv_source.source_id)
            assert "data" in data
            assert len(data["data"]) == 2
            assert data["data"][0]["name"] == "Alice"
            
        finally:
            os.unlink(temp_file)
    
    @pytest.mark.asyncio
    async def test_fetch_file_data(self, builder):
        """测试获取文件数据"""
        # 创建临时文件
        file_content = "这是测试文件内容"
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(file_content)
            temp_file = f.name
        
        try:
            # 创建文件数据源
            file_source = DataSource(
                source_id="file_source",
                name="文件数据源",
                source_type=DataSourceType.FILE,
                connection_config={"file_path": temp_file}
            )
            await builder.add_data_source(file_source)
            
            # 获取数据
            data = await builder.fetch_data_from_source(file_source.source_id)
            assert data["content"] == file_content
            assert data["file_path"] == temp_file
            
        finally:
            os.unlink(temp_file)
    
    @pytest.mark.asyncio
    async def test_fetch_api_data(self, builder):
        """测试获取API数据"""
        # 创建API数据源
        api_source = DataSource(
            source_id="api_source",
            name="API数据源",
            source_type=DataSourceType.API,
            connection_config={"url": "https://api.example.com/data"}
        )
        await builder.add_data_source(api_source)
        
        # 获取数据
        data = await builder.fetch_data_from_source(api_source.source_id)
        assert "api_url" in data
        assert data["status"] == "success"
    
    @pytest.mark.asyncio
    async def test_render_template_simple(self, builder, sample_template):
        """测试简单模板渲染"""
        variables = {"title": "测试标题", "content": "测试内容"}
        
        # 模拟Jinja2不可用的情况
        with patch('tools.report_builder.JINJA2_AVAILABLE', False):
            content = await builder._render_template(sample_template, variables)
            assert "测试标题" in content
            assert "测试内容" in content
    
    @pytest.mark.asyncio
    async def test_render_template_jinja2(self, builder, sample_template):
        """测试Jinja2模板渲染"""
        variables = {"title": "测试标题", "content": "测试内容"}
        
        # 模拟Jinja2可用的情况
        with patch('tools.report_builder.JINJA2_AVAILABLE', True):
            with patch('tools.report_builder.Template') as mock_template:
                mock_instance = Mock()
                mock_template.return_value = mock_instance
                mock_instance.render.return_value = "<h1>测试标题</h1><p>测试内容</p>"
                
                content = await builder._render_template(sample_template, variables)
                assert content == "<h1>测试标题</h1><p>测试内容</p>"
    
    @pytest.mark.asyncio
    async def test_save_report_html(self, builder):
        """测试保存HTML报告"""
        content = "<h1>测试报告</h1>"
        
        with tempfile.NamedTemporaryFile(suffix='.html', delete=False) as f:
            temp_file = f.name
        
        try:
            result_path = await builder._save_report(content, temp_file, ReportFormat.HTML)
            assert result_path == temp_file
            
            with open(temp_file, 'r', encoding='utf-8') as f:
                saved_content = f.read()
            assert saved_content == content
            
        finally:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    @pytest.mark.asyncio
    async def test_save_report_txt(self, builder):
        """测试保存文本报告"""
        content = "<h1>测试报告</h1><p>内容</p>"
        
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_file = f.name
        
        try:
            result_path = await builder._save_report(content, temp_file, ReportFormat.TXT)
            assert result_path == temp_file
            
            with open(temp_file, 'r', encoding='utf-8') as f:
                saved_content = f.read()
            # HTML标签应该被移除
            assert "<h1>" not in saved_content
            assert "测试报告" in saved_content
            
        finally:
            if os.path.exists(temp_file):
                os.unlink(temp_file)
    
    @pytest.mark.asyncio
    async def test_generate_report(self, builder, sample_template, sample_data_source, sample_report_config):
        """测试生成报告"""
        # 创建临时数据文件
        test_data = {"title": "数据标题", "content": "数据内容"}
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_file = f.name
        
        try:
            # 设置数据源
            sample_data_source.connection_config["file_path"] = temp_file
            await builder.create_template(sample_template)
            await builder.add_data_source(sample_data_source)
            
            # 生成报告
            result = await builder.generate_report(sample_report_config)
            
            assert result.status == "success"
            assert result.report_id == sample_report_config.report_id
            assert result.format == sample_report_config.format
            assert os.path.exists(result.file_path)
            
            # 清理生成的报告文件
            if os.path.exists(result.file_path):
                os.unlink(result.file_path)
                
        finally:
            os.unlink(temp_file)
    
    @pytest.mark.asyncio
    async def test_create_report_config(self, builder, sample_report_config):
        """测试创建报告配置"""
        result = await builder.create_report_config(sample_report_config)
        assert result is True
        assert sample_report_config.report_id in builder.reports
        
        retrieved_config = await builder.get_report_config(sample_report_config.report_id)
        assert retrieved_config is not None
        assert retrieved_config.title == sample_report_config.title
    
    @pytest.mark.asyncio
    async def test_list_reports(self, builder, sample_report_config):
        """测试列出报告配置"""
        await builder.create_report_config(sample_report_config)
        
        reports = await builder.list_reports()
        assert len(reports) == 1
        assert reports[0].report_id == sample_report_config.report_id
    
    @pytest.mark.asyncio
    async def test_update_report_config(self, builder, sample_report_config):
        """测试更新报告配置"""
        await builder.create_report_config(sample_report_config)
        
        updates = {"title": "更新后的标题"}
        result = await builder.update_report_config(sample_report_config.report_id, updates)
        assert result is True
        
        updated_config = await builder.get_report_config(sample_report_config.report_id)
        assert updated_config.title == "更新后的标题"
    
    @pytest.mark.asyncio
    async def test_delete_report_config(self, builder, sample_report_config):
        """测试删除报告配置"""
        await builder.create_report_config(sample_report_config)
        
        result = await builder.delete_report_config(sample_report_config.report_id)
        assert result is True
        assert sample_report_config.report_id not in builder.reports
    
    @pytest.mark.asyncio
    async def test_schedule_report(self, builder, sample_report_config):
        """测试调度报告"""
        await builder.create_report_config(sample_report_config)
        
        result = await builder.schedule_report(sample_report_config.report_id, 1800)
        assert result is True
        
        config = await builder.get_report_config(sample_report_config.report_id)
        assert config.auto_refresh is True
        assert config.refresh_interval == 1800
    
    @pytest.mark.asyncio
    async def test_get_report_status(self, builder, sample_report_config):
        """测试获取报告状态"""
        await builder.create_report_config(sample_report_config)
        
        status = await builder.get_report_status(sample_report_config.report_id)
        assert status["report_id"] == sample_report_config.report_id
        assert status["title"] == sample_report_config.title
        assert status["status"] == "inactive"
    
    @pytest.mark.asyncio
    async def test_validate_template(self, builder, sample_template):
        """测试验证模板"""
        result = await builder.validate_template(sample_template)
        assert result["valid"] is True
        assert len(result["errors"]) == 0
        
        # 测试空模板
        empty_template = ReportTemplate(
            template_id="empty",
            name="空模板",
            description="",
            template_content="",
            format=ReportFormat.HTML
        )
        
        result = await builder.validate_template(empty_template)
        assert result["valid"] is False
        assert len(result["errors"]) > 0
    
    @pytest.mark.asyncio
    async def test_get_available_formats(self, builder):
        """测试获取可用格式"""
        formats = await builder.get_available_formats()
        assert isinstance(formats, list)
        assert "html" in formats
        assert "pdf" in formats
        assert "markdown" in formats
    
    @pytest.mark.asyncio
    async def test_get_template_variables(self, builder, sample_template):
        """测试获取模板变量"""
        await builder.create_template(sample_template)
        
        variables = await builder.get_template_variables(sample_template.template_id)
        assert "title" in variables
        assert "content" in variables
    
    @pytest.mark.asyncio
    async def test_preview_report(self, builder, sample_template, sample_report_config):
        """测试预览报告"""
        await builder.create_template(sample_template)
        
        sample_data = {"source_001": {"preview": True, "data": "预览数据"}}
        content = await builder.preview_report(sample_report_config, sample_data)
        
        assert "测试标题" in content
        assert "测试内容" in content
