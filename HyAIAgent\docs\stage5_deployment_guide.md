# HyAIAgent 第五阶段部署指南

## 概述

本指南详细说明了如何在不同环境中部署HyAIAgent第五阶段版本，包括开发环境、测试环境和生产环境的部署配置。

## 目录

1. [环境要求](#环境要求)
2. [开发环境部署](#开发环境部署)
3. [测试环境部署](#测试环境部署)
4. [生产环境部署](#生产环境部署)
5. [Docker部署](#docker部署)
6. [云平台部署](#云平台部署)
7. [监控与维护](#监控与维护)
8. [故障排除](#故障排除)

## 环境要求

### 基础要求

| 组件 | 最低要求 | 推荐配置 |
|------|----------|----------|
| **操作系统** | Windows 10, macOS 10.15, Ubuntu 18.04 | Windows 11, macOS 12+, Ubuntu 20.04+ |
| **Python** | 3.8.0 | 3.9+ |
| **内存** | 4GB RAM | 8GB+ RAM |
| **存储** | 2GB 可用空间 | 10GB+ SSD |
| **网络** | 稳定互联网连接 | 高速宽带 |

### 依赖软件

```bash
# Python包管理器
pip >= 21.0

# 可选：虚拟环境管理
conda >= 4.10 或 virtualenv >= 20.0

# 可选：容器化部署
Docker >= 20.10
Docker Compose >= 1.29
```

## 开发环境部署

### 1. 环境准备

```bash
# 克隆项目
git clone https://github.com/your-org/HyAIAgent.git
cd HyAIAgent

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

### 2. 安装依赖

```bash
# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements-dev.txt
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
nano .env
```

**环境变量配置示例**:
```env
# AI服务配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# 数据库配置
KV_DATABASE_PATH=data/dev_kv_store.json

# 日志配置
LOG_LEVEL=DEBUG
LOG_FILE=logs/dev_hyaiagent.log

# 应用配置
APP_ENV=development
APP_DEBUG=true
```

### 4. 初始化数据

```bash
# 创建必要目录
mkdir -p data logs temp

# 初始化数据库
python scripts/init_database.py

# 运行数据库迁移（如果有）
python scripts/migrate.py
```

### 5. 启动开发服务器

```bash
# 启动应用
python main.py

# 或使用开发模式
python main.py --dev
```

### 6. 验证部署

```bash
# 运行测试套件
python -m pytest test/ -v

# 检查系统状态
python scripts/health_check.py
```

## 测试环境部署

### 1. 环境配置

```bash
# 使用测试配置
cp .env.test .env

# 安装测试依赖
pip install -r requirements-test.txt
```

### 2. 测试数据准备

```bash
# 创建测试数据
python scripts/create_test_data.py

# 运行数据验证
python scripts/validate_test_data.py
```

### 3. 自动化测试

```bash
# 运行完整测试套件
python -m pytest test/ --cov=. --cov-report=html

# 运行性能测试
python -m pytest test/performance/ -v

# 运行集成测试
python -m pytest test/integration/ -v
```

### 4. 测试环境监控

```bash
# 启动监控
python scripts/start_monitoring.py --env=test

# 查看监控数据
python scripts/view_metrics.py
```

## 生产环境部署

### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y python3.9 python3.9-venv python3-pip nginx supervisor

# 创建应用用户
sudo useradd -m -s /bin/bash hyaiagent
sudo usermod -aG sudo hyaiagent
```

### 2. 应用部署

```bash
# 切换到应用用户
sudo su - hyaiagent

# 部署应用
git clone https://github.com/your-org/HyAIAgent.git /opt/hyaiagent
cd /opt/hyaiagent

# 创建生产环境
python3.9 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### 3. 生产配置

```bash
# 生产环境变量
cp .env.production .env

# 编辑生产配置
nano .env
```

**生产环境配置**:
```env
# 生产环境标识
APP_ENV=production
APP_DEBUG=false

# 安全配置
SECRET_KEY=your_very_secure_secret_key_here
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# 数据库配置
KV_DATABASE_PATH=/opt/hyaiagent/data/prod_kv_store.json

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/var/log/hyaiagent/app.log

# 性能配置
WORKERS=4
MAX_REQUESTS=1000
TIMEOUT=30
```

### 4. 系统服务配置

**Supervisor配置** (`/etc/supervisor/conf.d/hyaiagent.conf`):
```ini
[program:hyaiagent]
command=/opt/hyaiagent/venv/bin/python /opt/hyaiagent/main.py
directory=/opt/hyaiagent
user=hyaiagent
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/hyaiagent/supervisor.log
environment=PATH="/opt/hyaiagent/venv/bin"
```

**Nginx配置** (`/etc/nginx/sites-available/hyaiagent`):
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /static/ {
        alias /opt/hyaiagent/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 5. 启动服务

```bash
# 启动Supervisor
sudo systemctl enable supervisor
sudo systemctl start supervisor
sudo supervisorctl reread
sudo supervisorctl update

# 启动Nginx
sudo systemctl enable nginx
sudo systemctl start nginx

# 启动应用
sudo supervisorctl start hyaiagent
```

### 6. SSL证书配置

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 设置自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Docker部署

### 1. Dockerfile

```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data logs temp

# 设置环境变量
ENV PYTHONPATH=/app
ENV APP_ENV=production

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python scripts/health_check.py || exit 1

# 启动命令
CMD ["python", "main.py"]
```

### 2. Docker Compose

```yaml
version: '3.8'

services:
  hyaiagent:
    build: .
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=production
      - LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "scripts/health_check.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - hyaiagent
    restart: unless-stopped

volumes:
  data:
  logs:
```

### 3. 构建和运行

```bash
# 构建镜像
docker build -t hyaiagent:latest .

# 运行容器
docker-compose up -d

# 查看日志
docker-compose logs -f hyaiagent

# 停止服务
docker-compose down
```

## 云平台部署

### AWS部署

```bash
# 安装AWS CLI
pip install awscli

# 配置AWS凭证
aws configure

# 创建EC2实例
aws ec2 run-instances \
    --image-id ami-0abcdef1234567890 \
    --count 1 \
    --instance-type t3.medium \
    --key-name your-key-pair \
    --security-group-ids sg-903004f8 \
    --subnet-id subnet-6e7f829e
```

### Azure部署

```bash
# 安装Azure CLI
pip install azure-cli

# 登录Azure
az login

# 创建资源组
az group create --name HyAIAgentRG --location eastus

# 创建虚拟机
az vm create \
    --resource-group HyAIAgentRG \
    --name HyAIAgentVM \
    --image UbuntuLTS \
    --admin-username azureuser \
    --generate-ssh-keys
```

## 监控与维护

### 1. 系统监控

```bash
# 安装监控工具
pip install prometheus-client grafana-api

# 启动监控
python scripts/start_monitoring.py --port=9090
```

### 2. 日志管理

```bash
# 日志轮转配置
sudo nano /etc/logrotate.d/hyaiagent

# 内容：
/var/log/hyaiagent/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 hyaiagent hyaiagent
    postrotate
        supervisorctl restart hyaiagent
    endscript
}
```

### 3. 备份策略

```bash
# 创建备份脚本
cat > /opt/hyaiagent/scripts/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/hyaiagent"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据
tar -czf $BACKUP_DIR/data_$DATE.tar.gz /opt/hyaiagent/data/
tar -czf $BACKUP_DIR/config_$DATE.tar.gz /opt/hyaiagent/config/

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
EOF

chmod +x /opt/hyaiagent/scripts/backup.sh

# 设置定时备份
crontab -e
# 添加: 0 2 * * * /opt/hyaiagent/scripts/backup.sh
```

### 4. 性能优化

```bash
# 系统优化
echo 'vm.swappiness=10' >> /etc/sysctl.conf
echo 'net.core.somaxconn=65535' >> /etc/sysctl.conf
sysctl -p

# 应用优化
export PYTHONOPTIMIZE=1
export PYTHONDONTWRITEBYTECODE=1
```

## 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查日志
   sudo supervisorctl tail hyaiagent stderr
   
   # 检查配置
   python scripts/validate_config.py
   ```

2. **内存使用过高**
   ```bash
   # 监控内存使用
   python scripts/memory_profiler.py
   
   # 优化配置
   export PYTHONMALLOC=malloc
   ```

3. **数据库连接问题**
   ```bash
   # 检查数据库文件权限
   ls -la data/
   
   # 修复权限
   chown -R hyaiagent:hyaiagent data/
   ```

### 紧急恢复

```bash
# 快速恢复脚本
cat > /opt/hyaiagent/scripts/emergency_recovery.sh << 'EOF'
#!/bin/bash
echo "开始紧急恢复..."

# 停止服务
supervisorctl stop hyaiagent

# 恢复最新备份
LATEST_BACKUP=$(ls -t /backup/hyaiagent/data_*.tar.gz | head -1)
tar -xzf $LATEST_BACKUP -C /

# 重启服务
supervisorctl start hyaiagent

echo "恢复完成"
EOF
```

## 安全建议

1. **定期更新依赖包**
2. **使用强密码和密钥**
3. **启用防火墙**
4. **定期备份数据**
5. **监控异常访问**
6. **使用HTTPS**

---

**部署完成后，请确保进行充分的测试以验证所有功能正常工作。**
