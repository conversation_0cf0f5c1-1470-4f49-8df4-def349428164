"""
上下文智能搜索模块测试

测试上下文智能搜索系统的各个组件功能，包括：
1. 用户意图分类测试
2. 上下文分析测试
3. 上下文搜索管理器测试
4. 搜索结果重新排序测试
5. 会话管理测试

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, List, Any

# 导入被测试的模块
from operations.context_search import (
    ContextualSearchManager, IntentClassifier, ContextAnalyzer,
    UserIntent, ContextType, ConversationContext, SearchContext,
    ContextualSearchResult
)
from operations.search_operations import SearchOperations, SearchResponse, SearchResult
from operations.multi_round_search import MultiRoundSearchManager, SearchStrategy, MultiRoundSearchResult
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager
from core.kv_store import KVStore

class TestIntentClassifier:
    """意图分类器测试类"""
    
    @pytest.fixture
    def classifier(self):
        """创建意图分类器实例"""
        return IntentClassifier()
    
    def test_classify_information_seeking(self, classifier):
        """测试信息查找意图分类"""
        queries = [
            "什么是机器学习",
            "What is artificial intelligence",
            "如何学习Python编程",
            "为什么要使用Docker"
        ]
        
        for query in queries:
            intent, confidence = classifier.classify_intent(query)
            assert intent == UserIntent.INFORMATION_SEEKING
            assert confidence > 0.0
            
        print("✅ 信息查找意图分类测试通过")
    
    def test_classify_problem_solving(self, classifier):
        """测试问题解决意图分类"""
        queries = [
            "如何解决Python导入错误",
            "How to fix database connection problem",
            "修复网络连接故障",
            "解决内存泄漏问题"
        ]
        
        for query in queries:
            intent, confidence = classifier.classify_intent(query)
            assert intent == UserIntent.PROBLEM_SOLVING
            assert confidence > 0.0
            
        print("✅ 问题解决意图分类测试通过")
    
    def test_classify_comparison(self, classifier):
        """测试比较分析意图分类"""
        queries = [
            "Python vs Java哪个更好",
            "Compare React and Vue.js",
            "MySQL和PostgreSQL的区别",
            "iOS vs Android优缺点"
        ]
        
        for query in queries:
            intent, confidence = classifier.classify_intent(query)
            assert intent == UserIntent.COMPARISON
            assert confidence > 0.0
            
        print("✅ 比较分析意图分类测试通过")
    
    def test_classify_tutorial(self, classifier):
        """测试教程学习意图分类"""
        queries = [
            "Python机器学习教程",
            "React tutorial for beginners",
            "学习Docker的步骤",
            "JavaScript入门指南"
        ]
        
        for query in queries:
            intent, confidence = classifier.classify_intent(query)
            assert intent == UserIntent.TUTORIAL
            assert confidence > 0.0
            
        print("✅ 教程学习意图分类测试通过")
    
    def test_classify_news_update(self, classifier):
        """测试新闻更新意图分类"""
        queries = [
            "最新的AI技术发展",
            "Latest Python updates",
            "今天的科技新闻",
            "Recent developments in blockchain"
        ]
        
        for query in queries:
            intent, confidence = classifier.classify_intent(query)
            assert intent == UserIntent.NEWS_UPDATE
            assert confidence > 0.0
            
        print("✅ 新闻更新意图分类测试通过")
    
    def test_classify_with_context(self, classifier):
        """测试带上下文的意图分类"""
        # 创建对话上下文
        context = ConversationContext(
            conversation_id="test_conv",
            intent_history=[UserIntent.TUTORIAL, UserIntent.TUTORIAL]
        )
        
        query = "更多相关内容"
        intent, confidence = classifier.classify_intent(query, context)
        
        # 应该受到历史意图影响
        assert intent == UserIntent.TUTORIAL
        assert confidence > 0.0
        
        print("✅ 带上下文的意图分类测试通过")

class TestContextAnalyzer:
    """上下文分析器测试类"""
    
    @pytest.fixture
    def analyzer(self):
        """创建上下文分析器实例"""
        return ContextAnalyzer()
    
    def test_extract_entities(self, analyzer):
        """测试实体提取"""
        text = "John Smith works at Microsoft Corp in Seattle. Contact <NAME_EMAIL> or ************."
        entities = analyzer.extract_entities(text)
        
        assert "person" in entities
        assert "organization" in entities
        assert "email" in entities
        assert "phone" in entities
        
        print(f"✅ 实体提取测试通过 - 提取了{len(entities)}种类型的实体")
    
    def test_identify_topics(self, analyzer):
        """测试主题识别"""
        text = "Python machine learning algorithms for data science and artificial intelligence research"
        topics = analyzer.identify_topics(text)
        
        assert len(topics) > 0
        assert any(topic[0] == "technology" for topic in topics)
        
        print(f"✅ 主题识别测试通过 - 识别了{len(topics)}个主题")
    
    def test_analyze_conversation_context(self, analyzer):
        """测试对话上下文分析"""
        messages = [
            {"role": "user", "content": "我想学习Python机器学习"},
            {"role": "assistant", "content": "Python是机器学习的热门语言"},
            {"role": "user", "content": "有什么好的教程推荐吗？"}
        ]
        
        context = analyzer.analyze_conversation_context(messages)
        
        assert context.conversation_id is not None
        assert len(context.messages) == 3
        assert len(context.topics) > 0
        assert len(context.keywords) > 0
        
        print("✅ 对话上下文分析测试通过")

class TestContextualSearchManager:
    """上下文搜索管理器测试类"""
    
    @pytest.fixture
    def search_manager(self):
        """创建上下文搜索管理器实例"""
        # 创建模拟依赖
        config_manager = Mock(spec=ConfigManager)
        search_operations = Mock(spec=SearchOperations)
        multi_round_manager = Mock(spec=MultiRoundSearchManager)
        security_manager = Mock(spec=SecurityManager)
        kv_store = Mock(spec=KVStore)
        kv_store.set = AsyncMock()
        
        # 配置模拟对象
        multi_round_manager.execute_multi_round_search = AsyncMock(
            return_value=MultiRoundSearchResult(
                session_id="test_session",
                original_query="test query",
                total_rounds=2,
                search_rounds=[],
                final_results=[
                    SearchResult(
                        title="Python机器学习教程",
                        url="https://example.com/ml-tutorial",
                        content="Python机器学习的完整教程，包含理论和实践",
                        score=0.8,
                        published_date="2023-12-01T10:00:00Z",
                        source="tavily"
                    )
                ],
                overall_quality=0.8,
                total_execution_time=2.5,
                strategy_used=SearchStrategy.PROGRESSIVE,
                success=True
            )
        )
        
        return ContextualSearchManager(
            config_manager=config_manager,
            search_operations=search_operations,
            multi_round_manager=multi_round_manager,
            security_manager=security_manager,
            kv_store=kv_store
        )
    
    @pytest.mark.asyncio
    async def test_execute_contextual_search_basic(self, search_manager):
        """测试基本上下文搜索"""
        query = "Python机器学习教程"
        
        result = await search_manager.execute_contextual_search(query)
        
        assert result.success is True
        assert result.original_query == query
        assert result.detected_intent is not None
        assert len(result.search_results) > 0
        assert result.execution_time >= 0
        
        print("✅ 基本上下文搜索测试通过")
    
    @pytest.mark.asyncio
    async def test_execute_contextual_search_with_conversation(self, search_manager):
        """测试带对话上下文的搜索"""
        query = "有什么好的教程推荐"
        conversation_messages = [
            {"role": "user", "content": "我想学习Python机器学习"},
            {"role": "assistant", "content": "Python是机器学习的热门语言"}
        ]
        
        result = await search_manager.execute_contextual_search(
            query=query,
            conversation_messages=conversation_messages
        )
        
        assert result.success is True
        assert result.optimized_query != query  # 查询应该被优化
        assert result.detected_intent == UserIntent.TUTORIAL
        assert result.context_relevance >= 0.0
        
        print("✅ 带对话上下文的搜索测试通过")
    
    @pytest.mark.asyncio
    async def test_execute_contextual_search_with_preferences(self, search_manager):
        """测试带用户偏好的搜索"""
        query = "机器学习算法"
        preferences = {
            "preferred_sources": ["github.com"],
            "language": "zh-CN"
        }
        
        result = await search_manager.execute_contextual_search(
            query=query,
            search_preferences=preferences,
            user_id="test_user"
        )
        
        assert result.success is True
        assert "has_preferences" in result.context_factors
        assert result.context_factors["has_preferences"] == 1.0
        
        print("✅ 带用户偏好的搜索测试通过")
    
    def test_select_search_strategy(self, search_manager):
        """测试搜索策略选择"""
        test_cases = [
            (UserIntent.INFORMATION_SEEKING, SearchStrategy.PROGRESSIVE),
            (UserIntent.PROBLEM_SOLVING, SearchStrategy.FOCUSED),
            (UserIntent.COMPARISON, SearchStrategy.PARALLEL),
            (UserIntent.RESEARCH, SearchStrategy.EXPLORATORY),
            (UserIntent.NEWS_UPDATE, SearchStrategy.ADAPTIVE)
        ]
        
        for intent, expected_strategy in test_cases:
            search_context = SearchContext(session_id="test", intent=intent)
            strategy = search_manager._select_search_strategy(intent, search_context)
            assert strategy == expected_strategy
        
        print("✅ 搜索策略选择测试通过")
    
    def test_get_max_rounds_for_intent(self, search_manager):
        """测试意图对应的最大轮数"""
        test_cases = [
            (UserIntent.RESEARCH, 4),
            (UserIntent.PROBLEM_SOLVING, 3),
            (UserIntent.COMPARISON, 3),
            (UserIntent.INFORMATION_SEEKING, 2),
            (UserIntent.TUTORIAL, 2)
        ]
        
        for intent, expected_rounds in test_cases:
            rounds = search_manager._get_max_rounds_for_intent(intent)
            assert rounds == expected_rounds
        
        print("✅ 意图最大轮数测试通过")
    
    @pytest.mark.asyncio
    async def test_search_history_management(self, search_manager):
        """测试搜索历史管理"""
        user_id = "test_user"
        
        # 执行几次搜索
        for i in range(3):
            await search_manager.execute_contextual_search(
                query=f"test query {i}",
                user_id=user_id
            )
        
        # 获取搜索历史
        history = await search_manager.get_search_history(user_id, limit=5)
        
        assert len(history) >= 0  # 可能为0，因为是模拟环境
        
        print("✅ 搜索历史管理测试通过")
    
    @pytest.mark.asyncio
    async def test_cleanup_old_sessions(self, search_manager):
        """测试清理旧会话"""
        # 添加一些测试会话数据
        old_time = (datetime.now() - timedelta(hours=25)).isoformat()
        search_manager.search_sessions["old_session"] = {
            "created_at": old_time
        }
        
        recent_time = datetime.now().isoformat()
        search_manager.search_sessions["recent_session"] = {
            "created_at": recent_time
        }
        
        # 清理旧会话
        cleaned_count = await search_manager.cleanup_old_sessions(max_age_hours=24)
        
        assert cleaned_count >= 0
        assert "recent_session" in search_manager.search_sessions
        
        print("✅ 清理旧会话测试通过")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
