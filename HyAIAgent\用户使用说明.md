# HyAIAgent 用户使用说明

## 📖 简介

HyAIAgent 是一个基于 PyQt6 开发的 AI 聊天助手应用程序，支持与 OpenAI 兼容的 API 接口进行对话。该应用提供了友好的图形界面，支持对话历史管理、配置自定义和提示词模板等功能。

## 🚀 快速开始

### 1. 环境要求

- **Python 版本**: 3.8 或更高版本
- **操作系统**: Windows、macOS、Linux
- **内存**: 建议 4GB 以上
- **硬盘空间**: 至少 100MB 可用空间

### 2. 安装依赖

在项目根目录下运行以下命令安装所需依赖：

```bash
pip install -r requirements.txt
```

### 3. 配置 API 密钥

#### 方法一：创建 .env 文件（推荐）

在项目根目录创建 `.env` 文件，添加以下内容：

```env
# OpenAI API 配置
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# 或者使用其他兼容的 API 服务
# OPENAI_API_KEY=your_deepseek_key
# OPENAI_BASE_URL=https://api.deepseek.com/v1
```

#### 方法二：修改配置文件

编辑 `config.json` 文件中的 AI 配置部分：

```json
{
  "ai": {
    "default_provider": "openai",
    "providers": {
      "openai": {
        "api_key": "your_api_key_here",
        "base_url": "https://api.openai.com/v1",
        "model": "gpt-3.5-turbo",
        "max_tokens": 1000,
        "temperature": 0.7
      }
    }
  }
}
```

### 4. 启动应用

运行以下命令启动 HyAIAgent：

```bash
python main.py
```

## 🎯 主要功能

### 1. AI 对话

- **发送消息**: 在输入框中输入问题，点击"发送"按钮或按回车键
- **查看回复**: AI 的回复会显示在聊天区域中
- **对话历史**: 应用会自动保存对话历史，支持上下文连续对话

### 2. 对话管理

- **新建对话**: 点击菜单栏"文件" → "新建对话"清空当前对话
- **保存对话**: 点击菜单栏"文件" → "保存对话"将当前对话保存到数据库
- **自动保存**: 可在设置中开启自动保存功能

### 3. 设置配置

点击菜单栏"设置" → "首选项"可以配置：

- **AI 模型**: 选择使用的 AI 模型（gpt-3.5-turbo、gpt-4 等）
- **最大 Token 数**: 控制 AI 回复的最大长度
- **温度参数**: 控制 AI 回复的创造性（0.0-2.0）
- **字体大小**: 调整界面字体大小
- **自动保存**: 开启/关闭对话自动保存功能

### 4. 提示词模板

应用支持使用 Jinja2 模板语法的提示词：

- **全局提示词**: 存放在 `prompts/global/` 目录
- **模板提示词**: 存放在 `prompts/templates/` 目录
- **动态渲染**: 支持变量替换和条件逻辑

## 📁 目录结构

```
HyAIAgent/
├── main.py                 # 主程序入口
├── config.json            # 配置文件
├── requirements.txt       # 依赖列表
├── .env                   # 环境变量（需要创建）
├── core/                  # 核心模块
│   ├── config_manager.py  # 配置管理
│   ├── ai_client.py       # AI 客户端
│   ├── kv_store.py        # 键值数据库
│   └── prompt_manager.py  # 提示词管理
├── ui/                    # 用户界面
│   └── chat_window.py     # 聊天窗口
├── data/                  # 数据存储
│   └── kv_store.json      # 数据库文件
├── logs/                  # 日志文件
├── prompts/               # 提示词模板
│   ├── global/           # 全局提示词
│   └── templates/        # 模板提示词
└── 开发指引文件/          # 开发文档
```

## ⚙️ 配置说明

### AI 提供商配置

支持多个 AI 提供商，包括：

- **OpenAI**: 官方 GPT 模型
- **DeepSeek**: 国产 AI 模型
- **其他兼容 OpenAI API 的服务**

### 界面配置

- **主题**: 使用 Fusion 样式
- **字体**: 默认使用 Microsoft YaHei
- **布局**: 上下分割布局，聊天区域和输入区域

### 日志配置

- **控制台输出**: INFO 级别及以上
- **文件输出**: DEBUG 级别及以上
- **日志轮转**: 单文件最大 10MB，保留 7 天
- **压缩**: 自动压缩旧日志文件

## 🔧 故障排除

### 常见问题

#### 1. 启动失败

**问题**: 应用无法启动或报错
**解决方案**:
- 检查 Python 版本是否为 3.8+
- 确认所有依赖已正确安装
- 查看 `logs/` 目录下的日志文件

#### 2. API 连接失败

**问题**: 无法连接到 AI 服务
**解决方案**:
- 检查 API 密钥是否正确配置
- 确认网络连接正常
- 验证 API 服务地址是否可访问

#### 3. 界面显示异常

**问题**: 界面布局错乱或显示不正常
**解决方案**:
- 检查系统 DPI 设置
- 尝试重启应用程序
- 重置配置文件到默认状态

### 日志查看

应用运行时会生成详细的日志信息：

- **位置**: `logs/hyaiagent.log`
- **级别**: DEBUG、INFO、WARNING、ERROR
- **格式**: 时间戳 | 级别 | 模块:函数:行号 - 消息

## 📞 技术支持

如果遇到问题或需要帮助，请：

1. 查看日志文件获取详细错误信息
2. 检查配置文件是否正确
3. 确认网络连接和 API 服务状态
4. 参考本文档的故障排除部分

## 🔄 更新说明

### 版本 1.0.0

- ✅ 基础 AI 对话功能
- ✅ PyQt6 图形界面
- ✅ 配置管理系统
- ✅ 对话历史保存
- ✅ 提示词模板支持
- ✅ 多 AI 提供商支持
- ✅ 完整的日志系统

---

**感谢使用 HyAIAgent！**
