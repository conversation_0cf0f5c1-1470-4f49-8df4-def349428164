"""
HyAIAgent 第四阶段 - 搜索缓存测试
测试搜索缓存机制的功能
"""

import asyncio
import pytest
import json
import tempfile
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

# 导入被测试的模块
from operations.search_cache import (
    SearchCache,
    CacheEntry,
    CacheStats
)
from operations.search_operations import SearchResult, SearchResponse
from operations.content_processor import ProcessedContent, ProcessedResponse
from core.config_manager import ConfigManager
from operations.security_manager import SecurityManager
from core.kv_store import KVStore


class TestSearchCache:
    """搜索缓存测试"""
    
    @pytest.fixture
    def temp_config_file(self):
        """创建临时配置文件"""
        config_data = {
            "search": {
                "cache": {
                    "max_entries": 100,
                    "default_ttl_seconds": 3600,
                    "max_size_bytes": 10 * 1024 * 1024,  # 10MB
                    "cleanup_interval_seconds": 60,
                    "enable_persistent": True
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            temp_file = f.name
        
        yield temp_file
        
        # 清理临时文件
        os.unlink(temp_file)
    
    @pytest.fixture
    def temp_db_file(self):
        """创建临时数据库文件"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            temp_file = f.name
        
        yield temp_file
        
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)
    
    @pytest.fixture
    def config_manager(self, temp_config_file):
        """创建配置管理器"""
        return ConfigManager(config_path=temp_config_file)
    
    @pytest.fixture
    def kv_store(self, temp_db_file):
        """创建键值存储"""
        store = KVStore(db_path=temp_db_file)
        yield store
        store.close()
    
    @pytest.fixture
    def cache(self, config_manager, kv_store):
        """创建搜索缓存"""
        cache = SearchCache(
            config_manager=config_manager,
            kv_store=kv_store
        )
        yield cache
        # 注意：这里不能使用await，因为fixture不是async的
        # 在实际使用中，应该在测试结束时手动调用close()
    
    def test_cache_entry_creation(self):
        """测试缓存条目创建"""
        now = datetime.now()
        entry = CacheEntry(
            key="test_key",
            data={"test": "data"},
            created_at=now,
            expires_at=now + timedelta(hours=1),
            access_count=1,
            last_accessed=now,
            cache_type="search",
            size_bytes=100
        )
        
        assert entry.key == "test_key"
        assert entry.data == {"test": "data"}
        assert entry.cache_type == "search"
        assert entry.size_bytes == 100
        assert not entry.is_expired()
        assert entry.is_valid()
    
    def test_cache_entry_expiration(self):
        """测试缓存条目过期"""
        now = datetime.now()
        # 创建已过期的条目
        entry = CacheEntry(
            key="expired_key",
            data={"test": "data"},
            created_at=now - timedelta(hours=2),
            expires_at=now - timedelta(hours=1),
            access_count=1,
            last_accessed=now - timedelta(hours=1),
            cache_type="search",
            size_bytes=100
        )
        
        assert entry.is_expired()
        assert not entry.is_valid()
    
    def test_cache_entry_serialization(self):
        """测试缓存条目序列化"""
        now = datetime.now()
        entry = CacheEntry(
            key="test_key",
            data={"test": "data"},
            created_at=now,
            expires_at=now + timedelta(hours=1),
            access_count=1,
            last_accessed=now,
            cache_type="search",
            size_bytes=100
        )
        
        # 转换为字典
        entry_dict = entry.to_dict()
        assert isinstance(entry_dict, dict)
        assert entry_dict["key"] == "test_key"
        assert isinstance(entry_dict["created_at"], str)
        
        # 从字典重建
        rebuilt_entry = CacheEntry.from_dict(entry_dict)
        assert rebuilt_entry.key == entry.key
        assert rebuilt_entry.data == entry.data
        assert rebuilt_entry.created_at == entry.created_at
    
    @pytest.mark.asyncio
    async def test_cache_initialization(self, cache):
        """测试缓存初始化"""
        assert cache.config_manager is not None
        assert cache.cache_config is not None
        assert cache.max_entries == 100
        assert cache.default_ttl == 3600
        assert cache.enable_persistent is True
        assert isinstance(cache.memory_cache, dict)
        assert len(cache.memory_cache) == 0
    
    def test_generate_cache_key(self, cache):
        """测试缓存键生成"""
        # 相同查询和参数应该生成相同的键
        key1 = cache._generate_cache_key("python programming", max_results=5, search_depth="basic")
        key2 = cache._generate_cache_key("python programming", max_results=5, search_depth="basic")
        assert key1 == key2
        
        # 不同查询应该生成不同的键
        key3 = cache._generate_cache_key("java programming", max_results=5, search_depth="basic")
        assert key1 != key3
        
        # 不同参数应该生成不同的键
        key4 = cache._generate_cache_key("python programming", max_results=10, search_depth="basic")
        assert key1 != key4
        
        # 键应该是32位MD5哈希
        assert len(key1) == 32
        assert key1.isalnum()
    
    def test_calculate_size(self, cache):
        """测试数据大小计算"""
        # 测试字典
        dict_data = {"key": "value", "number": 123}
        dict_size = cache._calculate_size(dict_data)
        assert dict_size > 0
        
        # 测试字符串
        str_data = "Hello, World!"
        str_size = cache._calculate_size(str_data)
        assert str_size > 0
        
        # 测试中文字符串
        chinese_data = "你好，世界！"
        chinese_size = cache._calculate_size(chinese_data)
        assert chinese_size > len(chinese_data)  # 中文字符占用更多字节
    
    @pytest.mark.asyncio
    async def test_search_result_caching(self, cache):
        """测试搜索结果缓存"""
        # 创建测试搜索结果
        search_results = [
            SearchResult(
                title="Test Result 1",
                url="https://example1.com",
                content="Test content 1",
                score=0.9
            ),
            SearchResult(
                title="Test Result 2",
                url="https://example2.com",
                content="Test content 2",
                score=0.8
            )
        ]
        
        search_response = SearchResponse(
            query="test query",
            results=search_results,
            total_results=2,
            search_time=1.5,
            timestamp=datetime.now(),
            search_depth="basic"
        )
        
        # 设置缓存
        success = await cache.set_search_result("test query", search_response, max_results=5)
        assert success is True
        
        # 获取缓存
        cached_result = await cache.get_search_result("test query", max_results=5)
        assert cached_result is not None
        assert cached_result.query == "test query"
        assert len(cached_result.results) == 2
        assert cached_result.total_results == 2
        
        # 验证统计信息
        stats = await cache.get_cache_stats()
        assert stats.total_entries == 1
        assert stats.hit_count == 1
        assert stats.miss_count == 0
    
    @pytest.mark.asyncio
    async def test_processed_result_caching(self, cache):
        """测试处理结果缓存"""
        # 创建测试处理结果
        processed_contents = [
            ProcessedContent(
                original_url="https://example1.com",
                title="Test Title 1",
                cleaned_content="Cleaned content 1",
                summary="Summary 1",
                keywords=["test", "content"],
                entities=[],
                content_type="blog",
                language="en",
                word_count=100,
                reading_time=1,
                quality_score=0.8,
                timestamp=datetime.now()
            )
        ]
        
        processed_response = ProcessedResponse(
            original_query="test query",
            processed_results=processed_contents,
            total_processed=1,
            processing_time=2.5,
            summary="Test summary",
            key_insights=["insight 1"],
            related_topics=["topic 1"],
            confidence_score=0.9,
            timestamp=datetime.now()
        )
        
        # 设置缓存
        success = await cache.set_processed_result("test query", processed_response, max_results=5)
        assert success is True
        
        # 获取缓存
        cached_result = await cache.get_processed_result("test query", max_results=5)
        assert cached_result is not None
        assert cached_result.original_query == "test query"
        assert len(cached_result.processed_results) == 1
        assert cached_result.confidence_score == 0.9
        
        # 验证统计信息
        stats = await cache.get_cache_stats()
        assert stats.total_entries == 1
        assert stats.hit_count == 1
    
    @pytest.mark.asyncio
    async def test_cache_miss(self, cache):
        """测试缓存未命中"""
        # 尝试获取不存在的缓存
        result = await cache.get_search_result("nonexistent query")
        assert result is None
        
        # 验证统计信息
        stats = await cache.get_cache_stats()
        assert stats.miss_count == 1
        assert stats.hit_count == 0
    
    @pytest.mark.asyncio
    async def test_cache_expiration(self, cache):
        """测试缓存过期"""
        # 创建测试搜索结果
        search_response = SearchResponse(
            query="test query",
            results=[],
            total_results=0,
            search_time=1.0,
            timestamp=datetime.now(),
            search_depth="basic"
        )
        
        # 设置短TTL缓存
        success = await cache.set_search_result("test query", search_response, ttl_seconds=1)
        assert success is True
        
        # 立即获取应该成功
        cached_result = await cache.get_search_result("test query")
        assert cached_result is not None
        
        # 等待过期
        await asyncio.sleep(1.1)
        
        # 再次获取应该失败
        expired_result = await cache.get_search_result("test query")
        assert expired_result is None
        
        # 验证过期统计
        stats = await cache.get_cache_stats()
        assert stats.expired_count >= 1
    
    @pytest.mark.asyncio
    async def test_cache_eviction(self, cache):
        """测试缓存驱逐"""
        # 设置小的最大条目数
        cache.max_entries = 2
        
        # 添加多个缓存条目
        for i in range(3):
            search_response = SearchResponse(
                query=f"test query {i}",
                results=[],
                total_results=0,
                search_time=1.0,
                timestamp=datetime.now(),
                search_depth="basic"
            )
            
            await cache.set_search_result(f"test query {i}", search_response)
            await asyncio.sleep(0.1)  # 确保时间戳不同
        
        # 验证只保留了最大条目数
        stats = await cache.get_cache_stats()
        assert stats.total_entries <= cache.max_entries
        assert stats.eviction_count >= 1
        
        # 最早的条目应该被驱逐
        first_result = await cache.get_search_result("test query 0")
        assert first_result is None
        
        # 最新的条目应该还在
        last_result = await cache.get_search_result("test query 2")
        assert last_result is not None
    
    @pytest.mark.asyncio
    async def test_cache_clear(self, cache):
        """测试缓存清理"""
        # 添加搜索结果缓存
        search_response = SearchResponse(
            query="search test",
            results=[],
            total_results=0,
            search_time=1.0,
            timestamp=datetime.now(),
            search_depth="basic"
        )
        await cache.set_search_result("search test", search_response)
        
        # 添加处理结果缓存
        processed_response = ProcessedResponse(
            original_query="processed test",
            processed_results=[],
            total_processed=0,
            processing_time=1.0,
            summary="",
            key_insights=[],
            related_topics=[],
            confidence_score=0.0,
            timestamp=datetime.now()
        )
        await cache.set_processed_result("processed test", processed_response)
        
        # 验证缓存存在
        stats = await cache.get_cache_stats()
        assert stats.total_entries == 2
        
        # 清理搜索缓存
        cleared_count = await cache.clear_cache("search")
        assert cleared_count == 1
        
        # 验证只清理了搜索缓存
        stats = await cache.get_cache_stats()
        assert stats.total_entries == 1
        
        # 清理所有缓存
        cleared_count = await cache.clear_cache()
        assert cleared_count == 1
        
        # 验证所有缓存都被清理
        stats = await cache.get_cache_stats()
        assert stats.total_entries == 0
    
    @pytest.mark.asyncio
    async def test_cache_stats(self, cache):
        """测试缓存统计信息"""
        # 初始统计
        stats = await cache.get_cache_stats()
        assert stats.total_entries == 0
        assert stats.hit_rate == 0.0
        assert stats.average_entry_size == 0.0
        
        # 添加一些缓存条目
        search_response = SearchResponse(
            query="test query",
            results=[],
            total_results=0,
            search_time=1.0,
            timestamp=datetime.now(),
            search_depth="basic"
        )
        
        await cache.set_search_result("test query 1", search_response)
        await cache.set_search_result("test query 2", search_response)
        
        # 进行一些缓存操作
        await cache.get_search_result("test query 1")  # 命中
        await cache.get_search_result("test query 2")  # 命中
        await cache.get_search_result("nonexistent")   # 未命中
        
        # 验证统计信息
        stats = await cache.get_cache_stats()
        assert stats.total_entries == 2
        assert stats.hit_count == 2
        assert stats.miss_count == 1
        assert stats.hit_rate == 2/3  # 2命中 / 3总请求
        assert stats.average_entry_size > 0
        assert stats.oldest_entry_age >= 0
        assert stats.newest_entry_age >= 0
    
    @pytest.mark.asyncio
    async def test_persistent_cache(self, cache):
        """测试持久化缓存"""
        # 创建测试搜索结果
        search_response = SearchResponse(
            query="persistent test",
            results=[],
            total_results=0,
            search_time=1.0,
            timestamp=datetime.now(),
            search_depth="basic"
        )
        
        # 设置缓存
        await cache.set_search_result("persistent test", search_response)
        
        # 清理内存缓存但保留持久化缓存
        cache.memory_cache.clear()
        
        # 从持久化缓存获取
        cached_result = await cache.get_search_result("persistent test")
        assert cached_result is not None
        assert cached_result.query == "persistent test"
        
        # 验证已恢复到内存缓存
        assert len(cache.memory_cache) == 1


# 运行测试的主函数
if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
