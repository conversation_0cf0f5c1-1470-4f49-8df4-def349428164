"""
DocumentProcessor 测试模块

测试文档处理器的各种功能，包括：
- 多格式文档处理
- 批量文档处理
- 文档搜索功能
- 文档解析和元数据提取
- 错误处理和边界情况

作者: HyAIAgent开发团队
创建时间: 2025-07-28
版本: 1.0.0
"""

import pytest
import asyncio
import tempfile
import shutil
import json
import csv
from pathlib import Path
from typing import Dict, Any

# 导入被测试的模块
from operations.document_processor import DocumentProcessor
from operations.security_manager import SecurityManager
from operations.file_operations import FileOperations


class TestDocumentProcessor:
    """DocumentProcessor 测试类"""
    
    @pytest.fixture
    def test_workspace(self):
        """创建测试工作空间"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        # 清理
        if Path(temp_dir).exists():
            shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def document_processor(self, test_workspace):
        """创建DocumentProcessor实例"""
        return DocumentProcessor(test_workspace)
    
    @pytest.fixture
    def sample_documents(self, test_workspace):
        """创建示例文档"""
        workspace = Path(test_workspace)
        
        # 创建文本文档
        (workspace / "sample.txt").write_text("这是一个测试文本文件。\n包含多行内容。", encoding='utf-8')
        
        # 创建Markdown文档
        (workspace / "readme.md").write_text("# 标题\n\n这是一个Markdown文档。", encoding='utf-8')
        
        # 创建JSON文档
        json_data = {
            "name": "测试项目",
            "version": "1.0.0",
            "dependencies": ["pytest", "asyncio"],
            "config": {
                "debug": True,
                "port": 8080
            }
        }
        (workspace / "config.json").write_text(json.dumps(json_data, ensure_ascii=False, indent=2), encoding='utf-8')
        
        # 创建CSV文档
        csv_data = [
            ["姓名", "年龄", "城市"],
            ["张三", "25", "北京"],
            ["李四", "30", "上海"],
            ["王五", "28", "广州"]
        ]
        with open(workspace / "data.csv", 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerows(csv_data)
        
        # 创建XML文档
        xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<project>
    <name>测试项目</name>
    <version>1.0.0</version>
    <dependencies>
        <dependency>pytest</dependency>
        <dependency>asyncio</dependency>
    </dependencies>
</project>"""
        (workspace / "project.xml").write_text(xml_content, encoding='utf-8')
        
        # 创建INI配置文件
        ini_content = """[database]
host = localhost
port = 5432
name = testdb

[logging]
level = INFO
file = app.log
"""
        (workspace / "config.ini").write_text(ini_content, encoding='utf-8')
        
        return {
            "text": "sample.txt",
            "markdown": "readme.md",
            "json": "config.json",
            "csv": "data.csv",
            "xml": "project.xml",
            "ini": "config.ini"
        }
    
    @pytest.mark.asyncio
    async def test_process_text_document(self, document_processor, sample_documents):
        """测试处理文本文档"""
        result = await document_processor.process_document(sample_documents["text"])
        
        assert result['success'] is True
        assert result['format'] == 'text'
        assert result['content']['type'] == 'text'
        assert result['content']['line_count'] == 2
        assert result['content']['word_count'] > 0
        assert result['metadata']['file_name'] == 'sample.txt'
    
    @pytest.mark.asyncio
    async def test_process_json_document(self, document_processor, sample_documents):
        """测试处理JSON文档"""
        result = await document_processor.process_document(sample_documents["json"])
        
        assert result['success'] is True
        assert result['format'] == 'data'
        assert result['content']['type'] == 'json'
        assert 'parsed_data' in result['content']
        assert result['content']['parsed_data']['name'] == '测试项目'
        assert result['content']['structure']['type'] == 'object'
    
    @pytest.mark.asyncio
    async def test_process_csv_document(self, document_processor, sample_documents):
        """测试处理CSV文档"""
        result = await document_processor.process_document(sample_documents["csv"])
        
        assert result['success'] is True
        assert result['format'] == 'data'
        assert result['content']['type'] == 'csv'
        assert result['content']['structure']['row_count'] == 4
        assert result['content']['structure']['column_count'] == 3
        assert result['content']['structure']['headers'] == ['姓名', '年龄', '城市']
    
    @pytest.mark.asyncio
    async def test_process_xml_document(self, document_processor, sample_documents):
        """测试处理XML文档"""
        result = await document_processor.process_document(sample_documents["xml"])
        
        assert result['success'] is True
        assert result['format'] == 'data'
        assert result['content']['type'] == 'xml'
        assert result['content']['root_tag'] == 'project'
        assert 'structure' in result['content']
    
    @pytest.mark.asyncio
    async def test_process_ini_document(self, document_processor, sample_documents):
        """测试处理INI配置文档"""
        result = await document_processor.process_document(sample_documents["ini"])
        
        assert result['success'] is True
        assert result['format'] == 'config'
        assert result['content']['type'] == 'ini'
        assert 'sections' in result['content']
        assert 'database' in result['content']['sections']
        assert result['content']['sections']['database']['host'] == 'localhost'
    
    @pytest.mark.asyncio
    async def test_batch_process_documents(self, document_processor, sample_documents):
        """测试批量处理文档"""
        result = await document_processor.batch_process_documents(["*.txt", "*.json", "*.csv"])
        
        assert result['success'] is True
        assert result['processed_count'] >= 3
        assert result['successful_count'] >= 3
        assert len(result['results']) >= 3
        
        # 验证每个结果都有必要的字段
        for doc_result in result['results']:
            assert 'success' in doc_result
            assert 'file_path' in doc_result
            assert 'format' in doc_result
    
    @pytest.mark.asyncio
    async def test_search_in_documents(self, document_processor, sample_documents):
        """测试文档搜索功能"""
        # 搜索包含"测试"的文档
        result = await document_processor.search_in_documents("测试")
        
        assert result['success'] is True
        assert result['query'] == '测试'
        assert result['files_with_matches'] > 0
        assert result['total_matches'] > 0
        
        # 验证搜索结果结构
        for search_result in result['results']:
            assert 'file_path' in search_result
            assert 'matches' in search_result
            assert 'matching_lines' in search_result
            assert search_result['matches'] > 0
    
    @pytest.mark.asyncio
    async def test_search_case_sensitive(self, document_processor, sample_documents):
        """测试区分大小写搜索"""
        # 区分大小写搜索
        result_sensitive = await document_processor.search_in_documents("测试", case_sensitive=True)
        result_insensitive = await document_processor.search_in_documents("测试", case_sensitive=False)
        
        assert result_sensitive['success'] is True
        assert result_insensitive['success'] is True
        # 由于都是中文，结果应该相同
        assert result_sensitive['total_matches'] == result_insensitive['total_matches']
    
    @pytest.mark.asyncio
    async def test_process_nonexistent_document(self, document_processor):
        """测试处理不存在的文档"""
        result = await document_processor.process_document("nonexistent.txt")
        
        assert result['success'] is False
        assert 'error' in result
        assert 'not found' in result['error'].lower() or 'security validation failed' in result['error'].lower()
    
    @pytest.mark.asyncio
    async def test_process_invalid_json(self, document_processor, test_workspace):
        """测试处理无效JSON文档"""
        # 创建无效JSON文件
        invalid_json_path = Path(test_workspace) / "invalid.json"
        invalid_json_path.write_text('{"invalid": json content}', encoding='utf-8')

        result = await document_processor.process_document("invalid.json")

        # 无效JSON会被当作文本处理，这是正确的fallback行为
        assert result['success'] is True
        assert result['format'] == 'data'  # 格式检测仍然是data（基于扩展名）
        assert result['content']['type'] == 'text'  # 但内容类型是text（fallback结果）
    
    @pytest.mark.asyncio
    async def test_document_metadata_extraction(self, document_processor, sample_documents):
        """测试文档元数据提取"""
        result = await document_processor.process_document(sample_documents["json"])
        
        assert result['success'] is True
        metadata = result['metadata']
        
        assert 'file_name' in metadata
        assert 'file_size' in metadata
        assert 'file_extension' in metadata
        assert 'format' in metadata
        assert 'created_time' in metadata
        assert 'modified_time' in metadata
        
        assert metadata['file_name'] == 'config.json'
        assert metadata['file_extension'] == '.json'
        assert metadata['format'] == 'data'
    
    @pytest.mark.asyncio
    async def test_processing_stats(self, document_processor, sample_documents):
        """测试处理统计功能"""
        # 初始统计应该为0
        initial_stats = document_processor.get_processing_stats()
        assert initial_stats['total_processed'] == 0
        assert initial_stats['successful_processed'] == 0
        
        # 处理一些文档
        await document_processor.process_document(sample_documents["text"])
        await document_processor.process_document(sample_documents["json"])
        
        # 检查统计更新
        stats = document_processor.get_processing_stats()
        assert stats['total_processed'] >= 2
        assert stats['successful_processed'] >= 2
        assert stats['success_rate'] > 0
        assert 'formats_processed' in stats
        
        # 重置统计
        document_processor.reset_stats()
        reset_stats = document_processor.get_processing_stats()
        assert reset_stats['total_processed'] == 0
    
    @pytest.mark.asyncio
    async def test_concurrent_processing(self, document_processor, sample_documents):
        """测试并发处理"""
        # 同时处理多个文档
        tasks = [
            document_processor.process_document(sample_documents["text"]),
            document_processor.process_document(sample_documents["json"]),
            document_processor.process_document(sample_documents["csv"])
        ]
        
        results = await asyncio.gather(*tasks)
        
        # 验证所有结果
        for result in results:
            assert result['success'] is True
            assert 'content' in result
            assert 'metadata' in result
    
    def test_format_detection(self, document_processor, test_workspace):
        """测试文档格式检测"""
        # 测试不同扩展名的格式检测
        test_files = [
            ("test.txt", "text"),
            ("test.json", "data"),
            ("test.csv", "data"),
            ("test.xml", "data"),
            ("test.html", "markup"),
            ("test.ini", "config"),
            ("test.yaml", "config"),
            ("unknown.xyz", "text")  # 未知格式默认为text
        ]
        
        for filename, expected_format in test_files:
            file_path = Path(test_workspace) / filename
            detected_format = document_processor._detect_document_format(file_path)
            assert detected_format == expected_format, f"格式检测失败: {filename} -> {detected_format} != {expected_format}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
