2025-07-27 23:07:55 | INFO     | main:setup_logging:49 - 日志系统初始化完成
2025-07-27 23:07:55 | INFO     | main:check_dependencies:60 - 检查依赖项...
2025-07-27 23:07:55 | DEBUG    | main:check_dependencies:83 - ✅ PyQt6 已安装
2025-07-27 23:07:55 | DEBUG    | main:check_dependencies:83 - ✅ openai 已安装
2025-07-27 23:07:55 | DEBUG    | main:check_dependencies:83 - ✅ loguru 已安装
2025-07-27 23:07:55 | DEBUG    | main:check_dependencies:83 - ✅ tinydb 已安装
2025-07-27 23:07:55 | DEBUG    | main:check_dependencies:83 - ✅ jinja2 已安装
2025-07-27 23:07:55 | DEBUG    | main:check_dependencies:83 - ✅ python-dotenv 已安装
2025-07-27 23:07:55 | INFO     | main:check_dependencies:93 - 所有依赖项检查通过
2025-07-27 23:07:55 | INFO     | main:create_directories:141 - 创建必要的目录...
2025-07-27 23:07:55 | DEBUG    | main:create_directories:153 - 目录已创建: data
2025-07-27 23:07:55 | DEBUG    | main:create_directories:153 - 目录已创建: logs
2025-07-27 23:07:55 | DEBUG    | main:create_directories:153 - 目录已创建: prompts\global
2025-07-27 23:07:55 | DEBUG    | main:create_directories:153 - 目录已创建: prompts\templates
2025-07-27 23:07:55 | INFO     | main:create_directories:155 - 目录创建完成
2025-07-27 23:07:55 | INFO     | main:check_configuration:105 - 检查配置文件...
2025-07-27 23:07:55 | INFO     | core.config_manager:load_config:41 - 配置文件加载成功: config.json
2025-07-27 23:07:55 | INFO     | core.config_manager:load_env:61 - 环境变量加载完成
2025-07-27 23:07:55 | INFO     | main:check_configuration:120 - AI配置检查通过
2025-07-27 23:07:55 | INFO     | main:check_configuration:129 - 配置检查完成
2025-07-27 23:07:55 | INFO     | main:initialize_application:186 - 应用程序初始化完成
2025-07-27 23:07:55 | INFO     | core.config_manager:load_config:41 - 配置文件加载成功: config.json
2025-07-27 23:07:55 | INFO     | core.config_manager:load_env:61 - 环境变量加载完成
2025-07-27 23:07:55 | INFO     | core.kv_store:__init__:42 - KV数据库初始化完成: data\kv_store.json
2025-07-27 23:07:55 | INFO     | core.prompt_manager:__init__:43 - 提示词管理器初始化完成: prompts
2025-07-27 23:07:55 | DEBUG    | core.prompt_manager:_load_prompt_file:82 - 加载提示词: global.basic_chat
2025-07-27 23:07:55 | DEBUG    | core.prompt_manager:_load_prompt_file:82 - 加载提示词: templates.template_example
2025-07-27 23:07:55 | INFO     | core.prompt_manager:_load_all_prompts:59 - 加载提示词文件完成，共2个
2025-07-27 23:07:55 | INFO     | core.ai_client:__init__:47 - AI客户端初始化完成 - 模型: gpt-3.5-turbo, 基础URL: https://api.openai.com/v1
2025-07-27 23:07:55 | INFO     | ui.chat_window:setup_ai_client:282 - AI客户端设置完成
2025-07-27 23:07:55 | INFO     | ui.chat_window:__init__:176 - 聊天窗口初始化完成
2025-07-27 23:07:55 | DEBUG    | core.kv_store:set:78 - 设置键值对: chat_1753628875 = 
用户:
你好，AI
--------------------------------------------------

AI助手:
这是AI的测试回复
---------------------...
2025-07-27 23:07:55 | INFO     | core.kv_store:close:338 - KV数据库连接已关闭
2025-07-27 23:07:55 | INFO     | ui.chat_window:closeEvent:400 - 聊天窗口已关闭
2025-07-28 00:09:10 | INFO     | __main__:setup_logging:49 - 日志系统初始化完成
2025-07-28 00:09:10 | INFO     | __main__:check_dependencies:60 - 检查依赖项...
2025-07-28 00:09:10 | DEBUG    | __main__:check_dependencies:83 - ✅ PyQt6 已安装
2025-07-28 00:09:10 | DEBUG    | __main__:check_dependencies:83 - ✅ openai 已安装
2025-07-28 00:09:10 | DEBUG    | __main__:check_dependencies:83 - ✅ loguru 已安装
2025-07-28 00:09:10 | DEBUG    | __main__:check_dependencies:83 - ✅ tinydb 已安装
2025-07-28 00:09:10 | DEBUG    | __main__:check_dependencies:83 - ✅ jinja2 已安装
2025-07-28 00:09:10 | DEBUG    | __main__:check_dependencies:83 - ✅ python-dotenv 已安装
2025-07-28 00:09:10 | INFO     | __main__:check_dependencies:93 - 所有依赖项检查通过
2025-07-28 00:09:10 | INFO     | __main__:create_directories:141 - 创建必要的目录...
2025-07-28 00:09:10 | DEBUG    | __main__:create_directories:153 - 目录已创建: data
2025-07-28 00:09:10 | DEBUG    | __main__:create_directories:153 - 目录已创建: logs
2025-07-28 00:09:10 | DEBUG    | __main__:create_directories:153 - 目录已创建: prompts\global
2025-07-28 00:09:10 | DEBUG    | __main__:create_directories:153 - 目录已创建: prompts\templates
2025-07-28 00:09:10 | INFO     | __main__:create_directories:155 - 目录创建完成
2025-07-28 00:09:10 | INFO     | __main__:check_configuration:105 - 检查配置文件...
2025-07-28 00:09:10 | INFO     | core.config_manager:load_config:41 - 配置文件加载成功: config.json
2025-07-28 00:09:10 | INFO     | core.config_manager:load_env:61 - 环境变量加载完成
2025-07-28 00:09:10 | INFO     | __main__:check_configuration:120 - AI配置检查通过
2025-07-28 00:09:10 | INFO     | __main__:check_configuration:129 - 配置检查完成
2025-07-28 00:09:10 | INFO     | __main__:initialize_application:186 - 应用程序初始化完成
2025-07-28 00:09:10 | INFO     | __main__:main:217 - 启动主窗口...
2025-07-28 00:09:10 | INFO     | core.config_manager:load_config:41 - 配置文件加载成功: config.json
2025-07-28 00:09:10 | INFO     | core.config_manager:load_env:61 - 环境变量加载完成
2025-07-28 00:09:10 | INFO     | core.kv_store:__init__:42 - KV数据库初始化完成: data\kv_store.json
2025-07-28 00:09:10 | INFO     | core.prompt_manager:__init__:43 - 提示词管理器初始化完成: prompts
2025-07-28 00:09:10 | DEBUG    | core.prompt_manager:_load_prompt_file:82 - 加载提示词: global.basic_chat
2025-07-28 00:09:10 | DEBUG    | core.prompt_manager:_load_prompt_file:82 - 加载提示词: templates.template_example
2025-07-28 00:09:10 | INFO     | core.prompt_manager:_load_all_prompts:59 - 加载提示词文件完成，共2个
2025-07-28 00:09:11 | INFO     | core.ai_client:__init__:47 - AI客户端初始化完成 - 模型: gpt-3.5-turbo, 基础URL: https://api.openai.com/v1
2025-07-28 00:09:11 | INFO     | ui.chat_window:setup_ai_client:282 - AI客户端设置完成
2025-07-28 00:09:11 | INFO     | ui.chat_window:__init__:176 - 聊天窗口初始化完成
2025-07-28 00:09:11 | INFO     | __main__:main:222 - HyAIAgent启动成功
2025-07-28 00:10:11 | INFO     | core.ai_client:chat:81 - 发送消息到AI: 的...
2025-07-28 00:10:13 | ERROR    | core.ai_client:chat:110 - OpenAI API错误: Connection error.
2025-07-28 00:11:09 | INFO     | core.ai_client:clear_history:196 - 对话历史已清空
2025-07-28 09:37:16 | INFO     | core.kv_store:close:338 - KV数据库连接已关闭
2025-07-28 09:37:16 | INFO     | ui.chat_window:closeEvent:400 - 聊天窗口已关闭
2025-07-28 09:37:16 | INFO     | __main__:main:227 - 应用程序退出，退出码: 0
2025-07-28 14:28:56 | INFO     | main:setup_logging:49 - 日志系统初始化完成
2025-07-28 14:28:56 | INFO     | main:check_dependencies:60 - 检查依赖项...
2025-07-28 14:28:56 | DEBUG    | main:check_dependencies:83 - ✅ PyQt6 已安装
2025-07-28 14:28:56 | DEBUG    | main:check_dependencies:83 - ✅ openai 已安装
2025-07-28 14:28:56 | DEBUG    | main:check_dependencies:83 - ✅ loguru 已安装
2025-07-28 14:28:56 | DEBUG    | main:check_dependencies:83 - ✅ tinydb 已安装
2025-07-28 14:28:56 | DEBUG    | main:check_dependencies:83 - ✅ jinja2 已安装
2025-07-28 14:28:56 | DEBUG    | main:check_dependencies:83 - ✅ python-dotenv 已安装
2025-07-28 14:28:56 | INFO     | main:check_dependencies:93 - 所有依赖项检查通过
2025-07-28 14:28:56 | INFO     | main:create_directories:141 - 创建必要的目录...
2025-07-28 14:28:56 | DEBUG    | main:create_directories:153 - 目录已创建: data
2025-07-28 14:28:56 | DEBUG    | main:create_directories:153 - 目录已创建: logs
2025-07-28 14:28:56 | DEBUG    | main:create_directories:153 - 目录已创建: prompts\global
2025-07-28 14:28:56 | DEBUG    | main:create_directories:153 - 目录已创建: prompts\templates
2025-07-28 14:28:56 | INFO     | main:create_directories:155 - 目录创建完成
2025-07-28 14:28:56 | INFO     | main:check_configuration:105 - 检查配置文件...
2025-07-28 14:28:56 | INFO     | core.config_manager:load_config:41 - 配置文件加载成功: config.json
2025-07-28 14:28:56 | INFO     | core.config_manager:load_env:61 - 环境变量加载完成
2025-07-28 14:28:56 | INFO     | main:check_configuration:120 - AI配置检查通过
2025-07-28 14:28:56 | INFO     | main:check_configuration:129 - 配置检查完成
2025-07-28 14:28:56 | INFO     | main:initialize_application:186 - 应用程序初始化完成
2025-07-28 14:28:56 | INFO     | core.config_manager:load_config:41 - 配置文件加载成功: config.json
2025-07-28 14:28:56 | INFO     | core.config_manager:load_env:61 - 环境变量加载完成
2025-07-28 14:28:56 | INFO     | core.kv_store:__init__:42 - KV数据库初始化完成: data\kv_store.json
2025-07-28 14:28:56 | INFO     | core.prompt_manager:__init__:43 - 提示词管理器初始化完成: prompts
2025-07-28 14:28:56 | DEBUG    | core.prompt_manager:_load_prompt_file:82 - 加载提示词: global.basic_chat
2025-07-28 14:28:56 | DEBUG    | core.prompt_manager:_load_prompt_file:82 - 加载提示词: templates.template_example
2025-07-28 14:28:56 | INFO     | core.prompt_manager:_load_all_prompts:59 - 加载提示词文件完成，共2个
2025-07-28 14:28:56 | INFO     | core.ai_client:__init__:47 - AI客户端初始化完成 - 模型: gpt-3.5-turbo, 基础URL: https://api.openai.com/v1
2025-07-28 14:28:56 | INFO     | ui.chat_window:setup_ai_client:282 - AI客户端设置完成
2025-07-28 14:28:56 | INFO     | ui.chat_window:__init__:176 - 聊天窗口初始化完成
2025-07-28 14:28:56 | DEBUG    | core.kv_store:set:78 - 设置键值对: chat_1753684136 = 
用户:
你好，AI
--------------------------------------------------

AI助手:
这是AI的测试回复
---------------------...
2025-07-28 14:28:56 | INFO     | core.kv_store:close:338 - KV数据库连接已关闭
2025-07-28 14:28:56 | INFO     | ui.chat_window:closeEvent:400 - 聊天窗口已关闭
2025-07-28 21:42:21 | INFO     | __main__:setup_logging:49 - 日志系统初始化完成
2025-07-28 21:42:21 | INFO     | __main__:check_dependencies:60 - 检查依赖项...
2025-07-28 21:42:21 | DEBUG    | __main__:check_dependencies:83 - ✅ PyQt6 已安装
2025-07-28 21:42:21 | DEBUG    | __main__:check_dependencies:83 - ✅ openai 已安装
2025-07-28 21:42:21 | DEBUG    | __main__:check_dependencies:83 - ✅ loguru 已安装
2025-07-28 21:42:21 | DEBUG    | __main__:check_dependencies:83 - ✅ tinydb 已安装
2025-07-28 21:42:21 | DEBUG    | __main__:check_dependencies:83 - ✅ jinja2 已安装
2025-07-28 21:42:21 | DEBUG    | __main__:check_dependencies:83 - ✅ python-dotenv 已安装
2025-07-28 21:42:21 | INFO     | __main__:check_dependencies:93 - 所有依赖项检查通过
2025-07-28 21:42:21 | INFO     | __main__:create_directories:141 - 创建必要的目录...
2025-07-28 21:42:21 | DEBUG    | __main__:create_directories:153 - 目录已创建: data
2025-07-28 21:42:21 | DEBUG    | __main__:create_directories:153 - 目录已创建: logs
2025-07-28 21:42:21 | DEBUG    | __main__:create_directories:153 - 目录已创建: prompts\global
2025-07-28 21:42:21 | DEBUG    | __main__:create_directories:153 - 目录已创建: prompts\templates
2025-07-28 21:42:21 | INFO     | __main__:create_directories:155 - 目录创建完成
2025-07-28 21:42:21 | INFO     | __main__:check_configuration:105 - 检查配置文件...
2025-07-28 21:42:21 | INFO     | core.config_manager:load_config:41 - 配置文件加载成功: config.json
2025-07-28 21:42:21 | INFO     | core.config_manager:load_env:61 - 环境变量加载完成
2025-07-28 21:42:21 | INFO     | __main__:check_configuration:120 - AI配置检查通过
2025-07-28 21:42:21 | INFO     | __main__:check_configuration:129 - 配置检查完成
2025-07-28 21:42:21 | INFO     | __main__:initialize_application:186 - 应用程序初始化完成
2025-07-28 21:42:21 | INFO     | __main__:main:217 - 启动主窗口...
2025-07-28 21:42:21 | INFO     | core.config_manager:load_config:41 - 配置文件加载成功: config.json
2025-07-28 21:42:21 | INFO     | core.config_manager:load_env:61 - 环境变量加载完成
2025-07-28 21:42:21 | INFO     | core.kv_store:__init__:42 - KV数据库初始化完成: data\kv_store.json
2025-07-28 21:42:21 | INFO     | core.prompt_manager:__init__:43 - 提示词管理器初始化完成: prompts
2025-07-28 21:42:21 | DEBUG    | core.prompt_manager:_load_prompt_file:82 - 加载提示词: global.basic_chat
2025-07-28 21:42:21 | DEBUG    | core.prompt_manager:_load_prompt_file:82 - 加载提示词: templates.template_example
2025-07-28 21:42:21 | INFO     | core.prompt_manager:_load_all_prompts:59 - 加载提示词文件完成，共2个
2025-07-28 21:42:21 | INFO     | core.ai_client:__init__:47 - AI客户端初始化完成 - 模型: gpt-3.5-turbo, 基础URL: https://api.openai.com/v1
2025-07-28 21:42:21 | INFO     | ui.chat_window:setup_ai_client:282 - AI客户端设置完成
2025-07-28 21:42:21 | INFO     | ui.chat_window:__init__:176 - 聊天窗口初始化完成
2025-07-28 21:42:21 | INFO     | __main__:main:222 - HyAIAgent启动成功
2025-07-28 21:42:46 | INFO     | core.kv_store:close:338 - KV数据库连接已关闭
2025-07-28 21:42:46 | INFO     | ui.chat_window:closeEvent:400 - 聊天窗口已关闭
2025-07-28 21:42:46 | INFO     | __main__:main:227 - 应用程序退出，退出码: 0
2025-07-28 21:43:35 | INFO     | __main__:setup_logging:49 - 日志系统初始化完成
2025-07-28 21:43:35 | INFO     | __main__:check_dependencies:60 - 检查依赖项...
2025-07-28 21:43:35 | DEBUG    | __main__:check_dependencies:83 - ✅ PyQt6 已安装
2025-07-28 21:43:35 | DEBUG    | __main__:check_dependencies:83 - ✅ openai 已安装
2025-07-28 21:43:35 | DEBUG    | __main__:check_dependencies:83 - ✅ loguru 已安装
2025-07-28 21:43:35 | DEBUG    | __main__:check_dependencies:83 - ✅ tinydb 已安装
2025-07-28 21:43:35 | DEBUG    | __main__:check_dependencies:83 - ✅ jinja2 已安装
2025-07-28 21:43:35 | DEBUG    | __main__:check_dependencies:83 - ✅ python-dotenv 已安装
2025-07-28 21:43:35 | INFO     | __main__:check_dependencies:93 - 所有依赖项检查通过
2025-07-28 21:43:35 | INFO     | __main__:create_directories:141 - 创建必要的目录...
2025-07-28 21:43:35 | DEBUG    | __main__:create_directories:153 - 目录已创建: data
2025-07-28 21:43:35 | DEBUG    | __main__:create_directories:153 - 目录已创建: logs
2025-07-28 21:43:35 | DEBUG    | __main__:create_directories:153 - 目录已创建: prompts\global
2025-07-28 21:43:35 | DEBUG    | __main__:create_directories:153 - 目录已创建: prompts\templates
2025-07-28 21:43:35 | INFO     | __main__:create_directories:155 - 目录创建完成
2025-07-28 21:43:35 | INFO     | __main__:check_configuration:105 - 检查配置文件...
2025-07-28 21:43:35 | INFO     | core.config_manager:load_config:41 - 配置文件加载成功: config.json
2025-07-28 21:43:35 | INFO     | core.config_manager:load_env:61 - 环境变量加载完成
2025-07-28 21:43:35 | INFO     | __main__:check_configuration:120 - AI配置检查通过
2025-07-28 21:43:35 | INFO     | __main__:check_configuration:129 - 配置检查完成
2025-07-28 21:43:35 | INFO     | __main__:initialize_application:186 - 应用程序初始化完成
2025-07-28 21:43:35 | INFO     | __main__:main:217 - 启动主窗口...
2025-07-28 21:43:35 | INFO     | core.config_manager:load_config:41 - 配置文件加载成功: config.json
2025-07-28 21:43:35 | INFO     | core.config_manager:load_env:61 - 环境变量加载完成
2025-07-28 21:43:35 | INFO     | core.kv_store:__init__:42 - KV数据库初始化完成: data\kv_store.json
2025-07-28 21:43:35 | INFO     | core.prompt_manager:__init__:43 - 提示词管理器初始化完成: prompts
2025-07-28 21:43:35 | DEBUG    | core.prompt_manager:_load_prompt_file:82 - 加载提示词: global.basic_chat
2025-07-28 21:43:35 | DEBUG    | core.prompt_manager:_load_prompt_file:82 - 加载提示词: templates.template_example
2025-07-28 21:43:35 | INFO     | core.prompt_manager:_load_all_prompts:59 - 加载提示词文件完成，共2个
2025-07-28 21:43:35 | INFO     | core.ai_client:__init__:47 - AI客户端初始化完成 - 模型: gpt-3.5-turbo, 基础URL: https://api.openai.com/v1
2025-07-28 21:43:35 | INFO     | ui.chat_window:setup_ai_client:282 - AI客户端设置完成
2025-07-28 21:43:35 | INFO     | ui.chat_window:__init__:176 - 聊天窗口初始化完成
2025-07-28 21:43:35 | INFO     | __main__:main:222 - HyAIAgent启动成功
2025-07-28 21:44:07 | INFO     | core.kv_store:close:338 - KV数据库连接已关闭
2025-07-28 21:44:07 | INFO     | ui.chat_window:closeEvent:400 - 聊天窗口已关闭
2025-07-28 21:44:07 | INFO     | __main__:main:227 - 应用程序退出，退出码: 0
