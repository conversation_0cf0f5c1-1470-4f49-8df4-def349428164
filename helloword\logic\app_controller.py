#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用控制器模块
负责业务逻辑处理和界面控制
遵循MVC模式中的Controller角色
"""

import datetime
from PyQt6.QtWidgets import QMessageBox
from PyQt6.QtCore import QObject, pyqtSignal


class AppController(QObject):
    """
    应用控制器类
    负责处理业务逻辑，协调界面和数据模型
    """
    
    # 定义控制器信号
    result_ready = pyqtSignal(str)  # 结果准备就绪信号
    error_occurred = pyqtSignal(str, str)  # 错误发生信号 (title, message)
    
    def __init__(self, ui_window):
        super().__init__()
        self.ui = ui_window
        self.init_connections()
        
    def init_connections(self):
        """初始化信号槽连接"""
        # 连接界面信号到控制器方法
        self.ui.greeting_requested.connect(self.handle_greeting_request)
        self.ui.time_requested.connect(self.handle_time_request)
        self.ui.clear_requested.connect(self.handle_clear_request)
        
        # 连接控制器信号到界面方法
        self.result_ready.connect(self.ui.append_result)
        self.error_occurred.connect(self.show_error_message)
        
        # 连接回车键到问候功能
        self.ui.name_input.returnPressed.connect(self._handle_enter_key)
        
    def handle_greeting_request(self, greeting, name):
        """
        处理问候请求
        
        Args:
            greeting (str): 问候语
            name (str): 用户姓名
        """
        try:
            # 验证输入
            if not name:
                self.error_occurred.emit("提示", "请先输入您的姓名！")
                self.ui.focus_name_input()
                return
            
            # 生成问候消息
            message = self._generate_greeting_message(greeting, name)
            
            # 发送结果
            self.result_ready.emit(message)
            
        except Exception as e:
            self.error_occurred.emit("错误", f"处理问候请求时发生错误: {str(e)}")
    
    def handle_time_request(self):
        """处理时间显示请求"""
        try:
            current_time = datetime.datetime.now()
            time_str = current_time.strftime("%Y年%m月%d日 %H:%M:%S")
            message = f"当前时间: {time_str}\n"
            
            self.result_ready.emit(message)
            
        except Exception as e:
            self.error_occurred.emit("错误", f"获取时间时发生错误: {str(e)}")
    
    def handle_clear_request(self):
        """处理清除请求"""
        try:
            self.ui.clear_result()
            
        except Exception as e:
            self.error_occurred.emit("错误", f"清除内容时发生错误: {str(e)}")
    
    def _handle_enter_key(self):
        """处理回车键按下事件"""
        user_input = self.ui.get_user_input()
        self.handle_greeting_request(user_input['greeting'], user_input['name'])
    
    def _generate_greeting_message(self, greeting, name):
        """
        生成问候消息
        
        Args:
            greeting (str): 问候语
            name (str): 用户姓名
            
        Returns:
            str: 格式化的问候消息
        """
        # 这里可以扩展更复杂的业务逻辑
        # 比如根据时间选择不同的问候语，或者记录用户历史等
        
        current_hour = datetime.datetime.now().hour
        
        # 根据时间添加额外的问候
        time_greeting = ""
        if 5 <= current_hour < 12:
            time_greeting = "早上好！"
        elif 12 <= current_hour < 18:
            time_greeting = "下午好！"
        elif 18 <= current_hour < 22:
            time_greeting = "晚上好！"
        else:
            time_greeting = "夜深了，注意休息！"
        
        message = f"{greeting}, {name}! {time_greeting} 欢迎使用Python PyQt6 GUI程序！\n"
        
        return message
    
    def show_error_message(self, title, message):
        """
        显示错误消息
        
        Args:
            title (str): 错误标题
            message (str): 错误消息
        """
        QMessageBox.warning(self.ui, title, message)
    
    # 扩展方法：可以添加更多业务逻辑
    def get_application_info(self):
        """获取应用程序信息"""
        return {
            'name': 'PyQt6 Hello World',
            'version': '2.0.0',
            'author': 'Python学习示例',
            'description': '展示PyQt6 MVC架构的示例应用'
        }
    
    def save_user_preferences(self, preferences):
        """保存用户偏好设置（示例方法）"""
        # 这里可以实现配置文件保存逻辑
        pass
    
    def load_user_preferences(self):
        """加载用户偏好设置（示例方法）"""
        # 这里可以实现配置文件加载逻辑
        return {}
    
    def validate_input(self, input_data):
        """
        验证输入数据
        
        Args:
            input_data (dict): 输入数据
            
        Returns:
            tuple: (is_valid, error_message)
        """
        if not input_data.get('name', '').strip():
            return False, "姓名不能为空"
        
        if len(input_data.get('name', '')) > 50:
            return False, "姓名长度不能超过50个字符"
        
        return True, ""
