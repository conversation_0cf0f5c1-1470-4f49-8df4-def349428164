# PyQt6 Hello World 应用启动器 (PowerShell版本)
# 支持更好的中文显示

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host "🐍 PyQt6 Hello World 应用启动器" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan
Write-Host ""

# 检查Python是否安装
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 检测到Python环境: $pythonVersion" -ForegroundColor Green
    } else {
        throw "Python not found"
    }
} catch {
    Write-Host "❌ 错误: 未找到Python" -ForegroundColor Red
    Write-Host "请先安装Python 3.8或更高版本" -ForegroundColor Yellow
    Write-Host "下载地址: https://www.python.org/downloads/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按回车键退出"
    exit 1
}

Write-Host ""
Write-Host "🔄 正在启动应用..." -ForegroundColor Yellow
Write-Host ""

# 运行Python启动器
try {
    python run.py
    Write-Host ""
    Write-Host "✅ 应用程序已结束" -ForegroundColor Green
} catch {
    Write-Host ""
    Write-Host "❌ 应用程序启动失败" -ForegroundColor Red
}

Write-Host ""
Read-Host "按回车键退出"
