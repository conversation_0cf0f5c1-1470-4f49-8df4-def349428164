"""
文件操作核心模块
提供安全的异步文件操作功能，集成SecurityManager进行安全检查
"""

import os
import asyncio
import aiofiles
import chardet
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, AsyncGenerator
from datetime import datetime
import fnmatch
import hashlib

from .security_manager import SecurityManager

logger = logging.getLogger(__name__)


class FileOperations:
    """
    文件操作核心类
    
    提供以下功能：
    - 异步文件读写操作
    - 安全的文件操作验证
    - 文件搜索和过滤
    - 编码自动检测和处理
    - 完善的错误处理和日志记录
    """
    
    def __init__(self, workspace_path: str = "./workspace", 
                 security_manager: Optional[SecurityManager] = None):
        """
        初始化文件操作管理器
        
        Args:
            workspace_path (str): 工作目录路径
            security_manager (Optional[SecurityManager]): 安全管理器实例
        """
        self.workspace_path = Path(workspace_path).resolve()
        self.security_manager = security_manager or SecurityManager(workspace_path)
        self.operation_stats = {
            "total_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "bytes_read": 0,
            "bytes_written": 0
        }
        
        # 确保工作目录存在
        self.workspace_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"FileOperations initialized with workspace: {self.workspace_path}")
    
    async def read_file(self, file_path: str, encoding: Optional[str] = None) -> Dict[str, Any]:
        """
        异步读取文件内容
        
        Args:
            file_path (str): 文件路径
            encoding (Optional[str]): 文件编码，None时自动检测
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        self.operation_stats["total_operations"] += 1
        
        try:
            # 构建绝对路径
            abs_path = self.workspace_path / file_path

            # 安全验证
            validation_result = self.security_manager.validate_operation(str(abs_path), "read")
            if not validation_result.get("valid", False):
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Security validation failed",
                    "details": "; ".join(validation_result.get("errors", ["Access denied"])),
                    "file_path": file_path
                }
            
            # 检测文件编码
            if encoding is None:
                encoding = await self._detect_encoding(abs_path)
            
            # 异步读取文件
            async with aiofiles.open(abs_path, 'r', encoding=encoding) as f:
                content = await f.read()
            
            file_size = abs_path.stat().st_size
            self.operation_stats["successful_operations"] += 1
            self.operation_stats["bytes_read"] += file_size
            
            logger.info(f"Successfully read file: {file_path} ({file_size} bytes)")
            
            return {
                "success": True,
                "content": content,
                "file_path": file_path,
                "encoding": encoding,
                "size": file_size,
                "last_modified": datetime.fromtimestamp(abs_path.stat().st_mtime).isoformat()
            }
            
        except Exception as e:
            self.operation_stats["failed_operations"] += 1
            logger.error(f"Failed to read file {file_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }
    
    async def write_file(self, file_path: str, content: str, 
                        encoding: str = 'utf-8', mode: str = 'w') -> Dict[str, Any]:
        """
        异步写入文件内容
        
        Args:
            file_path (str): 文件路径
            content (str): 文件内容
            encoding (str): 文件编码
            mode (str): 写入模式 ('w', 'a')
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        self.operation_stats["total_operations"] += 1
        
        try:
            # 构建绝对路径
            abs_path = self.workspace_path / file_path

            # 安全验证
            validation_result = self.security_manager.validate_operation(str(abs_path), "write")
            if not validation_result.get("valid", False):
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Security validation failed",
                    "details": "; ".join(validation_result.get("errors", ["Access denied"])),
                    "file_path": file_path
                }
            
            # 确保父目录存在
            abs_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 异步写入文件
            async with aiofiles.open(abs_path, mode, encoding=encoding) as f:
                await f.write(content)
            
            file_size = len(content.encode(encoding))
            self.operation_stats["successful_operations"] += 1
            self.operation_stats["bytes_written"] += file_size
            
            logger.info(f"Successfully wrote file: {file_path} ({file_size} bytes)")
            
            return {
                "success": True,
                "file_path": file_path,
                "size": file_size,
                "encoding": encoding,
                "mode": mode,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.operation_stats["failed_operations"] += 1
            logger.error(f"Failed to write file {file_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }
    
    async def delete_file(self, file_path: str) -> Dict[str, Any]:
        """
        异步删除文件
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        self.operation_stats["total_operations"] += 1
        
        try:
            # 构建绝对路径
            abs_path = self.workspace_path / file_path

            # 先检查文件是否存在
            if not abs_path.exists():
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "File does not exist",
                    "file_path": file_path
                }

            # 安全验证
            validation_result = self.security_manager.validate_operation(str(abs_path), "delete")
            if not validation_result.get("valid", False):
                self.operation_stats["failed_operations"] += 1
                return {
                    "success": False,
                    "error": "Security validation failed",
                    "details": "; ".join(validation_result.get("errors", ["Access denied"])),
                    "file_path": file_path
                }
            
            file_size = abs_path.stat().st_size
            abs_path.unlink()
            
            self.operation_stats["successful_operations"] += 1
            
            logger.info(f"Successfully deleted file: {file_path}")
            
            return {
                "success": True,
                "file_path": file_path,
                "deleted_size": file_size,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.operation_stats["failed_operations"] += 1
            logger.error(f"Failed to delete file {file_path}: {e}")
            return {
                "success": False,
                "error": str(e),
                "file_path": file_path
            }
    
    async def list_files(self, directory: str = "", pattern: str = "*", 
                        recursive: bool = False) -> Dict[str, Any]:
        """
        列出目录中的文件
        
        Args:
            directory (str): 目录路径，空字符串表示根目录
            pattern (str): 文件名模式
            recursive (bool): 是否递归搜索
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            base_path = self.workspace_path / directory if directory else self.workspace_path

            # 检查目录是否在工作空间内
            if not str(base_path.resolve()).startswith(str(self.workspace_path.resolve())):
                return {
                    "success": False,
                    "error": "Directory outside workspace",
                    "directory": directory
                }

            files = []
            
            if recursive:
                for file_path in base_path.rglob(pattern):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(self.workspace_path)
                        files.append(await self._get_file_info(file_path, relative_path))
            else:
                for file_path in base_path.glob(pattern):
                    if file_path.is_file():
                        relative_path = file_path.relative_to(self.workspace_path)
                        files.append(await self._get_file_info(file_path, relative_path))
            
            return {
                "success": True,
                "directory": directory,
                "pattern": pattern,
                "recursive": recursive,
                "files": files,
                "count": len(files)
            }
            
        except Exception as e:
            logger.error(f"Failed to list files in {directory}: {e}")
            return {
                "success": False,
                "error": str(e),
                "directory": directory
            }
    
    async def search_files(self, query: str, search_content: bool = False, 
                          file_pattern: str = "*") -> Dict[str, Any]:
        """
        搜索文件
        
        Args:
            query (str): 搜索查询
            search_content (bool): 是否搜索文件内容
            file_pattern (str): 文件名模式
            
        Returns:
            Dict[str, Any]: 搜索结果
        """
        try:
            results = []
            
            for file_path in self.workspace_path.rglob(file_pattern):
                if not file_path.is_file():
                    continue
                
                relative_path = file_path.relative_to(self.workspace_path)
                
                # 检查文件名匹配
                name_match = query.lower() in file_path.name.lower()
                content_match = False
                
                # 检查文件内容匹配
                if search_content and self.security_manager.check_file_type(str(relative_path)):
                    try:
                        encoding = await self._detect_encoding(file_path)
                        async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                            content = await f.read()
                            content_match = query.lower() in content.lower()
                    except Exception:
                        pass  # 忽略无法读取的文件
                
                if name_match or content_match:
                    file_info = await self._get_file_info(file_path, relative_path)
                    file_info["match_type"] = []
                    if name_match:
                        file_info["match_type"].append("filename")
                    if content_match:
                        file_info["match_type"].append("content")
                    
                    results.append(file_info)
            
            return {
                "success": True,
                "query": query,
                "search_content": search_content,
                "file_pattern": file_pattern,
                "results": results,
                "count": len(results)
            }
            
        except Exception as e:
            logger.error(f"Failed to search files: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query
            }

    async def advanced_search_files(self, query: str, search_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        高级文件搜索功能

        Args:
            query (str): 搜索查询
            search_options (Dict[str, Any]): 搜索选项配置
                - search_content (bool): 是否搜索文件内容
                - file_pattern (str): 文件名模式
                - case_sensitive (bool): 是否区分大小写
                - regex_mode (bool): 是否使用正则表达式
                - max_results (int): 最大结果数量
                - include_hidden (bool): 是否包含隐藏文件
                - file_size_min (int): 最小文件大小（字节）
                - file_size_max (int): 最大文件大小（字节）
                - modified_after (str): 修改时间之后（ISO格式）
                - modified_before (str): 修改时间之前（ISO格式）
                - file_extensions (List[str]): 指定文件扩展名
                - exclude_patterns (List[str]): 排除的文件模式

        Returns:
            Dict[str, Any]: 搜索结果
        """
        try:
            # 设置默认搜索选项
            options = {
                "search_content": False,
                "file_pattern": "*",
                "case_sensitive": False,
                "regex_mode": False,
                "max_results": 1000,
                "include_hidden": False,
                "file_size_min": 0,
                "file_size_max": float('inf'),
                "modified_after": None,
                "modified_before": None,
                "file_extensions": [],
                "exclude_patterns": []
            }

            if search_options:
                options.update(search_options)

            results = []
            search_count = 0

            # 编译正则表达式（如果启用）
            import re
            search_pattern = None
            if options["regex_mode"]:
                try:
                    pattern_flags = 0 if options["case_sensitive"] else re.IGNORECASE
                    search_pattern = re.compile(query, pattern_flags)
                except re.error as e:
                    return {
                        "success": False,
                        "error": f"Invalid regex pattern: {e}",
                        "query": query
                    }

            # 解析时间过滤器
            from datetime import datetime
            modified_after = None
            modified_before = None

            if options["modified_after"]:
                try:
                    modified_after = datetime.fromisoformat(options["modified_after"])
                except ValueError:
                    return {
                        "success": False,
                        "error": "Invalid modified_after date format. Use ISO format.",
                        "query": query
                    }

            if options["modified_before"]:
                try:
                    modified_before = datetime.fromisoformat(options["modified_before"])
                except ValueError:
                    return {
                        "success": False,
                        "error": "Invalid modified_before date format. Use ISO format.",
                        "query": query
                    }

            # 遍历文件进行搜索
            for file_path in self.workspace_path.rglob(options["file_pattern"]):
                if not file_path.is_file():
                    continue

                # 检查结果数量限制
                if search_count >= options["max_results"]:
                    break

                relative_path = file_path.relative_to(self.workspace_path)

                # 检查隐藏文件
                if not options["include_hidden"] and any(part.startswith('.') for part in relative_path.parts):
                    continue

                # 检查排除模式
                if options["exclude_patterns"]:
                    excluded = False
                    for exclude_pattern in options["exclude_patterns"]:
                        if fnmatch.fnmatch(str(relative_path), exclude_pattern):
                            excluded = True
                            break
                    if excluded:
                        continue

                # 检查文件扩展名
                if options["file_extensions"]:
                    file_ext = file_path.suffix.lower()
                    if file_ext not in [ext.lower() for ext in options["file_extensions"]]:
                        continue

                # 获取文件信息
                try:
                    file_stat = file_path.stat()
                    file_size = file_stat.st_size
                    file_mtime = datetime.fromtimestamp(file_stat.st_mtime)
                except OSError:
                    continue

                # 检查文件大小过滤器
                if file_size < options["file_size_min"] or file_size > options["file_size_max"]:
                    continue

                # 检查修改时间过滤器
                if modified_after and file_mtime < modified_after:
                    continue
                if modified_before and file_mtime > modified_before:
                    continue

                # 执行搜索匹配
                name_match = False
                content_match = False
                match_details = {}

                # 检查文件名匹配
                if options["regex_mode"] and search_pattern:
                    name_match = bool(search_pattern.search(file_path.name))
                else:
                    if options["case_sensitive"]:
                        name_match = query in file_path.name
                    else:
                        name_match = query.lower() in file_path.name.lower()

                # 检查文件内容匹配
                if options["search_content"] and self.security_manager.check_file_type(str(relative_path)):
                    try:
                        encoding = await self._detect_encoding(file_path)
                        async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                            content = await f.read()

                            if options["regex_mode"] and search_pattern:
                                matches = list(search_pattern.finditer(content))
                                content_match = len(matches) > 0
                                if content_match:
                                    match_details["content_matches"] = len(matches)
                                    match_details["match_positions"] = [(m.start(), m.end()) for m in matches[:10]]  # 限制前10个匹配位置
                            else:
                                if options["case_sensitive"]:
                                    content_match = query in content
                                else:
                                    content_match = query.lower() in content.lower()

                                if content_match:
                                    # 计算匹配次数
                                    search_term = query if options["case_sensitive"] else query.lower()
                                    search_content = content if options["case_sensitive"] else content.lower()
                                    match_details["content_matches"] = search_content.count(search_term)

                    except Exception as e:
                        logger.warning(f"Failed to read file {relative_path} for content search: {e}")

                # 如果有匹配，添加到结果中
                if name_match or content_match:
                    file_info = await self._get_file_info(file_path, relative_path)
                    file_info.update({
                        "name_match": name_match,
                        "content_match": content_match,
                        "match_details": match_details
                    })
                    results.append(file_info)
                    search_count += 1

            return {
                "success": True,
                "query": query,
                "search_options": options,
                "results": results,
                "count": len(results),
                "total_searched": search_count,
                "truncated": search_count >= options["max_results"]
            }

        except Exception as e:
            logger.error(f"Failed to perform advanced search: {e}")
            return {
                "success": False,
                "error": str(e),
                "query": query
            }

    async def filter_files(self, filter_options: Dict[str, Any]) -> Dict[str, Any]:
        """
        文件过滤功能

        Args:
            filter_options (Dict[str, Any]): 过滤选项
                - file_pattern (str): 文件名模式
                - file_extensions (List[str]): 文件扩展名列表
                - file_size_min (int): 最小文件大小
                - file_size_max (int): 最大文件大小
                - modified_after (str): 修改时间之后
                - modified_before (str): 修改时间之前
                - include_hidden (bool): 是否包含隐藏文件
                - exclude_patterns (List[str]): 排除模式
                - sort_by (str): 排序字段 (name, size, modified)
                - sort_order (str): 排序顺序 (asc, desc)
                - max_results (int): 最大结果数量

        Returns:
            Dict[str, Any]: 过滤结果
        """
        try:
            # 设置默认过滤选项
            options = {
                "file_pattern": "*",
                "file_extensions": [],
                "file_size_min": 0,
                "file_size_max": float('inf'),
                "modified_after": None,
                "modified_before": None,
                "include_hidden": False,
                "exclude_patterns": [],
                "sort_by": "name",
                "sort_order": "asc",
                "max_results": 1000
            }

            options.update(filter_options)

            results = []

            # 解析时间过滤器
            from datetime import datetime
            modified_after = None
            modified_before = None

            if options["modified_after"]:
                try:
                    modified_after = datetime.fromisoformat(options["modified_after"])
                except ValueError:
                    return {
                        "success": False,
                        "error": "Invalid modified_after date format. Use ISO format."
                    }

            if options["modified_before"]:
                try:
                    modified_before = datetime.fromisoformat(options["modified_before"])
                except ValueError:
                    return {
                        "success": False,
                        "error": "Invalid modified_before date format. Use ISO format."
                    }

            # 遍历文件进行过滤
            for file_path in self.workspace_path.rglob(options["file_pattern"]):
                if not file_path.is_file():
                    continue

                relative_path = file_path.relative_to(self.workspace_path)

                # 检查隐藏文件
                if not options["include_hidden"] and any(part.startswith('.') for part in relative_path.parts):
                    continue

                # 检查排除模式
                if options["exclude_patterns"]:
                    excluded = False
                    for exclude_pattern in options["exclude_patterns"]:
                        if fnmatch.fnmatch(str(relative_path), exclude_pattern):
                            excluded = True
                            break
                    if excluded:
                        continue

                # 检查文件扩展名
                if options["file_extensions"]:
                    file_ext = file_path.suffix.lower()
                    if file_ext not in [ext.lower() for ext in options["file_extensions"]]:
                        continue

                # 获取文件信息
                try:
                    file_stat = file_path.stat()
                    file_size = file_stat.st_size
                    file_mtime = datetime.fromtimestamp(file_stat.st_mtime)
                except OSError:
                    continue

                # 检查文件大小过滤器
                if file_size < options["file_size_min"] or file_size > options["file_size_max"]:
                    continue

                # 检查修改时间过滤器
                if modified_after and file_mtime < modified_after:
                    continue
                if modified_before and file_mtime > modified_before:
                    continue

                # 添加到结果中
                file_info = await self._get_file_info(file_path, relative_path)
                results.append(file_info)

            # 排序结果
            if options["sort_by"] == "name":
                results.sort(key=lambda x: x["name"], reverse=(options["sort_order"] == "desc"))
            elif options["sort_by"] == "size":
                results.sort(key=lambda x: x["size"], reverse=(options["sort_order"] == "desc"))
            elif options["sort_by"] == "modified":
                results.sort(key=lambda x: x["modified"], reverse=(options["sort_order"] == "desc"))

            # 限制结果数量
            if len(results) > options["max_results"]:
                results = results[:options["max_results"]]
                truncated = True
            else:
                truncated = False

            return {
                "success": True,
                "filter_options": options,
                "results": results,
                "count": len(results),
                "truncated": truncated
            }

        except Exception as e:
            logger.error(f"Failed to filter files: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _detect_encoding(self, file_path: Path) -> str:
        """
        检测文件编码
        
        Args:
            file_path (Path): 文件路径
            
        Returns:
            str: 检测到的编码
        """
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(8192)  # 读取前8KB用于检测
            
            result = chardet.detect(raw_data)
            encoding = result.get('encoding', 'utf-8')
            
            # 如果检测结果不确定，使用utf-8作为默认值
            if not encoding or result.get('confidence', 0) < 0.7:
                encoding = 'utf-8'
            
            return encoding
            
        except Exception:
            return 'utf-8'  # 默认编码
    
    async def _get_file_info(self, file_path: Path, relative_path: Path) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path (Path): 绝对文件路径
            relative_path (Path): 相对文件路径
            
        Returns:
            Dict[str, Any]: 文件信息
        """
        try:
            stat = file_path.stat()
            return {
                "path": str(relative_path),
                "name": file_path.name,
                "size": stat.st_size,
                "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "extension": file_path.suffix,
                "is_readable": os.access(file_path, os.R_OK),
                "is_writable": os.access(file_path, os.W_OK)
            }
        except Exception as e:
            return {
                "path": str(relative_path),
                "name": file_path.name,
                "error": str(e)
            }
    
    def get_operation_stats(self) -> Dict[str, Any]:
        """
        获取操作统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self.operation_stats.copy()
        if stats["total_operations"] > 0:
            stats["success_rate"] = stats["successful_operations"] / stats["total_operations"]
        else:
            stats["success_rate"] = 0.0
        
        stats["workspace_path"] = str(self.workspace_path)
        return stats
    
    def reset_stats(self) -> None:
        """重置操作统计"""
        self.operation_stats = {
            "total_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "bytes_read": 0,
            "bytes_written": 0
        }
        logger.info("Operation statistics reset")
