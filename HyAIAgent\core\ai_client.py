"""
AI客户端模块
负责与OpenAI API通信，管理对话历史和处理AI响应
"""

import openai
from typing import List, Dict, Optional, Any
from loguru import logger
import time


class SimpleAIClient:
    """
    简单的AI客户端
    负责与OpenAI兼容的API进行通信，支持对话历史管理
    """
    
    def __init__(self, api_key: str, base_url: str = "https://api.openai.com/v1",
                 model: str = "gpt-3.5-turbo", max_tokens: int = 1000, 
                 temperature: float = 0.7, timeout: int = 30):
        """
        初始化AI客户端
        
        Args:
            api_key (str): API密钥
            base_url (str): API基础URL
            model (str): 模型名称
            max_tokens (int): 最大token数
            temperature (float): 温度参数
            timeout (int): 超时时间（秒）
        """
        self.api_key = api_key
        self.base_url = base_url
        self.model = model
        self.max_tokens = max_tokens
        self.temperature = temperature
        self.timeout = timeout
        self.conversation_history = []
        
        # 初始化OpenAI客户端
        self.client = openai.OpenAI(
            api_key=api_key,
            base_url=base_url,
            timeout=timeout
        )
        
        logger.info(f"AI客户端初始化完成 - 模型: {model}, 基础URL: {base_url}")
    
    def chat(self, message: str, system_prompt: Optional[str] = None) -> str:
        """
        发送消息并获取AI回复
        
        Args:
            message (str): 用户消息
            system_prompt (str, optional): 系统提示词
            
        Returns:
            str: AI回复内容
        """
        try:
            # 构建消息列表
            messages = []
            
            # 添加系统提示词（如果提供）
            if system_prompt:
                messages.append({
                    "role": "system",
                    "content": system_prompt
                })
            
            # 添加对话历史
            messages.extend(self.conversation_history)
            
            # 添加当前用户消息
            user_message = {
                "role": "user", 
                "content": message
            }
            messages.append(user_message)
            
            logger.info(f"发送消息到AI: {message[:100]}...")
            
            # 调用OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            # 获取AI回复
            ai_response = response.choices[0].message.content
            
            # 添加到对话历史
            self.conversation_history.append(user_message)
            self.conversation_history.append({
                "role": "assistant",
                "content": ai_response
            })
            
            # 限制历史长度（保留最近20轮对话）
            if len(self.conversation_history) > 40:  # 20轮 * 2条消息
                self.conversation_history = self.conversation_history[-40:]
            
            logger.info(f"收到AI回复: {ai_response[:100]}...")
            return ai_response
            
        except openai.APIError as e:
            error_msg = f"OpenAI API错误: {str(e)}"
            logger.error(error_msg)
            return f"抱歉，发生了API错误: {str(e)}"
            
        except openai.RateLimitError as e:
            error_msg = "API调用频率超限，请稍后再试"
            logger.error(error_msg)
            return error_msg
            
        except openai.APITimeoutError as e:
            error_msg = "API调用超时，请检查网络连接"
            logger.error(error_msg)
            return error_msg
            
        except Exception as e:
            error_msg = f"发生未知错误: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def chat_stream(self, message: str, system_prompt: Optional[str] = None):
        """
        流式发送消息并获取AI回复
        
        Args:
            message (str): 用户消息
            system_prompt (str, optional): 系统提示词
            
        Yields:
            str: AI回复的片段
        """
        try:
            # 构建消息列表
            messages = []
            
            if system_prompt:
                messages.append({
                    "role": "system",
                    "content": system_prompt
                })
            
            messages.extend(self.conversation_history)
            
            user_message = {
                "role": "user", 
                "content": message
            }
            messages.append(user_message)
            
            logger.info(f"开始流式发送消息到AI: {message[:100]}...")
            
            # 调用OpenAI流式API
            stream = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                stream=True
            )
            
            full_response = ""
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    full_response += content
                    yield content
            
            # 添加到对话历史
            self.conversation_history.append(user_message)
            self.conversation_history.append({
                "role": "assistant",
                "content": full_response
            })
            
            # 限制历史长度
            if len(self.conversation_history) > 40:
                self.conversation_history = self.conversation_history[-40:]
            
            logger.info(f"流式回复完成: {full_response[:100]}...")
            
        except Exception as e:
            error_msg = f"流式调用失败: {str(e)}"
            logger.error(error_msg)
            yield error_msg
    
    def clear_history(self) -> None:
        """清空对话历史"""
        self.conversation_history = []
        logger.info("对话历史已清空")
    
    def get_history(self) -> List[Dict[str, str]]:
        """
        获取对话历史
        
        Returns:
            List[Dict[str, str]]: 对话历史列表
        """
        return self.conversation_history.copy()
    
    def get_history_summary(self) -> str:
        """
        获取对话历史摘要
        
        Returns:
            str: 对话历史摘要信息
        """
        if not self.conversation_history:
            return "暂无对话历史"
        
        user_count = sum(1 for msg in self.conversation_history if msg['role'] == 'user')
        assistant_count = sum(1 for msg in self.conversation_history if msg['role'] == 'assistant')
        
        return f"对话轮次: {user_count}, AI回复: {assistant_count}"
    
    def set_model(self, model: str) -> None:
        """
        设置模型
        
        Args:
            model (str): 新的模型名称
        """
        self.model = model
        logger.info(f"模型已切换为: {model}")
    
    def set_temperature(self, temperature: float) -> None:
        """
        设置温度参数
        
        Args:
            temperature (float): 温度值 (0.0-2.0)
        """
        if 0.0 <= temperature <= 2.0:
            self.temperature = temperature
            logger.info(f"温度参数已设置为: {temperature}")
        else:
            logger.warning(f"温度参数超出范围 (0.0-2.0): {temperature}")
    
    def set_max_tokens(self, max_tokens: int) -> None:
        """
        设置最大token数
        
        Args:
            max_tokens (int): 最大token数
        """
        if max_tokens > 0:
            self.max_tokens = max_tokens
            logger.info(f"最大token数已设置为: {max_tokens}")
        else:
            logger.warning(f"最大token数必须大于0: {max_tokens}")
    
    def get_config(self) -> Dict[str, Any]:
        """
        获取当前配置
        
        Returns:
            Dict[str, Any]: 当前配置信息
        """
        return {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "base_url": self.base_url,
            "history_length": len(self.conversation_history)
        }
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 发送一个简单的测试消息
            test_response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            
            if test_response.choices[0].message.content:
                logger.info("API连接测试成功")
                return True
            else:
                logger.error("API连接测试失败：无响应内容")
                return False
                
        except Exception as e:
            logger.error(f"API连接测试失败: {str(e)}")
            return False
