"""
任务提示词测试

验证专用提示词文件是否能正确加载和使用
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_prompt_files_exist():
    """测试提示词文件是否存在"""
    print("🚀 开始测试提示词文件存在性...")
    
    # 定义需要检查的提示词文件
    prompt_files = [
        "prompts/tasks/file_operations.md",
        "prompts/tasks/document_analysis.md", 
        "prompts/tasks/batch_processing.md"
    ]
    
    project_root = Path(__file__).parent.parent
    all_exist = True
    
    for prompt_file in prompt_files:
        file_path = project_root / prompt_file
        if file_path.exists():
            print(f"✅ 提示词文件存在: {prompt_file}")
        else:
            print(f"❌ 提示词文件不存在: {prompt_file}")
            all_exist = False
    
    return all_exist


def test_prompt_content_quality():
    """测试提示词内容质量"""
    print("\n🔍 开始测试提示词内容质量...")
    
    project_root = Path(__file__).parent.parent
    prompt_files = {
        "file_operations.md": "文件操作专用提示词",
        "document_analysis.md": "文档分析专用提示词",
        "batch_processing.md": "批量处理专用提示词"
    }
    
    all_quality_good = True
    
    for filename, description in prompt_files.items():
        file_path = project_root / "prompts" / "tasks" / filename
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查内容长度
            if len(content) < 1000:
                print(f"❌ {description} 内容过短: {len(content)} 字符")
                all_quality_good = False
                continue
            
            # 检查关键章节
            required_sections = ["角色定义", "处理流程", "注意事项", "目标"]
            missing_sections = []
            
            for section in required_sections:
                if section not in content:
                    missing_sections.append(section)
            
            if missing_sections:
                print(f"❌ {description} 缺少关键章节: {', '.join(missing_sections)}")
                all_quality_good = False
            else:
                print(f"✅ {description} 内容质量良好: {len(content)} 字符")
                
        except Exception as e:
            print(f"❌ 读取 {description} 时发生错误: {str(e)}")
            all_quality_good = False
    
    return all_quality_good


def test_prompt_manager_integration():
    """测试与PromptManager的集成"""
    print("\n🔧 开始测试PromptManager集成...")
    
    try:
        from core.prompt_manager import PromptManager
        
        # 初始化PromptManager
        project_root = Path(__file__).parent.parent
        prompt_manager = PromptManager(str(project_root / "prompts"))
        
        # 测试加载任务提示词（如果PromptManager支持的话）
        print("✅ PromptManager初始化成功")
        
        # 检查prompts目录结构
        prompts_dir = project_root / "prompts"
        tasks_dir = prompts_dir / "tasks"
        
        if tasks_dir.exists():
            task_files = list(tasks_dir.glob("*.md"))
            print(f"✅ 发现 {len(task_files)} 个任务提示词文件")
            for task_file in task_files:
                print(f"   - {task_file.name}")
        else:
            print("❌ tasks目录不存在")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入PromptManager: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ PromptManager集成测试失败: {str(e)}")
        return False


def test_prompt_template_format():
    """测试提示词模板格式"""
    print("\n📝 开始测试提示词模板格式...")
    
    project_root = Path(__file__).parent.parent
    tasks_dir = project_root / "prompts" / "tasks"
    
    all_format_good = True
    
    for prompt_file in tasks_dir.glob("*.md"):
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查Markdown格式
            has_headers = any(line.startswith('#') for line in content.split('\n'))
            has_code_blocks = '```' in content
            has_emoji = any(char in content for char in ['🎯', '📋', '🔍', '⚡', '🛡️'])
            
            format_score = sum([has_headers, has_code_blocks, has_emoji])
            
            if format_score >= 2:
                print(f"✅ {prompt_file.name} 格式良好 (评分: {format_score}/3)")
            else:
                print(f"❌ {prompt_file.name} 格式需要改进 (评分: {format_score}/3)")
                all_format_good = False
                
        except Exception as e:
            print(f"❌ 检查 {prompt_file.name} 格式时发生错误: {str(e)}")
            all_format_good = False
    
    return all_format_good


def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 任务提示词测试套件")
    print("=" * 60)
    
    # 运行各项测试
    test_results = []
    
    test_results.append(("文件存在性测试", test_prompt_files_exist()))
    test_results.append(("内容质量测试", test_prompt_content_quality()))
    test_results.append(("PromptManager集成测试", test_prompt_manager_integration()))
    test_results.append(("模板格式测试", test_prompt_template_format()))
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！任务提示词系统功能正常。")
        return True
    else:
        print("\n❌ 部分测试失败，需要检查任务提示词实现。")
        return False


if __name__ == "__main__":
    # 运行测试
    success = main()
    exit(0 if success else 1)
