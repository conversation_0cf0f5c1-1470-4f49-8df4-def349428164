"""
第四阶段简化集成测试
测试步骤4.19 - 验证提示词模板和基本集成功能
"""

import pytest
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

class TestPhase4SimpleIntegration:
    """第四阶段简化集成测试类"""
    
    def setup_method(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.prompts_dir = Path("HyAIAgent/prompts/tasks")
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_prompt_templates_exist_and_valid(self):
        """测试1: 验证提示词模板存在且有效"""
        template_files = [
            "web_search.md",
            "information_analysis.md",
            "content_summary.md", 
            "research_planning.md"
        ]
        
        for template_file in template_files:
            template_path = self.prompts_dir / template_file
            
            # 验证文件存在
            assert template_path.exists(), f"提示词模板文件不存在: {template_file}"
            
            # 验证文件内容
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 基本内容验证
            assert len(content) > 1000, f"模板文件内容过少: {template_file}"
            assert content.count('{') > 10, f"模板变量不足: {template_file}"
            assert content.count('}') > 10, f"模板变量不足: {template_file}"
            assert '```' in content, f"缺少代码块: {template_file}"
            assert '#' in content, f"缺少标题: {template_file}"
    
    def test_web_search_template_structure(self):
        """测试2: 验证网络搜索模板结构"""
        template_path = self.prompts_dir / "web_search.md"
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键章节
        required_sections = [
            "基础搜索提示词",
            "专业领域搜索提示词",
            "搜索结果处理提示词",
            "特殊场景搜索提示词"
        ]
        
        for section in required_sections:
            assert section in content, f"缺少必要章节: {section}"
        
        # 检查关键模板变量
        key_variables = [
            "{original_query}",
            "{search_context}",
            "{search_results}",
            "{tech_query}",
            "{business_query}"
        ]
        
        for var in key_variables:
            assert var in content, f"缺少关键变量: {var}"
    
    def test_information_analysis_template_structure(self):
        """测试3: 验证信息分析模板结构"""
        template_path = self.prompts_dir / "information_analysis.md"
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键章节
        required_sections = [
            "基础信息分析",
            "专业领域分析",
            "高级分析技术"
        ]
        
        for section in required_sections:
            assert section in content, f"缺少必要章节: {section}"
        
        # 检查关键模板变量
        key_variables = [
            "{raw_information}",
            "{analysis_goal}",
            "{technical_information}",
            "{business_information}"
        ]
        
        for var in key_variables:
            assert var in content, f"缺少关键变量: {var}"
    
    def test_content_summary_template_structure(self):
        """测试4: 验证内容摘要模板结构"""
        template_path = self.prompts_dir / "content_summary.md"
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键章节
        required_sections = [
            "基础摘要生成",
            "专业领域摘要",
            "特殊类型摘要",
            "摘要质量控制"
        ]
        
        for section in required_sections:
            assert section in content, f"缺少必要章节: {section}"
        
        # 检查关键模板变量
        key_variables = [
            "{original_content}",
            "{summary_length}",
            "{target_audience}",
            "{technical_content}"
        ]
        
        for var in key_variables:
            assert var in content, f"缺少关键变量: {var}"
    
    def test_research_planning_template_structure(self):
        """测试5: 验证调研规划模板结构"""
        template_path = self.prompts_dir / "research_planning.md"
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键章节
        required_sections = [
            "基础调研规划",
            "专业领域调研规划",
            "调研执行管理"
        ]
        
        for section in required_sections:
            assert section in content, f"缺少必要章节: {section}"
        
        # 检查关键模板变量
        key_variables = [
            "{research_topic}",
            "{research_objectives}",
            "{market_research_topic}",
            "{tech_research_topic}"
        ]
        
        for var in key_variables:
            assert var in content, f"缺少关键变量: {var}"
    
    def test_template_variable_consistency(self):
        """测试6: 验证模板变量一致性"""
        template_files = [
            "web_search.md",
            "information_analysis.md",
            "content_summary.md",
            "research_planning.md"
        ]
        
        all_variables = set()
        
        for template_file in template_files:
            template_path = self.prompts_dir / template_file
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取所有变量
            import re
            variables = re.findall(r'\{([^}]+)\}', content)
            all_variables.update(variables)
        
        # 验证变量命名规范
        for var in all_variables:
            # 变量名应该是小写字母、数字和下划线
            assert re.match(r'^[a-z0-9_]+$', var), f"变量名格式不正确: {var}"
            # 变量名不应该过短
            assert len(var) >= 3, f"变量名过短: {var}"
    
    def test_template_code_block_syntax(self):
        """测试7: 验证模板代码块语法"""
        template_files = [
            "web_search.md",
            "information_analysis.md", 
            "content_summary.md",
            "research_planning.md"
        ]
        
        for template_file in template_files:
            template_path = self.prompts_dir / template_file
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查代码块配对
            code_block_count = content.count('```')
            assert code_block_count % 2 == 0, f"代码块未正确配对: {template_file}"
            
            # 检查至少有一些代码块
            assert code_block_count >= 10, f"代码块数量不足: {template_file}"
    
    def test_template_markdown_structure(self):
        """测试8: 验证模板Markdown结构"""
        template_files = [
            "web_search.md",
            "information_analysis.md",
            "content_summary.md", 
            "research_planning.md"
        ]
        
        for template_file in template_files:
            template_path = self.prompts_dir / template_file
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查标题层次
            import re
            h1_count = len(re.findall(r'^#\s+', content, re.MULTILINE))
            h2_count = len(re.findall(r'^##\s+', content, re.MULTILINE))
            h3_count = len(re.findall(r'^###\s+', content, re.MULTILINE))
            
            assert h1_count >= 1, f"缺少一级标题: {template_file}"
            assert h2_count >= 3, f"二级标题不足: {template_file}"
            assert h3_count >= 5, f"三级标题不足: {template_file}"
    
    def test_template_content_quality(self):
        """测试9: 验证模板内容质量"""
        template_files = [
            "web_search.md",
            "information_analysis.md",
            "content_summary.md",
            "research_planning.md"
        ]
        
        for template_file in template_files:
            template_path = self.prompts_dir / template_file
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 内容长度检查 (调整为更合理的标准)
            assert len(content) > 3000, f"模板内容过短: {template_file}"
            
            # 中文内容检查
            chinese_chars = len([c for c in content if '\u4e00' <= c <= '\u9fff'])
            assert chinese_chars > 100, f"中文内容不足: {template_file}"
            
            # 模板变量密度检查
            import re
            variables = re.findall(r'\{[^}]+\}', content)
            variable_density = len(variables) / len(content) * 1000
            assert variable_density > 5, f"模板变量密度过低: {template_file}"
    
    def test_integration_workflow_simulation(self):
        """测试10: 集成工作流程模拟"""
        # 模拟一个完整的工作流程：搜索 -> 处理 -> 分析 -> 摘要
        
        # 1. 模拟搜索结果
        mock_search_result = {
            'title': '人工智能发展报告',
            'content': '人工智能技术在2024年取得了重大突破...',
            'url': 'https://example.com/ai-report'
        }
        
        # 2. 模拟内容处理
        mock_processed_content = {
            'processed_content': '处理后的AI发展报告内容',
            'metadata': {'content_type': 'text', 'word_count': 500}
        }
        
        # 3. 模拟信息分析
        mock_analysis_result = {
            'analysis_result': {
                'key_topics': ['人工智能', '技术突破', '发展趋势'],
                'sentiment': 'positive',
                'summary': 'AI技术发展迅速'
            },
            'metadata': {'analysis_type': 'comprehensive'}
        }
        
        # 4. 模拟摘要生成
        mock_summary = {
            'summary': '2024年人工智能技术取得重大突破，主要体现在...',
            'key_points': ['技术突破', '应用扩展', '产业影响'],
            'metadata': {'summary_type': 'executive'}
        }
        
        # 验证数据流转
        assert mock_search_result['content'] is not None
        assert mock_processed_content['processed_content'] is not None
        assert len(mock_analysis_result['analysis_result']['key_topics']) > 0
        assert len(mock_summary['summary']) > 0
        
        # 验证数据结构
        assert 'title' in mock_search_result
        assert 'metadata' in mock_processed_content
        assert 'analysis_result' in mock_analysis_result
        assert 'key_points' in mock_summary
    
    def test_file_operations_integration(self):
        """测试11: 文件操作集成"""
        # 测试保存和读取功能
        test_data = {
            'search_results': [
                {'title': '测试1', 'content': '内容1'},
                {'title': '测试2', 'content': '内容2'}
            ],
            'analysis_summary': {
                'key_topics': ['测试', '集成'],
                'overall_sentiment': 'positive'
            },
            'timestamp': '2025-07-30T12:30:00'
        }
        
        # 保存数据
        save_path = os.path.join(self.temp_dir, 'integration_test.json')
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        # 验证文件存在
        assert os.path.exists(save_path)
        
        # 读取并验证数据
        with open(save_path, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        assert loaded_data == test_data
        assert len(loaded_data['search_results']) == 2
        assert 'key_topics' in loaded_data['analysis_summary']
    
    def test_configuration_validation(self):
        """测试12: 配置验证"""
        # 测试配置结构
        test_config = {
            'search': {
                'max_results': 10,
                'timeout': 30,
                'engines': ['tavily', 'web_search']
            },
            'processing': {
                'max_content_length': 10000,
                'chunk_size': 1000
            },
            'analysis': {
                'confidence_threshold': 0.7
            },
            'storage': {
                'base_path': self.temp_dir
            }
        }
        
        # 验证配置完整性
        assert 'search' in test_config
        assert 'processing' in test_config
        assert 'analysis' in test_config
        assert 'storage' in test_config
        
        # 验证配置值
        assert test_config['search']['max_results'] > 0
        assert test_config['processing']['chunk_size'] > 0
        assert 0 < test_config['analysis']['confidence_threshold'] < 1
        assert os.path.exists(test_config['storage']['base_path'])

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
