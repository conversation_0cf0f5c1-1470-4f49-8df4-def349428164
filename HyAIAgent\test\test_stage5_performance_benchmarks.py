#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
第五阶段性能基准测试
测试系统在各种负载条件下的性能表现
"""

import asyncio
import pytest
import sys
import os
import time
import psutil
import threading
import concurrent.futures
from pathlib import Path
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock
import statistics

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.config_manager import ConfigManager
from core.kv_store import KVStore
from monitoring.performance_monitor import PerformanceMonitor
from tools.chart_generator import ChartGenerator
from advanced.reasoning_engine import ReasoningEngine

class TestStage5PerformanceBenchmarks:
    """第五阶段性能基准测试类"""
    
    @pytest.fixture(autouse=True)
    async def setup_method(self):
        """测试前置设置"""
        self.config_manager = ConfigManager()
        self.kv_store = KVStore(db_path="data/test_performance.json")
        self.performance_monitor = PerformanceMonitor()
        
        # Mock AI客户端
        self.ai_client = Mock()
        self.ai_client.chat = AsyncMock(return_value="性能测试响应")
        
        # 性能基准数据
        self.benchmark_results = {}
        
        yield
        
        # 清理
        await self.kv_store.close()
        if os.path.exists("data/test_performance.json"):
            os.remove("data/test_performance.json")
    
    async def measure_performance(self, test_name: str, test_func, *args, **kwargs):
        """测量性能指标"""
        # 获取初始系统状态
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        initial_cpu = psutil.cpu_percent(interval=0.1)
        
        start_time = time.time()
        
        try:
            # 执行测试函数
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func(*args, **kwargs)
            else:
                result = test_func(*args, **kwargs)
            
            end_time = time.time()
            
            # 获取结束时系统状态
            final_memory = psutil.Process().memory_info().rss / 1024 / 1024
            final_cpu = psutil.cpu_percent(interval=0.1)
            
            # 计算性能指标
            duration = end_time - start_time
            memory_delta = final_memory - initial_memory
            cpu_delta = final_cpu - initial_cpu
            
            self.benchmark_results[test_name] = {
                'duration': duration,
                'memory_usage': memory_delta,
                'cpu_usage': cpu_delta,
                'success': True,
                'result': result
            }
            
            return result
            
        except Exception as e:
            self.benchmark_results[test_name] = {
                'duration': time.time() - start_time,
                'memory_usage': 0,
                'cpu_usage': 0,
                'success': False,
                'error': str(e)
            }
            raise
    
    @pytest.mark.asyncio
    async def test_kv_store_performance(self):
        """测试KV存储性能"""
        
        async def kv_write_test():
            """KV写入性能测试"""
            for i in range(1000):
                await self.kv_store.set(f"test_key_{i}", f"test_value_{i}")
            return 1000
        
        async def kv_read_test():
            """KV读取性能测试"""
            results = []
            for i in range(1000):
                value = self.kv_store.get(f"test_key_{i}")
                results.append(value)
            return len([r for r in results if r is not None])
        
        # 测试写入性能
        write_count = await self.measure_performance("kv_write_1000", kv_write_test)
        assert write_count == 1000
        
        # 测试读取性能
        read_count = await self.measure_performance("kv_read_1000", kv_read_test)
        assert read_count == 1000
        
        print(f"✅ KV存储性能测试完成")
        print(f"   写入1000条记录: {self.benchmark_results['kv_write_1000']['duration']:.3f}s")
        print(f"   读取1000条记录: {self.benchmark_results['kv_read_1000']['duration']:.3f}s")
    
    @pytest.mark.asyncio
    async def test_concurrent_operations_performance(self):
        """测试并发操作性能"""
        
        async def concurrent_kv_operations():
            """并发KV操作"""
            tasks = []
            
            # 创建100个并发写入任务
            for i in range(100):
                task = asyncio.create_task(
                    self.kv_store.set(f"concurrent_key_{i}", f"concurrent_value_{i}")
                )
                tasks.append(task)
            
            # 等待所有任务完成
            await asyncio.gather(*tasks)
            return len(tasks)
        
        # 测试并发操作
        task_count = await self.measure_performance("concurrent_kv_100", concurrent_kv_operations)
        assert task_count == 100
        
        print(f"✅ 并发操作性能测试完成")
        print(f"   100个并发KV操作: {self.benchmark_results['concurrent_kv_100']['duration']:.3f}s")
    
    @pytest.mark.asyncio
    async def test_memory_usage_under_load(self):
        """测试负载下的内存使用"""
        
        async def memory_stress_test():
            """内存压力测试"""
            data_store = []
            
            # 创建大量数据
            for i in range(10000):
                data = {
                    'id': i,
                    'data': f"test_data_{i}" * 100,  # 创建较大的字符串
                    'metadata': {
                        'timestamp': time.time(),
                        'index': i,
                        'category': f"category_{i % 10}"
                    }
                }
                data_store.append(data)
                
                # 每1000条记录检查一次内存
                if i % 1000 == 0:
                    current_memory = psutil.Process().memory_info().rss / 1024 / 1024
                    if current_memory > 500:  # 如果内存超过500MB，停止测试
                        break
            
            return len(data_store)
        
        # 测试内存使用
        data_count = await self.measure_performance("memory_stress", memory_stress_test)
        
        memory_usage = self.benchmark_results['memory_stress']['memory_usage']
        print(f"✅ 内存压力测试完成")
        print(f"   创建{data_count}条记录，内存增长: {memory_usage:.2f}MB")
        
        # 内存使用不应超过200MB
        assert memory_usage < 200, f"内存使用过多: {memory_usage:.2f}MB"
    
    @pytest.mark.asyncio
    async def test_chart_generation_performance(self):
        """测试图表生成性能"""
        
        async def chart_generation_test():
            """图表生成性能测试"""
            chart_generator = ChartGenerator()
            results = []
            
            # 生成多种类型的图表
            chart_types = ['bar', 'line', 'pie', 'scatter']
            
            for chart_type in chart_types:
                for size in [10, 50, 100]:  # 不同数据量
                    chart_data = {
                        'type': chart_type,
                        'data': {
                            'labels': [f'Item_{i}' for i in range(size)],
                            'values': [i * 2 for i in range(size)]
                        },
                        'title': f'{chart_type.title()} Chart - {size} items'
                    }
                    
                    result = await chart_generator.generate_chart(chart_data)
                    results.append(result.success)
            
            return sum(results)
        
        # 测试图表生成
        success_count = await self.measure_performance("chart_generation", chart_generation_test)
        
        print(f"✅ 图表生成性能测试完成")
        print(f"   成功生成{success_count}个图表，用时: {self.benchmark_results['chart_generation']['duration']:.3f}s")
    
    @pytest.mark.asyncio
    async def test_reasoning_engine_performance(self):
        """测试推理引擎性能"""
        
        async def reasoning_performance_test():
            """推理引擎性能测试"""
            reasoning_engine = ReasoningEngine(
                ai_client=self.ai_client,
                kv_store=self.kv_store
            )
            
            results = []
            problems = [
                "如何优化数据库查询性能？",
                "什么是最佳的软件架构模式？",
                "如何实现高可用系统？",
                "微服务架构的优缺点是什么？",
                "如何进行有效的代码审查？"
            ]
            
            for problem in problems:
                reasoning_chain = await reasoning_engine.multi_step_reasoning(problem)
                results.append(reasoning_chain is not None)
            
            return sum(results)
        
        # 测试推理性能
        success_count = await self.measure_performance("reasoning_performance", reasoning_performance_test)
        
        print(f"✅ 推理引擎性能测试完成")
        print(f"   完成{success_count}个推理任务，用时: {self.benchmark_results['reasoning_performance']['duration']:.3f}s")
    
    @pytest.mark.asyncio
    async def test_system_monitoring_performance(self):
        """测试系统监控性能"""
        
        async def monitoring_performance_test():
            """监控性能测试"""
            results = []
            
            # 连续监控100次
            for i in range(100):
                metrics = await self.performance_monitor.collect_system_metrics()
                results.append(len(metrics) > 0)
                
                # 短暂延迟避免过度占用CPU
                await asyncio.sleep(0.01)
            
            return sum(results)
        
        # 测试监控性能
        success_count = await self.measure_performance("monitoring_performance", monitoring_performance_test)
        
        print(f"✅ 系统监控性能测试完成")
        print(f"   完成{success_count}次监控采集，用时: {self.benchmark_results['monitoring_performance']['duration']:.3f}s")
    
    def test_performance_benchmark_summary(self):
        """性能基准测试总结"""
        if not self.benchmark_results:
            pytest.skip("没有性能基准数据")
        
        print("\n" + "="*70)
        print("📊 第五阶段性能基准测试总结")
        print("="*70)
        
        # 计算总体统计
        successful_tests = [r for r in self.benchmark_results.values() if r['success']]
        failed_tests = [r for r in self.benchmark_results.values() if not r['success']]
        
        if successful_tests:
            durations = [r['duration'] for r in successful_tests]
            memory_usages = [r['memory_usage'] for r in successful_tests]
            
            print(f"成功测试数量: {len(successful_tests)}")
            print(f"失败测试数量: {len(failed_tests)}")
            print(f"平均执行时间: {statistics.mean(durations):.3f}s")
            print(f"最大执行时间: {max(durations):.3f}s")
            print(f"最小执行时间: {min(durations):.3f}s")
            print(f"平均内存使用: {statistics.mean(memory_usages):.2f}MB")
            print(f"最大内存使用: {max(memory_usages):.2f}MB")
        
        print("\n详细基准数据:")
        for test_name, result in self.benchmark_results.items():
            status = "✅" if result['success'] else "❌"
            print(f"  {status} {test_name}:")
            print(f"     执行时间: {result['duration']:.3f}s")
            print(f"     内存使用: {result['memory_usage']:.2f}MB")
            if not result['success']:
                print(f"     错误信息: {result.get('error', 'Unknown error')}")
        
        # 性能基准检查
        if successful_tests:
            max_duration = max(r['duration'] for r in successful_tests)
            max_memory = max(r['memory_usage'] for r in successful_tests)
            
            # 性能要求
            assert max_duration < 10.0, f"单个测试执行时间过长: {max_duration:.3f}s"
            assert max_memory < 150.0, f"单个测试内存使用过多: {max_memory:.2f}MB"
            
            print(f"\n✅ 所有性能基准符合要求")
            print(f"   最大执行时间: {max_duration:.3f}s (要求 < 10.0s)")
            print(f"   最大内存使用: {max_memory:.2f}MB (要求 < 150.0MB)")
        else:
            pytest.fail("所有性能测试都失败了")

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
