"""
测试提示词模板功能
测试步骤4.18 - 搜索专用提示词的各种模板
"""

import pytest
import os
import re
from pathlib import Path

class TestPromptTemplates:
    """测试提示词模板类"""
    
    def setup_method(self):
        """测试前准备"""
        self.prompts_dir = Path("HyAIAgent/prompts/tasks")
        self.template_files = [
            "web_search.md",
            "information_analysis.md", 
            "content_summary.md",
            "research_planning.md"
        ]
    
    def test_prompt_files_exist(self):
        """测试1: 验证所有提示词文件是否存在"""
        for template_file in self.template_files:
            file_path = self.prompts_dir / template_file
            assert file_path.exists(), f"提示词文件不存在: {file_path}"
            assert file_path.is_file(), f"路径不是文件: {file_path}"
    
    def test_web_search_template_structure(self):
        """测试2: 验证网络搜索模板结构"""
        file_path = self.prompts_dir / "web_search.md"
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必要的章节
        required_sections = [
            "基础搜索提示词",
            "通用搜索查询优化", 
            "多轮搜索策略提示词",
            "搜索结果质量评估",
            "专业领域搜索提示词",
            "技术文档搜索",
            "学术研究搜索",
            "商业信息搜索",
            "搜索结果处理提示词",
            "特殊场景搜索提示词"
        ]
        
        for section in required_sections:
            assert section in content, f"缺少必要章节: {section}"
    
    def test_information_analysis_template_structure(self):
        """测试3: 验证信息分析模板结构"""
        file_path = self.prompts_dir / "information_analysis.md"
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必要的章节
        required_sections = [
            "基础信息分析",
            "信息结构化分析",
            "多维度信息对比分析", 
            "信息质量评估",
            "专业领域分析",
            "技术信息分析",
            "商业信息分析",
            "学术研究分析",
            "高级分析技术",
            "趋势分析",
            "关联性分析",
            "综合评估分析"
        ]
        
        for section in required_sections:
            assert section in content, f"缺少必要章节: {section}"
    
    def test_content_summary_template_structure(self):
        """测试4: 验证内容摘要模板结构"""
        file_path = self.prompts_dir / "content_summary.md"
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必要的章节
        required_sections = [
            "基础摘要生成",
            "通用内容摘要",
            "分层次摘要",
            "结构化摘要",
            "专业领域摘要",
            "技术文档摘要",
            "商业报告摘要",
            "学术论文摘要",
            "特殊类型摘要",
            "多媒体内容摘要",
            "对话内容摘要",
            "摘要质量控制"
        ]
        
        for section in required_sections:
            assert section in content, f"缺少必要章节: {section}"
    
    def test_research_planning_template_structure(self):
        """测试5: 验证调研规划模板结构"""
        file_path = self.prompts_dir / "research_planning.md"
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必要的章节
        required_sections = [
            "基础调研规划",
            "调研项目总体规划",
            "调研问题设计",
            "数据收集策略",
            "专业领域调研规划",
            "市场调研规划",
            "技术调研规划",
            "调研执行管理",
            "调研项目管理"
        ]
        
        for section in required_sections:
            assert section in content, f"缺少必要章节: {section}"
    
    def test_template_variable_format(self):
        """测试6: 验证模板变量格式"""
        for template_file in self.template_files:
            file_path = self.prompts_dir / template_file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查模板变量格式 {variable_name}
            variables = re.findall(r'\{([^}]+)\}', content)
            assert len(variables) > 0, f"模板文件 {template_file} 没有找到模板变量"
            
            # 验证变量名格式（应该是小写字母、数字和下划线）
            for var in variables:
                assert re.match(r'^[a-z0-9_]+$', var), f"变量名格式不正确: {var} in {template_file}"
    
    def test_template_code_blocks(self):
        """测试7: 验证代码块格式"""
        for template_file in self.template_files:
            file_path = self.prompts_dir / template_file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查代码块格式
            code_blocks = re.findall(r'```[\s\S]*?```', content)
            assert len(code_blocks) > 0, f"模板文件 {template_file} 没有找到代码块"
            
            # 验证代码块是否正确闭合
            open_blocks = content.count('```')
            assert open_blocks % 2 == 0, f"代码块未正确闭合: {template_file}"
    
    def test_template_markdown_headers(self):
        """测试8: 验证Markdown标题格式"""
        for template_file in self.template_files:
            file_path = self.prompts_dir / template_file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查标题格式
            headers = re.findall(r'^#+\s+.+$', content, re.MULTILINE)
            assert len(headers) > 0, f"模板文件 {template_file} 没有找到标题"
            
            # 验证主标题存在
            main_title = re.search(r'^#\s+.+$', content, re.MULTILINE)
            assert main_title is not None, f"模板文件 {template_file} 缺少主标题"
    
    def test_web_search_template_functionality(self):
        """测试9: 验证网络搜索模板功能性"""
        file_path = self.prompts_dir / "web_search.md"
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能模板
        key_templates = [
            "original_query",
            "search_context", 
            "search_goal",
            "search_results",
            "quality_score",
            "tech_query",
            "business_query"
        ]
        
        for template in key_templates:
            assert f"{{{template}}}" in content, f"缺少关键模板变量: {template}"
    
    def test_information_analysis_template_functionality(self):
        """测试10: 验证信息分析模板功能性"""
        file_path = self.prompts_dir / "information_analysis.md"
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能模板
        key_templates = [
            "raw_information",
            "analysis_goal",
            "source_a",
            "source_b", 
            "source_c",
            "information_to_assess",
            "technical_information",
            "business_information"
        ]
        
        for template in key_templates:
            assert f"{{{template}}}" in content, f"缺少关键模板变量: {template}"
    
    def test_content_summary_template_functionality(self):
        """测试11: 验证内容摘要模板功能性"""
        file_path = self.prompts_dir / "content_summary.md"
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能模板
        key_templates = [
            "original_content",
            "summary_length",
            "target_audience",
            "summary_purpose",
            "content_type",
            "technical_content",
            "business_content",
            "academic_content"
        ]
        
        for template in key_templates:
            assert f"{{{template}}}" in content, f"缺少关键模板变量: {template}"
    
    def test_research_planning_template_functionality(self):
        """测试12: 验证调研规划模板功能性"""
        file_path = self.prompts_dir / "research_planning.md"
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能模板
        key_templates = [
            "research_topic",
            "research_objectives",
            "research_scope",
            "time_constraints",
            "resource_constraints",
            "market_research_topic",
            "tech_research_topic",
            "research_project"
        ]
        
        for template in key_templates:
            assert f"{{{template}}}" in content, f"缺少关键模板变量: {template}"
    
    def test_template_file_sizes(self):
        """测试13: 验证模板文件大小合理性"""
        for template_file in self.template_files:
            file_path = self.prompts_dir / template_file
            file_size = file_path.stat().st_size
            
            # 文件大小应该在合理范围内（1KB - 100KB）
            assert 1000 < file_size < 100000, f"文件大小不合理: {template_file} ({file_size} bytes)"
    
    def test_template_encoding(self):
        """测试14: 验证模板文件编码"""
        for template_file in self.template_files:
            file_path = self.prompts_dir / template_file
            
            # 尝试用UTF-8编码读取文件
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 验证内容不为空
                    assert len(content) > 0, f"文件内容为空: {template_file}"
            except UnicodeDecodeError:
                pytest.fail(f"文件编码不是UTF-8: {template_file}")
    
    def test_template_completeness(self):
        """测试15: 验证模板完整性"""
        # 验证所有模板文件都包含完整的提示词结构
        for template_file in self.template_files:
            file_path = self.prompts_dir / template_file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 每个模板都应该包含：
            # 1. 主标题
            # 2. 至少3个二级标题
            # 3. 至少5个代码块
            # 4. 至少10个模板变量
            
            main_titles = re.findall(r'^#\s+.+$', content, re.MULTILINE)
            assert len(main_titles) >= 1, f"缺少主标题: {template_file}"
            
            sub_titles = re.findall(r'^##\s+.+$', content, re.MULTILINE)
            assert len(sub_titles) >= 3, f"二级标题不足: {template_file}"
            
            code_blocks = re.findall(r'```[\s\S]*?```', content)
            assert len(code_blocks) >= 5, f"代码块不足: {template_file}"
            
            variables = re.findall(r'\{([^}]+)\}', content)
            assert len(variables) >= 10, f"模板变量不足: {template_file}"

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
