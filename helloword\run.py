#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyQt6 Hello World 应用启动器
提供依赖检查和应用启动功能
"""

import sys
import subprocess
import importlib.util
import os

# 设置Windows控制台UTF-8编码
if sys.platform == "win32":
    try:
        # 尝试设置控制台编码为UTF-8
        os.system("chcp 65001 >nul 2>&1")
    except:
        pass

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("[ERROR] 需要Python 3.8或更高版本")
        print(f"        当前版本: {sys.version}")
        return False
    print(f"[OK] Python版本检查通过: {sys.version.split()[0]}")
    return True

def check_pyqt6():
    """检查PyQt6是否已安装"""
    try:
        import PyQt6
        print(f"[OK] PyQt6已安装: {PyQt6.QtCore.PYQT_VERSION_STR}")
        return True
    except ImportError:
        print("[WARN] PyQt6未安装")
        return False

def install_pyqt6():
    """安装PyQt6"""
    print("[INFO] 正在安装PyQt6...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt6"])
        print("[OK] PyQt6安装成功")
        return True
    except subprocess.CalledProcessError:
        print("[ERROR] PyQt6安装失败")
        return False

def run_application():
    """运行主应用程序"""
    try:
        print("[INFO] 启动PyQt6 Hello World应用...")
        from main import main
        main()
    except Exception as e:
        print(f"[ERROR] 应用启动失败: {e}")
        return False
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("PyQt6 Hello World Application Launcher")
    print("=" * 60)

    # 检查Python版本
    if not check_python_version():
        input("Press Enter to exit...")
        sys.exit(1)

    # 检查PyQt6
    if not check_pyqt6():
        choice = input("Auto install PyQt6? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            if not install_pyqt6():
                input("Press Enter to exit...")
                sys.exit(1)
        else:
            print("Please install PyQt6 manually: pip install PyQt6")
            input("Press Enter to exit...")
            sys.exit(1)

    # 运行应用
    print("-" * 60)
    if run_application():
        print("[OK] Application exited normally")
    else:
        print("[ERROR] Application exited with error")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main()
