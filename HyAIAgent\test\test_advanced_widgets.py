"""
高级UI组件测试模块

测试智能输入组件、动态布局组件、交互式图表组件、多媒体展示组件等功能。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import sys
import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# PyQt6 imports
from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap
from PyQt6.QtTest import QTest

# 导入被测试的模块
from ui.advanced_widgets import (
    SmartInputWidget, DynamicLayoutWidget, InteractiveChartWidget,
    MultimediaWidget, ThemeManager, AccessibilityHelper, AdvancedWidgets
)
from core.config_manager import ConfigManager
from core.kv_store import KVStore


class TestSmartInputWidget:
    """智能输入组件测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def smart_input(self, app):
        """创建智能输入组件实例"""
        widget = SmartInputWidget(input_type="text")
        yield widget
        widget.close()
        
    def test_smart_input_initialization(self, smart_input):
        """测试智能输入组件初始化"""
        assert smart_input.input_type == "text"
        assert smart_input.is_valid == True
        assert smart_input.input_field is not None
        assert smart_input.voice_button is not None
        assert smart_input.handwriting_button is not None
        
    def test_text_validation_email(self, app):
        """测试邮箱验证"""
        widget = SmartInputWidget(input_type="email")
        
        # 测试有效邮箱
        widget.input_field.setText("<EMAIL>")
        assert widget.is_valid == True
        
        # 测试无效邮箱
        widget.input_field.setText("invalid-email")
        assert widget.is_valid == False
        
        widget.close()
        
    def test_text_validation_number(self, app):
        """测试数字验证"""
        widget = SmartInputWidget(input_type="number")
        
        # 测试有效数字
        widget.input_field.setText("123.45")
        assert widget.is_valid == True
        
        # 测试无效数字
        widget.input_field.setText("not-a-number")
        assert widget.is_valid == False
        
        widget.close()
        
    def test_signal_emission(self, smart_input):
        """测试信号发射"""
        # 使用Mock对象捕获信号
        text_changed_mock = Mock()
        input_validated_mock = Mock()
        
        smart_input.textChanged.connect(text_changed_mock)
        smart_input.inputValidated.connect(input_validated_mock)
        
        # 触发文本变化
        smart_input.input_field.setText("test text")
        
        # 验证信号是否被发射
        text_changed_mock.assert_called_with("test text")
        input_validated_mock.assert_called_with(True)


class TestDynamicLayoutWidget:
    """动态布局组件测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def dynamic_layout(self, app):
        """创建动态布局组件实例"""
        widget = DynamicLayoutWidget()
        yield widget
        widget.close()
        
    def test_dynamic_layout_initialization(self, dynamic_layout):
        """测试动态布局组件初始化"""
        assert dynamic_layout.current_layout_type == "vertical"
        assert dynamic_layout.widgets == []
        assert dynamic_layout.stacked_layout is not None
        
    def test_add_remove_widget(self, dynamic_layout, app):
        """测试添加和移除组件"""
        # 创建测试组件
        test_widget = QWidget()
        
        # 添加组件
        dynamic_layout.add_widget(test_widget)
        assert len(dynamic_layout.widgets) == 1
        assert dynamic_layout.widgets[0]['widget'] == test_widget
        
        # 移除组件
        dynamic_layout.remove_widget(test_widget)
        assert len(dynamic_layout.widgets) == 0
        
    def test_layout_type_switching(self, dynamic_layout, app):
        """测试布局类型切换"""
        # 添加一些测试组件
        for i in range(3):
            widget = QWidget()
            dynamic_layout.add_widget(widget)
            
        # 测试不同布局类型
        dynamic_layout.set_layout_type("horizontal")
        assert dynamic_layout.current_layout_type == "horizontal"
        
        dynamic_layout.set_layout_type("grid")
        assert dynamic_layout.current_layout_type == "grid"
        
        dynamic_layout.set_layout_type("vertical")
        assert dynamic_layout.current_layout_type == "vertical"
        
    def test_auto_adjust_layout(self, dynamic_layout, app):
        """测试自动布局调整"""
        # 添加测试组件
        for i in range(2):
            widget = QWidget()
            dynamic_layout.add_widget(widget)
            
        # 测试比较任务的自动调整
        dynamic_layout.auto_adjust_layout("comparison", 2)
        assert dynamic_layout.current_layout_type == "horizontal"
        
        # 测试仪表板任务的自动调整
        dynamic_layout.auto_adjust_layout("dashboard", 6)
        assert dynamic_layout.current_layout_type == "grid"


class TestInteractiveChartWidget:
    """交互式图表组件测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def chart_widget(self, app):
        """创建交互式图表组件实例"""
        widget = InteractiveChartWidget(chart_type="line")
        yield widget
        widget.close()
        
    def test_chart_initialization(self, chart_widget):
        """测试图表组件初始化"""
        assert chart_widget.chart_type == "line"
        assert chart_widget.data_series == []
        assert chart_widget.chart_view is not None
        assert chart_widget.chart_scene is not None
        
    def test_set_data(self, chart_widget):
        """测试设置图表数据"""
        test_data = [
            {"value": 10, "label": "A"},
            {"value": 20, "label": "B"},
            {"value": 15, "label": "C"}
        ]
        
        chart_widget.set_data(test_data)
        assert chart_widget.data_series == test_data
        
    def test_add_data_point(self, chart_widget):
        """测试添加数据点"""
        initial_data = [{"value": 10, "label": "A"}]
        chart_widget.set_data(initial_data)
        
        new_point = {"value": 20, "label": "B"}
        chart_widget.add_data_point(new_point)
        
        assert len(chart_widget.data_series) == 2
        assert chart_widget.data_series[1] == new_point
        
    def test_chart_type_change(self, chart_widget):
        """测试图表类型变化"""
        # 设置测试数据
        test_data = [{"value": 10}, {"value": 20}, {"value": 15}]
        chart_widget.set_data(test_data)
        
        # 测试图表类型切换
        chart_widget.chart_type_combo.setCurrentText("bar")
        assert chart_widget.chart_type == "bar"
        
        chart_widget.chart_type_combo.setCurrentText("pie")
        assert chart_widget.chart_type == "pie"


class TestMultimediaWidget:
    """多媒体展示组件测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def multimedia_widget(self, app):
        """创建多媒体展示组件实例"""
        widget = MultimediaWidget()
        yield widget
        widget.close()
        
    def test_multimedia_initialization(self, multimedia_widget):
        """测试多媒体组件初始化"""
        assert multimedia_widget.current_media is None
        assert multimedia_widget.media_type is None
        assert len(multimedia_widget.supported_formats) == 3
        
    def test_supported_formats(self, multimedia_widget):
        """测试支持的格式"""
        formats = multimedia_widget.supported_formats
        
        assert 'image' in formats
        assert 'video' in formats
        assert 'audio' in formats
        
        assert '.jpg' in formats['image']
        assert '.mp4' in formats['video']
        assert '.mp3' in formats['audio']
        
    @patch('PyQt6.QtGui.QPixmap')
    def test_load_image(self, mock_pixmap, multimedia_widget):
        """测试加载图片"""
        # 模拟有效的图片文件
        mock_pixmap.return_value.isNull.return_value = False
        mock_pixmap.return_value.width.return_value = 400
        mock_pixmap.return_value.height.return_value = 300
        
        # 测试加载图片
        multimedia_widget.load_media("test.jpg")
        
        assert multimedia_widget.media_type == "image"
        assert multimedia_widget.current_media == "test.jpg"


class TestThemeManager:
    """主题管理器测试类"""
    
    @pytest.fixture
    def theme_manager(self):
        """创建主题管理器实例"""
        return ThemeManager()
        
    def test_theme_manager_initialization(self, theme_manager):
        """测试主题管理器初始化"""
        assert theme_manager.current_theme == "default"
        assert len(theme_manager.themes) >= 4
        assert "default" in theme_manager.themes
        assert "dark" in theme_manager.themes
        
    def test_get_theme(self, theme_manager):
        """测试获取主题"""
        default_theme = theme_manager.get_theme("default")
        assert isinstance(default_theme, dict)
        assert "background" in default_theme
        assert "foreground" in default_theme
        
        # 测试不存在的主题
        unknown_theme = theme_manager.get_theme("unknown")
        assert unknown_theme == theme_manager.themes["default"]
        
    def test_available_themes(self, theme_manager):
        """测试获取可用主题列表"""
        themes = theme_manager.get_available_themes()
        assert isinstance(themes, list)
        assert len(themes) >= 4
        assert "default" in themes
        assert "dark" in themes


class TestAdvancedWidgets:
    """高级UI组件管理器测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        
    @pytest.fixture
    def advanced_widgets(self, app):
        """创建高级UI组件管理器实例"""
        manager = AdvancedWidgets()
        yield manager
        manager.cleanup()
        
    def test_advanced_widgets_initialization(self, advanced_widgets):
        """测试高级UI组件管理器初始化"""
        assert advanced_widgets.current_theme == "default"
        assert advanced_widgets.theme_manager is not None
        assert len(advanced_widgets.widgets_registry) == 0
        
    def test_create_smart_input(self, advanced_widgets):
        """测试创建智能输入组件"""
        widget = advanced_widgets.create_smart_input("email")
        
        assert isinstance(widget, SmartInputWidget)
        assert widget.input_type == "email"
        assert len(advanced_widgets.widgets_registry) == 1
        
    def test_create_dynamic_layout(self, advanced_widgets):
        """测试创建动态布局组件"""
        widget = advanced_widgets.create_dynamic_layout()
        
        assert isinstance(widget, DynamicLayoutWidget)
        assert len(advanced_widgets.widgets_registry) == 1
        
    def test_create_interactive_chart(self, advanced_widgets):
        """测试创建交互式图表组件"""
        widget = advanced_widgets.create_interactive_chart("bar")
        
        assert isinstance(widget, InteractiveChartWidget)
        assert widget.chart_type == "bar"
        assert len(advanced_widgets.widgets_registry) == 1
        
    def test_create_multimedia_widget(self, advanced_widgets):
        """测试创建多媒体展示组件"""
        widget = advanced_widgets.create_multimedia_widget()
        
        assert isinstance(widget, MultimediaWidget)
        assert len(advanced_widgets.widgets_registry) == 1
        
    def test_theme_management(self, advanced_widgets):
        """测试主题管理"""
        # 测试设置主题
        advanced_widgets.set_theme("dark")
        assert advanced_widgets.current_theme == "dark"
        
        # 测试获取可用主题
        themes = advanced_widgets.get_available_themes()
        assert isinstance(themes, list)
        assert "dark" in themes
        
    def test_widget_stats(self, advanced_widgets):
        """测试组件统计"""
        # 创建一些组件
        advanced_widgets.create_smart_input()
        advanced_widgets.create_dynamic_layout()
        advanced_widgets.create_interactive_chart()
        
        stats = advanced_widgets.get_widget_stats()
        
        assert stats["total_widgets"] == 3
        assert stats["current_theme"] == "default"
        assert "widget_types" in stats


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
