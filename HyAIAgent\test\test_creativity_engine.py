"""
创意生成引擎测试模块

测试CreativityEngine类的所有核心功能，包括：
- 创意生成功能
- 创意评估机制
- 创意优化算法
- 模板管理系统
- 历史记录管理

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import tempfile
import uuid
from datetime import datetime
from pathlib import Path

from advanced.creativity_engine import (
    CreativityEngine, CreativityRequest, CreativityResult, CreativityTemplate,
    CreativityType, CreativityLevel, EvaluationCriteria
)
from core.ai_client import SimpleAIClient
from core.prompt_manager import PromptManager
from core.kv_store import KVStore


class TestCreativityEngine:
    """创意生成引擎测试类"""
    
    def __init__(self):
        self.test_results = []
    
    async def test_generate_creativity(self, engine: CreativityEngine):
        """测试创意生成功能"""
        print("🎨 测试创意生成功能...")
        
        # 创建测试请求
        request = CreativityRequest(
            request_id=f"test_request_{uuid.uuid4().hex[:8]}",
            user_id="test_user_001",
            creativity_type=CreativityType.STORYTELLING,
            title="科幻冒险故事",
            description="创作一个关于太空探险的科幻故事",
            requirements={
                "genre": "科幻",
                "theme": "探索未知",
                "word_count": "500",
                "target_audience": "青少年"
            },
            constraints=["内容积极向上", "符合科学常识"],
            creativity_level=CreativityLevel.INTERMEDIATE,
            max_results=2
        )
        
        # 执行创意生成
        result = await engine.generate_creativity(request)
        
        # 验证结果
        assert result["success"] is True, f"创意生成失败: {result.get('error', 'Unknown error')}"
        assert "results" in result, "结果中缺少创意内容"
        assert len(result["results"]) <= request.max_results, "生成结果数量超出限制"
        assert result["request_id"] == request.request_id, "请求ID不匹配"
        
        print(f"✅ 创意生成成功 - 请求ID: {result['request_id']}, 生成数量: {result['generation_count']}")
        return result
    
    async def test_evaluate_creativity(self, engine: CreativityEngine):
        """测试创意评估功能"""
        print("📊 测试创意评估功能...")
        
        # 创建测试创意结果
        result = CreativityResult(
            result_id=f"test_result_{uuid.uuid4().hex[:8]}",
            request_id="test_request_001",
            title="测试创意作品",
            content="这是一个测试用的创意内容，包含了创新的想法和清晰的表达。",
            creativity_type=CreativityType.WRITING
        )
        
        # 执行创意评估
        evaluation = await engine.evaluate_creativity(
            result, 
            [EvaluationCriteria.ORIGINALITY, EvaluationCriteria.CLARITY, EvaluationCriteria.CREATIVITY]
        )
        
        # 验证结果
        assert evaluation["success"] is True, f"创意评估失败: {evaluation.get('error', 'Unknown error')}"
        assert "evaluation_scores" in evaluation, "评估结果中缺少分数"
        assert "overall_score" in evaluation, "评估结果中缺少总分"
        assert 0.0 <= evaluation["overall_score"] <= 1.0, "总分超出有效范围"
        
        print(f"✅ 创意评估成功 - 结果ID: {evaluation['result_id']}, 总分: {evaluation['overall_score']:.2f}")
        return evaluation
    
    async def test_optimize_creativity(self, engine: CreativityEngine):
        """测试创意优化功能"""
        print("🔧 测试创意优化功能...")
        
        # 创建测试创意结果
        original_result = CreativityResult(
            result_id=f"test_original_{uuid.uuid4().hex[:8]}",
            request_id="test_request_002",
            title="原始创意",
            content="这是一个需要优化的创意内容。",
            creativity_type=CreativityType.DESIGN,
            overall_score=0.6
        )
        
        # 执行创意优化
        optimization = await engine.optimize_creativity(
            original_result,
            ["提高创新性", "增强可行性", "改善表达清晰度"]
        )
        
        # 验证结果
        assert optimization["success"] is True, f"创意优化失败: {optimization.get('error', 'Unknown error')}"
        assert "optimized_result" in optimization, "优化结果中缺少优化后的内容"
        assert "improvement_score" in optimization, "优化结果中缺少改进分数"
        
        optimized_result = optimization["optimized_result"]
        assert optimized_result["title"].endswith("(优化版)"), "优化后标题格式不正确"
        
        print(f"✅ 创意优化成功 - 原始ID: {optimization['original_result_id']}, 改进分数: {optimization['improvement_score']:.2f}")
        return optimization
    
    async def test_add_template(self, engine: CreativityEngine):
        """测试添加创意模板"""
        print("📝 测试添加创意模板...")
        
        # 创建测试模板
        template = CreativityTemplate(
            template_id=f"test_template_{uuid.uuid4().hex[:8]}",
            name="测试营销创意模板",
            creativity_type=CreativityType.MARKETING,
            description="用于测试的营销创意模板",
            prompt_template="""请为{product_name}设计一个营销创意方案：

产品特点：{product_features}
目标用户：{target_users}
营销目标：{marketing_goals}

请提供创新的营销策略和具体执行方案。""",
            parameters=["product_name", "product_features", "target_users", "marketing_goals"],
            examples=["产品推广", "品牌宣传", "活动策划"],
            difficulty_level=CreativityLevel.INTERMEDIATE
        )
        
        # 添加模板
        result = await engine.add_template(template)
        
        # 验证结果
        assert result["success"] is True, f"添加模板失败: {result.get('error', 'Unknown error')}"
        assert result["template_id"] == template.template_id, "模板ID不匹配"
        
        print(f"✅ 模板添加成功 - ID: {result['template_id']}")
        return result
    
    async def test_get_creativity_history(self, engine: CreativityEngine):
        """测试获取创意历史"""
        print("📚 测试获取创意历史...")
        
        # 先生成一些测试数据
        test_user = "test_user_history"
        request = CreativityRequest(
            request_id=f"history_test_{uuid.uuid4().hex[:8]}",
            user_id=test_user,
            creativity_type=CreativityType.BRAINSTORM,
            title="历史测试创意",
            description="用于测试历史记录功能的创意请求"
        )
        
        await engine.generate_creativity(request)
        
        # 获取历史记录
        history = await engine.get_creativity_history(
            user_id=test_user,
            creativity_type=CreativityType.BRAINSTORM,
            limit=10
        )
        
        # 验证结果
        assert history["success"] is True, f"获取历史失败: {history.get('error', 'Unknown error')}"
        assert "results" in history, "历史记录中缺少结果列表"
        assert history["user_id"] == test_user, "用户ID不匹配"
        
        print(f"✅ 历史记录获取成功 - 用户: {history['user_id']}, 记录数: {history['total_count']}")
        return history
    
    async def test_get_creativity_stats(self, engine: CreativityEngine):
        """测试获取创意统计"""
        print("📈 测试获取创意统计...")
        
        # 获取统计信息
        stats = await engine.get_creativity_stats()
        
        # 验证结果
        assert stats["success"] is True, f"获取统计失败: {stats.get('error', 'Unknown error')}"
        assert "stats" in stats, "统计结果中缺少统计数据"
        
        stats_data = stats["stats"]
        assert "total_requests" in stats_data, "统计数据中缺少总请求数"
        assert "successful_generations" in stats_data, "统计数据中缺少成功生成数"
        assert "success_rate" in stats_data, "统计数据中缺少成功率"
        
        print(f"✅ 统计信息获取成功 - 总请求: {stats_data['total_requests']}, 成功率: {stats_data['success_rate']:.2%}")
        return stats
    
    async def test_clear_creativity_history(self, engine: CreativityEngine):
        """测试清理创意历史"""
        print("🗑️ 测试清理创意历史...")
        
        # 创建测试用户数据
        test_user = "test_user_clear"
        request = CreativityRequest(
            request_id=f"clear_test_{uuid.uuid4().hex[:8]}",
            user_id=test_user,
            creativity_type=CreativityType.WRITING,
            title="清理测试创意",
            description="用于测试清理功能的创意请求"
        )
        
        await engine.generate_creativity(request)
        
        # 清理特定用户的历史
        clear_result = await engine.clear_creativity_history(user_id=test_user)
        
        # 验证结果
        assert clear_result["success"] is True, f"清理历史失败: {clear_result.get('error', 'Unknown error')}"
        assert clear_result["user_id"] == test_user, "用户ID不匹配"
        assert clear_result["cleared_count"] > 0, "清理数量应该大于0"
        
        print(f"✅ 历史清理成功 - 用户: {clear_result['user_id']}, 清理数量: {clear_result['cleared_count']}")
        return clear_result


async def run_comprehensive_test():
    """运行创意引擎综合测试"""
    print("🚀 开始创意生成引擎综合测试...")
    
    # 创建临时目录和测试环境
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 初始化核心组件
        ai_client = SimpleAIClient(
            api_key="test-key",
            model="gpt-3.5-turbo"
        )
        
        prompt_manager = PromptManager(temp_path / "prompts")
        kv_store = KVStore(temp_path / "test_creativity.json")
        
        # 创建创意引擎
        creativity_engine = CreativityEngine(
            ai_client=ai_client,
            prompt_manager=prompt_manager,
            kv_store=kv_store,
            max_history_size=100,
            default_temperature=0.8
        )
        
        # 等待初始化完成
        await asyncio.sleep(0.1)
        
        # 创建测试实例
        test_instance = TestCreativityEngine()
        
        try:
            # 执行所有测试
            await test_instance.test_generate_creativity(creativity_engine)
            await test_instance.test_evaluate_creativity(creativity_engine)
            await test_instance.test_optimize_creativity(creativity_engine)
            await test_instance.test_add_template(creativity_engine)
            await test_instance.test_get_creativity_history(creativity_engine)
            await test_instance.test_get_creativity_stats(creativity_engine)
            await test_instance.test_clear_creativity_history(creativity_engine)
            
            print("\n🎉 所有创意生成引擎测试通过！")
            
        except Exception as e:
            print(f"\n❌ 测试失败: {str(e)}")
            raise


if __name__ == "__main__":
    asyncio.run(run_comprehensive_test())
