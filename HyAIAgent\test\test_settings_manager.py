"""
SettingsManager 测试模块

测试个性化设置系统的各项功能，包括用户偏好学习、推荐系统、
工作环境配置、快捷键设置等。

作者: HyAIAgent开发团队
创建时间: 2025-07-30
版本: 1.0.0
"""

import sys
import os
import pytest
import asyncio
import tempfile
import shutil
import json
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# PyQt6 imports
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QSettings, QStandardPaths
from PyQt6.QtTest import QTest

# 导入被测试的模块
from ui.settings_manager import SettingsManager, UserPreference, WorkspaceProfile, RecommendationItem, SettingsDialog
from core.config_manager import ConfigManager
from core.kv_store import KVStore


class TestSettingsManager:
    """SettingsManager 测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
        # 清理
        if hasattr(app, 'quit'):
            app.quit()
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        # 清理
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def mock_config_manager(self):
        """创建模拟的ConfigManager"""
        mock_config = Mock(spec=ConfigManager)
        mock_config.get.return_value = {}
        mock_config.set.return_value = True
        return mock_config
    
    @pytest.fixture
    def mock_kv_store(self):
        """创建模拟的KVStore"""
        mock_kv = Mock(spec=KVStore)
        mock_kv.get.return_value = None
        mock_kv.set.return_value = True
        return mock_kv
    
    @pytest.fixture
    def settings_manager(self, app, temp_dir, mock_config_manager, mock_kv_store):
        """创建SettingsManager实例"""
        with patch('ui.settings_manager.QSettings') as mock_qsettings, \
             patch('ui.settings_manager.QStandardPaths') as mock_paths:
            mock_settings_obj = Mock()
            mock_settings_obj.value.return_value = None
            mock_qsettings.return_value = mock_settings_obj
            mock_paths.writableLocation.return_value = str(temp_dir)
            manager = SettingsManager(
                config_manager=mock_config_manager,
                kv_store=mock_kv_store
            )
            yield manager
    
    def test_initialization(self, settings_manager):
        """测试初始化"""
        assert settings_manager is not None
        assert isinstance(settings_manager.preferences, dict)
        assert isinstance(settings_manager.workspace_profiles, dict)
        assert isinstance(settings_manager.recommendations, list)
        assert settings_manager.current_profile_id is None
    
    @pytest.mark.asyncio
    async def test_learn_user_preference(self, settings_manager):
        """测试用户偏好学习"""
        # 测试学习新偏好
        result = await settings_manager.learn_user_preference(
            key="theme",
            value="dark",
            category="ui",
            priority=2.0
        )
        
        assert result is True
        assert "theme" in settings_manager.preferences
        
        pref = settings_manager.preferences["theme"]
        assert pref.key == "theme"
        assert pref.value == "dark"
        assert pref.category == "ui"
        assert pref.priority == 2.0
        assert pref.usage_count == 1
        
        # 测试更新现有偏好
        result = await settings_manager.learn_user_preference(
            key="theme",
            value="light",
            category="ui",
            priority=1.5
        )
        
        assert result is True
        updated_pref = settings_manager.preferences["theme"]
        assert updated_pref.value == "light"
        assert updated_pref.usage_count == 2
        assert updated_pref.priority == 2.0  # 应该保持更高的优先级
    
    @pytest.mark.asyncio
    async def test_generate_recommendations(self, settings_manager):
        """测试推荐生成"""
        # 添加一些偏好数据
        await settings_manager.learn_user_preference("theme", "dark", "ui", 2.0)
        await settings_manager.learn_user_preference("font_size", 14, "ui", 1.5)
        await settings_manager.learn_user_preference("auto_save", True, "editor", 3.0)
        
        # 增加使用次数
        for _ in range(3):
            await settings_manager.learn_user_preference("theme", "dark", "ui", 2.0)
        
        # 生成推荐
        recommendations = await settings_manager.generate_recommendations()
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        
        # 检查推荐项结构
        for rec in recommendations:
            assert isinstance(rec, RecommendationItem)
            assert rec.item_id is not None
            assert rec.title is not None
            assert rec.score >= 0
    
    def test_create_workspace_profile(self, settings_manager):
        """测试创建工作环境配置文件"""
        profile_id = settings_manager.create_workspace_profile(
            name="开发环境",
            description="用于软件开发的工作环境",
            ui_theme="dark"
        )
        
        assert profile_id is not None
        assert profile_id in settings_manager.workspace_profiles
        
        profile = settings_manager.workspace_profiles[profile_id]
        assert profile.name == "开发环境"
        assert profile.description == "用于软件开发的工作环境"
        assert profile.ui_theme == "dark"
        assert not profile.is_active
    
    def test_activate_workspace_profile(self, settings_manager):
        """测试激活工作环境配置文件"""
        # 创建配置文件
        profile_id = settings_manager.create_workspace_profile(
            name="测试环境",
            description="测试用环境"
        )
        
        # 激活配置文件
        result = settings_manager.activate_workspace_profile(profile_id)
        
        assert result is True
        assert settings_manager.current_profile_id == profile_id
        
        profile = settings_manager.workspace_profiles[profile_id]
        assert profile.is_active is True
        
        # 测试激活不存在的配置文件
        result = settings_manager.activate_workspace_profile("nonexistent")
        assert result is False
    
    def test_update_profile_shortcuts(self, settings_manager):
        """测试更新配置文件快捷键"""
        # 创建配置文件
        profile_id = settings_manager.create_workspace_profile(
            name="快捷键测试",
            description="测试快捷键功能"
        )
        
        # 更新快捷键
        shortcuts = {
            "new_file": "Ctrl+N",
            "save_file": "Ctrl+S",
            "open_file": "Ctrl+O"
        }
        
        result = settings_manager.update_profile_shortcuts(profile_id, shortcuts)
        
        assert result is True
        
        profile = settings_manager.workspace_profiles[profile_id]
        assert profile.shortcuts == shortcuts
        
        # 测试更新不存在的配置文件
        result = settings_manager.update_profile_shortcuts("nonexistent", shortcuts)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_sync_settings(self, settings_manager):
        """测试设置同步"""
        # 测试本地备份同步
        result = await settings_manager.sync_settings("local_backup")
        assert result is True
        
        # 检查备份文件是否创建
        backup_dir = settings_manager.settings_dir / "backups"
        assert backup_dir.exists()
        
        backup_files = list(backup_dir.glob("settings_backup_*.json"))
        assert len(backup_files) > 0
        
        # 验证备份文件内容
        with open(backup_files[0], 'r', encoding='utf-8') as f:
            backup_data = json.load(f)
            assert 'preferences' in backup_data
            assert 'profiles' in backup_data
            assert 'backup_time' in backup_data
    
    def test_get_preference(self, settings_manager):
        """测试获取偏好值"""
        # 测试获取不存在的偏好
        result = settings_manager.get_preference("nonexistent", "default")
        assert result == "default"
        
        # 添加偏好并测试获取
        settings_manager.preferences["test_key"] = UserPreference(
            key="test_key",
            value="test_value",
            category="test"
        )
        
        result = settings_manager.get_preference("test_key")
        assert result == "test_value"
        
        # 验证使用次数增加
        pref = settings_manager.preferences["test_key"]
        assert pref.usage_count == 1  # 初始0 + 获取时1
    
    def test_get_recommendations(self, settings_manager):
        """测试获取推荐"""
        # 添加一些推荐
        rec1 = RecommendationItem(
            item_id="rec1",
            item_type="preference",
            title="推荐1",
            description="描述1",
            score=0.8,
            category="ui"
        )
        
        rec2 = RecommendationItem(
            item_id="rec2",
            item_type="preference",
            title="推荐2",
            description="描述2",
            score=0.6,
            category="editor"
        )
        
        settings_manager.recommendations = [rec1, rec2]
        
        # 测试获取所有推荐
        all_recs = settings_manager.get_recommendations()
        assert len(all_recs) == 2
        
        # 测试按类别过滤
        ui_recs = settings_manager.get_recommendations(category="ui")
        assert len(ui_recs) == 1
        assert ui_recs[0].category == "ui"
        
        # 测试限制数量
        limited_recs = settings_manager.get_recommendations(limit=1)
        assert len(limited_recs) == 1
    
    def test_get_workspace_profiles(self, settings_manager):
        """测试获取工作环境配置文件"""
        # 创建一些配置文件
        profile_id1 = settings_manager.create_workspace_profile("环境1", "描述1")
        profile_id2 = settings_manager.create_workspace_profile("环境2", "描述2")
        
        profiles = settings_manager.get_workspace_profiles()
        
        assert len(profiles) == 2
        assert profile_id1 in profiles
        assert profile_id2 in profiles
        assert profiles[profile_id1].name == "环境1"
        assert profiles[profile_id2].name == "环境2"
    
    def test_get_current_profile(self, settings_manager):
        """测试获取当前配置文件"""
        # 没有活跃配置文件时
        current = settings_manager.get_current_profile()
        assert current is None
        
        # 创建并激活配置文件
        profile_id = settings_manager.create_workspace_profile("当前环境", "当前描述")
        settings_manager.activate_workspace_profile(profile_id)
        
        current = settings_manager.get_current_profile()
        assert current is not None
        assert current.name == "当前环境"
        assert current.is_active is True
    
    @pytest.mark.asyncio
    async def test_get_settings_stats(self, settings_manager):
        """测试获取设置统计信息"""
        # 使用learn_user_preference方法添加偏好，这样会正确更新stats
        await settings_manager.learn_user_preference("test1", "value1", "test")
        await settings_manager.learn_user_preference("test2", "value2", "test")

        profile_id = settings_manager.create_workspace_profile("测试环境", "测试")
        settings_manager.activate_workspace_profile(profile_id)

        stats = settings_manager.get_settings_stats()

        assert isinstance(stats, dict)
        assert stats['preferences_count'] == 2
        assert stats['profiles_created'] == 1
        assert stats['active_profile'] == profile_id
        assert 'total_usage_records' in stats
        assert 'behavior_patterns_count' in stats


class TestSettingsDialog:
    """SettingsDialog 测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app
    
    @pytest.fixture
    def settings_manager(self, app):
        """创建模拟的SettingsManager"""
        mock_manager = Mock(spec=SettingsManager)
        mock_manager.preferences = {}
        mock_manager.workspace_profiles = {}
        mock_manager.get_recommendations.return_value = []
        return mock_manager
    
    def test_dialog_initialization(self, app, settings_manager):
        """测试对话框初始化"""
        dialog = SettingsDialog(settings_manager)
        
        assert dialog is not None
        assert dialog.settings_manager == settings_manager
        assert dialog.windowTitle() == "个性化设置"
        assert dialog.isModal() is True
        
        # 检查标签页是否创建
        assert dialog.tab_widget is not None
        assert dialog.tab_widget.count() == 4  # 4个标签页
    
    def test_load_current_settings(self, app, settings_manager):
        """测试加载当前设置"""
        # 设置模拟数据
        settings_manager.preferences = {
            "theme": UserPreference(key="theme", value="dark", category="ui"),
            "font_size": UserPreference(key="font_size", value=14, category="ui")
        }
        
        settings_manager.workspace_profiles = {
            "profile1": WorkspaceProfile(
                profile_id="profile1",
                name="开发环境",
                description="开发用",
                ui_theme="default",
                layout_config={},
                shortcuts={},
                preferences={},
                is_active=True
            )
        }
        
        settings_manager.get_recommendations.return_value = [
            RecommendationItem(
                item_id="rec1",
                item_type="preference",
                title="推荐主题",
                description="推荐使用深色主题",
                score=0.8,
                category="ui"
            )
        ]
        
        dialog = SettingsDialog(settings_manager)
        
        # 验证数据是否正确加载
        assert dialog.preferences_list.count() == 2
        assert dialog.profiles_list.count() == 1
        assert dialog.recommendations_list.count() == 1


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
