"""
PerformanceMonitor 测试模块

测试性能监控器的各项功能，包括系统指标收集、告警机制、数据持久化等。
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from HyAIAgent.monitoring.performance_monitor import (
    PerformanceMonitor, SystemMetrics, PerformanceAlert
)


class TestSystemMetrics:
    """SystemMetrics 数据模型测试"""
    
    def test_system_metrics_creation(self):
        """测试系统指标创建"""
        timestamp = datetime.now()
        metrics = SystemMetrics(
            timestamp=timestamp,
            cpu_usage=75.5,
            memory_usage=60.2,
            disk_usage=45.0,
            network_io={'bytes_sent': 1000, 'bytes_recv': 2000},
            process_count=150,
            thread_count=8,
            response_time=1.5,
            active_tasks=5
        )
        
        assert metrics.timestamp == timestamp
        assert metrics.cpu_usage == 75.5
        assert metrics.memory_usage == 60.2
        assert metrics.disk_usage == 45.0
        assert metrics.network_io == {'bytes_sent': 1000, 'bytes_recv': 2000}
        assert metrics.process_count == 150
        assert metrics.thread_count == 8
        assert metrics.response_time == 1.5
        assert metrics.active_tasks == 5
    
    def test_system_metrics_to_dict(self):
        """测试系统指标转换为字典"""
        timestamp = datetime.now()
        metrics = SystemMetrics(
            timestamp=timestamp,
            cpu_usage=75.5,
            memory_usage=60.2,
            disk_usage=45.0,
            network_io={},
            process_count=150,
            thread_count=8,
            response_time=1.5,
            active_tasks=5
        )
        
        data = metrics.to_dict()
        assert isinstance(data, dict)
        assert data['timestamp'] == timestamp.isoformat()
        assert data['cpu_usage'] == 75.5
        assert data['memory_usage'] == 60.2


class TestPerformanceAlert:
    """PerformanceAlert 数据模型测试"""
    
    def test_performance_alert_creation(self):
        """测试性能告警创建"""
        timestamp = datetime.now()
        alert = PerformanceAlert(
            alert_id="test_alert_001",
            alert_type="cpu_usage",
            severity="high",
            message="CPU使用率过高",
            metric_name="cpu_usage",
            current_value=85.5,
            threshold_value=80.0,
            timestamp=timestamp
        )
        
        assert alert.alert_id == "test_alert_001"
        assert alert.alert_type == "cpu_usage"
        assert alert.severity == "high"
        assert alert.message == "CPU使用率过高"
        assert alert.metric_name == "cpu_usage"
        assert alert.current_value == 85.5
        assert alert.threshold_value == 80.0
        assert alert.timestamp == timestamp
        assert alert.resolved is False
    
    def test_performance_alert_to_dict(self):
        """测试性能告警转换为字典"""
        timestamp = datetime.now()
        alert = PerformanceAlert(
            alert_id="test_alert_001",
            alert_type="cpu_usage",
            severity="high",
            message="CPU使用率过高",
            metric_name="cpu_usage",
            current_value=85.5,
            threshold_value=80.0,
            timestamp=timestamp
        )
        
        data = alert.to_dict()
        assert isinstance(data, dict)
        assert data['alert_id'] == "test_alert_001"
        assert data['timestamp'] == timestamp.isoformat()


class TestPerformanceMonitor:
    """PerformanceMonitor 主类测试"""
    
    @pytest.fixture
    def mock_config_manager(self):
        """模拟配置管理器"""
        config_manager = Mock()
        config_manager.get.return_value = None
        return config_manager
    
    @pytest.fixture
    def mock_kv_store(self):
        """模拟键值存储"""
        kv_store = AsyncMock()
        return kv_store
    
    @pytest.fixture
    def performance_monitor(self, mock_config_manager, mock_kv_store):
        """创建性能监控器实例"""
        return PerformanceMonitor(
            config_manager=mock_config_manager,
            kv_store=mock_kv_store,
            monitoring_interval=1,
            history_size=100
        )
    
    def test_performance_monitor_initialization(self, performance_monitor):
        """测试性能监控器初始化"""
        assert performance_monitor.monitoring_interval == 1
        assert performance_monitor.history_size == 100
        assert performance_monitor.is_monitoring is False
        assert len(performance_monitor.metrics_history) == 0
        assert len(performance_monitor.alerts) == 0
        assert len(performance_monitor.response_times) == 0
        assert performance_monitor.active_tasks_count == 0
    
    def test_alert_thresholds_configuration(self, performance_monitor):
        """测试告警阈值配置"""
        assert 'cpu_usage' in performance_monitor.alert_thresholds
        assert 'memory_usage' in performance_monitor.alert_thresholds
        assert 'response_time' in performance_monitor.alert_thresholds

        cpu_thresholds = performance_monitor.alert_thresholds['cpu_usage']
        assert 'high' in cpu_thresholds
        assert 'critical' in cpu_thresholds
        assert cpu_thresholds['high'] == 80.0
        assert cpu_thresholds['critical'] == 95.0
    
    @pytest.mark.asyncio
    async def test_start_monitoring(self, performance_monitor):
        """测试开始监控"""
        result = await performance_monitor.start_monitoring()
        assert result is True
        assert performance_monitor.is_monitoring is True
        
        # 停止监控以清理
        await performance_monitor.stop_monitoring()
    
    @pytest.mark.asyncio
    async def test_stop_monitoring(self, performance_monitor):
        """测试停止监控"""
        # 先开始监控
        await performance_monitor.start_monitoring()
        assert performance_monitor.is_monitoring is True
        
        # 停止监控
        result = await performance_monitor.stop_monitoring()
        assert result is True
        assert performance_monitor.is_monitoring is False
    
    @pytest.mark.asyncio
    async def test_get_current_metrics(self, performance_monitor):
        """测试获取当前指标"""
        with patch('psutil.cpu_percent', return_value=75.0), \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk, \
             patch('psutil.net_io_counters') as mock_network, \
             patch('psutil.pids', return_value=list(range(100))):
            
            # 模拟内存信息
            mock_memory.return_value.percent = 60.0
            
            # 模拟磁盘信息
            mock_disk.return_value.used = 500 * 1024 * 1024 * 1024  # 500GB
            mock_disk.return_value.total = 1000 * 1024 * 1024 * 1024  # 1TB
            
            # 模拟网络信息
            mock_network.return_value.bytes_sent = 1000
            mock_network.return_value.bytes_recv = 2000
            mock_network.return_value.packets_sent = 10
            mock_network.return_value.packets_recv = 20
            
            metrics = await performance_monitor.get_current_metrics()
            
            assert metrics is not None
            assert isinstance(metrics, SystemMetrics)
            assert metrics.cpu_usage == 75.0
            assert metrics.memory_usage == 60.0
            assert metrics.disk_usage == 50.0  # 500/1000 * 100
            assert metrics.process_count == 100
    
    def test_record_response_time(self, performance_monitor):
        """测试记录响应时间"""
        performance_monitor.record_response_time(1.5)
        performance_monitor.record_response_time(2.0)
        performance_monitor.record_response_time(1.2)
        
        assert len(performance_monitor.response_times) == 3
        assert 1.5 in performance_monitor.response_times
        assert 2.0 in performance_monitor.response_times
        assert 1.2 in performance_monitor.response_times
    
    def test_active_tasks_management(self, performance_monitor):
        """测试活跃任务管理"""
        assert performance_monitor.active_tasks_count == 0
        
        performance_monitor.increment_active_tasks()
        assert performance_monitor.active_tasks_count == 1
        
        performance_monitor.increment_active_tasks()
        assert performance_monitor.active_tasks_count == 2
        
        performance_monitor.decrement_active_tasks()
        assert performance_monitor.active_tasks_count == 1
        
        performance_monitor.decrement_active_tasks()
        assert performance_monitor.active_tasks_count == 0
        
        # 测试不会变成负数
        performance_monitor.decrement_active_tasks()
        assert performance_monitor.active_tasks_count == 0
    
    def test_set_alert_threshold(self, performance_monitor):
        """测试设置告警阈值"""
        result = performance_monitor.set_alert_threshold('cpu_usage', 'high', 85.0)
        assert result is True
        assert performance_monitor.alert_thresholds['cpu_usage']['high'] == 85.0
        
        # 测试无效参数
        result = performance_monitor.set_alert_threshold('invalid_metric', 'high', 85.0)
        assert result is False
        
        result = performance_monitor.set_alert_threshold('cpu_usage', 'invalid_level', 85.0)
        assert result is False
    
    def test_get_performance_stats(self, performance_monitor):
        """测试获取性能统计"""
        # 添加一些测试数据
        performance_monitor.stats['total_samples'] = 100
        performance_monitor.stats['avg_cpu_usage'] = 75.0
        performance_monitor.stats['avg_memory_usage'] = 60.0
        
        stats = performance_monitor.get_performance_stats()
        
        assert isinstance(stats, dict)
        assert 'total_samples' in stats
        assert 'avg_cpu_usage' in stats
        assert 'uptime_seconds' in stats
        assert 'monitoring_status' in stats
        assert stats['monitoring_status'] == performance_monitor.is_monitoring
    
    def test_get_metrics_history(self, performance_monitor):
        """测试获取指标历史"""
        # 添加测试数据
        timestamp = datetime.now()
        metrics = SystemMetrics(
            timestamp=timestamp,
            cpu_usage=75.0,
            memory_usage=60.0,
            disk_usage=50.0,
            network_io={},
            process_count=100,
            thread_count=8,
            response_time=1.5,
            active_tasks=2
        )
        performance_monitor.metrics_history.append(metrics)
        
        history = performance_monitor.get_metrics_history()
        assert len(history) == 1
        assert isinstance(history[0], dict)
        assert history[0]['cpu_usage'] == 75.0
        
        # 测试限制数量
        history_limited = performance_monitor.get_metrics_history(limit=1)
        assert len(history_limited) == 1
    
    @pytest.mark.asyncio
    async def test_resolve_alert(self, performance_monitor):
        """测试解决告警"""
        # 创建测试告警
        alert = PerformanceAlert(
            alert_id="test_alert_001",
            alert_type="cpu_usage",
            severity="high",
            message="测试告警",
            metric_name="cpu_usage",
            current_value=85.0,
            threshold_value=80.0,
            timestamp=datetime.now()
        )
        performance_monitor.alerts.append(alert)
        
        # 解决告警
        result = await performance_monitor.resolve_alert("test_alert_001")
        assert result is True
        assert alert.resolved is True
        
        # 测试解决不存在的告警
        result = await performance_monitor.resolve_alert("nonexistent_alert")
        assert result is False
    
    def test_alert_callback_management(self, performance_monitor):
        """测试告警回调管理"""
        def test_callback(alert):
            pass
        
        # 添加回调
        performance_monitor.add_alert_callback(test_callback)
        assert test_callback in performance_monitor.alert_callbacks
        
        # 移除回调
        result = performance_monitor.remove_alert_callback(test_callback)
        assert result is True
        assert test_callback not in performance_monitor.alert_callbacks
        
        # 移除不存在的回调
        result = performance_monitor.remove_alert_callback(test_callback)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_cleanup(self, performance_monitor):
        """测试资源清理"""
        # 添加一些测试数据
        performance_monitor.metrics_history.append(Mock())
        performance_monitor.response_times.append(1.5)
        performance_monitor.alerts.append(Mock())
        performance_monitor.alert_callbacks.append(Mock())
        
        await performance_monitor.cleanup()
        
        assert len(performance_monitor.metrics_history) == 0
        assert len(performance_monitor.response_times) == 0
        assert len(performance_monitor.alerts) == 0
        assert len(performance_monitor.alert_callbacks) == 0


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
