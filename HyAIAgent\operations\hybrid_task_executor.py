"""
HyAIAgent 第四阶段 - 混合任务执行器模块
负责整合搜索和文件操作，执行复杂的混合任务
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Set, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import uuid
from pathlib import Path

# 配置日志
logger = logging.getLogger(__name__)


class HybridTaskType(Enum):
    """混合任务类型枚举"""
    SEARCH_AND_SAVE = "search_and_save"  # 搜索并保存
    RESEARCH_AND_ANALYZE = "research_and_analyze"  # 调研并分析
    COLLECT_AND_INTEGRATE = "collect_and_integrate"  # 收集并整合
    MONITOR_AND_REPORT = "monitor_and_report"  # 监控并报告
    VALIDATE_AND_UPDATE = "validate_and_update"  # 验证并更新


class TaskStepType(Enum):
    """任务步骤类型枚举"""
    SEARCH = "search"  # 搜索步骤
    FILE_OPERATION = "file_operation"  # 文件操作步骤
    DATA_PROCESSING = "data_processing"  # 数据处理步骤
    ANALYSIS = "analysis"  # 分析步骤
    INTEGRATION = "integration"  # 整合步骤
    VALIDATION = "validation"  # 验证步骤


class ExecutionStatus(Enum):
    """执行状态枚举"""
    PENDING = "pending"  # 等待中
    RUNNING = "running"  # 执行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    SKIPPED = "skipped"  # 跳过
    CANCELLED = "cancelled"  # 取消


@dataclass
class TaskStep:
    """任务步骤"""
    step_id: str
    step_type: TaskStepType
    description: str
    parameters: Dict[str, Any]
    dependencies: List[str]  # 依赖的步骤ID
    estimated_duration: int  # 预估执行时间（秒）
    retry_count: int = 0
    max_retries: int = 3
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['step_type'] = self.step_type.value
        return data


@dataclass
class StepResult:
    """步骤执行结果"""
    step_id: str
    status: ExecutionStatus
    result_data: Any
    error_message: Optional[str]
    execution_time: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['status'] = self.status.value
        data['timestamp'] = self.timestamp.isoformat()
        return data


@dataclass
class HybridTask:
    """混合任务"""
    task_id: str
    task_type: HybridTaskType
    title: str
    description: str
    steps: List[TaskStep]
    context: Dict[str, Any]
    priority: int = 5  # 1-10，10为最高优先级
    timeout: int = 3600  # 超时时间（秒）
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['task_type'] = self.task_type.value
        data['steps'] = [step.to_dict() for step in self.steps]
        data['created_at'] = self.created_at.isoformat()
        return data


@dataclass
class ExecutionReport:
    """执行报告"""
    task_id: str
    total_steps: int
    completed_steps: int
    failed_steps: int
    skipped_steps: int
    total_execution_time: float
    success_rate: float
    step_results: List[StepResult]
    recommendations: List[str]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['step_results'] = [result.to_dict() for result in self.step_results]
        data['timestamp'] = self.timestamp.isoformat()
        return data


class HybridTaskExecutor:
    """混合任务执行器
    
    负责执行包含搜索、文件操作、数据处理等多种操作的复杂任务
    """
    
    def __init__(self, 
                 search_operations=None,
                 file_operations=None,
                 content_processor=None,
                 data_integrator=None,
                 task_manager=None,
                 execution_engine=None):
        """初始化混合任务执行器"""
        self.search_operations = search_operations
        self.file_operations = file_operations
        self.content_processor = content_processor
        self.data_integrator = data_integrator
        self.task_manager = task_manager
        self.execution_engine = execution_engine
        
        # 任务存储
        self.active_tasks: Dict[str, HybridTask] = {}
        self.completed_tasks: Dict[str, HybridTask] = {}
        self.execution_reports: Dict[str, ExecutionReport] = {}
        
        # 步骤执行器映射
        self.step_executors: Dict[TaskStepType, Callable] = {
            TaskStepType.SEARCH: self._execute_search_step,
            TaskStepType.FILE_OPERATION: self._execute_file_operation_step,
            TaskStepType.DATA_PROCESSING: self._execute_data_processing_step,
            TaskStepType.ANALYSIS: self._execute_analysis_step,
            TaskStepType.INTEGRATION: self._execute_integration_step,
            TaskStepType.VALIDATION: self._execute_validation_step
        }
        
        # 执行配置
        self.max_concurrent_tasks = 3
        self.max_concurrent_steps = 5
        self.default_timeout = 3600
        
        logger.info("HybridTaskExecutor initialized")
    
    async def create_hybrid_task(self,
                               task_type: HybridTaskType,
                               title: str,
                               description: str,
                               parameters: Dict[str, Any],
                               context: Optional[Dict[str, Any]] = None) -> str:
        """创建混合任务
        
        Args:
            task_type: 任务类型
            title: 任务标题
            description: 任务描述
            parameters: 任务参数
            context: 任务上下文（可选）
            
        Returns:
            str: 任务ID
        """
        try:
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 分解任务为步骤
            steps = await self._decompose_task(task_type, parameters)
            
            # 创建任务对象
            hybrid_task = HybridTask(
                task_id=task_id,
                task_type=task_type,
                title=title,
                description=description,
                steps=steps,
                context=context or {},
                priority=parameters.get('priority', 5),
                timeout=parameters.get('timeout', self.default_timeout)
            )
            
            # 存储任务
            self.active_tasks[task_id] = hybrid_task
            
            logger.info(f"Hybrid task created: {task_id} ({task_type.value})")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to create hybrid task: {e}")
            raise
    
    async def execute_hybrid_task(self, task_id: str) -> ExecutionReport:
        """执行混合任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            ExecutionReport: 执行报告
        """
        try:
            if task_id not in self.active_tasks:
                raise ValueError(f"Task not found: {task_id}")
            
            task = self.active_tasks[task_id]
            start_time = datetime.now()
            
            logger.info(f"Starting hybrid task execution: {task_id}")
            
            # 执行步骤
            step_results = await self._execute_task_steps(task)
            
            # 计算执行统计
            total_steps = len(task.steps)
            completed_steps = sum(1 for result in step_results if result.status == ExecutionStatus.COMPLETED)
            failed_steps = sum(1 for result in step_results if result.status == ExecutionStatus.FAILED)
            skipped_steps = sum(1 for result in step_results if result.status == ExecutionStatus.SKIPPED)
            
            total_execution_time = (datetime.now() - start_time).total_seconds()
            success_rate = completed_steps / total_steps if total_steps > 0 else 0
            
            # 生成建议
            recommendations = await self._generate_recommendations(step_results, task)
            
            # 创建执行报告
            report = ExecutionReport(
                task_id=task_id,
                total_steps=total_steps,
                completed_steps=completed_steps,
                failed_steps=failed_steps,
                skipped_steps=skipped_steps,
                total_execution_time=total_execution_time,
                success_rate=success_rate,
                step_results=step_results,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
            # 存储报告
            self.execution_reports[task_id] = report
            
            # 移动任务到已完成
            self.completed_tasks[task_id] = self.active_tasks.pop(task_id)
            
            logger.info(f"Hybrid task execution completed: {task_id} (success rate: {success_rate:.2%})")
            return report
            
        except Exception as e:
            logger.error(f"Failed to execute hybrid task {task_id}: {e}")
            raise
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 任务状态信息
        """
        try:
            # 检查活跃任务
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                return {
                    "task_id": task_id,
                    "status": "active",
                    "task_type": task.task_type.value,
                    "title": task.title,
                    "total_steps": len(task.steps),
                    "created_at": task.created_at.isoformat()
                }
            
            # 检查已完成任务
            if task_id in self.completed_tasks:
                task = self.completed_tasks[task_id]
                report = self.execution_reports.get(task_id)
                return {
                    "task_id": task_id,
                    "status": "completed",
                    "task_type": task.task_type.value,
                    "title": task.title,
                    "total_steps": len(task.steps),
                    "success_rate": report.success_rate if report else 0,
                    "created_at": task.created_at.isoformat(),
                    "completed_at": report.timestamp.isoformat() if report else None
                }
            
            return {"task_id": task_id, "status": "not_found"}
            
        except Exception as e:
            logger.error(f"Failed to get task status for {task_id}: {e}")
            return {"task_id": task_id, "status": "error", "error": str(e)}
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消
        """
        try:
            if task_id in self.active_tasks:
                # 移除活跃任务
                cancelled_task = self.active_tasks.pop(task_id)
                
                # 创建取消报告
                report = ExecutionReport(
                    task_id=task_id,
                    total_steps=len(cancelled_task.steps),
                    completed_steps=0,
                    failed_steps=0,
                    skipped_steps=len(cancelled_task.steps),
                    total_execution_time=0,
                    success_rate=0,
                    step_results=[],
                    recommendations=["任务被用户取消"],
                    timestamp=datetime.now()
                )
                
                self.execution_reports[task_id] = report
                logger.info(f"Task cancelled: {task_id}")
                return True
            
            return False

        except Exception as e:
            logger.error(f"Failed to cancel task {task_id}: {e}")
            return False

    async def list_tasks(self, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出任务

        Args:
            status: 任务状态过滤（active, completed, all）

        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        try:
            tasks = []

            # 添加活跃任务
            if status in [None, "active", "all"]:
                for task_id, task in self.active_tasks.items():
                    tasks.append({
                        "task_id": task_id,
                        "status": "active",
                        "task_type": task.task_type.value,
                        "title": task.title,
                        "priority": task.priority,
                        "created_at": task.created_at.isoformat()
                    })

            # 添加已完成任务
            if status in [None, "completed", "all"]:
                for task_id, task in self.completed_tasks.items():
                    report = self.execution_reports.get(task_id)
                    tasks.append({
                        "task_id": task_id,
                        "status": "completed",
                        "task_type": task.task_type.value,
                        "title": task.title,
                        "priority": task.priority,
                        "success_rate": report.success_rate if report else 0,
                        "created_at": task.created_at.isoformat(),
                        "completed_at": report.timestamp.isoformat() if report else None
                    })

            # 按创建时间排序
            tasks.sort(key=lambda x: x["created_at"], reverse=True)
            return tasks

        except Exception as e:
            logger.error(f"Failed to list tasks: {e}")
            return []

    async def get_execution_report(self, task_id: str) -> Optional[ExecutionReport]:
        """获取执行报告

        Args:
            task_id: 任务ID

        Returns:
            ExecutionReport: 执行报告
        """
        return self.execution_reports.get(task_id)

    async def get_execution_statistics(self) -> Dict[str, Any]:
        """获取执行统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            total_tasks = len(self.completed_tasks)
            active_tasks = len(self.active_tasks)

            if total_tasks == 0:
                return {
                    "total_tasks": 0,
                    "active_tasks": active_tasks,
                    "average_success_rate": 0,
                    "task_type_distribution": {},
                    "average_execution_time": 0
                }

            # 计算平均成功率
            success_rates = [
                report.success_rate
                for report in self.execution_reports.values()
            ]
            average_success_rate = sum(success_rates) / len(success_rates) if success_rates else 0

            # 任务类型分布
            task_type_distribution = {}
            for task in self.completed_tasks.values():
                task_type = task.task_type.value
                task_type_distribution[task_type] = task_type_distribution.get(task_type, 0) + 1

            # 平均执行时间
            execution_times = [
                report.total_execution_time
                for report in self.execution_reports.values()
            ]
            average_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0

            return {
                "total_tasks": total_tasks,
                "active_tasks": active_tasks,
                "average_success_rate": round(average_success_rate, 3),
                "task_type_distribution": task_type_distribution,
                "average_execution_time": round(average_execution_time, 2)
            }

        except Exception as e:
            logger.error(f"Failed to get execution statistics: {e}")
            return {}

    # 私有方法

    async def _decompose_task(self, task_type: HybridTaskType, parameters: Dict[str, Any]) -> List[TaskStep]:
        """分解任务为步骤"""
        try:
            if task_type == HybridTaskType.SEARCH_AND_SAVE:
                return await self._decompose_search_and_save_task(parameters)
            elif task_type == HybridTaskType.RESEARCH_AND_ANALYZE:
                return await self._decompose_research_and_analyze_task(parameters)
            elif task_type == HybridTaskType.COLLECT_AND_INTEGRATE:
                return await self._decompose_collect_and_integrate_task(parameters)
            elif task_type == HybridTaskType.MONITOR_AND_REPORT:
                return await self._decompose_monitor_and_report_task(parameters)
            elif task_type == HybridTaskType.VALIDATE_AND_UPDATE:
                return await self._decompose_validate_and_update_task(parameters)
            else:
                raise ValueError(f"Unsupported task type: {task_type}")

        except Exception as e:
            logger.error(f"Failed to decompose task: {e}")
            raise

    async def _decompose_search_and_save_task(self, parameters: Dict[str, Any]) -> List[TaskStep]:
        """分解搜索并保存任务"""
        steps = []

        # 步骤1: 执行搜索
        search_step = TaskStep(
            step_id="search_001",
            step_type=TaskStepType.SEARCH,
            description="执行网络搜索",
            parameters={
                "query": parameters.get("query", ""),
                "max_results": parameters.get("max_results", 5),
                "search_depth": parameters.get("search_depth", "basic")
            },
            dependencies=[],
            estimated_duration=30
        )
        steps.append(search_step)

        # 步骤2: 处理搜索结果
        process_step = TaskStep(
            step_id="process_001",
            step_type=TaskStepType.DATA_PROCESSING,
            description="处理搜索结果",
            parameters={
                "processing_type": "content_extraction",
                "include_summary": parameters.get("include_summary", True)
            },
            dependencies=["search_001"],
            estimated_duration=60
        )
        steps.append(process_step)

        # 步骤3: 保存到文件
        save_step = TaskStep(
            step_id="save_001",
            step_type=TaskStepType.FILE_OPERATION,
            description="保存结果到文件",
            parameters={
                "file_path": parameters.get("output_file", "search_results.json"),
                "format": parameters.get("output_format", "json"),
                "overwrite": parameters.get("overwrite", False)
            },
            dependencies=["process_001"],
            estimated_duration=15
        )
        steps.append(save_step)

        return steps

    async def _decompose_research_and_analyze_task(self, parameters: Dict[str, Any]) -> List[TaskStep]:
        """分解调研并分析任务"""
        steps = []

        # 步骤1: 多轮搜索
        search_step = TaskStep(
            step_id="research_search_001",
            step_type=TaskStepType.SEARCH,
            description="执行多轮调研搜索",
            parameters={
                "topics": parameters.get("topics", []),
                "search_depth": "advanced",
                "max_results_per_topic": parameters.get("max_results_per_topic", 10)
            },
            dependencies=[],
            estimated_duration=180
        )
        steps.append(search_step)

        # 步骤2: 内容分析
        analyze_step = TaskStep(
            step_id="analyze_001",
            step_type=TaskStepType.ANALYSIS,
            description="分析调研内容",
            parameters={
                "analysis_type": "comprehensive",
                "include_trends": parameters.get("include_trends", True),
                "include_correlations": parameters.get("include_correlations", True)
            },
            dependencies=["research_search_001"],
            estimated_duration=120
        )
        steps.append(analyze_step)

        # 步骤3: 生成报告
        report_step = TaskStep(
            step_id="report_001",
            step_type=TaskStepType.FILE_OPERATION,
            description="生成分析报告",
            parameters={
                "report_format": parameters.get("report_format", "markdown"),
                "output_path": parameters.get("report_path", "research_report.md"),
                "include_charts": parameters.get("include_charts", False)
            },
            dependencies=["analyze_001"],
            estimated_duration=90
        )
        steps.append(report_step)

        return steps

    async def _decompose_collect_and_integrate_task(self, parameters: Dict[str, Any]) -> List[TaskStep]:
        """分解收集并整合任务"""
        steps = []

        # 步骤1: 收集多源数据
        collect_step = TaskStep(
            step_id="collect_001",
            step_type=TaskStepType.SEARCH,
            description="收集多源数据",
            parameters={
                "query": parameters.get("query", "collect data from sources"),  # 添加默认查询
                "sources": parameters.get("sources", []),
                "collection_strategy": parameters.get("strategy", "comprehensive"),
                "max_results": parameters.get("max_results", 10)
            },
            dependencies=[],
            estimated_duration=240
        )
        steps.append(collect_step)

        # 步骤2: 数据整合
        integrate_step = TaskStep(
            step_id="integrate_001",
            step_type=TaskStepType.INTEGRATION,
            description="整合收集的数据",
            parameters={
                "integration_type": parameters.get("integration_type", "merge"),
                "quality_threshold": parameters.get("quality_threshold", 0.7)
            },
            dependencies=["collect_001"],
            estimated_duration=120
        )
        steps.append(integrate_step)

        # 步骤3: 验证整合结果
        validate_step = TaskStep(
            step_id="validate_001",
            step_type=TaskStepType.VALIDATION,
            description="验证整合结果",
            parameters={
                "validation_rules": parameters.get("validation_rules", []),
                "quality_check": True
            },
            dependencies=["integrate_001"],
            estimated_duration=60
        )
        steps.append(validate_step)

        return steps

    async def _decompose_monitor_and_report_task(self, parameters: Dict[str, Any]) -> List[TaskStep]:
        """分解监控并报告任务"""
        steps = []

        # 步骤1: 定期监控
        monitor_step = TaskStep(
            step_id="monitor_001",
            step_type=TaskStepType.SEARCH,
            description="执行监控搜索",
            parameters={
                "monitor_targets": parameters.get("targets", []),
                "monitor_interval": parameters.get("interval", 3600),
                "alert_conditions": parameters.get("alert_conditions", [])
            },
            dependencies=[],
            estimated_duration=60
        )
        steps.append(monitor_step)

        # 步骤2: 分析变化
        analyze_step = TaskStep(
            step_id="monitor_analyze_001",
            step_type=TaskStepType.ANALYSIS,
            description="分析监控数据变化",
            parameters={
                "comparison_baseline": parameters.get("baseline", None),
                "change_threshold": parameters.get("change_threshold", 0.1)
            },
            dependencies=["monitor_001"],
            estimated_duration=90
        )
        steps.append(analyze_step)

        # 步骤3: 生成报告
        report_step = TaskStep(
            step_id="monitor_report_001",
            step_type=TaskStepType.FILE_OPERATION,
            description="生成监控报告",
            parameters={
                "report_type": "monitoring",
                "output_path": parameters.get("report_path", "monitor_report.json"),
                "include_alerts": True
            },
            dependencies=["monitor_analyze_001"],
            estimated_duration=45
        )
        steps.append(report_step)

        return steps

    async def _decompose_validate_and_update_task(self, parameters: Dict[str, Any]) -> List[TaskStep]:
        """分解验证并更新任务"""
        steps = []

        # 步骤1: 数据验证
        validate_step = TaskStep(
            step_id="validate_data_001",
            step_type=TaskStepType.VALIDATION,
            description="验证现有数据",
            parameters={
                "data_sources": parameters.get("data_sources", []),
                "validation_schema": parameters.get("schema", None)
            },
            dependencies=[],
            estimated_duration=120
        )
        steps.append(validate_step)

        # 步骤2: 搜索更新信息
        search_update_step = TaskStep(
            step_id="search_update_001",
            step_type=TaskStepType.SEARCH,
            description="搜索更新信息",
            parameters={
                "update_queries": parameters.get("update_queries", []),
                "freshness_requirement": parameters.get("freshness", "recent")
            },
            dependencies=["validate_data_001"],
            estimated_duration=90
        )
        steps.append(search_update_step)

        # 步骤3: 应用更新
        update_step = TaskStep(
            step_id="apply_update_001",
            step_type=TaskStepType.FILE_OPERATION,
            description="应用数据更新",
            parameters={
                "update_strategy": parameters.get("update_strategy", "merge"),
                "backup_original": parameters.get("backup", True)
            },
            dependencies=["search_update_001"],
            estimated_duration=60
        )
        steps.append(update_step)

        return steps

    async def _execute_task_steps(self, task: HybridTask) -> List[StepResult]:
        """执行任务步骤"""
        try:
            step_results = []
            step_data_cache = {}  # 步骤间数据传递缓存

            # 构建依赖图
            dependency_graph = self._build_dependency_graph(task.steps)

            # 按依赖顺序执行步骤
            executed_steps = set()

            while len(executed_steps) < len(task.steps):
                # 找到可以执行的步骤（依赖已满足）
                ready_steps = [
                    step for step in task.steps
                    if step.step_id not in executed_steps and
                    all(dep in executed_steps for dep in step.dependencies)
                ]

                if not ready_steps:
                    # 检查是否有循环依赖
                    remaining_steps = [s for s in task.steps if s.step_id not in executed_steps]
                    logger.error(f"Circular dependency detected in task {task.task_id}")

                    # 跳过剩余步骤
                    for step in remaining_steps:
                        result = StepResult(
                            step_id=step.step_id,
                            status=ExecutionStatus.SKIPPED,
                            result_data=None,
                            error_message="Skipped due to circular dependency",
                            execution_time=0,
                            timestamp=datetime.now()
                        )
                        step_results.append(result)
                        executed_steps.add(step.step_id)
                    break

                # 并发执行准备好的步骤
                concurrent_tasks = []
                for step in ready_steps[:self.max_concurrent_steps]:
                    concurrent_tasks.append(
                        self._execute_single_step(step, step_data_cache, task.context)
                    )

                # 等待步骤完成
                batch_results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)

                # 处理结果
                for i, result in enumerate(batch_results):
                    step = ready_steps[i]

                    if isinstance(result, Exception):
                        step_result = StepResult(
                            step_id=step.step_id,
                            status=ExecutionStatus.FAILED,
                            result_data=None,
                            error_message=str(result),
                            execution_time=0,
                            timestamp=datetime.now()
                        )
                    else:
                        step_result = result
                        # 缓存成功步骤的结果数据
                        if step_result.status == ExecutionStatus.COMPLETED:
                            step_data_cache[step.step_id] = step_result.result_data

                    step_results.append(step_result)
                    executed_steps.add(step.step_id)

            return step_results

        except Exception as e:
            logger.error(f"Failed to execute task steps: {e}")
            raise

    def _build_dependency_graph(self, steps: List[TaskStep]) -> Dict[str, List[str]]:
        """构建依赖关系图"""
        graph = {}
        for step in steps:
            graph[step.step_id] = step.dependencies.copy()
        return graph

    async def _execute_single_step(self,
                                 step: TaskStep,
                                 step_data_cache: Dict[str, Any],
                                 task_context: Dict[str, Any]) -> StepResult:
        """执行单个步骤"""
        start_time = datetime.now()

        try:
            # 获取步骤执行器
            executor = self.step_executors.get(step.step_type)
            if not executor:
                raise ValueError(f"No executor found for step type: {step.step_type}")

            # 准备执行参数
            execution_params = {
                "step": step,
                "step_data_cache": step_data_cache,
                "task_context": task_context
            }

            # 执行步骤
            result_data = await executor(**execution_params)

            execution_time = (datetime.now() - start_time).total_seconds()

            return StepResult(
                step_id=step.step_id,
                status=ExecutionStatus.COMPLETED,
                result_data=result_data,
                error_message=None,
                execution_time=execution_time,
                timestamp=datetime.now()
            )

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()

            # 检查是否需要重试
            if step.retry_count < step.max_retries:
                step.retry_count += 1
                logger.warning(f"Step {step.step_id} failed, retrying ({step.retry_count}/{step.max_retries}): {e}")

                # 等待一段时间后重试
                await asyncio.sleep(min(step.retry_count * 2, 10))
                return await self._execute_single_step(step, step_data_cache, task_context)

            logger.error(f"Step {step.step_id} failed after {step.max_retries} retries: {e}")

            return StepResult(
                step_id=step.step_id,
                status=ExecutionStatus.FAILED,
                result_data=None,
                error_message=str(e),
                execution_time=execution_time,
                timestamp=datetime.now()
            )

    # 步骤执行器方法

    async def _execute_search_step(self, **kwargs) -> Any:
        """执行搜索步骤"""
        step = kwargs["step"]
        step_data_cache = kwargs["step_data_cache"]
        task_context = kwargs["task_context"]

        try:
            if not self.search_operations:
                raise ValueError("SearchOperations not available")

            parameters = step.parameters

            # 执行搜索
            if "topics" in parameters:
                # 多主题搜索
                results = []
                for topic in parameters["topics"]:
                    search_result = await self.search_operations.search(
                        query=topic,
                        max_results=parameters.get("max_results_per_topic", 5),
                        search_depth=parameters.get("search_depth", "basic")
                    )
                    results.append({
                        "topic": topic,
                        "results": search_result
                    })
                return {"multi_topic_results": results}
            else:
                # 单一搜索
                query = parameters.get("query", "")
                if not query:
                    raise ValueError("Search query is required")

                result = await self.search_operations.search(
                    query=query,
                    max_results=parameters.get("max_results", 5),
                    search_depth=parameters.get("search_depth", "basic")
                )
                return {"search_results": result}

        except Exception as e:
            logger.error(f"Search step execution failed: {e}")
            raise

    async def _execute_file_operation_step(self, **kwargs) -> Any:
        """执行文件操作步骤"""
        step = kwargs["step"]
        step_data_cache = kwargs["step_data_cache"]
        task_context = kwargs["task_context"]

        try:
            if not self.file_operations:
                raise ValueError("FileOperations not available")

            parameters = step.parameters

            # 获取依赖步骤的数据
            input_data = None
            if step.dependencies:
                # 合并所有依赖步骤的数据
                input_data = {}
                for dep_id in step.dependencies:
                    if dep_id in step_data_cache:
                        dep_data = step_data_cache[dep_id]
                        if isinstance(dep_data, dict):
                            input_data.update(dep_data)
                        else:
                            input_data[dep_id] = dep_data

            # 根据操作类型执行文件操作
            if "file_path" in parameters:
                # 保存数据到文件
                file_path = parameters["file_path"]
                format_type = parameters.get("format", "json")

                if format_type == "json":
                    content = json.dumps(input_data, ensure_ascii=False, indent=2)
                elif format_type == "text":
                    content = str(input_data)
                else:
                    content = str(input_data)

                # 使用文件操作保存
                await self.file_operations.write_file(file_path, content)

                return {
                    "file_path": file_path,
                    "format": format_type,
                    "size": len(content)
                }

            elif "report_type" in parameters:
                # 生成报告
                report_type = parameters["report_type"]
                output_path = parameters.get("output_path", f"{report_type}_report.md")

                # 生成报告内容
                report_content = await self._generate_report_content(
                    report_type, input_data, parameters
                )

                # 保存报告
                await self.file_operations.write_file(output_path, report_content)

                return {
                    "report_path": output_path,
                    "report_type": report_type,
                    "content_length": len(report_content)
                }

            else:
                raise ValueError("Unknown file operation type")

        except Exception as e:
            logger.error(f"File operation step execution failed: {e}")
            raise

    async def _execute_data_processing_step(self, **kwargs) -> Any:
        """执行数据处理步骤"""
        step = kwargs["step"]
        step_data_cache = kwargs["step_data_cache"]
        task_context = kwargs["task_context"]

        try:
            if not self.content_processor:
                raise ValueError("ContentProcessor not available")

            parameters = step.parameters

            # 获取输入数据
            input_data = None
            if step.dependencies:
                for dep_id in step.dependencies:
                    if dep_id in step_data_cache:
                        input_data = step_data_cache[dep_id]
                        break

            if not input_data:
                raise ValueError("No input data available for processing")

            # 根据处理类型执行处理
            processing_type = parameters.get("processing_type", "content_extraction")

            if processing_type == "content_extraction":
                # 内容提取
                if "search_results" in input_data:
                    processed_results = []
                    for result in input_data["search_results"]:
                        processed = await self.content_processor.process_content(
                            content=result.get("content", ""),
                            content_type="web_page"
                        )
                        processed_results.append(processed)

                    return {"processed_results": processed_results}

            elif processing_type == "summarization":
                # 内容摘要
                if "processed_results" in input_data:
                    summaries = []
                    for result in input_data["processed_results"]:
                        summary = await self.content_processor.generate_summary(
                            content=result.get("processed_content", ""),
                            max_length=parameters.get("max_summary_length", 200)
                        )
                        summaries.append(summary)

                    return {"summaries": summaries}

            return {"processed_data": input_data}

        except Exception as e:
            logger.error(f"Data processing step execution failed: {e}")
            raise

    async def _execute_analysis_step(self, **kwargs) -> Any:
        """执行分析步骤"""
        step = kwargs["step"]
        step_data_cache = kwargs["step_data_cache"]
        task_context = kwargs["task_context"]

        try:
            parameters = step.parameters

            # 获取输入数据
            input_data = {}
            if step.dependencies:
                for dep_id in step.dependencies:
                    if dep_id in step_data_cache:
                        dep_data = step_data_cache[dep_id]
                        if isinstance(dep_data, dict):
                            input_data.update(dep_data)
                        else:
                            input_data[dep_id] = dep_data

            analysis_type = parameters.get("analysis_type", "basic")

            if analysis_type == "comprehensive":
                # 综合分析
                analysis_result = {
                    "data_summary": self._analyze_data_summary(input_data),
                    "key_insights": self._extract_key_insights(input_data),
                    "trends": self._analyze_trends(input_data) if parameters.get("include_trends") else None,
                    "correlations": self._analyze_correlations(input_data) if parameters.get("include_correlations") else None,
                    "recommendations": self._generate_analysis_recommendations(input_data)
                }

                return {"analysis_result": analysis_result}

            elif analysis_type == "change_detection":
                # 变化检测分析
                baseline = parameters.get("comparison_baseline")
                if baseline:
                    changes = self._detect_changes(input_data, baseline,
                                                 parameters.get("change_threshold", 0.1))
                    return {"change_analysis": changes}
                else:
                    return {"change_analysis": {"error": "No baseline provided for comparison"}}

            else:
                # 基础分析
                basic_analysis = {
                    "data_count": len(input_data),
                    "data_types": list(input_data.keys()),
                    "timestamp": datetime.now().isoformat()
                }
                return {"basic_analysis": basic_analysis}

        except Exception as e:
            logger.error(f"Analysis step execution failed: {e}")
            raise

    async def _execute_integration_step(self, **kwargs) -> Any:
        """执行整合步骤"""
        step = kwargs["step"]
        step_data_cache = kwargs["step_data_cache"]
        task_context = kwargs["task_context"]

        try:
            if not self.data_integrator:
                raise ValueError("DataIntegrator not available")

            parameters = step.parameters

            # 收集所有依赖步骤的数据作为数据源
            data_sources = []
            for dep_id in step.dependencies:
                if dep_id in step_data_cache:
                    dep_data = step_data_cache[dep_id]

                    # 注册数据源
                    source_id = await self.data_integrator.register_data_source(
                        source_data=dep_data,
                        source_type="processed_content",  # 假设为处理后的内容
                        metadata={"step_id": dep_id, "timestamp": datetime.now().isoformat()}
                    )
                    data_sources.append(source_id)

            if not data_sources:
                raise ValueError("No data sources available for integration")

            # 执行数据整合
            integration_type = parameters.get("integration_type", "merge")
            integration_id = await self.data_integrator.integrate_data(
                source_ids=data_sources,
                integration_type=integration_type,
                title=f"Hybrid task integration - {step.step_id}"
            )

            # 获取整合结果
            integrated_data = await self.data_integrator.get_integrated_data(integration_id)

            return {
                "integration_id": integration_id,
                "integrated_data": integrated_data.to_dict() if integrated_data else None,
                "source_count": len(data_sources)
            }

        except Exception as e:
            logger.error(f"Integration step execution failed: {e}")
            raise

    async def _execute_validation_step(self, **kwargs) -> Any:
        """执行验证步骤"""
        step = kwargs["step"]
        step_data_cache = kwargs["step_data_cache"]
        task_context = kwargs["task_context"]

        try:
            parameters = step.parameters

            # 获取要验证的数据
            validation_data = {}
            if step.dependencies:
                for dep_id in step.dependencies:
                    if dep_id in step_data_cache:
                        dep_data = step_data_cache[dep_id]
                        if isinstance(dep_data, dict):
                            validation_data.update(dep_data)
                        else:
                            validation_data[dep_id] = dep_data

            # 执行验证
            validation_results = {
                "is_valid": True,
                "validation_errors": [],
                "validation_warnings": [],
                "quality_score": 1.0
            }

            # 检查数据完整性
            if not validation_data:
                validation_results["is_valid"] = False
                validation_results["validation_errors"].append("No data to validate")
                validation_results["quality_score"] = 0.0

            # 应用验证规则
            validation_rules = parameters.get("validation_rules", [])
            for rule in validation_rules:
                rule_result = self._apply_validation_rule(validation_data, rule)
                if not rule_result["passed"]:
                    validation_results["is_valid"] = False
                    validation_results["validation_errors"].append(rule_result["message"])
                    validation_results["quality_score"] *= 0.8

            # 质量检查
            if parameters.get("quality_check", False):
                quality_issues = self._check_data_quality(validation_data)
                validation_results["validation_warnings"].extend(quality_issues)
                if quality_issues:
                    validation_results["quality_score"] *= 0.9

            return {"validation_result": validation_results}

        except Exception as e:
            logger.error(f"Validation step execution failed: {e}")
            raise

    # 辅助方法

    def _analyze_data_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """分析数据摘要"""
        return {
            "total_items": len(data),
            "data_types": list(data.keys()),
            "has_search_results": "search_results" in data,
            "has_processed_data": "processed_results" in data or "processed_data" in data
        }

    def _extract_key_insights(self, data: Dict[str, Any]) -> List[str]:
        """提取关键洞察"""
        insights = []

        if "search_results" in data:
            results_count = len(data["search_results"])
            insights.append(f"Found {results_count} search results")

        if "processed_results" in data:
            processed_count = len(data["processed_results"])
            insights.append(f"Processed {processed_count} content items")

        if "integration_id" in data:
            insights.append("Data integration completed successfully")

        return insights

    def _analyze_trends(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """分析趋势"""
        # 简化的趋势分析
        return {
            "trend_direction": "stable",
            "confidence": 0.7,
            "note": "Trend analysis requires time-series data"
        }

    def _analyze_correlations(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """分析相关性"""
        # 简化的相关性分析
        correlations = []
        data_keys = list(data.keys())

        for i in range(len(data_keys)):
            for j in range(i + 1, len(data_keys)):
                correlations.append({
                    "variable1": data_keys[i],
                    "variable2": data_keys[j],
                    "correlation": 0.5,  # 简化值
                    "significance": "moderate"
                })

        return {"correlations": correlations}

    def _generate_analysis_recommendations(self, data: Dict[str, Any]) -> List[str]:
        """生成分析建议"""
        recommendations = []

        if "search_results" in data and len(data["search_results"]) < 3:
            recommendations.append("Consider expanding search criteria for more comprehensive results")

        if "validation_result" in data:
            validation = data["validation_result"]
            if not validation.get("is_valid", True):
                recommendations.append("Address validation errors before proceeding")

        return recommendations

    def _detect_changes(self, current_data: Dict[str, Any],
                       baseline_data: Dict[str, Any],
                       threshold: float) -> Dict[str, Any]:
        """检测变化"""
        changes = {
            "added_keys": [],
            "removed_keys": [],
            "modified_keys": [],
            "change_magnitude": 0.0
        }

        current_keys = set(current_data.keys())
        baseline_keys = set(baseline_data.keys())

        changes["added_keys"] = list(current_keys - baseline_keys)
        changes["removed_keys"] = list(baseline_keys - current_keys)

        # 检查修改的键
        common_keys = current_keys & baseline_keys
        for key in common_keys:
            if current_data[key] != baseline_data[key]:
                changes["modified_keys"].append(key)

        # 计算变化幅度
        total_changes = len(changes["added_keys"]) + len(changes["removed_keys"]) + len(changes["modified_keys"])
        total_keys = len(current_keys | baseline_keys)
        changes["change_magnitude"] = total_changes / total_keys if total_keys > 0 else 0

        return changes

    def _apply_validation_rule(self, data: Dict[str, Any], rule: Dict[str, Any]) -> Dict[str, Any]:
        """应用验证规则"""
        rule_type = rule.get("type", "required_field")

        if rule_type == "required_field":
            field_name = rule.get("field")
            if field_name and field_name not in data:
                return {
                    "passed": False,
                    "message": f"Required field '{field_name}' is missing"
                }

        elif rule_type == "min_items":
            field_name = rule.get("field")
            min_count = rule.get("min_count", 1)
            if field_name in data and isinstance(data[field_name], list):
                if len(data[field_name]) < min_count:
                    return {
                        "passed": False,
                        "message": f"Field '{field_name}' has {len(data[field_name])} items, minimum required: {min_count}"
                    }

        elif rule_type == "data_type":
            field_name = rule.get("field")
            expected_type = rule.get("expected_type")
            if field_name in data:
                actual_type = type(data[field_name]).__name__
                if actual_type != expected_type:
                    return {
                        "passed": False,
                        "message": f"Field '{field_name}' has type {actual_type}, expected: {expected_type}"
                    }

        return {"passed": True, "message": "Validation rule passed"}

    def _check_data_quality(self, data: Dict[str, Any]) -> List[str]:
        """检查数据质量"""
        quality_issues = []

        # 检查空值
        for key, value in data.items():
            if value is None or (isinstance(value, str) and not value.strip()):
                quality_issues.append(f"Field '{key}' is empty or null")
            elif isinstance(value, list) and len(value) == 0:
                quality_issues.append(f"Field '{key}' is an empty list")
            elif isinstance(value, dict) and len(value) == 0:
                quality_issues.append(f"Field '{key}' is an empty dictionary")

        # 检查数据一致性
        if "search_results" in data and "processed_results" in data:
            search_count = len(data["search_results"])
            processed_count = len(data["processed_results"])
            if search_count != processed_count:
                quality_issues.append(f"Mismatch between search results ({search_count}) and processed results ({processed_count})")

        return quality_issues

    async def _generate_report_content(self,
                                     report_type: str,
                                     data: Dict[str, Any],
                                     parameters: Dict[str, Any]) -> str:
        """生成报告内容"""
        try:
            if report_type == "research":
                return self._generate_research_report(data, parameters)
            elif report_type == "monitoring":
                return self._generate_monitoring_report(data, parameters)
            elif report_type == "analysis":
                return self._generate_analysis_report(data, parameters)
            else:
                return self._generate_generic_report(data, parameters)

        except Exception as e:
            logger.error(f"Failed to generate report content: {e}")
            return f"# Report Generation Error\n\nFailed to generate {report_type} report: {str(e)}"

    def _generate_research_report(self, data: Dict[str, Any], parameters: Dict[str, Any]) -> str:
        """生成调研报告"""
        report_lines = [
            "# 调研分析报告",
            f"\n**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "\n## 执行摘要"
        ]

        if "multi_topic_results" in data:
            topics = data["multi_topic_results"]
            report_lines.append(f"\n本次调研涵盖了 {len(topics)} 个主题，共收集了相关信息。")

            report_lines.append("\n## 主题分析")
            for topic_data in topics:
                topic = topic_data["topic"]
                results = topic_data["results"]
                report_lines.append(f"\n### {topic}")
                report_lines.append(f"- 搜索结果数量: {len(results)}")

                if results:
                    report_lines.append("- 主要来源:")
                    for i, result in enumerate(results[:3], 1):
                        title = result.get("title", "未知标题")
                        url = result.get("url", "")
                        report_lines.append(f"  {i}. [{title}]({url})")

        if "analysis_result" in data:
            analysis = data["analysis_result"]
            report_lines.append("\n## 分析结果")

            if "key_insights" in analysis:
                report_lines.append("\n### 关键洞察")
                for insight in analysis["key_insights"]:
                    report_lines.append(f"- {insight}")

            if "recommendations" in analysis:
                report_lines.append("\n### 建议")
                for rec in analysis["recommendations"]:
                    report_lines.append(f"- {rec}")

        report_lines.append(f"\n---\n*报告由 HyAIAgent 自动生成*")

        return "\n".join(report_lines)

    def _generate_monitoring_report(self, data: Dict[str, Any], parameters: Dict[str, Any]) -> str:
        """生成监控报告"""
        report_data = {
            "title": "监控报告",
            "timestamp": datetime.now().isoformat(),
            "monitoring_data": data,
            "alerts": [],
            "summary": {
                "status": "normal",
                "issues_detected": 0
            }
        }

        # 检查变化分析
        if "change_analysis" in data:
            changes = data["change_analysis"]
            if changes.get("change_magnitude", 0) > 0.1:
                report_data["alerts"].append({
                    "type": "significant_change",
                    "message": f"检测到显著变化，变化幅度: {changes['change_magnitude']:.2%}",
                    "severity": "medium"
                })
                report_data["summary"]["issues_detected"] += 1

        return json.dumps(report_data, ensure_ascii=False, indent=2)

    def _generate_analysis_report(self, data: Dict[str, Any], parameters: Dict[str, Any]) -> str:
        """生成分析报告"""
        report_lines = [
            "# 数据分析报告",
            f"\n**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        ]

        if "analysis_result" in data:
            analysis = data["analysis_result"]

            if "data_summary" in analysis:
                summary = analysis["data_summary"]
                report_lines.append("\n## 数据概览")
                report_lines.append(f"- 数据项总数: {summary.get('total_items', 0)}")
                report_lines.append(f"- 数据类型: {', '.join(summary.get('data_types', []))}")

            if "trends" in analysis and analysis["trends"]:
                trends = analysis["trends"]
                report_lines.append("\n## 趋势分析")
                report_lines.append(f"- 趋势方向: {trends.get('trend_direction', '未知')}")
                report_lines.append(f"- 置信度: {trends.get('confidence', 0):.2%}")

            if "correlations" in analysis and analysis["correlations"]:
                correlations = analysis["correlations"]["correlations"]
                report_lines.append("\n## 相关性分析")
                for corr in correlations[:5]:  # 只显示前5个
                    report_lines.append(f"- {corr['variable1']} vs {corr['variable2']}: {corr['correlation']:.2f}")

        return "\n".join(report_lines)

    def _generate_generic_report(self, data: Dict[str, Any], parameters: Dict[str, Any]) -> str:
        """生成通用报告"""
        return json.dumps({
            "report_type": "generic",
            "timestamp": datetime.now().isoformat(),
            "data": data,
            "parameters": parameters
        }, ensure_ascii=False, indent=2)

    async def _generate_recommendations(self,
                                      step_results: List[StepResult],
                                      task: HybridTask) -> List[str]:
        """生成执行建议"""
        recommendations = []

        # 分析失败步骤
        failed_steps = [r for r in step_results if r.status == ExecutionStatus.FAILED]
        if failed_steps:
            recommendations.append(f"有 {len(failed_steps)} 个步骤执行失败，建议检查错误信息并重试")

        # 分析执行时间
        total_time = sum(r.execution_time for r in step_results)
        if total_time > task.timeout * 0.8:
            recommendations.append("任务执行时间接近超时限制，建议优化步骤或增加超时时间")

        # 分析步骤依赖
        if len(task.steps) > 5:
            recommendations.append("任务包含较多步骤，建议考虑拆分为多个子任务")

        # 分析成功率
        success_rate = len([r for r in step_results if r.status == ExecutionStatus.COMPLETED]) / len(step_results)
        if success_rate < 0.8:
            recommendations.append("任务成功率较低，建议检查步骤配置和依赖关系")

        return recommendations
