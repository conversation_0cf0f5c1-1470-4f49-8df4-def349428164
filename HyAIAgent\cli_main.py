"""
HyAIAgent CLI主程序入口

这是HyAIAgent的命令行界面入口点。
"""

import asyncio
import logging
import signal
import sys
from typing import Optional
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.autonomous_agent import AutonomousAgent, get_agent, shutdown_agent
from core.config_manager import ConfigManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/hyaiagent_cli.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


class HyAIAgentCLI:
    """HyAIAgent命令行界面"""
    
    def __init__(self):
        self.agent: Optional[AutonomousAgent] = None
        self.running = False
    
    async def start(self):
        """启动CLI"""
        try:
            logger.info("启动HyAIAgent CLI")
            
            # 获取代理实例
            self.agent = await get_agent()
            self.running = True
            
            # 显示欢迎信息
            self._show_welcome()
            
            # 主循环
            await self._main_loop()
            
        except Exception as e:
            logger.error(f"CLI启动失败: {str(e)}")
            sys.exit(1)
    
    def _show_welcome(self):
        """显示欢迎信息"""
        print("\n" + "="*60)
        print("🤖 欢迎使用 HyAIAgent - 智能自主AI助手")
        print("="*60)
        print("📝 输入您的请求，我将自主分析并执行")
        print("💡 输入 'help' 查看帮助信息")
        print("🚪 输入 'quit' 或 'exit' 退出程序")
        print("⏸️  输入 'pause' 暂停代理，'resume' 恢复代理")
        print("📊 输入 'status' 查看代理状态")
        print("-"*60)
    
    async def _main_loop(self):
        """主循环"""
        while self.running:
            try:
                # 获取用户输入
                user_input = await self._get_user_input()
                
                if not user_input.strip():
                    continue
                
                # 处理特殊命令
                if await self._handle_special_commands(user_input):
                    continue
                
                # 处理用户请求
                await self._process_user_request(user_input)
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                logger.error(f"主循环错误: {str(e)}")
                print(f"❌ 发生错误: {str(e)}")
    
    async def _get_user_input(self) -> str:
        """获取用户输入"""
        try:
            # 显示代理状态提示符
            status_indicator = self._get_status_indicator()
            prompt = f"\n{status_indicator} 请输入您的请求: "
            
            # 在异步环境中获取输入
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, input, prompt)
        except EOFError:
            return "quit"
    
    def _get_status_indicator(self) -> str:
        """获取状态指示器"""
        if not self.agent:
            return "❓"
        
        status_map = {
            "idle": "💤",
            "thinking": "🤔", 
            "planning": "📋",
            "executing": "⚡",
            "evaluating": "🔍",
            "paused": "⏸️",
            "error": "❌",
            "stopped": "🛑"
        }
        
        return status_map.get(self.agent.state, "❓")
    
    async def _handle_special_commands(self, user_input: str) -> bool:
        """处理特殊命令"""
        command = user_input.lower().strip()
        
        if command in ['quit', 'exit', 'q']:
            print("👋 正在退出...")
            self.running = False
            return True
        
        elif command == 'help':
            self._show_help()
            return True
        
        elif command == 'status':
            await self._show_status()
            return True
        
        elif command == 'pause':
            await self._pause_agent()
            return True
        
        elif command == 'resume':
            await self._resume_agent()
            return True
        
        elif command == 'restart':
            await self._restart_agent()
            return True
        
        return False
    
    def _show_help(self):
        """显示帮助信息"""
        print("\n📖 HyAIAgent 帮助信息")
        print("-"*40)
        print("🔧 基本命令:")
        print("  help     - 显示此帮助信息")
        print("  status   - 显示代理状态")
        print("  pause    - 暂停代理")
        print("  resume   - 恢复代理")
        print("  restart  - 重启代理")
        print("  quit     - 退出程序")
        print("\n💡 使用说明:")
        print("  - 直接输入您的请求，代理会自动分析并执行")
        print("  - 代理支持复杂的多步骤任务")
        print("  - 可以随时查看执行状态和进度")
        print("-"*40)
    
    async def _show_status(self):
        """显示代理状态"""
        if not self.agent:
            print("❌ 代理未初始化")
            return
        
        status = self.agent.get_detailed_status()
        
        print("\n📊 代理状态信息")
        print("-"*40)
        print(f"🆔 代理ID: {status['agent_id'][:8]}...")
        print(f"🔄 状态: {status['state']}")
        print(f"▶️  运行中: {'是' if status['is_running'] else '否'}")
        print(f"🎯 会话ID: {status.get('session_id', '无')}")
        print(f"📅 创建时间: {status['created_at']}")
        
        stats = status['stats']
        print(f"\n📈 统计信息:")
        print(f"  总请求数: {stats['total_requests']}")
        print(f"  成功执行: {stats['successful_executions']}")
        print(f"  失败执行: {stats['failed_executions']}")
        print(f"  平均响应时间: {stats['average_response_time']:.2f}秒")
        print("-"*40)
    
    async def _pause_agent(self):
        """暂停代理"""
        if not self.agent:
            print("❌ 代理未初始化")
            return
        
        success = await self.agent.pause()
        if success:
            print("⏸️  代理已暂停")
        else:
            print("❌ 暂停代理失败")
    
    async def _resume_agent(self):
        """恢复代理"""
        if not self.agent:
            print("❌ 代理未初始化")
            return
        
        success = await self.agent.resume()
        if success:
            print("▶️  代理已恢复")
        else:
            print("❌ 恢复代理失败")
    
    async def _restart_agent(self):
        """重启代理"""
        print("🔄 正在重启代理...")
        
        # 停止当前代理
        if self.agent and self.agent.is_running:
            await self.agent.stop()
        
        # 关闭全局代理
        await shutdown_agent()
        
        # 重新获取代理
        self.agent = await get_agent()
        print("✅ 代理重启完成")
    
    async def _process_user_request(self, request: str):
        """处理用户请求"""
        try:
            print(f"\n🤖 正在处理您的请求: {request[:50]}...")
            
            # 发送请求给代理
            result = await self.agent.process_request(request)
            
            if result['success']:
                print(f"✅ {result['message']}")
                if 'session_id' in result:
                    print(f"🎯 会话ID: {result['session_id'][:8]}...")
            else:
                print(f"❌ 处理失败: {result.get('error', '未知错误')}")
        
        except Exception as e:
            logger.error(f"处理用户请求失败: {str(e)}")
            print(f"❌ 处理请求时发生错误: {str(e)}")
    
    async def stop(self):
        """停止CLI"""
        self.running = False
        await self.cleanup()

    async def process_command(self, command: str) -> dict:
        """处理命令（公共接口）"""
        try:
            if not self.agent:
                return {"success": False, "error": "代理未初始化"}

            # 处理特殊命令
            if command.lower() in ['help', 'status', 'pause', 'resume', 'quit', 'exit']:
                return {"success": True, "result": f"特殊命令: {command}"}

            # 处理用户请求
            result = await self.agent.process_request(command)
            return {"success": True, "result": result}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def cleanup(self):
        """清理资源"""
        try:
            if self.agent and self.agent.is_running:
                await self.agent.stop()
            await shutdown_agent()
            logger.info("资源清理完成")
        except Exception as e:
            logger.error(f"清理资源失败: {str(e)}")


async def main():
    """主函数"""
    cli = HyAIAgentCLI()
    
    try:
        await cli.start()
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}")
    finally:
        await cli.cleanup()


if __name__ == "__main__":
    # 确保日志目录存在
    Path("logs").mkdir(exist_ok=True)
    
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 程序启动失败: {str(e)}")
        sys.exit(1)
