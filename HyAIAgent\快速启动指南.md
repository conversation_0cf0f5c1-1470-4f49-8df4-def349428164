# HyAIAgent 快速启动指南

## 🚀 5分钟快速上手

### 第一步：环境检查

确保你的系统满足以下要求：

- ✅ **Python 3.8+** 已安装
- ✅ **网络连接** 正常
- ✅ **API 密钥** 已准备（OpenAI 或兼容服务）

### 第二步：安装依赖

在项目目录下运行：

```bash
pip install -r requirements.txt
```

**依赖包说明**：
- `PyQt6>=6.4.0` - 图形界面框架
- `openai>=1.0.0` - AI 客户端
- `loguru>=0.6.0` - 日志系统
- `tinydb>=4.8.0` - 轻量级数据库
- `jinja2>=3.1.0` - 模板引擎
- `python-dotenv>=1.0.0` - 环境变量管理

### 第三步：配置 API 密钥

#### 方法一：创建 .env 文件（推荐）

在项目根目录创建 `.env` 文件：

```env
# OpenAI 官方 API
OPENAI_API_KEY=sk-your-openai-key-here
OPENAI_BASE_URL=https://api.openai.com/v1

# 或者使用 DeepSeek API
# OPENAI_API_KEY=sk-your-deepseek-key-here
# OPENAI_BASE_URL=https://api.deepseek.com/v1

# 或者使用其他兼容 OpenAI API 的服务
# OPENAI_API_KEY=your-api-key
# OPENAI_BASE_URL=https://your-api-endpoint/v1
```

#### 方法二：修改配置文件

编辑 `config.json` 文件：

```json
{
  "ai": {
    "providers": {
      "openai": {
        "api_key": "your-api-key-here",
        "base_url": "https://api.openai.com/v1"
      }
    }
  }
}
```

### 第四步：启动应用

运行以下命令：

```bash
python main.py
```

如果一切正常，你会看到：

1. 控制台显示初始化信息
2. 弹出聊天窗口界面
3. 状态栏显示"AI客户端已连接"

## 🎯 第一次使用

### 发送第一条消息

1. 在输入框中输入：`你好，请介绍一下你自己`
2. 点击"发送"按钮或按回车键
3. 等待 AI 回复（通常几秒钟）

### 基本操作

- **发送消息**: 输入框 → 回车键 或 点击发送按钮
- **新建对话**: 菜单栏 → 文件 → 新建对话
- **保存对话**: 菜单栏 → 文件 → 保存对话
- **修改设置**: 菜单栏 → 设置 → 首选项

## ⚙️ 常用设置

### 调整 AI 参数

1. 点击菜单栏"设置" → "首选项"
2. 调整以下参数：
   - **模型**: 选择 GPT-3.5-turbo 或 GPT-4
   - **最大 Token 数**: 控制回复长度（建议 1000-2000）
   - **温度**: 控制创造性（0.0-2.0，建议 0.7）

### 界面个性化

- **字体大小**: 设置 → 首选项 → 字体大小
- **自动保存**: 设置 → 首选项 → 自动保存对话

## 🔧 常见问题解决

### 问题1：启动失败

**现象**: 运行 `python main.py` 后报错

**解决方案**:
```bash
# 检查 Python 版本
python --version

# 重新安装依赖
pip install -r requirements.txt --force-reinstall

# 检查依赖是否正确安装
pip list | grep -E "(PyQt6|openai|loguru|tinydb|jinja2|python-dotenv)"
```

### 问题2：API 连接失败

**现象**: 状态栏显示"AI客户端连接失败"

**解决方案**:
1. 检查 `.env` 文件中的 API 密钥是否正确
2. 确认网络连接正常
3. 验证 API 服务地址是否可访问

```bash
# 测试网络连接
ping api.openai.com

# 检查环境变量
python -c "import os; from dotenv import load_dotenv; load_dotenv(); print(os.getenv('OPENAI_API_KEY'))"
```

### 问题3：界面显示异常

**现象**: 窗口布局错乱或字体显示异常

**解决方案**:
1. 检查系统 DPI 设置
2. 重启应用程序
3. 重置配置文件

```bash
# 备份并重置配置
cp config.json config.json.backup
# 删除 config.json，应用会自动创建默认配置
```

## 📊 性能优化建议

### 1. 模型选择

- **日常对话**: 使用 `gpt-3.5-turbo`（速度快，成本低）
- **复杂任务**: 使用 `gpt-4`（质量高，成本高）

### 2. Token 控制

- **短对话**: 设置 max_tokens = 500-1000
- **长文本**: 设置 max_tokens = 2000-4000
- **注意**: Token 数量直接影响 API 费用

### 3. 温度参数

- **事实性回答**: temperature = 0.0-0.3
- **创意性回答**: temperature = 0.7-1.0
- **平衡设置**: temperature = 0.7（推荐）

## 🎉 恭喜！

你已经成功启动了 HyAIAgent！现在可以：

1. 🗣️ **开始对话**: 与 AI 进行自然语言交流
2. 💾 **保存记录**: 重要对话自动保存
3. ⚙️ **个性化设置**: 根据需要调整参数
4. 📝 **使用模板**: 探索提示词模板功能

## 📚 进一步学习

- 📖 [用户使用说明.md](用户使用说明.md) - 详细功能介绍
- 🏗️ [开发指引文件/](开发指引文件/) - 开发文档
- 🔧 [config.json](config.json) - 配置文件说明

---

**需要帮助？** 查看日志文件 `logs/hyaiagent.log` 获取详细信息
